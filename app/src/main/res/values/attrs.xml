<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ToolbarView">
        <attr name="back_icon" format="integer" />
        <attr name="android:text" />
    </declare-styleable>

    <declare-styleable name="ViewTextLabel">
        <attr name="labelText" format="string" />
        <attr name="labelTextColor" format="color" />
        <attr name="labelColor" format="color" />
    </declare-styleable>

    <declare-styleable name="CurrencyEditText">
        <attr name="separateCurrencyColor" format="boolean" />
        <attr name="currencyColor" format="reference" />
    </declare-styleable>

    <declare-styleable name="InvoiceOnboardingItemView">
        <attr name="isDone" format="boolean" />
        <attr name="background" />
        <attr name="iconBackground"  format="reference"/>
        <attr name="TextColor" format="color"  />
        <attr name="title"/>
        <attr name="isDescription" format="boolean"/>
        <attr name="stepNumber" format="string"/>
    </declare-styleable>
</resources>
