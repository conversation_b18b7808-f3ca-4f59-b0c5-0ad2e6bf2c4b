<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_10dp"
    android:paddingBottom="@dimen/_18dp">

    <TextView
        android:id="@+id/heading"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/type_of_payment"
        app:layout_constraintBottom_toBottomOf="@id/bt_sell"
        app:layout_constraintEnd_toStartOf="@id/bt_sell"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/bt_sell" />

    <Button
        android:id="@+id/bt_sell"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@drawable/ppob_button_selector"
        android:enabled="false"
        android:paddingHorizontal="@dimen/dimen_2dp"
        android:paddingVertical="@dimen/_0dp"
        android:text="@string/make_sales"
        android:textColor="@color/ppob_text_color_selector"
        app:layout_constraintEnd_toStartOf="@id/bt_personal"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/bt_personal"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@drawable/ppob_right_button_selector"
        android:paddingHorizontal="@dimen/dimen_2dp"
        android:paddingVertical="@dimen/_0dp"
        android:text="@string/personal"
        android:textColor="@color/ppob_text_color_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@color/new_divider"
        app:layout_constraintTop_toBottomOf="@id/bt_sell" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/enter_selling_price"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    <EditText
        android:id="@+id/et_amount"
        style="@style/Heading1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_24dp"
        android:backgroundTint="@color/green_60"
        android:drawablePadding="@dimen/_4dp"
        android:hint="@string/rp_default_hint"
        android:inputType="number"
        android:textAlignment="textEnd"
        android:textColor="@color/green_60"
        android:textCursorDrawable="@drawable/green_cursor"
        app:drawableLeftCompat="@drawable/ic_rp_tag"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_profit"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/untung"
        android:textColor="@color/green_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_amount" />

    <TextView
        android:id="@+id/tv_profit_amount"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/green_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_amount"
        tools:text="Rp 96.000" />

    <View
        android:id="@+id/vw_dotted_line"
        android:layout_width="0dp"
        android:layout_height="2dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/horizontal_dotted_line"
        android:backgroundTint="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_profit" />

    <TextView
        android:id="@+id/tv_bill_label"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/label_bill"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_dotted_line" />

    <TextView
        android:id="@+id/tv_bill_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_bill_label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_bill_label"
        tools:text="Rp 96.000" />

    <TextView
        android:id="@+id/tv_transaction_fees_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/transaction_fees"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_bill_label" />

    <ImageView
        android:id="@+id/iv_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_transaction_fees_message"
        app:layout_constraintStart_toEndOf="@id/tv_transaction_fees_message"
        app:layout_constraintTop_toTopOf="@id/tv_transaction_fees_message"
        app:srcCompat="@drawable/ic_info_black20" />

    <TextView
        android:id="@+id/admin_fee_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@drawable/strike_through"
        android:text="@string/free"
        android:textColor="@color/black_20"
        app:layout_constraintBottom_toBottomOf="@+id/tv_transaction_fees_message"
        app:layout_constraintEnd_toStartOf="@id/tv_discounted_fee"
        app:layout_constraintTop_toTopOf="@+id/tv_transaction_fees_message" />

    <TextView
        android:id="@+id/tv_discounted_fee"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/free"
        app:layout_constraintBottom_toBottomOf="@+id/tv_transaction_fees_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_transaction_fees_message" />

    <TextView
        android:id="@+id/tv_discount"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/discount"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_transaction_fees_message"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_discount_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_discount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_discount"
        tools:text="-Rp1.000"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_saldo_reward"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/saldo_bonus_used"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_discount"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_saldo_reward_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_saldo_reward"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_saldo_reward"
        tools:text="-Rp1.000"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/tv_bnpl_fees_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/bnpl_fee"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_saldo_reward"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_bnpl_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_bnpl_fees_message"
        app:layout_constraintStart_toEndOf="@id/tv_bnpl_fees_message"
        app:layout_constraintTop_toTopOf="@id/tv_bnpl_fees_message"
        app:srcCompat="@drawable/ic_info_black20"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_bnpl_fee"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_bnpl_fees_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_bnpl_fees_message"
        tools:text="Rp3.750"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_total_payment"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_14dp"
        android:text="@string/label_total_payment"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_bnpl_fees_message" />

    <TextView
        android:id="@+id/tv_total_amount"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/red_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_total_payment"
        tools:text="Rp 96.000" />
</androidx.constraintlayout.widget.ConstraintLayout>