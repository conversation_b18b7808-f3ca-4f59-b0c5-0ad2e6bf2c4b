<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <TextView
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:padding="@dimen/_16dp"
        android:text="@string/add_new_category"
        android:textColor="@color/white" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_category"
        style="@style/OutlineTextInputStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_24dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:hint="@string/new_category_label"
        android:textColorHint="@color/black_40"
        app:hintTextColor="@color/black_40"
        app:hintTextAppearance="@style/Body3">

        <com.bukuwarung.activities.productcategory.view.CustomAutoCompleteTextView
            android:id="@+id/et_category"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:imeOptions="actionDone"
            android:inputType="textCapWords"
            android:maxLines="1"
            android:paddingStart="@dimen/_8dp"
            android:paddingEnd="@dimen/_0dp"
            android:textColorHint="@color/black_40" />
    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:orientation="horizontal"
        android:padding="@dimen/_16dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/ButtonOutline.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_6dp"
            android:layout_weight="1"
            android:text="@string/batal" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_6dp"
            android:layout_weight="1"
            android:text="@string/save" />
    </LinearLayout>


</LinearLayout>