<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardBackgroundColor="@color/white">

    <androidx.cardview.widget.CardView
        android:id="@+id/cvReceipt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:cardBackgroundColor="@color/white"
        app:contentPaddingBottom="25dp"
        app:contentPaddingLeft="20dp"
        app:contentPaddingRight="20dp"
        app:contentPaddingTop="25dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/img"
                android:layout_width="48dp"
                android:layout_height="0dp"
                android:src="@drawable/app_logo"
                app:layout_constraintBottom_toBottomOf="@+id/tvWarungPhone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvWarungName" />

            <TextView
                android:id="@+id/tvWarungName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_80"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@+id/img"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Warung Chimay" />

            <TextView
                android:id="@+id/tvWarungPhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_40"
                app:layout_constraintStart_toEndOf="@+id/img"
                app:layout_constraintTop_toBottomOf="@+id/tvWarungName"
                tools:text="08129213122" />

            <TextView
                android:id="@+id/tvReceiptDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_40"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvWarungPhone"
                tools:text="Dicetak : 23 Jul 2020" />

            <TextView
                android:id="@+id/tvTransactionId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_40"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvWarungPhone"
                tools:text="transactionId" />

            <View
                android:id="@+id/line1"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="16dp"
                android:background="@color/black_10"
                app:layout_constraintTop_toBottomOf="@id/tvTransactionId" />

            <TextView
                android:id="@+id/tvVerified"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:drawableStart="@drawable/ic_check_circle_60"
                android:drawablePadding="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:gravity="center_vertical"
                android:text="@string/verifiedbyapp"
                android:textColor="@color/black_60"
                android:textSize="12sp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@id/line1" />

            <TextView
                android:id="@+id/tvTransactionCategory"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_60"
                app:layout_constraintTop_toBottomOf="@+id/tvVerified"
                tools:text="Transaksi/Pelanggan:" />

            <TextView
                android:id="@+id/tvCustomerPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_60"
                app:layout_constraintTop_toBottomOf="@+id/tvTransactionCategory"
                tools:text="Kontak:" />

            <TextView
                android:id="@+id/tvTransactionNoteLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_60"
                app:layout_constraintTop_toBottomOf="@+id/tvCustomerPhone"
                tools:text="Catatan:" />

            <LinearLayout
                android:id="@+id/layoutProductDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/tvTransactionNoteLabel"
                tools:visibility="visible">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:text="@string/rincian_produk"
                    android:textColor="@color/black_60" />

            </LinearLayout>


            <RelativeLayout
                android:id="@+id/flNominal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@color/black_5"
                android:padding="@dimen/_8dp"
                app:layout_constraintTop_toBottomOf="@id/layoutProductDetails">

                <TextView
                    android:id="@+id/tvTransactionDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:fontFamily="@font/roboto"
                    android:textColor="@color/black_60"
                    android:textSize="12sp"
                    tools:text="Tanggal transaksi : 11 Nov 2020" />

                <TextView
                    android:id="@+id/tvOperation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tvTransactionDate"
                    android:layout_alignParentStart="true"
                    android:fontFamily="@font/roboto"
                    android:text="@string/total_bayar"
                    android:textColor="@color/black_80"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvTransactionNominal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBottom="@id/tvOperation"
                    android:layout_alignParentEnd="true"
                    android:layout_gravity="end"
                    android:fontFamily="@font/roboto"
                    android:textColor="@color/black_80"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="Rp.50.000" />
            </RelativeLayout>

            <androidx.constraintlayout.widget.Group
                android:id="@+id/cgDebtInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="layoutDebt, @+id/tvCustomerPhone"
                tools:visibility="visible" />

            <FrameLayout
                android:id="@+id/layoutDebt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/flNominal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/total_utang"
                        android:textColor="@color/black_60" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/total_sudah_dibayar"
                        android:textColor="@color/black_60" />

                    <TextView
                        android:id="@+id/tvRemainingOperation"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/credit"
                        android:textColor="@color/black_60" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvTotalCredit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        tools:text="Rp50.000" />

                    <TextView
                        android:id="@+id/tvTotalDebit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        tools:text="Rp50.000" />

                    <TextView
                        android:id="@+id/tvRemaining"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        tools:text="Rp250.000" />


                </LinearLayout>
            </FrameLayout>


            <View
                android:id="@+id/line2"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="16dp"
                android:background="@color/black_10"
                app:layout_constraintTop_toBottomOf="@id/layoutDebt" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:text="@string/print_receipt_footer"
                android:textAlignment="center"
                android:textColor="@color/black_40"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line2" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>


</LinearLayout>