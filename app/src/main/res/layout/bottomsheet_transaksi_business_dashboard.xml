<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/fully_transparent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_10dp"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    android:background="@drawable/round_corners_top_white_bg">

    <LinearLayout
        android:id="@+id/ll_view_pager"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_dismiss"
        android:orientation="vertical">

        <View
            android:id="@+id/vw_slider"
            android:layout_width="@dimen/_48dp"
            android:layout_height="@dimen/_4dp"
            android:background="@drawable/bottoomsheet_slider_dbdbdb"
            android:layout_gravity="center_horizontal"/>


        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tl_transaction"
            app:tabTextAppearance="@android:style/TextAppearance.Widget.TabWidget"
            app:tabIndicatorColor="@color/colorPrimary"
            android:layout_width="match_parent"
            app:tabTextColor="@color/black_40"
            android:background="@color/white"
            android:layout_marginTop="@dimen/_10dp"
            app:tabSelectedTextColor="@color/colorPrimary"
            android:layout_height="wrap_content" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_transaction"
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="0dp"/>
    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_dismiss"
        style="@style/ButtonFillNoPadding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_50dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:text="@string/button_dismiss"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:backgroundTint="@color/new_yellow"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_view_pager"/>

</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>