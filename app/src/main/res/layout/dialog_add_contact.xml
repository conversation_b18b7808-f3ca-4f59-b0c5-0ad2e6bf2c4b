<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="vm"
            type="com.bukuwarung.payments.viewmodels.AddContactViewModel" />

        <variable
            name="dialog"
            type="com.bukuwarung.payments.AddContactDialog" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="20dp"
            app:cardCornerRadius="4dp"
            app:cardElevation="0dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="24dp"
                android:background="@color/white">

                <TextView
                    android:id="@+id/txt_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@color/colorPrimary"
                    android:fontFamily="@font/roboto"
                    android:padding="@dimen/_16dp"
                    android:text="@string/dialog_add_contact_title"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_input_name"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="16dp"
                    android:hint="@string/dialog_add_contact_hint_customer_name"
                    android:background="@color/white"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:passwordToggleDrawable="@null"
                    app:boxStrokeWidth="1dp"
                    app:enableError="@{vm.viewState.showNameError}"
                    app:errorTextId="@{vm.viewState.nameError}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_title">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/input_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionNext"
                        android:inputType="textCapWords"
                        android:maxLines="1"
                        android:onTextChanged="@{(s, i, j, k)->vm.onNameTextChanged(s.toString())}"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/hint_color"
                        android:textSize="16sp" />

                    <requestFocus />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_input_phone"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="16dp"
                    android:hint="@string/dialog_add_contact_hint_customer_phone"
                    android:background="@color/white"
                    app:passwordToggleDrawable="@null"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="1dp"
                    android:maxLines="1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_input_name">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/input_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:onTextChanged="@{(s, i, j, k)->vm.onPhoneTextChanged(s.toString())}"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/hint_color"
                        android:textSize="16sp" />


                </com.google.android.material.textfield.TextInputLayout>


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_cancel"
                    style="@style/Button.OutlinePrimary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="8dp"
                    android:fontFamily="@font/roboto"
                    android:onClick="@{()-> dialog.dismiss()}"
                    android:text="@string/label_cancel"
                    android:textAllCaps="false"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@id/button_submit"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_input_phone" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_submit"
                    style="@style/Button"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:fontFamily="@font/roboto"
                    android:onClick="@{()-> vm.saveCustomer()}"
                    android:text="@string/label_submit"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/button_cancel"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/button_cancel"
                    app:layout_constraintTop_toTopOf="@id/button_cancel" />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

    </FrameLayout>


</layout>