<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_black_outline_8dp"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_12dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingBottom="@dimen/dimen_28dp">

    <TextView
        android:id="@+id/tv_selling_price"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/selling_price"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_selling_price_value"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_40"
        android:layout_marginEnd="@dimen/_4dp"
        app:layout_constraintEnd_toStartOf="@+id/iv_edit"
        app:layout_constraintTop_toTopOf="@+id/iv_edit"
        tools:text="Rp96.000" />

    <ImageView
        android:id="@+id/iv_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_edit_with_border" />

    <TextView
        android:id="@+id/tv_ask_payment_done"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/is_payment_done"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_mark_unpaid"
        app:layout_constraintTop_toBottomOf="@+id/tv_selling_price" />

    <TextView
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:text="@string/payment_remind_unpaid"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@+id/btn_mark_unpaid"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_ask_payment_done" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_mark_unpaid"
        style="@style/DefaultMaterialButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:fontFamily="@font/roboto_bold"
        android:letterSpacing="0.05"
        android:paddingStart="@dimen/_6dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_6dp"
        android:paddingBottom="@dimen/_8dp"
        android:text="@string/payment_mark_unpaid"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_selling_price_value" />

</androidx.constraintlayout.widget.ConstraintLayout>