<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.airbnb.lottie.LottieAnimationView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintBottom_toTopOf="@id/tv_message"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/success_message" />

    <ImageView
        android:id="@+id/closeMessage"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_16dp"
        android:layout_gravity="end"
        android:layout_marginTop="41dp"
        android:layout_marginEnd="24dp"
        android:src="@drawable/close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/profile_success_message"
        android:textAlignment="center"
        android:textColor="@color/black_80"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/txt_subtitle" />

    <TextView
        android:id="@+id/txt_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/profile_redirecting_message"
        android:textAlignment="center"
        android:textColor="@color/black_60"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@id/recordTransaksi" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/recordTransaksi"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/record_transaksi"
        android:textAllCaps="true"
        app:cornerRadius="4dp"
        app:layout_constraintBottom_toTopOf="@id/recordUtang" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/recordUtang"
        style="@style/ButtonOutline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:text="@string/record_utang"
        android:textAllCaps="true"
        app:cornerRadius="4dp"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
