<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginTop="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_heading"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/tv_heading"
                android:layout_margin="@dimen/_16dp"
                android:text="Transaksi Pembayaran"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <ImageView
                android:id="@+id/iv_dropdown"
                android:layout_width="@dimen/_14dp"
                android:layout_height="@dimen/_14dp"
                android:src="@drawable/ic_chevron_down"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginEnd="@dimen/_24dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_card"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toBottomOf="@id/cl_heading"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:paddingBottom="@dimen/_10dp">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tl_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:background="@color/white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:tabIndicatorColor="@color/colorPrimary"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabTextAppearance="@style/FilterTabLayoutTextAppearance"
            app:tabTextColor="@color/black_40" />

        <View
            android:id="@+id/divider2"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@id/tl_payment" />

        <View
            android:id="@+id/view_total_amount"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_60dp"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:background="@drawable/bg_rounded_rectangle_8dp"
            app:layout_constraintEnd_toEndOf="@id/tl_payment"
            app:layout_constraintStart_toStartOf="@id/tl_payment"
            app:layout_constraintTop_toBottomOf="@id/tl_payment" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_vertical_divider"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="@id/view_total_amount"
            app:layout_constraintEnd_toEndOf="@id/view_total_amount"
            app:layout_constraintStart_toStartOf="@id/view_total_amount"
            app:layout_constraintTop_toTopOf="@id/view_total_amount"
            app:srcCompat="@drawable/ic_divider_vertical" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_biaya_admin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_8dp"
            android:fontFamily="@font/roboto"
            android:text="@string/biaya_admin"
            android:textColor="@color/grey_91"
            android:textSize="@dimen/dimen_12sp"
            app:layout_constraintStart_toStartOf="@id/view_total_amount"
            app:layout_constraintTop_toTopOf="@id/view_total_amount" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_biaya_admin_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:fontFamily="@font/roboto_bold"
            android:textColor="@color/black80"
            android:textSize="@dimen/dimen_16sp"
            app:layout_constraintStart_toStartOf="@id/tv_biaya_admin"
            app:layout_constraintTop_toBottomOf="@id/tv_biaya_admin"
            tools:text="Rp300.000" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_cashback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_24dp"
            android:layout_marginTop="@dimen/_8dp"
            android:drawableEnd="@drawable/ic_chevron_right"
            android:fontFamily="@font/roboto"
            android:text="@string/saldo_bonus_diterima"
            android:textColor="@color/grey_91"
            android:textSize="@dimen/dimen_12sp"
            app:layout_constraintStart_toStartOf="@id/iv_vertical_divider"
            app:layout_constraintTop_toTopOf="@id/view_total_amount" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_cashback_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:fontFamily="@font/roboto_bold"
            android:textColor="@color/black80"
            android:textSize="@dimen/dimen_16sp"
            app:layout_constraintStart_toStartOf="@id/tv_cashback"
            app:layout_constraintTop_toBottomOf="@id/tv_cashback"
            tools:text="Rp200.000" />

        <View
            android:id="@+id/view_info"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_48dp"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:background="@drawable/bg_rounded_corner_info"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/view_total_amount"
            app:layout_constraintStart_toStartOf="@id/view_total_amount"
            app:layout_constraintTop_toBottomOf="@id/view_total_amount" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/view_info"
            app:layout_constraintStart_toStartOf="@id/view_info"
            app:layout_constraintTop_toTopOf="@id/view_info"
            app:srcCompat="@drawable/ic_alert_circle_blue" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_menghemat"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_12dp"
            android:fontFamily="@font/roboto"
            android:maxLines="2"
            android:textColor="@color/black_60"
            android:textSize="@dimen/dimen_12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/view_info"
            app:layout_constraintEnd_toEndOf="@id/view_info"
            app:layout_constraintStart_toEndOf="@id/iv_info"
            app:layout_constraintTop_toTopOf="@id/view_info"
            tools:text="Dengan menggunakan fitur bayar, kamu berhasil menghemat sebesar Rp500.000" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_payment"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_weight="1"
            app:layout_constraintEnd_toEndOf="@id/tl_payment"
            app:layout_constraintStart_toStartOf="@id/tl_payment"
            app:layout_constraintTop_toBottomOf="@id/view_info" />

        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@id/vp_payment" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/btn_detail"
                style="@style/SubHeading1"
                android:layout_width="74dp"
                android:layout_height="18dp"
                android:layout_marginVertical="@dimen/_16dp"
                android:fontFamily="@font/roboto_bold"
                android:text="@string/lihat_semuaz"
                android:textColor="@color/blue_60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>

