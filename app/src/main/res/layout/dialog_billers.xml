<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/include_tool_bar"
        style="@style/Body3"
        layout="@layout/layout_activity_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et_search"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_20dp"
        android:layout_marginTop="@dimen/_8dp"
        android:hint="@string/search_service"
        android:paddingHorizontal="@dimen/_16dp"
        android:singleLine="true"
        android:textColor="@color/blue_60"
        android:textColorHint="@color/black_10"
        app:drawableStartCompat="@drawable/ic_search"
        app:drawableTint="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar" />

    <TextView
        android:id="@+id/tv_choose_policy"
        style="@style/Body3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black_5"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_8dp"
        android:text="@string/choose_policy"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/et_search" />

    <include
        android:id="@+id/include_shimmer"
        layout="@layout/layout_biller_shimmer_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_choose_policy"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_item"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingBottom="?attr/actionBarSize"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_choose_policy"
        tools:itemCount="12"
        tools:listitem="@layout/layout_ppob_biller_item" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_search"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_empty"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:gravity="center"
        android:text="@string/empty_text"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Wilayah yang kamu cari tidak ditemukan..."
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_region_not_found"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_40dp"
        android:text="@string/region_not_found"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_choose_policy" />

    <TextView
        android:id="@+id/tv_search_again"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/search_again"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_region_not_found" />

    <ImageView
        android:id="@+id/iv_no_products"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_search_again"
        app:srcCompat="@drawable/ic_no_products" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_no_products"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_region_not_found, tv_search_again, iv_no_products" />
</androidx.constraintlayout.widget.ConstraintLayout>