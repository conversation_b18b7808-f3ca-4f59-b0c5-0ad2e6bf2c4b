<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/yellow_10"
    android:padding="@dimen/_20dp">

    <TextView
        android:id="@+id/tv_ticker_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/SubHeading1"
        android:textColor="@color/yellow_80"
        android:text="@string/info_seputar_pembayaran"
        app:drawableEndCompat="@drawable/ic_cross"
        app:drawableTint="@color/yellow_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_ticker_body"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body3"
        android:textColor="@color/black60"
        android:marqueeRepeatLimit="marquee_forever"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/ticker_info_detail"
        app:layout_constraintTop_toBottomOf="@id/tv_ticker_header"
        app:layout_constraintStart_toStartOf="@id/tv_ticker_header"
        app:layout_constraintEnd_toEndOf="@id/tv_ticker_header" />

</androidx.constraintlayout.widget.ConstraintLayout>