<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_16dp"
    android:paddingEnd="@dimen/_8dp"
    android:paddingBottom="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/_16dp"
        tools:text="Makanan dan Minuman Instan" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBadgeCount"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/_16dp"
        android:background="@drawable/bg_text_badge_new"
        android:paddingStart="@dimen/_4dp"
        android:paddingTop="2dp"
        android:paddingEnd="@dimen/_4dp"
        android:paddingBottom="2dp"
        android:textColor="@color/black_80"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvName"
        tools:text="919" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivImageAddCatelogueNext"
        android:layout_width="32dp"
        android:layout_height="32dp"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvName"
        app:srcCompat="@drawable/ic_chevron_right_black_60" />

</androidx.constraintlayout.widget.ConstraintLayout>