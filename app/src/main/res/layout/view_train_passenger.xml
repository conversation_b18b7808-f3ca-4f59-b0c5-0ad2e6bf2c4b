<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.bukuwarung.payments.compoundviews.BillDetailExpandableView
        android:id="@+id/passengerDetailView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        style="@style/Divider.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_2dp"
        app:layout_constraintTop_toBottomOf="@id/passengerDetailView" />
</androidx.constraintlayout.widget.ConstraintLayout>