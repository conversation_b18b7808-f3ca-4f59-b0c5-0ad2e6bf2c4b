<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_0">

    <include
        android:id="@+id/include_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        layout="@layout/toolbar"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:background="@drawable/ic_rectangle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guide" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_cameraWrapper"
        android:layout_width="330dp"
        android:layout_height="330dp"
        android:foreground="@drawable/ic_circle_capture"
        android:padding="-20dp"
        app:cardCornerRadius="360dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.camera.view.PreviewView
            android:id="@+id/preview_camera"
            android:layout_width="330dp"
            android:layout_height="330dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_preview"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:src="@drawable/ic_check_circle_green_liveliness"
            android:layout_height="match_parent"/>

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_success_icon"
            android:layout_width="@dimen/_50dp"
            android:layout_height="@dimen/_50dp"
            android:layout_gravity="center|bottom"
            android:src="@drawable/ic_check_circle_green_liveliness"
            android:visibility="gone" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_description_title"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/posisikan_wajah_di_area_bawah_ini"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_toolbar" />

    <TextView
        android:id="@+id/tv_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:layout_marginStart="@dimen/_16dp"
        android:textColor="@color/black_60"
        android:layout_marginTop="@dimen/_6dp"
        android:text="@string/description_liveliness"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_description_title" />


    <TextView
        android:id="@+id/tv_description2"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_20dp"
        android:gravity="center"
        android:text="@string/description2_liveliness"
        app:layout_constraintBottom_toTopOf="@id/btn_camera_capture"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_card_secure"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/_5dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/btn_camera_capture"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black_0"
            android:padding="@dimen/_10dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_secure"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:src="@drawable/ic_security"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_secure_1"
                style="@style/SubHeading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:text="@string/data_security_desc"
                app:layout_constraintStart_toEndOf="@+id/iv_secure"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_secure_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:text="@string/data_security"
                android:textColor="#8d8d8d"
                android:textSize="@dimen/text_10sp"
                app:layout_constraintStart_toEndOf="@id/iv_secure"
                app:layout_constraintTop_toBottomOf="@id/tv_secure_1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:text="@string/data_security_link"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/text_10sp"
                app:layout_constraintStart_toEndOf="@id/iv_secure"
                app:layout_constraintTop_toBottomOf="@id/tv_secure_2" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_camera_capture"
        style="@style/ButtonOutline"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:backgroundTint="@color/new_yellow"
        android:padding="@dimen/_12dp"
        android:text="@string/take_pic"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rippleColor="@color/black_40" />

</androidx.constraintlayout.widget.ConstraintLayout>