<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Pulsa Rp 5.000" />

    <TextView
        android:id="@+id/tv_profit_margin_title"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:gravity="center"
        android:text="@string/profit_margin_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <ImageView
        android:id="@+id/iv_minus"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:layout_marginVertical="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_profit_margin_title"
        app:srcCompat="@drawable/ic_blue_minus"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/iv_plus"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:layout_marginVertical="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_profit_margin_title"
        app:srcCompat="@drawable/ic_blue_plus"
        tools:ignore="ContentDescription" />

    <com.bukuwarung.baseui.CurrencyEditText
        android:id="@+id/et_selling_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:background="@drawable/bg_edittext_default"
        android:cursorVisible="true"
        android:gravity="center"
        android:imeOptions="actionDone"
        android:inputType="numberDecimal"
        android:maxLines="1"
        android:padding="@dimen/_14dp"
        android:textCursorDrawable="@drawable/blue_cursor"
        android:textSize="@dimen/text_20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/iv_minus"
        app:layout_constraintEnd_toStartOf="@id/iv_plus"
        app:layout_constraintStart_toEndOf="@id/iv_minus"
        app:layout_constraintTop_toTopOf="@id/iv_minus"
        tools:text="Rp100.00" />

    <TextView
        android:id="@+id/tv_step_decrease"
        style="@style/Body4"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="@id/iv_minus"
        app:layout_constraintTop_toBottomOf="@id/iv_minus"
        tools:text="-Rp500" />

    <TextView
        android:id="@+id/tv_step_increase"
        style="@style/Body4"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_8dp"
        android:gravity="end"
        app:layout_constraintEnd_toEndOf="@id/iv_plus"
        app:layout_constraintTop_toBottomOf="@id/iv_plus"
        tools:text="+Rp500" />

    <TextView
        android:id="@+id/tv_harga_modal"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/harga_modal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_step_increase" />

    <TextView
        android:id="@+id/tv_harga_modal_value"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_step_increase"
        tools:text="Rp146.00" />

    <TextView
        android:id="@+id/tv_admin_fee"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/transaction_fees"
        android:textColor="@color/heading_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_harga_modal" />

    <ImageView
        android:id="@+id/iv_tooltip"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_0dp"
        android:layout_marginStart="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_admin_fee"
        app:layout_constraintStart_toEndOf="@id/tv_admin_fee"
        app:layout_constraintTop_toTopOf="@id/tv_admin_fee"
        app:srcCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/tv_reduced_admin_fee"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/heading_text"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_tooltip"
        tools:text="Rp1.500" />

    <TextView
        android:id="@+id/tv_original_admin_fee"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/strike_through"
        android:gravity="end"
        android:textColor="@color/black_20"
        app:layout_constraintEnd_toStartOf="@id/tv_reduced_admin_fee"
        app:layout_constraintTop_toTopOf="@id/tv_admin_fee"
        tools:text="Rp1.500" />

    <TextView
        android:id="@+id/tv_discount"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/discount"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_admin_fee"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_discount_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_discount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_discount"
        tools:text="Rp2.000"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_rounding"
        style="@style/Body4"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/round_up_selling_price"
        android:textSize="@dimen/text_12sp"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/sw_rounding"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_discount"
        tools:visibility="visible" />

    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/sw_rounding"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/_0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_rounding"
        app:layout_constraintEnd_toEndOf="@id/tv_reduced_admin_fee"
        app:layout_constraintStart_toEndOf="@id/tv_rounding"
        app:layout_constraintTop_toTopOf="@id/tv_rounding"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_info"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_blue_outline"
        android:drawablePadding="@dimen/_10dp"
        android:text="@string/selling_price_definition"
        android:textColor="@color/black_80"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_info_blue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_rounding"
        tools:visibility="visible" />

    <View
        android:id="@+id/vw_divider_2"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_info" />

    <TextView
        android:id="@+id/tv_profit_text"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/profit_text"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider_2"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_profit_value"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_profit_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_profit_text"
        tools:text="Rp4.000"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_selling_price"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/total_additional_cost"
        android:textColor="@color/heading_text"
        app:layout_constraintEnd_toStartOf="@id/tv_selling_price_value"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_profit_text" />

    <TextView
        android:id="@+id/tv_selling_price_value"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:textColor="@color/green"
        app:layout_constraintBottom_toBottomOf="@id/tv_selling_price"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_selling_price"
        app:layout_constraintTop_toTopOf="@id/tv_selling_price"
        tools:text="+Rp1.500" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20dp"
        android:insetTop="@dimen/_0dp"
        android:insetBottom="@dimen/_0dp"
        android:padding="@dimen/_14dp"
        android:text="@string/label_submit"
        android:textAllCaps="false"
        app:layout_goneMarginStart="@dimen/_0dp"
        app:layout_goneMarginEnd="@dimen/_0dp"
        app:layout_constraintEnd_toStartOf="@id/iv_next"
        app:layout_constraintStart_toEndOf="@id/iv_previous"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_price" />

    <ImageView
        android:id="@+id/iv_next"
        android:layout_width="@dimen/_50dp"
        android:layout_height="@dimen/_50dp"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_price"
        app:srcCompat="@drawable/ic_white_forward"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/iv_previous"
        android:layout_width="@dimen/_50dp"
        android:layout_height="@dimen/_50dp"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_price"
        app:srcCompat="@drawable/ic_white_back"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/tv_saved"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_50dp"
        android:drawablePadding="@dimen/_8dp"
        android:gravity="center"
        android:text="@string/stored"
        android:textColor="@color/green"
        android:textSize="@dimen/text_18sp"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/_20dp"
        app:drawableStartCompat="@drawable/ic_tick_green"
        app:layout_constraintEnd_toStartOf="@id/iv_next"
        app:layout_constraintStart_toEndOf="@id/iv_previous"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_price" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_postpaid_elements"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_rounding, sw_rounding, tv_info, tv_selling_price, tv_selling_price_value" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_prepaid_elements"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_harga_modal, tv_harga_modal_value, tv_profit_text, tv_profit_value" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_rounding_elements"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_rounding, sw_rounding, tv_info"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>