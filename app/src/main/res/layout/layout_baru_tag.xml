<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/labelCard"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/out_red"
    app:cardCornerRadius="@dimen/_12dp"
    app:cardElevation="@dimen/_0dp">

    <TextView
        android:id="@+id/labelText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_4dp"
        android:paddingVertical="@dimen/_4dp"
        android:text="@string/new_lable_2"
        android:textColor="@color/white"
        android:textSize="@dimen/text_8sp"
        android:textStyle="bold" />
</androidx.cardview.widget.CardView>