<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/Toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/TootleTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_0dp"
                android:paddingEnd="@dimen/_24dp"
                android:text="@string/topup_saldo_bw"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:id="@+id/topup_fees_info"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/blue_5"
        android:drawablePadding="@dimen/_4dp"
        android:padding="@dimen/_16dp"
        android:textColor="@color/blue_90"
        app:drawableRightCompat="@drawable/ic_cross"
        app:drawableTint="@color/blue_90"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <ScrollView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/btn_submit"
        app:layout_constraintTop_toBottomOf="@+id/topup_fees_info">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_limit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/iv_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_saldo_icon_home" />

                <TextView
                    android:id="@+id/tv_saldo_limit"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    app:layout_constraintStart_toEndOf="@id/iv_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/saldo_limit" />

                <TextView
                    android:id="@+id/tv_saldo_limit_left"
                    style="@style/Label2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginTop="@dimen/_5dp"
                    android:textColor="@color/blue_90"
                    app:layout_constraintStart_toEndOf="@id/iv_icon"
                    app:layout_constraintTop_toBottomOf="@id/tv_saldo_limit"
                    tools:text="@string/saldo_limit_available" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/vw_divider_limit"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_5dp"
                android:background="@color/black_5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_limit" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_amount_inputs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_divider_limit">

                <com.bukuwarung.baseui.CurrencyEditText
                    android:id="@+id/cet_amount_input"
                    style="@style/Heading1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/white"
                    android:inputType="number"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingBottom="@dimen/_8dp"
                    android:text="@string/currency"
                    app:currencyColor="@color/colorPrimary"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/vw_bottom_border"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/black_10"
                    app:layout_constraintTop_toBottomOf="@id/cet_amount_input" />

                <TextView
                    android:id="@+id/tv_amount_limit_error"
                    style="@style/Body3"
                    android:layout_marginTop="@dimen/_4dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_bottom_border"
                    tools:text="@string/min_10k" />

                <TextView
                    android:id="@+id/tv_50k_amount"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:background="@drawable/bg_black_10_outline"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/_4dp"
                    android:paddingVertical="@dimen/_12dp"
                    android:text="@string/rp50k"
                    app:layout_constraintEnd_toStartOf="@id/tv_100k_amount"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_amount_limit_error" />

                <TextView
                    android:id="@+id/tv_100k_amount"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:background="@drawable/bg_black_10_outline"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/_4dp"
                    android:paddingVertical="@dimen/_12dp"
                    android:text="@string/rp100k"
                    app:layout_constraintEnd_toStartOf="@id/tv_250k_amount"
                    app:layout_constraintStart_toEndOf="@id/tv_50k_amount"
                    app:layout_constraintTop_toTopOf="@id/tv_50k_amount"
                    app:layout_goneMarginStart="@dimen/_4dp" />

                <TextView
                    android:id="@+id/tv_250k_amount"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:background="@drawable/bg_black_10_outline"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/_4dp"
                    android:paddingVertical="@dimen/_12dp"
                    android:text="@string/rp250k"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_100k_amount"
                    app:layout_constraintTop_toTopOf="@id/tv_50k_amount"
                    app:layout_goneMarginStart="@dimen/_4dp" />

                <TextView
                    android:id="@+id/tv_500k_amount"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:background="@drawable/bg_black_10_outline"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/_4dp"
                    android:paddingVertical="@dimen/_12dp"
                    android:text="@string/rp500k"
                    app:layout_constraintEnd_toStartOf="@id/tv_750k_amount"
                    app:layout_constraintHorizontal_chainStyle="spread"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_50k_amount" />

                <TextView
                    android:id="@+id/tv_750k_amount"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:background="@drawable/bg_black_10_outline"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/_4dp"
                    android:paddingVertical="@dimen/_12dp"
                    android:text="@string/rp750k"
                    app:layout_constraintEnd_toStartOf="@id/tv_1000k_amount"
                    app:layout_constraintStart_toEndOf="@id/tv_500k_amount"
                    app:layout_constraintTop_toTopOf="@id/tv_500k_amount"
                    app:layout_goneMarginStart="@dimen/_4dp" />

                <TextView
                    android:id="@+id/tv_1000k_amount"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:background="@drawable/bg_black_10_outline"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/_4dp"
                    android:paddingVertical="@dimen/_12dp"
                    android:text="@string/rp1000k"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_750k_amount"
                    app:layout_constraintTop_toTopOf="@id/tv_500k_amount"
                    app:layout_goneMarginStart="@dimen/_4dp" />

                <View
                    android:id="@+id/vw_divider"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/horizontal_dashed_line"
                    app:layout_constraintTop_toBottomOf="@id/tv_500k_amount" />

                <TextView
                    android:id="@+id/tv_bw_admin_fee"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/transaction_fees"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_divider" />

                <TextView
                    android:id="@+id/tv_bw_admin_fee_suggestion"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2dp"
                    android:text="@string/charged_to_you"
                    android:textColor="@color/black_40"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_bw_admin_fee"
                    app:layout_constraintEnd_toStartOf="@+id/tv_bw_admin_fee_value"
                    app:layout_constraintStart_toEndOf="@+id/tv_bw_admin_fee"
                    app:layout_constraintTop_toTopOf="@+id/tv_bw_admin_fee" />

                <TextView
                    android:id="@+id/tv_bw_admin_fee_value"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/free_upper_case"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_divider" />

                <TextView
                    android:id="@+id/tv_total_saldo"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/total"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_bw_admin_fee_suggestion" />

                <TextView
                    android:id="@+id/tv_saldo_value"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_bw_admin_fee_suggestion"
                    tools:text="Rp700.000" />

                <View
                    android:id="@+id/vw_divider2"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/horizontal_dashed_line"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/tv_total_saldo" />

                <TextView
                    android:id="@+id/tv_abort_message"
                    style="@style/Body3"
                    android:layout_marginTop="@dimen/_10dp"
                    android:background="@drawable/bg_solid_yellow_warning_corner_8dp_stroke_yellow80"
                    android:drawablePadding="@dimen/_10dp"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/_10dp"
                    android:paddingTop="@dimen/_6dp"
                    android:paddingEnd="@dimen/_10dp"
                    android:paddingBottom="@dimen/_6dp"
                    android:text="@string/continue_pay_and_abort_previous"
                    android:textColor="@color/black_60"
                    android:visibility="gone"
                    app:drawableStartCompat="@drawable/ic_info_yellow"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_divider2" />

                <TextView
                    android:id="@+id/tv_kyc_upgrade_info"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:background="@drawable/bg_solid_yellow_warning_corner_8dp_stroke_yellow80"
                    android:drawablePadding="@dimen/_10dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/_10dp"
                    android:paddingVertical="@dimen/_6dp"
                    android:textColor="@color/black_60"
                    android:visibility="gone"
                    app:drawableStartCompat="@drawable/ic_info_yellow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_abort_message"
                    tools:text="Kamu harus Verifikasi Akun untuk menggunakan fitur Saldo BukuWarung. Pelajari lebih lanjut"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gr_qris_existing_topup_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="vw_divider2, tv_abort_message" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gr_qris_summary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="vw_divider, tv_bw_admin_fee, tv_bw_admin_fee_suggestion, tv_bw_admin_fee_value, tv_total_saldo, tv_saldo_value" />

                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginTop="@dimen/_20dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_500k_amount" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.bukuwarung.widget.PoweredByFooterView
                android:id="@+id/powered_by_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_amount_inputs"
                app:layout_constraintVertical_bias="1" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:enabled="false"
        android:text="@string/topup_saldo"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>