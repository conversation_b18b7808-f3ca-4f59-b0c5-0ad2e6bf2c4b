<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_heading"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/SubHeading1"
        tools:text="Kamu membuat produk sendiri?"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_label"
        android:layout_width="@dimen/_0dp"
        android:paddingHorizontal="@dimen/_2dp"
        android:layout_marginTop="3dp"
        android:background="@drawable/bg_round_rectangle_f4f4f4"
        android:layout_height="@dimen/_14dp"
        style="@style/Label2"
        android:text="@string/select_one"
        android:layout_marginStart="@dimen/_4dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toEndOf="@id/tv_heading"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>