<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="@drawable/credit_amount_inactive">

    <ImageView
        android:id="@+id/amountImage"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:src="@drawable/ic_amount_new"
        android:layout_marginEnd="@dimen/_8dp"/>

    <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txt_main_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/credit_amount_hint"
        android:textColor="#5C5C5C"
        android:textStyle="normal"
        android:textSize="12sp"/>

    <LinearLayout
        android:id="@+id/amount_box"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/currency_symbol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="3.0dip"
            android:fontFamily="@font/roboto"
            android:lineHeight="40sp"
            android:paddingRight="2dp"
            android:text="Rp"
            android:textColor="@color/black"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/transaction_input_amount_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:autoSizeMaxTextSize="34dp"
            android:autoSizeMinTextSize="24dp"
            android:autoSizeStepGranularity="1dp"
            android:hint="0"
            android:lineHeight="40sp"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textColorHint="@color/black"
            android:textSize="24.0sp"
            android:textStyle="bold" />

        <View
            android:id="@+id/cursor"
            android:layout_width="2.0dip"
            android:layout_height="42.0dip"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="2.0dip"
            android:layout_marginRight="2.0dip"
            android:background="@color/colorPrimary" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/exprLayour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:gravity="center"
        android:visibility="gone">

        <TextView
            android:id="@+id/transaction_input_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:fontFamily="@font/roboto"
            android:gravity="center_horizontal"
            android:lineHeight="20sp"
            android:lineSpacingExtra="6sp"
            android:text=""
            android:textAlignment="center"
            android:textColor="#666666"
            android:textSize="15sp"
            android:textStyle="normal" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#8D8D8D"
        android:layout_marginTop="@dimen/_8dp"/>

</LinearLayout>

</LinearLayout>