<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_solid_yellow5_corner_8dp_stroke_yellow50"
    android:padding="@dimen/_16dp">

    <ImageView
        android:id="@+id/iv_atur"
        android:layout_width="@dimen/_66dp"
        android:layout_height="@dimen/_66dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_banner_atur"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/tv_atur_heading"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:text="@string/atur_banner_title"
        app:layout_constraintStart_toEndOf="@id/iv_atur"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_atur_message"
        style="@style/Body5"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_8dp"
        android:layout_marginVertical="@dimen/_8dp"
        android:text="@string/atur_banner_message"
        app:layout_constraintEnd_toStartOf="@id/btn_continue"
        app:layout_constraintStart_toEndOf="@id/iv_atur"
        app:layout_constraintTop_toBottomOf="@id/tv_atur_heading" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_continue"
        style="@style/ButtonFillNoPadding"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_14dp"
        android:paddingVertical="@dimen/_8dp"
        android:text="@string/atur"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>