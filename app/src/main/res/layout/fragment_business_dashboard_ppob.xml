<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginTop="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_heading"
            android:layout_margin="@dimen/_16dp"
            android:text="@string/transaksi_pulsa_dan_tagihan"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/iv_dropdown"
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:src="@drawable/ic_chevron_down"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_marginEnd="@dimen/_24dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/cl_heading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingBottom="@dimen/_16dp">



        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/black_5" />

        <LinearLayout
            android:id="@+id/ll_tab_layout"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/_10dp"
            android:layout_marginRight="@dimen/_10dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider1">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tl_transaction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_10dp"
                android:background="@color/white"
                app:tabIndicatorColor="@color/colorPrimary"
                app:tabSelectedTextColor="@color/colorPrimary"
                app:tabTextAppearance="@android:style/TextAppearance.Widget.TabWidget"
                app:tabTextColor="@color/black_40" />
        </LinearLayout>

        <View
            android:id="@+id/divider2"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@id/ll_tab_layout" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_penjualan_balance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:backgroundTint="@color/white"
            android:elevation="@dimen/_12dp"
            android:visibility="visible"
            app:cardCornerRadius="@dimen/_12dp"
            app:cardUseCompatPadding="true"
            app:layout_constraintTop_toBottomOf="@id/divider2">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_penjualan_balance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                android:backgroundTint="@color/white">


                <TextView
                    android:id="@+id/tv_total_penjualan"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:lineSpacingExtra="@dimen/_2sp"
                    android:text="@string/total_penjualan"
                    android:textColor="@color/grey_91"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_total_penjualan_amount"
                    style="@style/Heading3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="top"
                    android:lineSpacingExtra="@dimen/_5sp"
                    android:text="Rp0"
                    android:textColor="@color/black_80"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_total_penjualan" />

                <TextView
                    android:id="@+id/tv_total_modal"
                    style="@style/SubHeading2"
                    app:layout_constraintHorizontal_bias="0.6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:fontFamily="@font/roboto"
                    android:lineSpacingExtra="@dimen/_2sp"
                    android:text="@string/total_modal"
                    android:textColor="@color/grey_91"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_total_modal_amount"
                    style="@style/Heading3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="top"
                    android:lineSpacingExtra="@dimen/_5sp"
                    android:text="Rp0"
                    android:textColor="@color/black_80"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="@id/tv_total_modal"
                    app:layout_constraintTop_toBottomOf="@id/tv_total_modal" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_untang"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:backgroundTint="@color/green_5"
                    android:elevation="@dimen/_12dp"
                    app:cardCornerRadius="@dimen/_12dp"
                    app:cardUseCompatPadding="true"
                    app:layout_constraintTop_toBottomOf="@id/tv_total_penjualan_amount">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_untang"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/_8dp"
                        android:backgroundTint="@color/white">

                        <TextView
                            android:id="@+id/tv_untung"
                            style="@style/SubHeading2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/untung"
                            android:textColor="@color/green_80"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_pribadi_balance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:backgroundTint="@color/white"
            android:elevation="@dimen/_12dp"
            android:visibility="gone"
            app:cardCornerRadius="@dimen/_12dp"
            app:cardUseCompatPadding="true"
            app:layout_constraintTop_toBottomOf="@id/divider2">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_pribadi_balance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                android:backgroundTint="@color/white">

                <TextView
                    android:id="@+id/tv_total_penguluaran"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:lineSpacingExtra="@dimen/_2sp"
                    android:text="@string/total_pengeluaran"
                    android:textColor="@color/grey_91"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_total_penguluaran_amount"
                    style="@style/Heading3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="top"
                    android:lineSpacingExtra="@dimen/_5sp"
                    android:text="Rp0"
                    android:textColor="@color/black_80"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_total_penguluaran" />


                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_pengeluaran"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:backgroundTint="@color/black_5"
                    android:elevation="@dimen/_12dp"
                    app:cardCornerRadius="@dimen/_12dp"
                    app:cardUseCompatPadding="true"
                    app:layout_constraintTop_toBottomOf="@id/tv_total_penguluaran_amount">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_pengeluaran"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/_8dp"
                        android:backgroundTint="@color/white">

                        <TextView
                            android:id="@+id/tv_pengeluaran"
                            style="@style/SubHeading2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pengeluaran_untuk"
                            android:textColor="@color/black_60"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>


        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="cv_penjualan_balance,cv_pribadi_balance" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:backgroundTint="@color/white"
            android:elevation="@dimen/_12dp"
            app:cardCornerRadius="@dimen/_12dp"
            app:cardUseCompatPadding="true"
            app:layout_constraintTop_toBottomOf="@id/barrier">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                android:backgroundTint="@color/white"
                android:paddingBottom="@dimen/_20dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_bar_header"
                    style="@style/SubHeading1"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/ppob_sales_bar_header"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/iv_empty_stock"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_200dp"
                    android:layout_marginStart="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_12dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:scaleType="fitXY"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_bar_header"
                    app:srcCompat="@drawable/pribadi_empty" />

                <com.github.mikephil.charting.charts.BarChart
                    android:id="@+id/barChart_view"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_220dp"
                    android:layout_marginStart="@dimen/_10dp"
                    android:paddingStart="@dimen/_10dp"
                    android:paddingEnd="@dimen/_10dp"
                    android:paddingBottom="@dimen/_20dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_bar_header" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/btn_detail"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_bold"
            android:padding="@dimen/_16dp"
            android:text="@string/lihat_semua"
            android:textAlignment="center"
            android:textColor="@color/blue_60"
            android:textSize="@dimen/text_14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cv_bar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>

