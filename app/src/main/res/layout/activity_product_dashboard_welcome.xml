<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/img_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="@dimen/_5dp"
        android:padding="@dimen/_10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_back" />

    <ImageView
        android:id="@+id/iv_dashboard_image"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:srcCompat="@drawable/bg_welcome_intro"
        app:layout_constraintHeight_percent="0.4" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_dashboard_image">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            android:layout_marginTop="-7dp"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_10dp">

            <TextView
                android:id="@+id/tv_dashboard_welcome_title"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/laporan_bulanan"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/Heading1"/>

            <TextView
                android:id="@+id/tv_dashboard_welcome_subtitle"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_10dp"
                android:paddingEnd="@dimen/_10dp"
                android:textColor="#5C5C5C"
                android:layout_marginEnd="@dimen/_20dp"
                android:text="@string/selamat_datang_juragan"
                android:textAlignment="center"
                android:layout_marginTop="@dimen/_10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_dashboard_welcome_title" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cl_welcome_start"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_dashboard_welcome_subtitle"
                app:layout_constraintLeft_toLeftOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_10dp">

                    <ImageView
                        android:id="@+id/iv_stock_image"
                        android:layout_width="@dimen/_40dp"
                        android:layout_height="@dimen/_40dp"
                        app:srcCompat="@drawable/check_circle"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_weight="2"/>

                    <TextView
                        android:id="@+id/tv_stock_heading"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:text="@string/pantau_kemajuan_usaha_dalam_1_bulan"
                        app:layout_constraintStart_toEndOf="@id/iv_stock_image"
                        app:layout_constraintTop_toTopOf="@id/iv_stock_image"
                        style="@style/SubHeading1"
                        app:layout_constraintHorizontal_weight="8"
                        />

                    <TextView
                        android:id="@+id/tv_stock_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/kamu_bisa_mengetahuinya"
                        style="@style/Body2"
                        android:textColor="#5C5C5C"
                        android:layout_marginTop="@dimen/_5dp"
                        android:lineSpacingExtra="1sp"
                        android:paddingEnd="@dimen/_10dp"
                        app:layout_constraintStart_toStartOf="@+id/tv_stock_heading"
                        app:layout_constraintTop_toBottomOf="@id/tv_stock_heading"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_weight="9"
                        tools:ignore="RtlSymmetry" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cl_welcome_mid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@+id/cl_welcome_start"
                app:layout_constraintLeft_toLeftOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_10dp">

                    <ImageView
                        android:id="@+id/iv_reportn_image"
                        android:layout_width="@dimen/_40dp"
                        android:layout_height="@dimen/_40dp"
                        app:layout_constraintHorizontal_weight="2"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/check_circle" />

                    <TextView
                        android:id="@+id/tv_reportn_heading"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:text="@string/laporan_makin_akurat"
                        style="@style/SubHeading1"
                        app:layout_constraintStart_toEndOf="@+id/iv_reportn_image"
                        app:layout_constraintTop_toTopOf="@id/iv_reportn_image"/>

                    <TextView
                        android:id="@+id/tv_reportn_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/laporan_usaha_akan"
                        style="@style/Body2"
                        android:textColor="#5C5C5C"
                        android:layout_marginStart="@dimen/_10dp"
                        android:layout_marginTop="@dimen/_5dp"
                        android:paddingEnd="@dimen/_10dp"
                        android:lineSpacingExtra="1sp"
                        app:layout_constraintStart_toStartOf="@+id/tv_reportn_heading"
                        app:layout_constraintTop_toBottomOf="@id/tv_reportn_heading"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_weight="9"
                        tools:ignore="RtlSymmetry" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cl_welcome_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@+id/cl_welcome_mid"
                app:layout_constraintLeft_toLeftOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_10dp">

                    <ImageView
                        android:id="@+id/iv_utang_image"
                        android:layout_width="@dimen/_40dp"
                        android:layout_height="@dimen/_40dp"
                        android:paddingEnd="@dimen/_5dp"
                        app:layout_constraintHorizontal_weight="2"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/check_circle" />

                    <TextView
                        android:id="@+id/tv_utang_heading"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:text="@string/laporan_makin_akurat"
                        style="@style/SubHeading1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_utang_image"
                        app:layout_constraintTop_toTopOf="@id/iv_utang_image"
                        app:layout_constraintHorizontal_weight="8"
                        />

                    <TextView
                        android:id="@+id/tv_utang_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/laporan_usaha_akan"
                        style="@style/Body2"
                        android:textColor="#5C5C5C"
                        android:layout_marginTop="@dimen/_5dp"
                        android:paddingEnd="@dimen/_10dp"
                        android:lineSpacingExtra="1sp"
                        app:layout_constraintTop_toBottomOf="@+id/tv_utang_heading"
                        app:layout_constraintStart_toStartOf="@id/tv_utang_heading"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_weight="9"
                        tools:ignore="RtlSymmetry" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_proceed"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/ButtonFill"
        android:text="@string/cek_laporan_usaha_sekarang"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintWidth_percent="0.8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>