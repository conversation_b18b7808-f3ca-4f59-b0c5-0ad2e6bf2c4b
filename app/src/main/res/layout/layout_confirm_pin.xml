<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/secureInfo"
        layout="@layout/layout_secure_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="@string/enter_current_pin"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/secureInfo" />

    <TextView
        android:id="@+id/tvSubTitle"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/enter_pin_subtitle"
        android:textAlignment="center"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <LinearLayout
        android:id="@+id/pin_fill_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_32dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSubTitle">

        <View
            android:id="@+id/pin_code_slot_1"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginHorizontal="@dimen/_10dp" />

        <View
            android:id="@+id/pin_code_slot_2"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginHorizontal="@dimen/_10dp" />

        <View
            android:id="@+id/pin_code_slot_3"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginHorizontal="@dimen/_10dp" />

        <View
            android:id="@+id/pin_code_slot_4"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginHorizontal="@dimen/_10dp" />

        <View
            android:id="@+id/pin_code_slot_5"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginHorizontal="@dimen/_10dp" />

        <View
            android:id="@+id/pin_code_slot_6"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginHorizontal="@dimen/_10dp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_pin_error"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:text='@string/pin_not_correct'
        android:textAlignment="center"
        android:textColor="@color/red_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pin_fill_container" />

    <TextView
        android:id="@+id/tv_forgot_pin"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:text="@string/pin_forgot"
        android:textAlignment="center"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_pin_error" />

</androidx.constraintlayout.widget.ConstraintLayout>