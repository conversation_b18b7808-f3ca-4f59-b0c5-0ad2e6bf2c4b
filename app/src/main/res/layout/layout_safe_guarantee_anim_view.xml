<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/middle_of_img"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:layout_marginEnd="3dp"
        app:layout_constraintEnd_toEndOf="@id/shield_img"
        app:layout_constraintStart_toStartOf="@id/shield_img"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/txt_bg"
        android:layout_width="0dp"
        android:layout_height="21dp"
        android:background="@drawable/bg_rounded_blue80"
        app:layout_constraintBottom_toBottomOf="@id/shield_img"
        app:layout_constraintEnd_toEndOf="@id/animated_txt"
        app:layout_constraintStart_toStartOf="@id/middle_of_img"
        app:layout_constraintTop_toTopOf="@id/shield_img" />

    <ImageView
        android:id="@+id/shield_img"
        android:layout_width="32dp"
        android:layout_height="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_shield" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/animated_txt"
        style="@style/SubHeading2"
        android:layout_width="88dp"
        android:layout_height="18dp"
        android:drawablePadding="@dimen/_4dp"
        android:gravity="center"
        android:paddingStart="@dimen/_4dp"
        android:paddingEnd="@dimen/_6dp"
        android:textColor="@color/white"
        app:autoSizeMaxTextSize="12sp"
        app:autoSizeMinTextSize="6sp"
        app:autoSizeTextType="uniform"
        app:drawableEndCompat="@drawable/ic_info_referral"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/txt_bg"
        app:layout_constraintStart_toEndOf="@id/shield_img"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Langsung Terkirim" />

</androidx.constraintlayout.widget.ConstraintLayout>
