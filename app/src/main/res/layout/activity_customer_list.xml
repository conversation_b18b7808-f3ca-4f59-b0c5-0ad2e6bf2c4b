<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.bukuwarung.payments.viewmodels.CustomerListViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:theme="@style/ToolbarTheme">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/toolBarLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:fontFamily="@font/roboto"
                    android:maxLines="1"
                    android:paddingEnd="16dp"
                    android:text="@string/activity_customer_list_title"
                    android:textColor="#ffffff"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:autoSizeMaxTextSize="16sp"
                    app:autoSizeMinTextSize="10sp"
                    app:autoSizeStepGranularity="1sp"
                    app:autoSizeTextType="uniform" />

            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/white"
            android:onClick="@{()->vm.enableSearch()}"
            app:layout_constraintBottom_toBottomOf="@id/input_search"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/input_search" />

        <ImageView
            android:id="@+id/icon_search"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_search"
            app:layout_constraintBottom_toBottomOf="@id/input_search"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/input_search"
            app:tint="@color/colorPrimary" />


        <TextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:gravity="center_vertical"
            android:hint="@string/search_contacts"
            android:textColorHint="@color/black_40"
            android:textSize="16sp"
            app:goneUnless="@{!vm.viewState.searchEnabled}"
            app:layout_constraintBottom_toBottomOf="@id/input_search"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/icon_search"
            app:layout_constraintTop_toTopOf="@id/input_search" />

        <EditText
            android:id="@+id/input_search"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:background="@color/white"
            android:fontFamily="@font/roboto"
            android:gravity="center_vertical"
            android:hint="@string/search_contacts"
            android:inputType="textCapSentences"
            android:onTextChanged="@{(s, i, j, k)->vm.onSearchQueryChanged(s)}"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:singleLine="true"
            android:textColor="@color/black_60"
            android:textColorHint="@color/black_40"
            android:textSize="16sp"
            app:clearText="@{!vm.viewState.searchEnabled}"
            app:invisibleUnless="@{vm.viewState.searchEnabled}"
            app:keyboardVisibility="@{vm.viewState.searchEnabled}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/icon_search"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

        <ImageView
            android:id="@+id/icon_cancel"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:contentDescription="@null"
            android:onClick="@{()-> vm.disableSearch()}"
            android:src="@drawable/ic_close"
            app:goneUnless="@{vm.viewState.searchEnabled}"
            app:layout_constraintBottom_toBottomOf="@id/input_search"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/input_search"
            app:tint="@color/colorPrimary" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_customers"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/button_add_contact"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/input_search"
            app:verticalDivider="@{@drawable/recycler_view_divider}"
            tools:listitem="@layout/item_list_customer" />


        <View
            style="@style/ShadowBottom"
            android:layout_height="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/input_search" />

        <View
            android:id="@+id/button_add_contact"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/white"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/txt_label_add_contact_desc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_label_add_contact" />


        <ImageView
            android:id="@+id/icon_add_contact"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="16dp"
            android:background="@drawable/bg_circle"
            android:backgroundTint="@color/yellow"
            android:contentDescription="@null"
            android:padding="10dp"
            android:src="@drawable/ic_contact_add"
            app:layout_constraintBottom_toBottomOf="@id/button_add_contact"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/button_add_contact"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/txt_label_add_contact"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/roboto"
            android:paddingTop="16dp"
            android:text="@string/label_add_new_contact"
            android:textColor="@color/yellow"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/txt_label_add_contact_desc"
            app:layout_constraintStart_toEndOf="@id/icon_add_contact" />

        <TextView
            android:id="@+id/txt_label_add_contact_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/roboto"
            android:paddingTop="2dp"
            android:paddingBottom="16dp"
            android:textColor="@color/black_60"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/icon_add_contact"
            app:textHTML="@{@string/label_add_contact_without_phone}" />


        <ImageView
            android:id="@+id/arrow_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/yellow"
            android:contentDescription="@null"
            android:padding="10dp"
            android:src="@drawable/ic_chevron_right"
            app:layout_constraintBottom_toBottomOf="@id/button_add_contact"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/button_add_contact"
            app:tint="@color/colorPrimary" />

        <View
            android:id="@+id/shadow"
            style="@style/ShadowTop"
            android:layout_height="4dp"
            app:layout_constraintBottom_toTopOf="@id/button_add_contact"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/add_contact_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="shadow,arrow_img,txt_label_add_contact_desc,txt_label_add_contact,icon_add_contact,button_add_contact" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
