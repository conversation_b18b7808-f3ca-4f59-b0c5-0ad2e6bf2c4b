<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:foreground="?android:attr/selectableItemBackground"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingTop="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/_10dp">
        <ImageView
            android:id="@+id/bg"
            style="@style/Heading3"
            android:layout_width="@dimen/_30dp"
            android:layout_height="@dimen/_30dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:src="@drawable/penjualan_product"
            android:textAllCaps="true"
            android:layout_marginTop="@dimen/_5dp"
            android:layout_marginBottom="@dimen/_12dp"
            />
        <LinearLayout
            android:id="@+id/nameLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:maxLines="2"
                android:text="@string/default_placeholder"
                android:textColor="#222222"
                android:textSize="14sp"
                tools:text="Penjualan asdad as ewr wer wefsdfs fsd rewr wrwe rsdfsfewrw" />

            <TextView
                android:id="@+id/trxCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:maxLines="1"
                android:text="@string/default_placeholder"
                android:textColor="#8D8D8D"
                android:textSize="12sp"
                tools:text="3 Transaksi" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvIncome"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="end|center_vertical"
            android:lineSpacingExtra="6dp"
            android:maxLines="2"
            android:paddingEnd="@dimen/_16dp"
            android:textColor="@color/black_40"
            android:textSize="14sp"
            tools:text="SGD 1000,000,000,000,000" />

        <TextView
            android:id="@+id/tvExpense"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="end|center_vertical"
            android:lineSpacingExtra="6dp"
            android:maxLines="2"
            android:visibility="gone"
            android:paddingEnd="@dimen/_16dp"
            android:textColor="@color/out_red"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="SGD 1000,000,000,000,000" />

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="2dp"
        android:background="@color/new_divider" />
</LinearLayout>
