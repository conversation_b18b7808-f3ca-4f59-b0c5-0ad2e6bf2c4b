<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">
    <LinearLayout
        android:id="@+id/dataLayout"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_below="@+id/headerLayout">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="7"
            android:orientation="vertical"
            android:paddingLeft="12dp">

            <TextView
                android:id="@+id/note"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="7"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="10sp"
                android:maxLines="1"
                android:paddingRight="4dp"
                android:text="23232.0"
                android:textColor="@color/heading_text"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/note"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/roboto"
                android:maxLines="1"
                android:text="balance note"
                android:textColor="@color/body_text"
                android:textSize="12sp" />
        </RelativeLayout>

        <TextView
            android:id="@+id/credit"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="7"
            android:background="@color/credit_column_bg"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:lineSpacingExtra="10sp"
            android:maxLines="1"
            android:text="-"
            android:paddingLeft="4dp"
            android:paddingRight="8dp"
            android:textColor="@color/in_green"
            android:textSize="16sp"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="12sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform" />

        <TextView
            android:id="@+id/debit"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="7"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:lineSpacingExtra="10sp"
            android:maxLines="1"
            android:paddingLeft="4dp"
            android:paddingRight="8dp"
            android:text="10000"
            android:textColor="@color/out_red"
            android:textSize="16sp"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="12sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform" />

    </LinearLayout>
</RelativeLayout>
