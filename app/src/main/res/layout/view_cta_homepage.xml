<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#E9F1FC">

    <TextView
        android:id="@+id/tv_qris_status"
        style="@style/Body3"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_40dp"
        android:layout_marginStart="@dimen/_10dp"
        android:drawablePadding="@dimen/_10dp"
        android:gravity="center_vertical"
        android:padding="@dimen/_10dp"
        android:text="@string/qris_is_processed_more"
        app:drawableStartCompat="@drawable/qris_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>