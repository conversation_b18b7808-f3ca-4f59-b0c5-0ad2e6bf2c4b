<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="item"
            type="com.bukuwarung.database.entity.ProductEntity" />
    </data>

    <RelativeLayout
        android:background="@drawable/rounded_corner_background_bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:paddingLeft="@dimen/_16dp"
            android:paddingRight="@dimen/_16dp"
            android:paddingTop="30dp"
            android:paddingBottom="@dimen/_16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >
            <TextView
                android:id="@+id/productName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:text="@string/modify_stock"
                android:textColor="@color/black_80"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_alignParentRight="true"
                android:layout_width="wrap_content"
                android:src="@drawable/ic_cross"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/stockInitial"
                android:layout_marginTop="@dimen/_18dp"
                android:layout_below="@+id/productName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:textSize="16sp"
                android:textColor="@color/black_80"
                android:text="Stok saat ini" />

            <TextView
                android:id="@+id/stockInitialValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/productName"
                android:layout_marginTop="@dimen/_18dp"
                android:layout_marginRight="3dp"
                android:layout_toLeftOf="@+id/unitValue"
                android:fontFamily="@font/roboto"
                android:textColor="@{item.stock&lt;=item.minimumStock?@color/out_red:@color/black_80}"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="Stok: 11,2"
                tools:visibility="visible" />

            <TextView
                android:layout_marginLeft="3dp"
                android:layout_below="@+id/productName"
                android:id="@+id/unitValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:fontFamily="@font/roboto"
                android:layout_marginTop="@dimen/_18dp"
                android:textColor="@{item.stock&lt;=item.minimumStock?@color/out_red:@color/black_40}"
                android:textSize="16sp"
                android:visibility="@{item.measurementName!=null?View.VISIBLE:View.INVISIBLE}"
                android:text="@{item.measurementName}" />


            <LinearLayout
                android:id="@+id/stockSpinnerLayout"
                android:layout_marginTop="@dimen/_18dp"
                android:layout_below="@+id/stockInitial"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="2">
            <com.skydoves.powerspinner.PowerSpinnerView
                android:layout_width="wrap_content"
                android:id="@+id/spinner"
                android:layout_weight="1.2"
                android:layout_height="wrap_content"
                android:background="@drawable/button_round_cornerd_stroke_pop_up"
                app:spinner_item_array="@array/stock_add_remove"
                app:spinner_arrow_tint="@color/black_60"
                android:text="Penambahan Stok"
                android:padding="@dimen/_12dp"
                android:textColor="@color/black_80"
                android:textColorHint="@color/black_80"
                android:textSize="14sp"
                android:foreground="?attr/selectableItemBackground"
                app:spinner_arrow_gravity="end"
                app:spinner_arrow_padding="8dp"
                app:spinner_divider_show="true"
                app:spinner_divider_color="@color/light_gray"
                app:spinner_divider_size="0.4dp"
                app:spinner_popup_animation="dropdown"
                app:spinner_popup_background="@color/white"
                app:spinner_popup_elevation="14dp" />


                <LinearLayout
                    android:gravity="right"
                    android:layout_gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.8">
                <include
                    android:id="@+id/numberStepperLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    layout="@layout/layout_customer_number_stepper"/>
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:layout_below="@+id/stockSpinnerLayout"
                android:id="@+id/total_stock"
                style="@style/Body1"
                android:layout_marginTop="@dimen/_16dp"
                android:textColor="@color/black_80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Total stok 200"
                />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_confirm"
                style="@style/ButtonFill"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/total_stock"
                android:layout_gravity="center"
                android:layout_marginTop="30dp"
                android:text="@string/confirmation"
                android:textStyle="bold" />
        </RelativeLayout>
    </RelativeLayout>

</layout>