<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/webview_root"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="@color/colorPrimary"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:drawableLeft="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:maxLines="1"
                android:text="@string/notifications"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textColor="#ffffff"
                android:textStyle="bold" />
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="@dimen/dimen_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        />

    <LinearLayout
        android:id="@+id/errorState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/toolbar"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="@dimen/_40dp"
        android:gravity="center"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        >

        <ImageView
            android:id="@+id/error_state_img"
            android:layout_width="@dimen/_160dp"
            android:layout_height="@dimen/_120dp"
            app:srcCompat="@drawable/ic_no_inet"
            />

        <TextView
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_32dp"
            android:text="@string/well_no_internet_connection" />

        <TextView
            style="@style/Body5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/make_sure_yout_internet_msg"
            android:textAlignment="center" />

    </LinearLayout>
    
    <ImageView
        android:id="@+id/referralImagePreviewWeb"
        android:layout_width="500dp"
        android:layout_height="500dp"
        android:scaleType="fitCenter"
        android:src="@drawable/referral_share_payment"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/pbLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>