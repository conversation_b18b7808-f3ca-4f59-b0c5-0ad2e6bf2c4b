<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="isDebit"
            type="boolean" />

        <variable
            name="transactionType"
            type="int" />
    </data>

    <FrameLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <!-- Toolbar  for title and back button-->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/colorPrimary"
                app:theme="@style/ToolbarTheme">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/close"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="0dp"
                        android:src="@mipmap/back_white" />

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_toRightOf="@+id/close"
                        android:drawableLeft="@drawable/dot_highlighter"
                        android:ellipsize="end"
                        android:fontFamily="sans-serif-medium"
                        android:maxLines="1"
                        android:text="@{transactionType==-1?`Catat Utang`:`Catat Utang`}"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textColor="#ffffff"
                        android:textStyle="normal"
                        tools:text="@string/new_utang_piutang" />

                </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>

            <!--Customer name-->
            <LinearLayout
                android:id="@+id/fragment_container"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"></LinearLayout>

            <ScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fillViewport="true"
                android:visibility="invisible"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/formView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/_16dp">
                    <TextView
                        android:id="@+id/tvOffline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/yellow5"
                        android:drawablePadding="@dimen/_8dp"
                        android:elevation="@dimen/_12dp"
                        android:fontFamily="@font/roboto"
                        android:paddingStart="@dimen/_16dp"
                        android:paddingTop="12dp"
                        android:paddingEnd="@dimen/_16dp"
                        android:paddingBottom="12dp"
                        android:text="@string/recordings_are_saved_in_offline_mode"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        android:visibility="gone"
                        app:drawableStartCompat="@drawable/ic_offline_msg"
                        tools:visibility="visible" />

                    <RadioGroup
                        android:id="@+id/rg_trx_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:checkedButton="@id/rb_debit"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/_16dp"
                        android:paddingLeft="@dimen/_16dp"
                        android:paddingRight="@dimen/_16dp">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/rb_debit"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="4dp"
                            android:layout_weight="1"
                            android:background="@drawable/trx_type_red_bg_selector_light"
                            android:buttonTint="@color/white"
                            android:paddingStart="@dimen/_4dp"
                            android:paddingTop="@dimen/_10dp"
                            android:paddingBottom="@dimen/_10dp"
                            android:text="@string/bal_debit"
                            android:textColor="@drawable/trx_type_text_color_selector"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/rb_credit"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_weight="1"
                            android:background="@drawable/trx_type_green_bg_selector_light"
                            android:buttonTint="@color/white"
                            android:paddingStart="@dimen/_4dp"
                            android:paddingTop="@dimen/_10dp"
                            android:paddingBottom="@dimen/_10dp"
                            android:text="@string/bal_credit"
                            android:textColor="@drawable/trx_type_text_color_selector"
                            android:textStyle="bold" />
                    </RadioGroup>


                    <TextView
                        style="@style/Body2"
                        android:id="@+id/contact_layout_title"
                        android:layout_marginTop="@dimen/_10dp"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_width="wrap_content"
                        android:text="@string/Receive_from"
                        android:layout_height="wrap_content"/>
                    <LinearLayout
                        android:id="@+id/onboardingAddCustomer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <RelativeLayout
                            android:layout_marginTop="@dimen/_4dp"
                            android:id="@+id/contact_layout"
                            android:layout_width="match_parent"
                            android:layout_height="44dp"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:layout_marginBottom="@dimen/_12dp"
                            android:background="@drawable/rounded_rectangle_customer_name">

                            <ImageView
                                android:id="@+id/customer_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/_16dp"
                                android:layout_marginTop="@dimen/_12dp"
                                android:layout_marginBottom="@dimen/_12dp"
                                android:src="@drawable/ic_icon_customer_name" />

                            <TextView
                                android:id="@+id/name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/_14dp"
                                android:layout_marginTop="@dimen/_12dp"
                                android:layout_marginBottom="@dimen/_12dp"
                                android:layout_toRightOf="@+id/customer_icon"
                                android:fontFamily="@font/roboto"
                                android:hint="@string/input_customer_name"
                                android:textColor="@color/black_80"
                                android:textColorHint="@color/black_40"
                                android:textSize="@dimen/text_14sp" />
                        </RelativeLayout>
                    </LinearLayout>


                    <!--Customer balance-->
                    <RelativeLayout
                        android:id="@+id/customer_balance_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:layout_marginBottom="@dimen/_16dp">

                        <TextView
                            android:id="@+id/underpaid_amount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:textColor="@color/black_80"
                            android:textSize="@dimen/text_14sp" />

                        <TextView
                            android:id="@+id/customer_balance_currency_symbol"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_toLeftOf="@+id/customer_balance"
                            android:fontFamily="@font/roboto"
                            android:textColor="@{isDebit?@color/out_red:@color/green_80}"
                            android:textSize="14sp"
                            tools:text="Rp" />

                        <TextView
                            android:id="@+id/customer_balance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:fontFamily="@font/roboto"
                            android:textColor="@{isDebit?@color/out_red:@color/green_80}"
                            android:textSize="@dimen/text_14sp"
                            tools:text="0" />
                    </RelativeLayout>

                    <!--divider-->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_12dp"
                        android:background="@color/black_0" />

                    <!--Amount Input-->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/light_blue_background"
                        android:paddingLeft="@dimen/_16dp"
                        android:paddingTop="@dimen/_20dp"
                        android:paddingRight="@dimen/_16dp"
                        android:paddingBottom="@dimen/_20dp">

                        <LinearLayout
                            android:id="@+id/amount_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:weightSum="2">

                            <TextView
                                android:id="@+id/paid_receive_amount_title"
                                style="@style/Body1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto"
                                android:textColor="@color/black_80" />

                            <LinearLayout
                                android:id="@+id/input_amount_Layout"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_alignParentRight="true"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:id="@+id/amount_box"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end"
                                    android:gravity="center"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/currency_symbol"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginEnd="3.0dip"
                                        android:fontFamily="@font/roboto"
                                        android:lineHeight="40sp"
                                        android:paddingRight="2dp"
                                        android:text="Rp"
                                        android:textColor="@color/black_80"
                                        android:textSize="24sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/transaction_input_amount_result"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:autoSizeMaxTextSize="34dp"
                                        android:autoSizeMinTextSize="24dp"
                                        android:autoSizeStepGranularity="1dp"
                                        android:lineHeight="40sp"
                                        android:text="0"
                                        android:textAlignment="center"
                                        android:textColor="@color/black_80"
                                        android:textSize="24.0sp"
                                        android:textStyle="bold" />

                                    <View
                                        android:id="@+id/cursor"
                                        android:layout_width="2.0dip"
                                        android:layout_height="42.0dip"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginLeft="2.0dip"
                                        android:layout_marginRight="2.0dip"
                                        android:background="@color/black_60" />

                                </LinearLayout>

                                <View
                                    android:layout_width="wrap_content"
                                    android:layout_height="1dp"
                                    android:layout_marginTop="@dimen/_8dp"
                                    android:background="@color/black_60" />

                                <LinearLayout
                                    android:id="@+id/expression_layout"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end"
                                    android:gravity="center"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/transaction_input_amount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_horizontal"
                                        android:fontFamily="@font/roboto"
                                        android:gravity="center_horizontal"
                                        android:lineHeight="20sp"
                                        android:lineSpacingExtra="6sp"
                                        android:text=""
                                        android:textAlignment="center"
                                        android:textColor="@color/black_40"
                                        android:textSize="15sp"
                                        android:textStyle="normal" />
                                </LinearLayout>

                            </LinearLayout>
                        </LinearLayout>

                        <CheckBox
                            android:id="@+id/payment_checkbox"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/amount_layout"
                            android:layout_marginTop="@dimen/_12dp"
                            android:buttonTint="@color/outline_text_input_color"
                            android:checked="false"
                            android:fontFamily="@font/roboto"
                            android:minWidth="0dp"
                            android:minHeight="0dp"
                            android:text="@{transactionType==-1?@string/get_it_all:@string/receive_full_payment}"
                            android:textColor="@color/black_80"
                            android:textSize="14sp"
                            android:textStyle="normal"
                            android:visibility="visible" />


                    </RelativeLayout>

                    <!--divider-->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_12dp"
                        android:background="@color/black_0" />

                    <!--Additional data like date, notes-->
                    <LinearLayout
                        android:id="@+id/additional_data_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/_16dp"
                        android:paddingTop="@dimen/_18dp"
                        android:paddingRight="@dimen/_16dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:text="@string/optional_information"
                            android:textColor="@color/black_80" />
                        <!--Note Layout-->
                        <LinearLayout
                            android:id="@+id/noteLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:background="@drawable/rounded_rectangle_customer_name">

                            <ImageView
                                android:id="@+id/note_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:padding="@dimen/_14dp"
                                android:src="@drawable/ic_icon_notes" />

                            <EditText
                                android:id="@+id/note"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:background="@null"
                                android:focusedByDefault="false"
                                android:fontFamily="@font/roboto"
                                android:gravity="center_vertical"
                                android:hint="@string/transaction_note_hint"
                                android:inputType="none|text|textCapSentences|textMultiLine"
                                android:lineSpacingExtra="5sp"
                                android:scrollbars="vertical"
                                android:textColor="#666666"
                                android:textColorHint="@color/black_20"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <!--Date Layout-->
                        <LinearLayout
                            android:id="@+id/linear_layout_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:id="@+id/date_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingTop="@dimen/_12dp"
                                android:paddingRight="@dimen/_12dp"
                                android:paddingBottom="@dimen/_12dp"
                                android:src="@drawable/ic_calendar" />

                            <TextView
                                android:id="@+id/date_text_view"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:focusable="false"
                                android:fontFamily="@font/roboto"
                                android:hint="@string/transaction_date_hint"
                                android:inputType="none|text|textCapSentences|textMultiLine"
                                android:lineSpacingExtra="5sp"
                                android:scrollbars="vertical"
                                android:textColor="@color/black_80"
                                android:textSize="16.3sp" />

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatCheckBox
                            android:id="@+id/sendCustomerSms"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="-4dp"
                            android:layout_marginTop="@dimen/_4dp"
                            android:checked="true"
                            android:fontFamily="@font/roboto"
                            android:text="@string/send_sms_title"
                            android:textColor="@color/black_60"
                            android:textSize="14sp"
                            android:textStyle="normal"
                            android:theme="@style/CheckBoxStyle"
                            android:visibility="gone" />


                    </LinearLayout>

                </LinearLayout>

            </ScrollView>

            <FrameLayout
                android:id="@+id/bottom_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="invisible">

                <!--Bottom Layout for save btn-->
                <LinearLayout
                    android:id="@+id/saveOnboarding"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:gravity="bottom"
                    android:orientation="vertical">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE" />

                    <!--Button-->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/save_button"
                        style="@style/DisableMaterialButtonStyleAdjacent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_8dp"
                        android:layout_marginRight="@dimen/_16dp"
                        android:layout_marginBottom="@dimen/_8dp"
                        android:fontFamily="@font/roboto"
                        android:gravity="center"
                        android:padding="12dp"
                        android:textAllCaps="false"
                        android:text="@string/save"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:cornerRadius="4dp" />

                </LinearLayout>

                <!--custom keyboard view-->
                <com.bukuwarung.keyboard.CustomKeyboardView
                    android:id="@+id/keyboard_view"
                    android:layout_width="match_parent"
                    android:layout_height="245dp"
                    android:layout_alignParentBottom="true"
                    android:layout_gravity="bottom"
                    android:animateLayoutChanges="true"
                    android:visibility="gone" />

            </FrameLayout>

        </LinearLayout>

        <!--animation Layout for success message-->
        <include
            android:id="@+id/animation_layout"
            layout="@layout/transaction_success_animation_layout" />
        <!--Fragment container for the User Contact Fragment-->

        <FrameLayout
            android:id="@+id/contact_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

</layout>