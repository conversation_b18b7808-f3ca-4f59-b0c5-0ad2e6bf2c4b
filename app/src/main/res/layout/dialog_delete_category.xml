<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_button"
    android:paddingBottom="@dimen/_50dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Heading2"
        android:text="@string/category_change_title"
        android:layout_marginTop="@dimen/dimen_24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.9"/>

    <TextView
        android:id="@+id/tv_body"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/category_change_body"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title"/>

    <CheckBox
        android:id="@+id/cb_cat_del_confirm"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/category_change_body_new"
        android:paddingHorizontal="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_body"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_hapus"
        style="@style/ButtonFill.Blue.Bold"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/hapus"
        android:textAllCaps="false"
        app:layout_constraintEnd_toEndOf="@id/cb_cat_del_confirm"
        app:layout_constraintTop_toBottomOf="@id/cb_cat_del_confirm"
        app:layout_constraintWidth_percent="0.4" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_batal"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:textAllCaps="false"
        style="@style/ButtonOutline.White"
        android:text="@string/batal"
        app:layout_constraintTop_toTopOf="@id/btn_hapus"
        app:layout_constraintBottom_toBottomOf="@id/btn_hapus"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintWidth_percent="0.4" />

</androidx.constraintlayout.widget.ConstraintLayout>