<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@android:color/transparent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="<PERSON><PERSON><PERSON> Akun Whatsapp"
        android:textColor="@color/white"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/bottomsheet_rounded"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp">

        <LinearLayout
            android:id="@+id/wa_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_wa_original"
                android:scaleType="centerCrop"
                android:layout_marginBottom="@dimen/_8dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="WhatsApp"
                android:textColor="@color/black"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/wa4b_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_wa4b_original"
                android:scaleType="centerCrop"
                android:layout_marginBottom="@dimen/_8dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="WhatsApp Bisnis"
                android:textColor="@color/black"/>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>