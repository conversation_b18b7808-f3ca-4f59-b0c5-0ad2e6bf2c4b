<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_business_logo"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_48dp"
        android:visibility="gone"
        android:scaleType="centerInside"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/app_logo"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_business_name"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_32dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_32dp"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_business_logo"
        tools:text="BukuWarung" />

    <TextView
        android:id="@+id/tv_business_phone"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_32dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_32dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_business_name"
        tools:text="081234567890" />

    <TextView
        android:id="@+id/tv_business_address"
        style="@style/Body3"
        android:layout_width="0dp"
        android:textAlignment="center"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_32dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_32dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_business_phone"
        tools:text="Pondok Sukmajaya Permai, Blok B5/6, Kota Depok"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_trx_date"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_business_address"
        tools:text="15 ok 2020, 15:30 WIB" />

    <TextView
        android:id="@+id/tv_trx_id"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_trx_date"
        tools:text="INV: GGWP123" />

    <TextView
        android:id="@+id/tv_pos_payment_method"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_business_address"
        tools:text="Non Tunai - Gopay" />

    <TextView
        android:id="@+id/tv_transaction_status"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textColor="@color/green_100"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_trx_date"
        tools:text="Lunas" />

    <View
        android:id="@+id/customer_line"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/horizontal_dashed_line"
        app:layout_constraintTop_toBottomOf="@id/tv_transaction_status" />

    <TextView
        android:id="@+id/customer_label"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/customers"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/customer_line" />

    <TextView
        android:id="@+id/tv_customer_name"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/customer_line"
        tools:text="Mehmed Zulfakar" />

    <TextView
        android:id="@+id/tv_customer_phone"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_customer_name"
        tools:text="081234567890" />

    <View
        android:id="@+id/customer_line_bottom"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/horizontal_dashed_line"
        app:layout_constraintTop_toBottomOf="@id/tv_customer_phone" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/customer_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="customer_line, tv_customer_phone, tv_customer_name, customer_label" />

</androidx.constraintlayout.widget.ConstraintLayout>