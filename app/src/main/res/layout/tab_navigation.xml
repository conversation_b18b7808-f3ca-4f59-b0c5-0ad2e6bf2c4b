<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tab_navigator"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/black_5" />


    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/navigation"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        app:itemIconTint="@drawable/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_text_color"
        app:itemTextAppearanceActive="@style/BottomNavigationView.Active"
        app:itemTextAppearanceInactive="@style/BottomNavigationView"
        app:labelVisibilityMode="labeled" />
</LinearLayout>
