<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lunaskanSuccessMessageLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/lunaskan_message_bg_image">
        <ImageView
            android:id="@+id/appLogo"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/app_logo"
            android:background="@drawable/background_circular_white48"
            android:padding="@dimen/_8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="60dp"/>

        <TextView
            android:id="@+id/ownerName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@+id/appLogo"
            app:layout_constraintTop_toTopOf="@+id/appLogo"
            android:layout_marginStart="@dimen/_8dp"
            android:text="ownername"
            android:textColor="@color/white"
            android:fontFamily="@font/roboto"
            android:textSize="16sp"
            android:drawableEnd="@drawable/ic_verified_user"
            android:drawablePadding="4dp"/>

        <TextView
            android:id="@+id/mobileNumber"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/ownerName"
            app:layout_constraintStart_toStartOf="@+id/ownerName"
            android:text="number"
            android:textSize="14sp"
            android:fontFamily="@font/roboto"
            android:textColor="@color/white"/>

        <ImageView
            android:id="@+id/successSticker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/lunaskan_success_message_sticker"
            app:layout_constraintTop_toBottomOf="@+id/appLogo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="32dp"/>

        <TextView
            android:id="@+id/congratsMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/successSticker"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/congrats_message"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="@font/roboto"
            android:textColor="@color/white"/>

        <TextView
            android:id="@+id/paymentCompletionAmount"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/congratsMessage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginStart="@dimen/_20dp"
            android:layout_marginEnd="@dimen/_20dp"
            android:gravity="center"
            android:text="@string/debit"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="@font/roboto"
            android:textColor="@color/white"/>

        <TextView
            android:id="@+id/paymentCompletionMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/paymentCompletionAmount"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="center"
            android:layout_marginTop="4dp"
            android:text="@string/payment_completion_message"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="@font/roboto"
            android:textColor="@color/white"/>

        <TextView
            android:id="@+id/transactionRecordedMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/paymentCompletionMessage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="26dp"
            android:text="@string/transaction_completed_message"
            android:textSize="14sp"
            android:fontFamily="@font/roboto"
            android:textColor="@color/white"
            android:drawableStart="@drawable/ic_round_verified_user"
            android:drawablePadding="11dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.google.android.material.button.MaterialButton
        android:id="@+id/shareBtn"
        style="@style/DefaultMaterialButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@+id/closeMessage"
        android:layout_marginBottom="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/share"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="16sp"
        android:textStyle="bold"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@drawable/ic_share"
        app:iconGravity="textStart"
        app:iconTint="@color/black_80"/>

    <TextView
        android:id="@+id/closeMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="37dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="@string/label_close"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:fontFamily="@font/roboto"/>

</androidx.constraintlayout.widget.ConstraintLayout>