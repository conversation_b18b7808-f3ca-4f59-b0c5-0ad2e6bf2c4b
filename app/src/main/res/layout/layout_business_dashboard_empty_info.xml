<?xml version="1.0" encoding="utf-8"?>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:padding="@dimen/_12dp"
        android:id="@+id/empty_info_root"
        android:background="@drawable/bg_rounded_rectangle_blue_5"
       >

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ic_info"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:layout_marginTop="@dimen/_8dp"
            android:src="@drawable/info"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintHorizontal_weight="3"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/tv_header"
            style="@style/Heading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_bold"
            android:layout_marginLeft="@dimen/_12dp"
            android:text="Kenapa harus catat utang?"
            android:textSize="@dimen/_14dp"
            app:layout_constraintHorizontal_weight="7"
            app:layout_constraintEnd_toEndOf="parent"

            android:textColor="@color/black_80"
            app:layout_constraintStart_toEndOf="@+id/ic_info"
            app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_subtext"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:layout_marginTop="@dimen/_6dp"
        android:fontFamily="@font/roboto"
        android:text="Modal adalah uang pribadi, Investor, atau pinjaman bank yang kamu gunakan untuk menjalankan usaha."
        android:textColor="@color/black_40"
        android:textSize="@dimen/_12dp"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ic_info"
        app:layout_constraintTop_toBottomOf="@id/tv_header" />
    <TextView
        android:id="@+id/tv_body"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:layout_marginTop="@dimen/_6dp"
        android:layout_marginLeft="@dimen/_12dp"
        android:text="Cara Catat Utang"
        android:textSize="@dimen/_14dp"
        android:textColor="@color/blue_60"
        app:layout_constraintStart_toEndOf="@+id/ic_info"
        app:layout_constraintTop_toBottomOf="@id/tv_subtext"
        app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

