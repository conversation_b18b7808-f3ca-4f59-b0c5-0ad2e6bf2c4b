<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_home"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/vw_coachmark"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toTopOf="@id/cl_home"
        app:layout_constraintBottom_toBottomOf="@id/cl_home"/>

    <TextView
        android:id="@+id/tv_header_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Heading3"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:layout_marginBottom="@dimen/_10dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_help_icon"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_help_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/to_learn"
        style="@style/SubHeading2"
        android:drawablePadding="@dimen/_2dp"
        android:textColor="@color/blue60"
        app:drawableEndCompat="@drawable/ic_question_blue"
        android:layout_marginEnd="@dimen/_20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_header_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_header_title"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tile_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_10dp"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_header_title" />

</androidx.constraintlayout.widget.ConstraintLayout>