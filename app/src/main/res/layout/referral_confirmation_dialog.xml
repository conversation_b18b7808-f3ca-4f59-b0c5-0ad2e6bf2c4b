<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingTop="24dp"
    android:paddingBottom="24dp"
    android:paddingStart="20dp"
    android:paddingEnd="20dp">
    <TextView
        android:id="@+id/referralConfirmationTitle"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/referral_confirmation_title"/>


    <TextView
        style="@style/Body2"
        android:id="@+id/referralConfirmationMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/referral_confirmation_message"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/referralConfirmationTitle"
        />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/buttonSkip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/referralConfirmationMessage"
        app:layout_constraintEnd_toStartOf="@+id/buttonSubmit"
        android:layout_marginTop="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/cancel"
        android:textAllCaps="false"
        android:textSize="14sp"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold"
        app:strokeWidth="1dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        app:strokeColor="@color/colorPrimary"
        android:backgroundTint="@color/white"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/buttonSubmit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/referralConfirmationMessage"
        app:layout_constraintStart_toEndOf="@+id/buttonSkip"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/yes"
        style="@style/ButtonFill.Blue"/>

</androidx.constraintlayout.widget.ConstraintLayout>