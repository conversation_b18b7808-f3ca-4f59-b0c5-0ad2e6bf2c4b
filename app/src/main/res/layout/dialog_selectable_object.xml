<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/topContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true">

        <RelativeLayout
            android:id="@+id/searchLayout"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_alignParentTop="true"
            android:background="@color/colorBackground"
            android:fitsSystemWindows="true"
            android:gravity="center_vertical"
            android:visibility="gone"
            app:layout_collapseMode="pin">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="6dp">

                <LinearLayout
                    android:layout_width="0px"
                    android:layout_height="0px"
                    android:clickable="true"
                    android:focusable="true"
                    android:focusableInTouchMode="true" />

                <EditText
                    android:id="@+id/searchQueryBox"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toLeftOf="@+id/clear"
                    android:background="@color/white"
                    android:hint="@string/general_search_hint_text"
                    android:maxLines="1"
                    android:paddingLeft="16dp"
                    android:textColor="@color/main_text_color"
                    android:textColorHint="@color/hint_color"
                    android:textSize="16dp"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/clear"
                    android:layout_width="40dp"
                    android:layout_height="44dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="16dp"
                    android:background="@color/white"
                    android:padding="7dp"
                    android:src="@mipmap/close_grey"
                    android:visibility="visible" />
            </RelativeLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/mainContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@color/colorPrimary"
            android:orientation="vertical"
            android:padding="12.0dip"
            android:visibility="visible">

            <ImageView
                android:id="@+id/closeDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_16dp"
                android:backgroundTint="@color/white"
                android:src="@drawable/ic_close"
                android:tint="@color/white"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginTop="7.0dip"
                android:layout_toEndOf="@id/closeDialog"
                android:textColor="@color/white_shade_bg"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="@string/select_business" />

            <ImageView
                android:id="@+id/openSearchBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:backgroundTint="@color/white"
                android:src="@drawable/ic_search"
                android:tint="@color/white"
                tools:ignore="ContentDescription" />

        </RelativeLayout>

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mainRV"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/topContainer"
        android:layout_above="@id/buttonLayout"/>

    <TextView
        android:id="@+id/textEmpty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/search_empty"
        android:textColor="@color/black"
        android:layout_centerInParent="true"
        android:gravity="center_vertical"
        android:textSize="18sp"
        android:visibility="gone"
        android:layout_below="@id/topContainer"
        android:layout_above="@id/buttonLayout"/>

    <LinearLayout
        android:id="@+id/buttonLayout"
        android:layout_width="match_parent"
        android:elevation="@dimen/stdElevation"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/section"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_above="@+id/bottom"
            android:background="@color/section_end" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/addMoreBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:backgroundTint="@color/white"
            android:elevation="0dp"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/add_more"
            android:textAllCaps="false"
            android:textColor="@color/colorPrimary"
            android:textStyle="bold"
            app:cornerRadius="4dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/colorPrimary"/>

    </LinearLayout>
</RelativeLayout>