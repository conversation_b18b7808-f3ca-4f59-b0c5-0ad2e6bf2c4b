<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/_8dp"
    app:cardElevation="@dimen/_0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_12dp">

        <ImageView
            android:id="@+id/calendar_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_calendar_outline"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black40" />

        <TextView
            android:id="@+id/date_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:text="@string/date"
            android:textColor="@color/black40"
            android:textSize="@dimen/text_12sp"
            app:layout_constraintStart_toEndOf="@id/calendar_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/date_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:textColor="@color/black80"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintStart_toStartOf="@id/date_label"
            app:layout_constraintTop_toBottomOf="@id/date_label" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
