<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/setupScreen"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="24dp"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="32dp"
    android:paddingTop="32dp"
    android:background="@color/white"
    android:paddingRight="32dp"
    android:paddingBottom="16dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:fontFamily="@font/roboto"
        android:text="Title"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:layout_marginTop="16dp"
        android:alpha="0.7"
        android:lineSpacingExtra="1dp"
        android:text="dialog body"
        android:textColor="@color/black"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="48dp"
        android:weightSum="2"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:gravity="center"
            android:lineSpacingExtra="1.9sp"
            android:minHeight="48dp"
            android:layout_weight="1"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:layout_marginRight="20dp"
            android:text="@string/cancel"
            android:textAllCaps="true"
            android:background="@color/colorPrimary"
            android:textColor="@color/white"
            android:textSize="14.1sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:gravity="center"
            android:layout_weight="1"
            android:lineSpacingExtra="1.9sp"
            android:minHeight="48dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:text="@string/delete"
            android:textAllCaps="true"
            android:background="@color/red_error"
            android:textColor="@color/white"
            android:textSize="14.1sp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>
