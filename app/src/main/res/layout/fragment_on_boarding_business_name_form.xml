<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/business_name_form"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingTop="@dimen/_24dp">

    <TextView
        android:id="@+id/header_label"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/business_name_input_layout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/header_label">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/business_name_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPersonName"
            android:textColor="@color/black" />

    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/business_name_error_label"
        style="@style/Label2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_6dp"
        android:textColor="@color/red_80"
        app:layout_constraintBottom_toTopOf="@id/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/business_name_input_layout"
        app:layout_constraintVertical_bias="0" />

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:background="@color/new_divider"
        app:layout_constraintBottom_toTopOf="@id/business_name_proceed_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/business_name_proceed_button"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
