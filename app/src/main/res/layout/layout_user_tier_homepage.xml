<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorGreyLight"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/sl_loyalty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:weightSum="1">

            <androidx.cardview.widget.CardView
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:elevation="@dimen/_4dp"
                android:layout_margin="@dimen/_10dp"
                app:cardCornerRadius="@dimen/_10dp"
                android:layout_weight="0.5" >
                <include
                    layout="@layout/layout_user_tier_shimmer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                android:elevation="@dimen/_4dp"
                app:cardCornerRadius="@dimen/_10dp"
                android:layout_weight="0.5">
                <include
                    layout="@layout/layout_user_tier_shimmer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </com.facebook.shimmer.ShimmerFrameLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_loyalty_points"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:elevation="@dimen/_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/_10dp"
        app:cardCornerRadius="@dimen/_10dp"
        app:layout_constraintWidth_percent="0.45">
        <include
            android:id="@+id/layout_membership_status"
            layout="@layout/layout_user_tier_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_loyalty_tier_item"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_10dp"
        android:elevation="@dimen/_4dp"
        app:cardCornerRadius="@dimen/_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.45">
        <include
            android:id="@+id/layout_points"
            layout="@layout/layout_user_tier_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>