<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp"
    android:background="@drawable/bottomsheet_rounded"
    android:paddingBottom="@dimen/_8dp">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_filter_group"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        tools:listitem="@layout/filter_group_item"
        android:layout_marginTop="@dimen/_8dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/btn_confirm_filter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_confirm_filter"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/confirm_label"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_reset"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16dp"
        android:text="@string/reset_label"
        android:layout_marginTop="@dimen/_10dp"
        android:textColor="@color/colorPrimary"
        app:layout_constraintEnd_toStartOf="@+id/iv_close"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_26dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_close"
        app:tint="@color/black_40" />
</androidx.constraintlayout.widget.ConstraintLayout>
