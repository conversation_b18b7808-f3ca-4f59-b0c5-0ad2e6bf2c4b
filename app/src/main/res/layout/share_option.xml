<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageButton
        android:id="@+id/iv_share"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/circle_hollow_blue"
        android:padding="@dimen/_8dp"
        tools:src="@drawable/ic_instagram"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_share"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_share"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>