<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginTop="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
       >

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/historyLogo"
            android:layout_width="@dimen/_120dp"
            android:layout_height="@dimen/_120dp"
            android:layout_marginTop="@dimen/_30dp"
            android:scaleType="fitEnd"
            app:srcCompat="@drawable/business_history"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintHorizontal_weight="4"
          />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/balanceStatus"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/sejak_kamu"
                app:layout_constraintHorizontal_weight="7"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/historyLogo"/>


        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/historyLogo"
            android:background="@color/black_5"/>

        <TextView
            android:id="@+id/btn_detail"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_bold"
            android:layout_margin="@dimen/_16dp"
            android:text="@string/lihat_riwayat"
            android:textAlignment="center"
            android:textSize="@dimen/_14dp"
            android:visibility="visible"
            android:textColor="@color/blue_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider1" />

        <include layout="@layout/layout_business_dashboard_empty_info"
            android:id="@+id/empty_info"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/historyLogo"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_16dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>

