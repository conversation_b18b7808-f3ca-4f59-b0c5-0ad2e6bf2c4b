<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_56dp"
    android:layout_marginStart="@dimen/_12dp"
    android:layout_marginEnd="@dimen/_12dp"
    android:layout_marginBottom="@dimen/_16dp"
    android:background="@color/black_50">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_snackbar_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14dp"
        android:fontFamily="@font/roboto_bold"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/printer_connect_failed" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close_snackbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_close" />

</androidx.constraintlayout.widget.ConstraintLayout>
