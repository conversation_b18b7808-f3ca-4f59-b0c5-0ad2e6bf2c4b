<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/language_item"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="8dp"
        android:padding="12dp"
        android:textSize="13.2sp"
        android:layout_marginRight="24dp"
        android:clickable="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:textAllCaps="false"
        app:strokeColor="@color/subheader"
        app:strokeWidth="1dp" />
</LinearLayout>
