<?xml version="1.0" encoding="utf-8"?>

<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_header"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/_14dp"
        android:orientation="vertical"
        app:behavior_hideable="true"
        app:layout_behavior = "com.google.android.material.bottomsheet.BottomSheetBehavior" >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@drawable/bottomsheet_rounded">

            <RelativeLayout
                android:id="@+id/rl_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/_12dp"
                android:paddingBottom="@dimen/_12dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:textSize="@dimen/text_18sp"
                    android:layout_marginStart="@dimen/_12dp"
                    android:textColor="@color/black_80"
                    android:layout_alignParentStart="true"
                    android:text="@string/add_product_title"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/_12dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:paddingTop="@dimen/_5dp"
                    android:paddingStart="@dimen/_10dp"
                    android:src="@drawable/close"/>

            </RelativeLayout>
            <androidx.core.widget.NestedScrollView
                android:id="@+id/container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fillViewport="true"
                android:isScrollContainer="true"
                app:layout_constraintTop_toBottomOf="@id/rl_header"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_child_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <EditText
                        android:id="@+id/add_product_text"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_marginRight="@dimen/_8dp"
                        android:background="@drawable/bg_edittext_selected"
                        android:padding="12dp"
                        android:maxLength="40"
                        android:textColor="@color/black_80"
                        app:layout_constraintEnd_toStartOf="@+id/select_unit"
                        app:layout_constraintHorizontal_weight="1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_bias="0.8"
                        android:hint="@string/add_product_hint"

                        />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/select_unit"
                        android:layout_width="0dp"
                        android:textColor="@color/black_80"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:layout_marginEnd="15dp"
                        android:background="@drawable/rounded_rectangle_gray"
                        android:drawableRight="@drawable/ic_chevron_right"
                        android:drawableTint="@color/black_80"
                        android:text="Pcs"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_bias="0.2"
                        android:textAllCaps="false"
                        android:maxLength="8"
                        android:gravity="center"

                        />


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/product_price_parent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/add_product_text"
                        android:layout_marginTop="32dp"
                        >
                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"

            app:layout_constraintGuide_percent="0.48" />

                        <TextView
                            android:id="@+id/product_price_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:text="@string/selling_price"
                            android:textSize="@dimen/text_16sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            android:textColor="@color/black_80"
                            />

                        <TextView
                            android:id="@+id/product_price_optional"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:text="@string/optional"
                            style="@style/Body1"
                            android:textColor="@color/black_20"
                            app:layout_constraintBaseline_toBaselineOf="@+id/product_price_label"
                            app:layout_constraintStart_toEndOf="@id/product_price_label"

                            />

       <androidx.constraintlayout.widget.ConstraintLayout
           android:layout_width="0dp"
           android:layout_height="wrap_content"
           app:layout_constraintStart_toEndOf="@+id/guideline"
          app:layout_constraintEnd_toEndOf="parent"
           android:layout_marginEnd="@dimen/_16dp"
           app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/selling_price_parent"
           >
           <androidx.constraintlayout.widget.Guideline
               android:id="@+id/guideline_internal"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:orientation="vertical"
               app:layout_constraintGuide_percent="0.30"
               />
        <TextView
            android:id="@+id/currency_symbol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:paddingEnd="2dp"
            android:text="Rp"
            android:textColor="@color/green_100"
            android:textSize="24sp"
            style="@style/Heading3"
            app:layout_constraintEnd_toStartOf="@+id/selling_price_edit"
            app:layout_constraintTop_toTopOf="parent"
            />



           <View
               android:id="@+id/cursor"
               android:layout_width="2.0dip"
               android:layout_height="28.0dip"
               android:layout_gravity="center_vertical"
               android:background="@color/black_60"
               app:layout_constraintBottom_toBottomOf="@id/selling_price_edit"
               app:layout_constraintEnd_toEndOf="@id/selling_price_edit"
               app:layout_constraintTop_toTopOf="@id/selling_price_edit" />

           <TextView
               android:id="@+id/selling_price_edit"
               style="@style/Heading3"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginEnd="@dimen/_8dp"
               android:autoSizeMaxTextSize="34dp"
               android:autoSizeMinTextSize="22dp"
               android:autoSizeStepGranularity="1dp"
               android:fontFamily="@font/roboto"
               android:hint="0"
               android:maxLength="14"
               android:textColor="@color/green_100"
               android:textColorHint="@color/green_100"
               android:textSize="22sp"
               android:textStyle="bold"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintTop_toTopOf="parent" />

         <!--  <TextView
               android:id="@+id/currency_symbol"
               style="@style/Heading3"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_gravity="center_vertical"
               android:maxLength="18"
               android:text="Rp"
               android:textColor="@color/black"
               android:textSize="22sp"
               android:textStyle="bold"
               app:layout_constraintBottom_toBottomOf="@id/balance"
               app:layout_constraintEnd_toStartOf="@id/balance"
               app:layout_constraintTop_toTopOf="@id/balance" />
-->
        <!--   <EditText
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:maxLength="12"
            android:maxLines="2"
            android:minWidth="20dp"
            android:background="@color/fui_transparent"
            android:id="@+id/selling_price_edit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/currency_symbol"
            app:layout_constraintBottom_toBottomOf="@id/currency_symbol"
            android:textColor="@color/green_100"
            android:textSize="24sp"
            android:inputType="numberDecimal"

            />-->

           <View
               android:id="@+id/nominal_line"
               style="@style/Divider.Horizontal"
               android:layout_width="0dp"
               android:layout_height="1dp"
               android:layout_marginTop="@dimen/_8dp"
               android:background="@color/black_60"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintStart_toStartOf="@id/guideline_internal"
               app:layout_constraintTop_toBottomOf="@id/selling_price_edit" />
           <LinearLayout
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               app:layout_constraintTop_toTopOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               android:id="@+id/result_layout"
               >
           <TextView
               android:id="@+id/text_amount_calc"
               style="@style/Body2"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_gravity="center_horizontal"
               android:fontFamily="@font/roboto"
               android:gravity="center_horizontal"
               android:lineSpacingExtra="6sp"
               android:textAlignment="center"
               android:textColor="@color/black_40"
               android:textSize="15sp"
               android:textStyle="normal"
               android:visibility="gone"

               tools:text="20x10" />
           </LinearLayout>


       </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/all_stock_parent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/product_price_parent"
                        android:layout_marginTop="34dp">

                        <TextView
                            android:id="@+id/current_stock_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:text="@string/all_items"
                            android:textSize="@dimen/text_16sp"
                            android:textColor="@color/black_80"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/current_stock_optional"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10dp"
                            android:text="@string/optional"
                            android:textSize="@dimen/text_16sp"
                            android:textColor="@color/black_20"
                            app:layout_constraintBaseline_toBaselineOf="@+id/current_stock_label"
                            app:layout_constraintStart_toEndOf="@id/current_stock_label" />

                        <ImageView
                            android:id="@+id/iv_current_tooltip"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_info_inventory"
                            app:layout_constraintStart_toEndOf="@+id/current_stock_optional"
                            app:layout_constraintTop_toTopOf="@+id/current_stock_optional"
                            app:layout_constraintBottom_toBottomOf="@+id/current_stock_optional"
                            app:tint="#BDBDBD"
                            android:layout_marginStart="6dp"
                            />

                        <!--TODO use this existing-->
                        <!--        <com.bukuwarung.activities.expense.NumberStepper/>-->

                        <include
                            android:id="@+id/current_stock_edit_layout"
                            layout="@layout/layout_customer_number_stepper"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/_10dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/minimum_stock_parent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/all_stock_parent"
                        android:layout_marginTop="38dp"
                        >

                        <TextView
                            android:id="@+id/minimum_stock_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:text="@string/minimum_stock"
                            android:textSize="@dimen/text_16sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            android:textColor="@color/black_80"
                            />

                        <TextView
                            android:id="@+id/minimum_stock_optional"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:text="@string/optional"
                            android:textSize="@dimen/text_16sp"
                            android:textColor="@color/black_20"
                            app:layout_constraintBaseline_toBaselineOf="@+id/minimum_stock_label"
                            app:layout_constraintStart_toEndOf="@id/minimum_stock_label" />
                        <ImageView
                            android:id="@+id/minimumTooltip"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_info_inventory"
                            app:layout_constraintStart_toEndOf="@+id/minimum_stock_optional"
                            app:layout_constraintTop_toTopOf="@+id/minimum_stock_optional"
                            app:layout_constraintBottom_toBottomOf="@+id/minimum_stock_optional"
                            app:tint="#BDBDBD"
                            android:layout_marginLeft="6dp"
                            />

                        <include
                            android:id="@+id/minimum_stock_edit_layout"
                            layout="@layout/layout_customer_number_stepper"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_10dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>
            <com.google.android.material.button.MaterialButton
                android:id="@+id/add_product_btn"
                style="@style/ButtonFill"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:text="@string/save"
                app:layout_constraintBottom_toBottomOf="parent"
                />

    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/keyboardView"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:layout_height="0dp" />
</androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>