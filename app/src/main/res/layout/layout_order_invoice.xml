<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/_16dp">

    <!--    Store details    -->
    <include
        android:id="@+id/layout_store_detail"
        layout="@layout/layout_store_detail" />

    <!--    Invoice detail container    -->
    <LinearLayout
        android:id="@+id/ll_invoice_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:orientation="vertical"
        tools:background="@color/black_10"
        tools:layout_height="96dp" />

    <!--    Footer    -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_invoice_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/note_hint">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_app_logo"
            android:layout_width="@dimen/_26dp"
            android:layout_height="@dimen/_32dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_footer_link"
            app:layout_constraintEnd_toStartOf="@id/tv_footer_message"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_footer_message"
            app:srcCompat="@drawable/app_logo" />

        <TextView
            android:id="@+id/tv_footer_message"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_38dp"
            android:text="@string/made_with_bukuwarung_app"
            app:layout_constraintBottom_toTopOf="@+id/tv_footer_link"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/img_app_logo" />

        <TextView
            android:id="@+id/tv_footer_link"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:text="@string/bukuwarung_url"
            android:textColor="@color/blue_60"
            app:layout_constraintStart_toEndOf="@id/img_app_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_footer_message" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>