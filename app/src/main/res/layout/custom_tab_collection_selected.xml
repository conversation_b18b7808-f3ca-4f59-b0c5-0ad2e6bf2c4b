<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="50dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/default_placeholder"
        android:textAllCaps="true"
        android:textColor="@color/colorPrimary"
        android:textSize="11sp"
        android:textStyle="bold"
        android:textAlignment="center"
        tools:text="Akan Datang"
        tools:ignore="SmallSp" />

    <TextView
        android:id="@+id/txtAmt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/default_placeholder"
        android:textColor="@color/colorPrimary"
        android:textSize="11sp"
        android:textStyle="bold"
        android:textAlignment="center"
        tools:text="Rp. 40.000.000"
        tools:ignore="SmallSp" />

</LinearLayout>