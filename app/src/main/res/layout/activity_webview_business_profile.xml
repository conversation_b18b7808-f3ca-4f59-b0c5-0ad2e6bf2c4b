<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/layout_webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_alignParentTop="true"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:visibility="gone"
            app:theme="@style/ToolbarTheme"
            app:titleTextColor="@color/white">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="0dp"
                    app:srcCompat="@mipmap/back_white" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_toRightOf="@+id/close"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto"
                    android:maxLines="1"
                    android:textSize="18dp"
                    android:text="Profil Usaha"
                    android:textColor="#ffffff"
                    android:textStyle="normal"
                    app:drawableLeftCompat="@drawable/dot_highlighter" />

                <TextView
                    android:id="@+id/delete_business_btn"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentBottom="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:text="@string/delete"
                    android:fontFamily="@font/roboto"
                    android:gravity="center"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="0dp"
                    android:paddingBottom="0dp"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="@color/white" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
        <WebView
            android:id="@+id/webview"
            android:visibility="gone"
            android:layout_below="@id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_margin="@dimen/_20dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:indeterminate="true"
            android:layout_centerHorizontal="true"
            android:visibility="visible"
            style="@style/Base.Widget.AppCompat.ProgressBar.Horizontal" />
    </RelativeLayout>
</RelativeLayout>