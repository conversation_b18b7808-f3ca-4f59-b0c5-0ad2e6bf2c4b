<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/_40dp">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/sv_view"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        app:layout_constraintBottom_toTopOf="@+id/bt_save_edit">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_history_details">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_announcement_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/alice_blue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_announce"
                    android:layout_width="@dimen/_50dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginTop="@dimen/_5dp"
                    android:layout_marginBottom="@dimen/_5dp"
                    android:src="@drawable/onboarding_announce"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_12dp"
                    android:text="@string/edit_fee_announcement"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_announce"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_announce"
                    app:layout_constraintTop_toTopOf="@+id/iv_announce" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_nominal_message"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/transaction_amount"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_announcement_info" />

            <TextView
                android:id="@+id/tv_actual_money_message1"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/digi_pay_amount_message"
                android:textColor="@color/black_20"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@id/tv_service_fee_message"
                app:layout_constraintTop_toBottomOf="@+id/tv_nominal_message" />

            <TextView
                android:id="@+id/tv_nominal"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/black_20"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_announcement_info"
                tools:text="Rp100.000" />

            <View
                android:id="@+id/divider1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="@dimen/_1dp"
                android:layout_margin="@dimen/_16dp"
                android:background="@color/black_10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_actual_money_message1" />

            <TextView
                android:id="@+id/tv_service_fee_message"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:text="@string/service_fee"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider1" />

            <TextView
                android:id="@+id/tv_customer_fee_message"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/charged_to_the_customer"
                android:textColor="@color/black_20"
                app:layout_constraintStart_toStartOf="@id/tv_service_fee_message"
                app:layout_constraintTop_toBottomOf="@+id/tv_service_fee_message" />

            <com.bukuwarung.baseui.CurrencyEditText
                android:id="@+id/et_input_nominal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@color/white"
                android:fontFamily="@font/roboto_bold"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:singleLine="true"
                android:textColor="@color/green_80"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_18sp"
                app:layout_constraintBottom_toTopOf="@+id/layout_total_transaksi"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider1" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_total_transaksi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                android:background="@color/black_5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_service_fee_message">

                <TextView
                    android:id="@+id/tv_total_transaksi"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginBottom="@dimen/_10dp"
                    android:text="@string/transaction_amount_total_label"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_actual_money_message2"
                    style="@style/Label1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_4dp"
                    android:text="@string/digi_pay_amount_message"
                    android:textColor="@color/black_20"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tv_total_transaksi"
                    app:layout_constraintTop_toBottomOf="@+id/tv_total_transaksi" />

                <TextView
                    android:id="@+id/tv_total_transaction"
                    style="@style/Heading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Rp102.000" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_add_notes"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:text="@string/add_notes"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_total_transaksi" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_write_notes"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_5dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:hint="@string/write_notes"
                android:inputType="text"
                app:drawableStartCompat="@drawable/ic_edit_with_border"
                app:drawableTint="@color/black_20"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_add_notes" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:background="@color/black_5"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingBottom="@dimen/_44dp"
                app:layout_constraintTop_toBottomOf="@id/et_write_notes">

                <TextView
                    android:id="@+id/tv_preview"
                    style="@style/Heading3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/proof_of_trx"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/et_write_notes" />

                <FrameLayout
                    android:id="@+id/receipt_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@drawable/bg_rounded_rectangle_white_8dp"
                    app:layout_constraintTop_toBottomOf="@id/tv_preview">

                    <com.bukuwarung.activities.print.PaymentReceipt
                        android:id="@+id/payment_receipt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <com.bukuwarung.payments.widget.OrderInvoice
                        android:id="@+id/order_invoice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone" />

                </FrameLayout>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_save_edit"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:padding="@dimen/_12dp"
        android:text="@string/save_changes"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:backgroundTint="@color/new_yellow"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sv_view" />
</androidx.constraintlayout.widget.ConstraintLayout>