<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_black_outline_8dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_bank"
        app:cardCornerRadius="@dimen/_4dp"
        android:layout_width="@dimen/_56dp"
        android:layout_height="@dimen/_45dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_bank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_bank_name"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toEndOf="@id/cv_bank"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Saldo BukuWarung" />

    <TextView
        android:id="@+id/tv_account_number"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_40"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginTop="@dimen/_2dp"
        app:layout_constraintStart_toEndOf="@id/cv_bank"
        app:layout_constraintTop_toBottomOf="@id/tv_bank_name"
        tools:text="Rp500.000" />

    <TextView
        android:id="@+id/tv_edit"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/edit"
        android:textColor="@color/blue_60"
        android:layout_marginTop="@dimen/_24dp"
        android:layout_marginEnd="@dimen/_24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>