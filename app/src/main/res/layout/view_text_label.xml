<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/labelCard"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/out_red"
    app:cardCornerRadius="6dp"
    app:cardElevation="@dimen/_0dp">

    <TextView
        android:id="@+id/labelText"
        style="@style/Label1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="4dp"
        android:paddingTop="2dp"
        android:paddingEnd="4dp"
        android:paddingBottom="2dp"
        android:textColor="@color/white"
        tools:text="@string/new_label" />
</androidx.cardview.widget.CardView>