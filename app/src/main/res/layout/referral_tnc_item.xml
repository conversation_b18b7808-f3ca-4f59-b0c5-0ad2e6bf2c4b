<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mainContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_16dp"
    android:gravity="start|top"
    android:background="@color/white"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/indexText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/default_placeholder"
        android:textColor="@color/black"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/contentText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="10"
        android:text="@string/default_placeholder"
        android:textColor="@color/black"
        android:textSize="14sp" />

</LinearLayout>