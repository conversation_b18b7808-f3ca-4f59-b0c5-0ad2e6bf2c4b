<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.chip.Chip
        android:id="@+id/category_chip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checkable="false"
        android:stateListAnimator="@null"
        android:text="@string/category_label"
        android:textColor="@color/black60"
        app:chipBackgroundColor="@color/black5"
        app:chipEndPadding="@dimen/_12dp"
        app:chipIcon="@drawable/ic_category"
        app:chipStartPadding="@dimen/_12dp"
        app:chipStrokeColor="@color/black10"
        app:chipStrokeWidth="@dimen/_1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/category_list"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:requiresFadingEdge="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/category_chip"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
