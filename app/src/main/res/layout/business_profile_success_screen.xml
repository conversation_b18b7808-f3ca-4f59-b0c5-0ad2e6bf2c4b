<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@drawable/ic_additional_business_card_bg"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_success"
        android:layout_height="98dp"
        android:layout_width="132dp"
        android:layout_marginTop="@dimen/_50dp"
        app:srcCompat="@drawable/ic_catatan"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tv_successful_completion"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        style="@style/Heading1"
        android:gravity="center"
        android:text="@string/bp_success"
        android:textColor="@color/white"
        android:layout_marginHorizontal="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_success"/>

    <TextView
        android:id="@+id/tv_successful_completion_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        style="@style/Body2"
        android:gravity="center"
        android:text="@string/bp_succcess_info"
        android:textColor="@color/white"
        android:layout_marginHorizontal="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_successful_completion"/>

    <FrameLayout
        android:id="@+id/card_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_successful_completion_info"
        android:layout_marginBottom="@dimen/_48dp"
        app:layout_constraintBottom_toTopOf="@id/ll_share"
        android:layout_marginStart="@dimen/_24dp"
        android:layout_marginEnd="@dimen/_24dp">

        <com.bukuwarung.activities.card.newcard.NewBusinessCardWidget
            android:id="@+id/new_business_card_preview_share"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="parent"
            tools:visibility="visible" />

    </FrameLayout>


    <LinearLayout
        android:id="@+id/ll_share"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_12dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:weightSum="1"
        app:layout_constraintEnd_toEndOf="@+id/card_container"
        app:layout_constraintStart_toStartOf="@+id/card_container"
         app:layout_constraintBottom_toTopOf="@id/btn_okay">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/editBusinessCard"
            style="@style/ButtonOutline.WhiteAll"
            android:layout_width="@dimen/_0dp"
            android:layout_weight="0.5"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold"
            android:layout_marginEnd="@dimen/_8dp"
            android:text="@string/edit_data" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/shareNormal"
            android:width="@dimen/_0dp"
            android:layout_weight="0.5"
            android:text="Bagikan Kartu"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            android:layout_marginStart="@dimen/_8dp"
            android:drawableStart="@drawable/ic_share"
            style="@style/ButtonOutline.WhiteAll"
            android:layout_width="@dimen/_0dp" />

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_okay"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:text="Oke Mengerti"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>