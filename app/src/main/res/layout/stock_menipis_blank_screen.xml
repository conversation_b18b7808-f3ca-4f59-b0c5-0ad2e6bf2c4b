<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingStart="@dimen/_20dp"
    android:paddingEnd="@dimen/_20dp">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/inventory_history_detail_empty_image" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        style="@style/SubHeading1"
        android:text="@string/no_stock_menipis_blank_screen_title"
        android:textAlignment="center" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:fontFamily="@font/roboto"
        style="@style/Body2"
        android:text="@string/inventory_list_empty_screen_message"
        android:textAlignment="center" />

</LinearLayout>

    