<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginTop="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_heading"
            android:layout_margin="@dimen/_16dp"
            android:text="Transaksi Pembukuan"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/iv_dropdown"
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:src="@drawable/ic_chevron_down"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_marginEnd="@dimen/_24dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_card"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/cl_heading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingBottom="@dimen/_10dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/transactionLogo"
            android:layout_width="@dimen/_180dp"
            android:layout_height="@dimen/_135dp"
            android:src="@drawable/business_utang"
            android:layout_marginTop="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/balanceStatus"
            style="@style/Heading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:gravity="center"
            android:text="@string/_2_478_transaksi"
            app:autoSizeTextType="uniform"
            android:padding="@dimen/_3dp"
            android:textColor="@color/black_80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/transactionLogo"/>

        <TextView
            android:id="@+id/heading2"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:paddingStart="@dimen/_10dp"
            android:paddingEnd="@dimen/_10dp"
            android:text="@string/berikut_total_catatan_transaksi_berdasarkan_kategori"
            android:textAlignment="center"
            android:textSize="@dimen/text_12sp"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/balanceStatus" />

        <LinearLayout
            android:id="@+id/ll_view_pager"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginLeft="@dimen/_10dp"
            android:layout_marginRight="@dimen/_10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/heading2"
            android:orientation="vertical">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tl_transaction"
                app:tabTextAppearance="@android:style/TextAppearance.Widget.TabWidget"
                app:tabIndicatorColor="@color/colorPrimary"
                android:layout_width="match_parent"
                app:tabTextColor="@color/black_40"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_10dp"
                android:background="@color/white"
                app:tabSelectedTextColor="@color/colorPrimary"
                android:layout_height="wrap_content" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp_transaction"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="@dimen/_0dp"/>
        </LinearLayout>


        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            app:layout_constraintTop_toBottomOf="@id/ll_view_pager"
            android:background="@color/black_5"/>
        <TextView
            android:id="@+id/btn_detail"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_bold"
            android:layout_margin="@dimen/_16dp"
            android:text="@string/lihat_semuaz"
            android:textAlignment="center"
            android:textSize="@dimen/_14dp"
            android:textColor="@color/blue_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider1" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_add_transaction"
            style="@style/ButtonFillNoPadding"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_50dp"
            android:layout_marginTop="@dimen/_16dp"
            android:text="Catat Transaksi Sekarang"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/heading2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>
     </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>

