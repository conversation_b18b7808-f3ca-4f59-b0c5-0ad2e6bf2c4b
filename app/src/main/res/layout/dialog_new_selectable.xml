<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/_16dp"
            android:paddingBottom="@dimen/_16dp"
            android:background="@color/colorPrimary"
            android:orientation="vertical"
            android:paddingLeft="24.0dip">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginTop="7.0dip"
                tools:text="@string/add_new_business_type"
                android:textAppearance="@style/tut_heading"
                android:textColor="@color/white"
                android:textStyle="bold" />

        </RelativeLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/new_divider" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="24dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/inputLayout"
                android:theme="@style/OutlineTextInputStyle"
                android:layout_width="match_parent"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                app:boxStrokeColor="@color/outline_text_input_color"
                app:boxStrokeWidth="1dp"
                android:layout_height="wrap_content"
                android:focusable="true"
                app:passwordToggleDrawable="@null"
                android:focusedByDefault="true"
                android:hint="@string/kategori_usaha"
                android:textColorHint="@color/outline_text_input_color"
                app:hintTextColor="@color/outline_text_input_color">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/nameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="true"
                    android:focusedByDefault="true"
                    android:textColor="@color/black"
                    android:textSize="15sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:layout_gravity="right"
                android:layout_marginTop="24dp">

                <TextView
                    android:id="@+id/btn_cancel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:text="@string/cancel_selectable"
                    android:textStyle="bold"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp"
                    android:layout_weight="1"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:textColor="#0091FF"
                    android:lineSpacingExtra="1sp"
                    android:translationY="-0.44sp"
                    android:background="@drawable/round_corner_light_blue_rectangle"
                    android:gravity="center"
                    />

                <TextView
                    android:id="@+id/btn_save"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:text="@string/save"
                    android:textStyle="bold"
                    android:layout_weight="1"
                    android:paddingLeft="16dp"
                    android:fontFamily="@font/roboto"
                    android:paddingRight="16dp"
                    android:textColor="#FFFFFF"
                    android:lineSpacingExtra="1sp"
                    android:translationY="-0.44sp"
                    style="@style/ButtonFill.Blue"
                    android:background="@drawable/button_next_bg"
                    android:gravity="center"
                    />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>