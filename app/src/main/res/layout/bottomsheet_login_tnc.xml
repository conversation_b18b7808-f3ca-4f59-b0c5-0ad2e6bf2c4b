<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/BottomSheetDialogTheme.RoundCorner"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/back"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:src="@mipmap/back_white"
                app:tint="@color/black_80" />

            <TextView
                android:id="@+id/title"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/_20dp"
                android:gravity="center"
                android:text="@string/main_referral_tnc_title"
                android:textColor="@color/black"
                android:textFontWeight="700"
                android:textSize="@dimen/text_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/back"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintTop_toTopOf="@id/back" />

        </LinearLayout>

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/_10dp"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toTopOf="@+id/acceptBtn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/acceptBtn"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/i_agree"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>