<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    >

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="@color/colorPrimary"
        android:paddingStart="@dimen/_0dp"
        android:paddingEnd="@dimen/_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:titleTextColor="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/_24dp"
                android:layout_toRightOf="@+id/back_btn"
                android:drawableLeft="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/user_profile"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>

    </androidx.appcompat.widget.Toolbar>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/disconnectBrick"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cv_brick_disconnection_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                >

                <ImageView
                    android:id="@+id/imageView"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_60dp"
                    android:scaleType="fitXY"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/user_profile_background_shape" />

                <ImageView
                    android:id="@+id/profilePic"
                    android:layout_width="@dimen/_80dp"
                    android:layout_height="@dimen/_80dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_20dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/imageView"
                    app:srcCompat="@drawable/ic_icon_shop" />

                <TextView
                    android:id="@+id/tv_owner_name"
                    style="@style/Heading1"
                    android:layout_width="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/black_80"
                    app:layout_constraintStart_toStartOf="@+id/profilePic"
                    app:layout_constraintTop_toBottomOf="@+id/profilePic"
                    tools:text="Usaha 123" />

                <ImageView
                    android:id="@+id/phone_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12dp"
                    app:layout_constraintStart_toStartOf="@+id/tv_owner_name"
                    app:layout_constraintTop_toBottomOf="@+id/tv_owner_name"
                    app:srcCompat="@drawable/ic_utang_phone" />

                <TextView
                    android:id="@+id/tv_phone_number"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/black_80"
                    app:layout_constraintBottom_toBottomOf="@+id/phone_icon"
                    app:layout_constraintStart_toEndOf="@+id/phone_icon"
                    app:layout_constraintTop_toTopOf="@+id/phone_icon"
                    tools:text="987654321" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/email_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@+id/phone_icon"
                    app:layout_constraintTop_toBottomOf="@+id/phone_icon">

                    <ImageView
                        android:id="@+id/email_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_12dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_profile_email_icon" />

                    <TextView
                        android:id="@+id/tv_email"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black_80"
                        app:layout_constraintBottom_toBottomOf="@+id/email_icon"
                        app:layout_constraintStart_toEndOf="@+id/email_icon"
                        app:layout_constraintTop_toTopOf="@+id/email_icon"
                        tools:text="<EMAIL>" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/dob_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@+id/email_layout"
                    app:layout_constraintTop_toBottomOf="@+id/email_layout">

                    <ImageView
                        android:id="@+id/dob_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_12dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_profile_dob" />

                    <TextView
                        android:id="@+id/tv_dob"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black_80"
                        app:layout_constraintBottom_toBottomOf="@+id/dob_icon"
                        app:layout_constraintStart_toEndOf="@+id/dob_icon"
                        app:layout_constraintTop_toTopOf="@+id/dob_icon"
                        tools:text="10 Desember 1995" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/divider_pin_top"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_8dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@color/black_0"
                    android:visibility="visible"
                    app:layout_constraintTop_toBottomOf="@+id/dob_layout" />

                <TextView
                    android:id="@+id/tv_pin_title"
                    style="@style/Heading3"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_20dp"
                    android:text="@string/account_settings_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/divider_pin_top" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_profile_pin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:clipToPadding="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:nestedScrollingEnabled="false"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_pin_title" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/rv_profile_pin"
                    android:id="@+id/brickDisconnectionParentLayout"
                    >

                    <TextView
                        android:id="@+id/financial_information"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:paddingVertical="@dimen/_8dp"
                        android:paddingHorizontal="@dimen/_16dp"
                        android:text="@string/financial_information"
                        android:background="@color/blue_pattens_light"
                        />

                    <TextView
                        android:id="@+id/tv_integrations_title"
                        style="@style/Heading3"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_20dp"
                        android:text="@string/integrations_with_financial_institutions"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/financial_information" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/brickAccountSuccessLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="invisible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_integrations_title"
                        >

                        <TextView
                            android:id="@+id/tv_integration_info"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_marginHorizontal="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_10dp"
                            android:ellipsize="end"
                            android:text="@string/financial_info"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_brick_integrations"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_10dp"
                            android:clipToPadding="false"
                            android:nestedScrollingEnabled="false"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tv_integration_info" />

                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/brickAccountSuccessLayout"
                        android:id="@+id/errorLayout"
                        android:visibility="gone"
                        >

                        <ImageView
                            android:id="@+id/errorImage"
                            android:layout_width="@dimen/_150dp"
                            android:layout_height="@dimen/_135dp"
                            android:src="@drawable/ic_brick_not_connected"
                            android:layout_marginTop="@dimen/_40dp"
                            android:layout_marginHorizontal="70dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />


                        <TextView
                            android:id="@+id/errorTitle"
                            style="@style/Heading2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_30dp"
                            android:layout_marginHorizontal="@dimen/_30dp"
                            android:gravity="center"
                            android:text="@string/user_profile_brick_network_error_title"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/errorImage" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:text="@string/user_profile_brick_disconnect_message"
                            android:layout_marginTop="@dimen/_20dp"
                            style="@style/Body1"
                            android:gravity="center"
                            android:id="@+id/errorMessage"
                            app:layout_constraintStart_toStartOf="@+id/errorTitle"
                            app:layout_constraintEnd_toEndOf="@+id/errorTitle"
                            app:layout_constraintTop_toBottomOf="@+id/errorTitle"
                            />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:text="@string/user_profile_brick_disconnect_message"
                            android:layout_marginTop="@dimen/_20dp"
                            style="@style/Body1"
                            android:gravity="center"
                            android:visibility="gone"
                            android:layout_marginHorizontal="@dimen/_30dp"
                            android:id="@+id/brickNotConnectedMessage"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/errorImage"
                            />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnRetry"
                            style="@style/ButtonOutline.Blue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_40dp"
                            android:layout_marginBottom="@dimen/_50dp"
                            android:enabled="true"
                            android:paddingTop="@dimen/_10dp"
                            android:paddingBottom="@dimen/_10dp"
                            android:paddingStart="@dimen/_40dp"
                            android:paddingEnd="@dimen/_40dp"
                            android:text="@string/retry"
                            android:textAllCaps="false"
                            android:textSize="@dimen/text_16sp"
                            android:textStyle="bold"
                            app:cornerRadius="@dimen/_2dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="@+id/errorMessage"
                            app:layout_constraintStart_toStartOf="@+id/errorMessage"
                            app:layout_constraintTop_toBottomOf="@+id/errorMessage" />


                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <ProgressBar
                        android:id="@+id/progressbar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="@+id/brickAccountSuccessLayout"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@+id/tv_integrations_title" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
