<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- TODO: will refactor using new style for toolbar-->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="28dp"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="35dp"
                android:layout_marginRight="35dp"
                android:layout_toLeftOf="@+id/saveBtn"
                android:layout_toRightOf="@id/closeBtn"
                android:alpha="1"
                android:fontFamily="@font/roboto"
                android:text="@string/new_cash_transaction"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:id="@+id/form"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/button_divider"
        app:layout_constraintTop_toBottomOf="@id/toolbar">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:paddingBottom="@dimen/_16dp">

            <androidx.constraintlayout.widget.Group
                android:id="@+id/ppob_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="expense, text_expense, income, text_income, cursor"/>
            <!--only for intro-->
            <View
                android:id="@+id/type_rg"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@id/expense"
                app:layout_constraintEnd_toEndOf="@id/income"
                app:layout_constraintStart_toStartOf="@id/expense"
                app:layout_constraintTop_toTopOf="@id/expense" />
            <!--Switch-->
            <View
                android:id="@+id/expense"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:background="@drawable/type_unselected_bg"
                app:layout_constraintEnd_toStartOf="@id/income"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/expense_rd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_14dp"
                android:buttonTint="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/expense"
                app:layout_constraintStart_toStartOf="@id/expense"
                app:layout_constraintTop_toTopOf="@id/expense" />

            <TextView
                android:id="@+id/text_expense"
                style="@style/SubHeading1"
                android:text="@string/expense_label"
                android:textColor="@color/greyDisabled"
                app:layout_constraintBottom_toBottomOf="@id/expense"
                app:layout_constraintStart_toEndOf="@id/expense_rd"
                app:layout_constraintTop_toTopOf="@id/expense" />

            <View
                android:id="@+id/income"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/type_inc_selected_bg"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/expense"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/income_rd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_14dp"
                android:buttonTint="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/income"
                app:layout_constraintStart_toStartOf="@id/income"
                app:layout_constraintTop_toTopOf="@id/income" />

            <TextView
                android:id="@+id/text_income"
                style="@style/SubHeading1"
                android:fontFamily="sans-serif"
                android:text="@string/income_label"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/income"
                app:layout_constraintStart_toEndOf="@id/income_rd"
                app:layout_constraintTop_toTopOf="@id/income" />

            <View
                android:id="@+id/border_nominal"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/bg_button_outline_primary"
                app:layout_constraintBottom_toBottomOf="@id/calc_guideline"
                app:layout_constraintTop_toBottomOf="@id/expense" />

            <TextView
                android:id="@+id/txt_main_title"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginBottom="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="@id/calc_guideline"
                app:layout_constraintStart_toStartOf="@id/border_nominal"
                app:layout_constraintTop_toTopOf="@id/border_nominal"
                tools:text="@string/nominal_income" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/ppob_grey_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="currency_symbol, balance, currency_symbol_modal, balance_modal"/>

            <TextView
                android:id="@+id/currency_symbol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="3dp"
                android:fontFamily="@font/roboto"
                android:lineHeight="40sp"
                android:paddingRight="2dp"
                android:text="Rp"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/balance"
                app:layout_constraintEnd_toStartOf="@id/balance"
                app:layout_constraintTop_toTopOf="@id/balance" />

            <TextView
                android:id="@+id/balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_12dp"
                android:autoSizeMaxTextSize="34dp"
                android:autoSizeMinTextSize="24dp"
                android:autoSizeStepGranularity="1dp"
                android:fontFamily="@font/roboto"
                android:hint="0"
                android:lineHeight="40sp"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="24.0sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@id/border_nominal"
                app:layout_constraintTop_toTopOf="@id/border_nominal" />

            <View
                android:id="@+id/cursor"
                android:layout_width="2.0dip"
                android:layout_height="28.0dip"
                android:layout_gravity="center_vertical"
                android:background="@color/colorPrimary"
                app:layout_constraintBottom_toBottomOf="@id/balance"
                app:layout_constraintEnd_toEndOf="@id/balance"
                app:layout_constraintTop_toTopOf="@id/balance" />

            <LinearLayout
                android:id="@+id/exprLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:gravity="center"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/balance"
                app:layout_constraintTop_toBottomOf="@id/balance">

                <TextView
                    android:id="@+id/text_amount_calc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="6sp"
                    android:text=""
                    android:textAlignment="center"
                    android:textColor="#666666"
                    android:textSize="15sp"
                    android:textStyle="normal" />
            </LinearLayout>

            <View
                android:id="@+id/calc_guideline"
                android:layout_width="1dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/exprLayout" />


            <View
                android:id="@+id/border_nominal_modal"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/bg_button_outline_primary"
                app:layout_constraintBottom_toBottomOf="@id/calc_guideline_modal"
                app:layout_constraintTop_toBottomOf="@id/calc_guideline" />

            <TextView
                android:id="@+id/mainTitle_modal"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginBottom="@dimen/_10dp"
                android:text="@string/nominal_modal"
                app:layout_constraintBottom_toBottomOf="@id/calc_guideline_modal"
                app:layout_constraintStart_toStartOf="@id/border_nominal_modal"
                app:layout_constraintTop_toTopOf="@id/border_nominal_modal" />

            <TextView
                android:id="@+id/currency_symbol_modal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="3dp"
                android:fontFamily="@font/roboto"
                android:lineHeight="40sp"
                android:paddingRight="2dp"
                android:text="Rp"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/balance_modal"
                app:layout_constraintEnd_toStartOf="@id/balance_modal"
                app:layout_constraintTop_toTopOf="@id/balance_modal" />

            <TextView
                android:id="@+id/balance_modal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_12dp"
                android:autoSizeMaxTextSize="34dp"
                android:autoSizeMinTextSize="24dp"
                android:autoSizeStepGranularity="1dp"
                android:fontFamily="@font/roboto"
                android:hint="0"
                android:lineHeight="40sp"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="24.0sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@id/border_nominal_modal"
                app:layout_constraintTop_toTopOf="@id/border_nominal_modal" />

            <View
                android:id="@+id/cursor_modal"
                android:layout_width="2.0dip"
                android:layout_height="28.0dip"
                android:layout_gravity="center_vertical"
                android:background="@color/colorPrimary"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/balance_modal"
                app:layout_constraintEnd_toEndOf="@id/balance_modal"
                app:layout_constraintTop_toTopOf="@id/balance_modal" />

            <LinearLayout
                android:id="@+id/exprLayout_modal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:gravity="center"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/balance_modal"
                app:layout_constraintTop_toBottomOf="@id/balance_modal">

                <TextView
                    android:id="@+id/text_amount_calc_modal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="6sp"
                    android:text=""
                    android:textAlignment="center"
                    android:textColor="#666666"
                    android:textSize="15sp"
                    android:textStyle="normal" />
            </LinearLayout>

            <View
                android:id="@+id/calc_guideline_modal"
                android:layout_width="1dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/exprLayout_modal" />

            <View
                android:id="@+id/modal_indicator_container"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/profit_bg"
                app:layout_constraintBottom_toBottomOf="@id/indicator_guideline"
                app:layout_constraintTop_toBottomOf="@id/calc_guideline_modal" />

            <View
                android:id="@+id/modal_tutorial_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/modal_indicator_container"
                app:layout_constraintEnd_toEndOf="@id/border_nominal_modal"
                app:layout_constraintStart_toStartOf="@id/border_nominal_modal"
                app:layout_constraintTop_toTopOf="@id/border_nominal_modal" />

            <TextView
                android:id="@+id/txt_modal_indicator"
                style="@style/Heading3"
                android:layout_marginStart="28dp"
                android:layout_marginTop="20dp"
                android:text="@string/profit"
                android:textColor="@color/green_100"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/calc_guideline_modal" />

            <TextView
                android:id="@+id/txt_profit_amount"
                style="@style/Heading3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="28dp"
                android:ellipsize="end"
                android:gravity="end"
                android:maxLines="1"
                android:text="@string/default_placeholder"
                android:textColor="@color/green_80"
                app:layout_constraintBottom_toBottomOf="@id/txt_modal_indicator"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/txt_modal_indicator"
                app:layout_constraintTop_toTopOf="@id/txt_modal_indicator"
                tools:text="Rp 50.000" />

            <View
                android:id="@+id/indicator_guideline"
                android:layout_width="1dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_modal_indicator" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/modal_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="indicator_guideline,txt_profit_amount,txt_modal_indicator,modal_indicator_container,
calc_guideline_modal,balance_modal,mainTitle_modal,currency_symbol_modal,border_nominal_modal" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/trx_detail_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="category_et, note_et, date_edit_text"
                tools:visibility="visible" />

            <EditText
                android:id="@+id/product_et"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:clickable="true"
                android:drawableStart="@drawable/ic_product"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:hint="@string/product_sold_label"
                android:inputType="none"
                app:layout_constraintTop_toBottomOf="@id/indicator_guideline" />

            <com.bukuwarung.widget.ViewTextLabel
                android:id="@+id/product_feature_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:visibility="gone"
                app:labelColor="@color/out_red"
                app:labelText="@string/new_label"
                app:labelTextColor="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/product_et"
                app:layout_constraintEnd_toEndOf="@id/product_et"
                app:layout_constraintTop_toTopOf="@id/product_et"
                tools:visibility="visible" />


            <EditText
                android:id="@+id/category_et"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:clickable="true"
                android:drawableStart="@drawable/ic_category"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:hint="@string/select_category"
                android:inputType="none"
                app:layout_constraintTop_toBottomOf="@id/product_et" />


            <EditText
                android:id="@+id/note_et"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:drawableStart="@drawable/ic_notes"
                android:hint="@string/notes_header"
                android:inputType="text"
                app:layout_constraintTop_toBottomOf="@id/category_et" />

            <EditText
                android:id="@+id/date_edit_text"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:clickable="true"
                android:drawableStart="@drawable/ic_calendar"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:hint="@string/date"
                android:inputType="none"
                app:layout_constraintTop_toBottomOf="@id/note_et" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <View
        android:id="@+id/button_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#EEEEEE"
        app:layout_constraintBottom_toTopOf="@id/btn_save" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:text="@string/save_cash"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Custom Keyboard  -->
    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/keyboardView"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/success_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_success"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/trx_success" />

        <TextView
            android:id="@+id/tv_trx_success"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/transaksi_berhasil_dicatat"
            android:textColor="@color/green_80"
            android:textSize="@dimen/text_24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
