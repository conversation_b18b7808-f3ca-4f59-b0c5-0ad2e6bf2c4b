<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingTop="@dimen/_16dp"
    android:paddingBottom="@dimen/_66dp">

    <View
        android:id="@+id/divider"
        style="@style/Divider"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_heading"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30dp"
        android:text="@string/saldo_freeze_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />

    <TextView
        android:id="@+id/tv_sub_heading"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/saldo_freeze_subtitle"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_heading" />

    <TextView
        android:id="@+id/tv_help"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/label_help_wa_btn"
        android:textColor="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_sub_heading"
        app:drawableRightCompat="@drawable/ic_arrow_right_blue_circle_bg" />

</androidx.constraintlayout.widget.ConstraintLayout>