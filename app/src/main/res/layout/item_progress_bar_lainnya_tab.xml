<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="260dp"
    android:minHeight="110dp"
    android:layout_height="wrap_content"
    android:background="@drawable/white_background_radius_8"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/_26dp"
        android:layout_height="@dimen/_32dp"
        app:srcCompat="@drawable/ic_help"
        android:layout_margin="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_item_heading"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        tools:text="hgcytchytcxhtc"
        style="@style/SubHeading1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"/>

    <ProgressBar
        android:id="@+id/pb_bp"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="0dp"
        android:layout_height="@dimen/_16dp"
        android:max="100"
        android:progressTint="@color/buku_CTA"
        android:progressBackgroundTint="@color/black_10"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_item_heading"
        android:progress="50" />

    <TextView
        android:id="@+id/tv_item_text"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        tools:text="hgcytchytcxhtc"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/black_40"
        style="@style/Body3"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_goneMarginBottom="@dimen/_16dp"
        app:layout_constraintBottom_toTopOf="@id/label_success"
        app:layout_constraintTop_toBottomOf="@id/pb_bp"
        app:layout_constraintStart_toEndOf="@id/iv_icon"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/label_success"
        android:background="@drawable/profit_bg"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_20dp"
        android:drawablePadding="@dimen/_6dp"
        android:drawableEnd="@drawable/ic_green_completed"
        android:paddingHorizontal="@dimen/_6dp"
        android:paddingVertical="@dimen/_2dp"
        android:gravity="center_vertical"
        tools:text="hgcytchytcxhtc"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:visibility="gone"
        android:textColor="@color/black_80"
        style="@style/Label2"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_item_text"
        app:layout_constraintStart_toEndOf="@id/iv_icon"/>

</androidx.constraintlayout.widget.ConstraintLayout>