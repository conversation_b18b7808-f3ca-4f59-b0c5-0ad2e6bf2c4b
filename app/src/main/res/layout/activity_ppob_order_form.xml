<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black5">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_ppob"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:title="@string/payment_confirmation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme" />

    <include
        android:id="@+id/include_top"
        layout="@layout/layout_sticky_top"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb_ppob"
        tools:visibility="visible" />

    <include
        android:id="@+id/include_timer"
        layout="@layout/layout_ppob_timer"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include_top"
        tools:visibility="visible" />

    <ScrollView
        android:id="@+id/sv_layout"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toTopOf="@id/include_payment_method"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_timer">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/include_biller_info"
                layout="@layout/layout_biller_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <include
                android:id="@+id/include_ppob_amount"
                layout="@layout/layout_ppob_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_biller_info" />

            <include
                android:id="@+id/include_saldo_reward"
                layout="@layout/layout_reward"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/include_ppob_amount"
                tools:visibility="visible" />

            <com.bukuwarung.payments.compoundviews.BillDetailExpandableView
                android:id="@+id/bill_detail_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/include_saldo_reward" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:alertText="@string/info_msg"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                app:type="info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bill_detail_view" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <include
        android:id="@+id/include_payment_method"
        layout="@layout/layout_order_form_payment_method"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>