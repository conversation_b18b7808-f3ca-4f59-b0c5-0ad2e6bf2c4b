<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_error"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:navigationIcon="@mipmap/back_white"
        app:contentInsetLeft="@dimen/_0dp"
        app:contentInsetStart="@dimen/_0dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Heading2"
                android:gravity="center"
                android:text="Store Name"
                android:textColor="@color/white"
                android:layout_marginStart="@dimen/_0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <ImageView
        android:id="@+id/iv_no_net"
        android:layout_width="90dp"
        android:layout_height="66dp"
        app:srcCompat="@drawable/ic_no_inet"
        app:layout_constraintBottom_toTopOf="@id/tv_internet_dropped"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="@dimen/_12dp"/>

    <TextView
        android:id="@+id/tv_internet_dropped"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Heading3"
        android:text="@string/internet_connection_dropped"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_make_sure"
        android:layout_marginBottom="@dimen/_12dp"/>

    <TextView
        android:id="@+id/tv_make_sure"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:lines="2"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_60dp"
        android:text="@string/make_sure_internet_avaialable"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/bt_try_again"
        android:layout_marginBottom="@dimen/_12dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_try_again"
        style="@style/Button.OutlinePrimary"
        android:layout_width="@dimen/_150dp"
        android:layout_height="wrap_content"
        android:textAllCaps="false"
        android:gravity="center"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        android:text="@string/retry"
        android:textColor="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>