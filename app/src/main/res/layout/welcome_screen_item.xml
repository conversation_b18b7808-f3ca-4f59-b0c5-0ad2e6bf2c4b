<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_80dp"
        android:layout_marginTop="@dimen/_20dp"
        app:srcCompat="@drawable/buku_logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_welcome"
        android:layout_width="@dimen/_0dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:layout_height="300dp"
        android:src="@drawable/welcome2"
        android:scaleType="centerInside"
        app:layout_constraintTop_toBottomOf="@id/iv_logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Keuntungan Usaha Terpantau Otomatis"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_24sp"
        android:layout_marginLeft="@dimen/_12dp"
        android:layout_marginRight="@dimen/_12dp"
        android:textStyle="bold"
        android:fontFamily="@font/roboto_bold"
        android:paddingLeft="@dimen/_8dp"
        android:paddingRight="@dimen/_8dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_welcome" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginLeft="@dimen/_12dp"
        android:layout_marginRight="@dimen/_12dp"
        android:paddingLeft="@dimen/_8dp"
        android:paddingRight="@dimen/_8dp"
        android:text="Catat penjualan, pemasukan, pengeluaran, dan stok jualan tanpa ribet."
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        android:fontFamily="@font/roboto"
        android:textStyle="normal"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:layout_marginBottom="90dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />


<!--    <Button-->
<!--        android:id="@+id/btn_skip"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="60dp"-->
<!--        android:layout_marginBottom="@dimen/_16dp"-->
<!--        android:layout_marginStart="@dimen/_8dp"-->
<!--        android:layout_marginEnd="@dimen/_8dp"-->
<!--        android:fontFamily="@font/roboto"-->
<!--        android:gravity="center"-->
<!--        android:text="@string/skip"-->
<!--        style="@style/Button.Text"-->
<!--        android:textAllCaps="false"-->
<!--        android:textSize="@dimen/text_16sp"-->
<!--        android:textStyle="bold"-->
<!--        app:layout_constraintEnd_toStartOf="@id/btn_next"-->
<!--        app:cornerRadius="@dimen/_8dp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:rippleColor="@color/black_40" />-->

<!--    <androidx.appcompat.widget.AppCompatButton-->
<!--        android:id="@+id/btn_next"-->
<!--        android:layout_width="@dimen/_60dp"-->
<!--        android:layout_height="60dp"-->
<!--        android:layout_marginBottom="@dimen/_16dp"-->
<!--        android:layout_marginStart="@dimen/_8dp"-->
<!--        android:layout_marginEnd="@dimen/_8dp"-->
<!--        android:fontFamily="@font/roboto"-->
<!--        android:gravity="center"-->
<!--        android:drawableStart="@drawable/ic_forward_new"-->
<!--        android:paddingStart="@dimen/_20dp"-->
<!--        android:textAllCaps="false"-->
<!--        android:textColor="@color/black_80"-->
<!--        android:textSize="@dimen/text_16sp"-->
<!--        android:textStyle="bold"-->
<!--        app:layout_constraintEnd_toStartOf="@id/btn_login"-->
<!--        app:backgroundTint="@color/buku_CTA_New"-->
<!--        app:cornerRadius="@dimen/_8dp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:rippleColor="@color/black_40" />-->

<!--    <androidx.appcompat.widget.AppCompatButton-->
<!--        android:id="@+id/btn_login"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="60dp"-->
<!--        android:layout_marginBottom="@dimen/_16dp"-->
<!--        android:layout_marginStart="@dimen/_8dp"-->
<!--        android:layout_marginEnd="@dimen/_8dp"-->
<!--        android:fontFamily="@font/roboto"-->
<!--        android:gravity="center"-->
<!--        android:text="@string/login"-->
<!--        android:textAllCaps="false"-->
<!--        android:textColor="@color/black_80"-->
<!--        android:textSize="@dimen/text_16sp"-->
<!--        android:textStyle="bold"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:backgroundTint="@color/buku_CTA_New"-->
<!--        app:cornerRadius="@dimen/_8dp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:rippleColor="@color/black_40" />-->

</androidx.constraintlayout.widget.ConstraintLayout>