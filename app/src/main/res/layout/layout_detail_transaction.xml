<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_rectangle_white_8dp"
    android:paddingBottom="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_detail_transaction"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/payment_detail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_18dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_chevron_up" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/gp_expandable"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_transaction">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_50v"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <TextView
            android:id="@+id/tv_transaction_fee"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/transaction_fees"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_transaction_fee_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_transaction_fee"
            tools:text="Rp1.000" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_transaction_fee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_transaction_fee, tv_transaction_fee_value" />

        <View
            android:id="@+id/vw_transaction_fee"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_transaction_fee" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gr_transaction_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tv_transaction_fee, tv_transaction_fee_value, vw_transaction_fee" />

        <TextView
            android:id="@+id/tv_total_received"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/total_received"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_transaction_fee" />

        <TextView
            android:id="@+id/tv_total_received_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_total_received"
            tools:text="Rp249.000" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_total_received"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_total_received, tv_total_received_value" />

        <View
            android:id="@+id/vw_total_received"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_total_received" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/grp_total_received"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tv_total_received, tv_total_received_value, vw_total_received" />

        <TextView
            android:id="@+id/tv_bnpl_admin_fee"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:drawablePadding="@dimen/_4dp"
            android:text="@string/bnpl_transaction_fees"
            android:textColor="@color/black_40"
            app:drawableEndCompat="@drawable/ic_info_black20"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_total_received" />

        <TextView
            android:id="@+id/tv_bnpl_admin_fee_value"
            style="@style/Body2"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_4dp"
            android:gravity="end"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:text="@string/free"
            android:textColor="@color/black_80"
            app:layout_constraintBottom_toBottomOf="@+id/tv_bnpl_admin_fee"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_bnpl_admin_fee"
            app:layout_constraintTop_toTopOf="@+id/tv_bnpl_admin_fee" />

        <View
            android:id="@+id/vw_bnpl_fee"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_bnpl_admin_fee" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gr_bnpl_admin_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_bnpl_admin_fee, tv_bnpl_admin_fee_value, vw_bnpl_fee" />

        <TextView
            android:id="@+id/tv_admin_fee"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:drawablePadding="@dimen/_4dp"
            android:text="@string/transaction_fees"
            android:textColor="@color/black_40"
            app:drawableEndCompat="@drawable/ic_info_black20"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_bnpl_admin_fee" />

        <TextView
            android:id="@+id/tv_admin_fee_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_4dp"
            android:background="@drawable/strike_through"
            android:text="@string/free"
            android:textColor="@color/black_40"
            app:layout_constraintBottom_toBottomOf="@+id/tv_admin_fee"
            app:layout_constraintEnd_toStartOf="@id/tv_discounted_fee"
            app:layout_constraintTop_toTopOf="@+id/tv_admin_fee" />

        <TextView
            android:id="@+id/tv_discounted_fee"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/free"
            app:layout_constraintBottom_toBottomOf="@+id/tv_admin_fee"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_admin_fee" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_admin_fee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_admin_fee, tv_admin_fee_value, tv_discounted_fee" />

        <View
            android:id="@+id/vw_admin_fee"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_admin_fee" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gr_admin_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            app:constraint_referenced_ids="tv_admin_fee_value, tv_discounted_fee, vw_admin_fee, tv_admin_fee" />

        <TextView
            android:id="@+id/tv_loyalty_discount"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:drawablePadding="@dimen/_4dp"
            android:textColor="@color/black_40"
            app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tv_loyalty_discount_value"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_admin_fee"
            tools:text="Diskon Level Bronze" />

        <TextView
            android:id="@+id/tv_loyalty_discount_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_loyalty_discount"
            tools:text="Rp1.750" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_loyalty_discount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_loyalty_discount, tv_loyalty_discount_value" />

        <View
            android:id="@+id/vw_loyalty_discount"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_loyalty_discount" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gr_loyalty_discount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_loyalty_discount, tv_loyalty_discount_value, vw_loyalty_discount" />

        <TextView
            android:id="@+id/tv_subscription_discount"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:drawablePadding="@dimen/_4dp"
            android:text="@string/discount_bukuwarung_plus"
            android:textColor="@color/black_40"
            app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tv_subscription_discount_value"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_loyalty_discount" />

        <TextView
            android:id="@+id/tv_subscription_discount_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_subscription_discount"
            tools:text="Rp1.750" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_subscription_discount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_subscription_discount, tv_subscription_discount_value" />

        <View
            android:id="@+id/vw_subscription_discount"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_subscription_discount" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gr_subscription_discount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_subscription_discount, tv_subscription_discount_value, vw_subscription_discount" />

        <TextView
            android:id="@+id/tv_qris_fee"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:drawablePadding="@dimen/_4dp"
            android:textColor="@color/black_40"
            app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tv_qris_fee_value"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_subscription_discount"
            tools:text="Biaya QRIS (0,7% jumlah tagihan)" />

        <TextView
            android:id="@+id/tv_qris_fee_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_qris_fee"
            tools:text="Rp1.750" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_qris_fee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_qris_fee, tv_qris_fee_value" />

        <View
            android:id="@+id/vw_qris_fee"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_qris_fee" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gr_qris_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_qris_fee, tv_qris_fee_value, vw_qris_fee" />

        <TextView
            android:id="@+id/tv_saldo_reward"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/saldo_bonus_used"
            android:textColor="@color/blue_60"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_qris_fee" />

        <TextView
            android:id="@+id/tv_saldo_reward_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/blue_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_saldo_reward"
            tools:text="-Rp1.000" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_saldo_reward"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_saldo_reward, tv_saldo_reward_value" />

        <View
            android:id="@+id/vw_saldo_reward"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_saldo_reward" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gr_saldo_reward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_saldo_reward, tv_saldo_reward_value, vw_saldo_reward" />

        <TextView
            android:id="@+id/tv_nominal_received"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/nominal_you_receive"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_saldo_reward" />

        <TextView
            android:id="@+id/tv_nominal_received_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_nominal_received"
            tools:text="Rp250.000" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_nominal_received"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_total_received, tv_nominal_received_value" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/grp_nominal_received"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tv_nominal_received, tv_nominal_received_value,vw_nominal_received" />


        <View
            android:id="@+id/vw_nominal_received"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_nominal_received" />

        <TextView
            android:id="@+id/tv_payment_method"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/label_payment_method"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_nominal_received" />

        <TextView
            android:id="@+id/tv_payment_method_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_payment_method"
            tools:text="QRIS-BNI" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_payment_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_payment_method, tv_payment_method_value" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/grp_payment_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tv_payment_method,vw_payment_method, tv_payment_method_value" />


        <View
            android:id="@+id/vw_payment_method"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_payment_method" />

        <TextView
            android:id="@+id/tv_payment_category"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/category_label"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_payment_method" />

        <TextView
            android:id="@+id/tv_payment_category_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toStartOf="@+id/iv_change_payment_category"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_payment_category"
            tools:text="Pembayaran Tunai" />

        <ImageView
            android:id="@+id/iv_change_payment_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_payment_category"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_payment_category"
            app:srcCompat="@drawable/ic_chevron_right" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_payment_category"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_payment_category, tv_payment_category_value, iv_change_payment_category" />

        <View
            android:id="@+id/vw_payment_category"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_payment_category" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gp_payment_categroy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_payment_category,tv_payment_category_value,iv_change_payment_category,vw_payment_category" />

        <TextView
            android:id="@+id/tv_notes"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/notes_payment"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_payment_category" />

        <TextView
            android:id="@+id/tv_notes_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_notes"
            tools:text="-" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_notes, tv_notes_value" />

        <View
            android:id="@+id/vw_notes"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_notes" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/grp_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tv_notes, tv_notes_value, vw_notes" />

        <TextView
            android:id="@+id/tv_cashback"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/cashback"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_notes" />

        <TextView
            android:id="@+id/tv_cashback_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_cashback"
            tools:text="Diproses - Rp1.500" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_cashback"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_cashback, tv_cashback_value" />

        <View
            android:id="@+id/vw_cashback"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_cashback" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gp_cashback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_cashback,tv_cashback_value, vw_cashback"
            tools:visibility="visible" />

        <include
            android:id="@+id/layout_payment_methods_detail"
            layout="@layout/layout_payment_methods_detail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_cashback" />

        <TextView
            android:id="@+id/tv_refund_bank_label"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/refund_bank_account"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_payment_methods_detail" />

        <TextView
            android:id="@+id/tv_refund_bank_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:gravity="end"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_refund_bank_label"
            tools:text="Mandiri - Ubah \n Agus Martono" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gp_refund_bank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_refund_bank_label,tv_refund_bank_value, vw_cashback"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>