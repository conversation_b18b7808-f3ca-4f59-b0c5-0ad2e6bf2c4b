<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_create_payment_link"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/create_payment_in_link"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_send_payment_link"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/send_payment_in_link"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_create_payment_link" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_utang_contact"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_send_payment_link"
        app:srcCompat="@drawable/ic_utang_contact_icon_green" />

    <TextView
        android:id="@+id/tv_customers"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:text="@string/customers"
        app:layout_constraintBottom_toBottomOf="@+id/iv_utang_contact"
        app:layout_constraintStart_toEndOf="@+id/iv_utang_contact"
        app:layout_constraintTop_toTopOf="@+id/iv_utang_contact" />

    <TextView
        android:id="@+id/tv_mobile_number"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_utang_contact"
        app:layout_constraintEnd_toStartOf="@+id/iv_edit"
        app:layout_constraintTop_toTopOf="@+id/iv_utang_contact"
        tools:text="087775598545" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/iv_utang_contact"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_utang_contact"
        app:srcCompat="@drawable/ic_edit_with_border" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_create_payment_link"
        style="@style/ButtonFill.Blue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/create_payment_in_link_button_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_utang_contact" />
</androidx.constraintlayout.widget.ConstraintLayout>