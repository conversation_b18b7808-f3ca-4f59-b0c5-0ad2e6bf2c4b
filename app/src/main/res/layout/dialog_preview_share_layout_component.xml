<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_gravity="center"
        android:background="@color/white"
        android:layout_height="50dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/light_grey" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:fontFamily="@font/roboto"
            android:letterSpacing="0.16"
            android:lineSpacingExtra="7.7sp"
            android:text="@string/share_business_card"
            android:textAllCaps="true"
            android:textColor="@color/black"
            android:textSize="12sp" />

        <ImageView
            android:id="@+id/close"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="8dp"
            android:padding="12dp"
            android:tint="@color/colorPrimary"
            android:src="@drawable/ic_close" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/card_layout"
        android:layout_width="350dp"
        android:layout_height="200dp"
        android:layout_gravity="center_horizontal"

        android:layout_marginTop="10dp"
        android:layout_marginBottom="16dp"
        android:background="#000000"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="16dp">

        <RelativeLayout
            android:id="@+id/profileAndNameLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/profilePic"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="24dp"
                android:layout_toRightOf="@+id/profilePic"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:textColor="#ffffff"
                android:textSize="21sp"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/name"
                android:layout_centerHorizontal="true"
                android:layout_toRightOf="@+id/profilePic"
                android:ellipsize="end"
                android:letterSpacing="0.01"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:textColor="#ffffff"
                android:textSize="12sp" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="11">

                <ImageView
                    android:id="@+id/ownerIcon"
                    android:layout_width="@dimen/icon_size"
                    android:layout_height="@dimen/icon_size"
                    android:layout_alignTop="@+id/owner"
                    android:layout_marginLeft="@dimen/icon_margin"
                    android:layout_marginTop="@dimen/item_margin_top"
                    android:src="@drawable/ic_person" />

                <TextView
                    android:id="@+id/owner"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/item_margin"
                    android:layout_toRightOf="@+id/ownerIcon"
                    android:letterSpacing="0.01"
                    android:text="Username"
                    android:textColor="#ffffff"
                    android:textSize="12sp" />

                <ImageView
                    android:id="@+id/phoneIcon"
                    android:layout_width="@dimen/icon_size"
                    android:layout_height="@dimen/icon_size"
                    android:layout_alignTop="@+id/phone"
                    android:layout_marginLeft="@dimen/icon_margin"
                    android:layout_marginTop="@dimen/item_margin_top"
                    android:tint="@color/white"
                    android:src="@mipmap/customer_phone_grey" />

                <TextView
                    android:id="@+id/phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/owner"
                    android:layout_marginLeft="@dimen/item_margin"
                    android:layout_marginTop="4dp"
                    android:layout_toRightOf="@+id/phoneIcon"
                    android:letterSpacing="0.01"
                    android:text="8976208450"
                    android:textColor="#ffffff"
                    android:textSize="12sp" />

                <ImageView
                    android:id="@+id/emailIcon"
                    android:layout_width="@dimen/icon_size"
                    android:layout_height="@dimen/icon_size"
                    android:layout_alignTop="@+id/email"
                    android:layout_marginStart="@dimen/icon_margin"
                    android:layout_marginTop="@dimen/item_margin_top"
                    android:src="@drawable/ic_email" />

                <TextView
                    android:id="@+id/email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/phone"
                    android:layout_marginStart="@dimen/item_margin"
                    android:layout_marginTop="4dp"
                    android:layout_toEndOf="@+id/emailIcon"
                    android:text="<EMAIL>"
                    android:textColor="#ffffff"
                    android:textSize="12sp" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="9"
                android:paddingLeft="16dp">

                <TextView
                    android:id="@+id/address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginRight="@dimen/icon_margin"
                    android:gravity="right"
                    android:lineSpacingExtra="2sp"
                    android:text="business address"
                    android:textColor="#ffffff"
                    android:textSize="12sp" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/shareOnWhatsapp"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="350dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/roboto"
        android:letterSpacing="0.09"
        android:paddingLeft="16dp"
        android:paddingTop="8dp"
        android:paddingRight="16dp"
        android:paddingBottom="8dp"
        android:text="Share on WhatsApp"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:backgroundTint="@color/colorPrimary"
        app:icon="@drawable/ic_share"
        app:iconPadding="8dp"
        app:iconTint="@color/colorPrimary"
        app:rippleColor="@color/colorPrimary"
        app:strokeColor="@color/light_grey"
        app:strokeWidth="1dp"
        android:visibility="gone"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/moreOptions"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="350dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"
        app:iconPadding="8dp"
        app:iconTint="@color/colorPrimary"
        app:strokeColor="@color/colorPrimary"
        app:strokeWidth="1dp"
        app:icon="@drawable/ic_share"
        android:paddingLeft="16dp"
        android:paddingTop="8dp"
        android:paddingRight="16dp"
        android:paddingBottom="8dp"
        android:text="@string/share_business_card"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        android:textSize="12.2sp"
        app:rippleColor="@color/colorPrimary" />
</LinearLayout>
