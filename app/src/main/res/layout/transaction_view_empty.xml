<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:paddingLeft="16dp"
    android:paddingTop="128dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:paddingTop="6dp"
        android:paddingBottom="24dp"
        android:text=""
        android:textAllCaps="true"
        android:textColor="#2196f3"
        android:textSize="14.2sp" />

    <LinearLayout
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:id="@+id/iconLayout"
        android:layout_centerHorizontal="true"
        android:layout_alignParentTop="true"
        android:background="@drawable/oval_primary"
        android:gravity="center"
        >

        <ImageView
            android:id="@+id/icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_lock" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iconLayout"
        android:layout_centerHorizontal="true"
        android:id="@+id/empty_trans_text"
        tools:text="Empty Transactions"
        android:layout_marginTop="24dp"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginRight="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="center_horizontal"
        android:textColor="#666666"
        android:textSize="16.3sp" />
</RelativeLayout>
