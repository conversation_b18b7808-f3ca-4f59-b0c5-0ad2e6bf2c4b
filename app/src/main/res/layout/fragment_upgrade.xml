<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/yellow_10"
    android:padding="@dimen/_10dp">

    <TextView
        android:id="@+id/tv_ticker_body"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:drawablePadding="@dimen/_8dp"
        android:text="@string/unlock_all_balance_access"
        android:textColor="@color/black80"
        android:lineSpacingExtra="2sp"
        app:drawableStartCompat="@drawable/ic_secure_new"
        app:layout_constraintEnd_toStartOf="@+id/bukuButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.ui_component.component.button.BukuButton
        android:id="@+id/bukuButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAllCaps="false"
        app:buttonText="@string/verify"
        app:buttonType="yellow60WithBlack80"
        app:cornerRadius="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_ticker_body"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_ticker_body" />

</androidx.constraintlayout.widget.ConstraintLayout>