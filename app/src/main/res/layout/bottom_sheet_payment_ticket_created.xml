<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp">

    <View
        android:id="@+id/vw_divider"
        android:layout_width="@dimen/_48dp"
        android:layout_height="4dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_heading"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:gravity="center"
        android:text="@string/ticket_created_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider" />

    <ImageView
        android:id="@+id/iv_bank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tv_ticket_processing"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_ticket_processing"
        app:srcCompat="@drawable/ic_bank_building" />

    <TextView
        android:id="@+id/tv_ticket_processing"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/ticket_processing_message"
        app:layout_constraintBottom_toBottomOf="@+id/iv_bank"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_bank"
        app:layout_constraintTop_toBottomOf="@id/tv_heading" />

    <ImageView
        android:id="@+id/iv_clock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tv_refund_time"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_refund_time"
        app:srcCompat="@drawable/ic_stop_watch" />

    <TextView
        android:id="@+id/tv_refund_time"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/ticket_refund_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_clock"
        app:layout_constraintTop_toBottomOf="@id/tv_ticket_processing" />

    <ImageView
        android:id="@+id/iv_money"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tv_network_issue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_network_issue"
        app:srcCompat="@drawable/ic_hand_cash" />

    <TextView
        android:id="@+id/tv_network_issue"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/ticket_network_issues_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_money"
        app:layout_constraintTop_toBottomOf="@id/tv_refund_time" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_confirm"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_48dp"
        android:text="@string/view_ticket_help"
        app:layout_constraintTop_toBottomOf="@+id/iv_money" />
</androidx.constraintlayout.widget.ConstraintLayout>
