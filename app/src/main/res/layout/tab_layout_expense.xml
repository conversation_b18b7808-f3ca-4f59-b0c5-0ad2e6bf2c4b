<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    android:fitsSystemWindows="true">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:contentInsetStartWithNavigation="0dp"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/title"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:ellipsize="end"
                android:maxLines="1"
                android:layout_marginStart="@dimen/_5dp"
                android:paddingEnd="24dp"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/img_refresh"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Usaha Saya 29u4289u8 jenfjksnf md mf 2u3y4u23y mdsnfsnfsnfjks 348u5389u53 " />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_refresh"
                android:layout_width="25dp"
                android:layout_height="@dimen/_0dp"
                android:layout_marginRight="@dimen/_10dp"
                app:srcCompat="@drawable/ic_refresh_data"
                app:layout_constraintEnd_toStartOf="@id/barrier"
                app:layout_constraintTop_toTopOf="@id/title"
                app:layout_constraintBottom_toBottomOf="@id/title"/>

            <ImageView
                android:id="@+id/notification_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="?android:attr/selectableItemBackground"
                android:src="@drawable/ic_alert"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/notify_highlighter_exp"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:background="@drawable/oval_pink"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/notification_icon"
                app:layout_constraintTop_toTopOf="@id/notification_icon"
                tools:text="2"
                tools:visibility="gone" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="left"
                app:constraint_referenced_ids="tv_help_icon,tv_pos_icon" />

            <TextView
                android:id="@+id/tv_help_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_12dp"
                android:text="@string/help"
                android:textColor="@color/white"
                android:visibility="gone"
                app:drawableTopCompat="@drawable/ic_help_new"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone" />

            <TextView
                android:id="@+id/tv_pos_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_12dp"
                android:text="@string/cashier_mode"
                android:textColor="@color/white"
                android:visibility="gone"
                app:drawableTopCompat="@drawable/ic_cashier"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:id="@+id/tvOffline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/toolbar"
        android:background="@color/yellow5"
        android:drawablePadding="@dimen/_8dp"
        android:elevation="@dimen/_12dp"
        android:fontFamily="@font/roboto"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="12dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="12dp"
        android:text="@string/recordings_are_saved_in_offline_mode"
        android:textColor="@color/black_80"
        android:textSize="14sp"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_offline_msg"
        tools:visibility="visible" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tvOffline"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@id/app_bar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:expanded="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_view"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                app:contentScrim="@color/white"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/layout_transaction_filter" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/summaryView"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginRight="@dimen/_16dp"
                        android:layout_marginBottom="12dp"
                        android:background="@color/white"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="6dp">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <View
                                android:id="@+id/v_line"
                                android:layout_width="1dp"
                                android:layout_height="0dp"
                                android:layout_marginTop="@dimen/_8dp"
                                android:background="@color/black_5"
                                app:layout_constraintBottom_toBottomOf="@id/creditTotal"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_income_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:fontFamily="@font/roboto"
                                android:text="@string/sales"
                                android:textColor="@color/black_60"
                                android:textSize="12sp"
                                app:layout_constraintEnd_toStartOf="@id/v_line"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_expense_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:fontFamily="@font/roboto"
                                android:text="@string/expense_label"
                                android:textColor="@color/black_60"
                                android:textSize="12sp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/v_line"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/creditTotal"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:fontFamily="@font/roboto"
                                android:paddingBottom="2dp"
                                android:textColor="@color/in_green"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toStartOf="@id/v_line"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/tv_income_label"
                                tools:text="Rp123.456" />

                            <TextView
                                android:id="@+id/debitTotal"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:fontFamily="@font/roboto"
                                android:textColor="@color/out_red"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/v_line"
                                app:layout_constraintTop_toBottomOf="@id/tv_income_label"
                                tools:text="Rp123.456" />

                            <View
                                android:id="@+id/bg_margin"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/_0dp"
                                android:background="@color/profit_green_bg"
                                app:layout_constraintBottom_toBottomOf="@id/under_paid_customers"
                                app:layout_constraintTop_toTopOf="@id/tv_margin_type" />


                            <TextView
                                android:id="@+id/tv_margin_type"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_16dp"
                                android:layout_marginTop="6dp"
                                android:fontFamily="@font/roboto"
                                android:paddingTop="12dp"
                                android:paddingBottom="12dp"
                                android:text="@string/profit_text"
                                android:textColor="@color/in_green"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/creditTotal" />

                            <TextView
                                android:id="@+id/under_paid_customers"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/rectangle_top_right_corner"
                                android:fontFamily="@font/roboto"
                                android:paddingStart="@dimen/_16dp"
                                android:paddingTop="2dp"
                                android:paddingEnd="@dimen/_8dp"
                                android:paddingBottom="2dp"
                                android:text="@string/total_customer_unpaid_amount"
                                android:textColor="#E50707"
                                android:textSize="10sp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/tv_margin_type" />

                            <TextView
                                android:id="@+id/tv_margin_nominal"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/_16dp"
                                android:fontFamily="@font/roboto"
                                android:paddingTop="12dp"
                                android:paddingBottom="12dp"
                                android:textColor="@color/in_green"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@id/tv_margin_type"
                                tools:text="Rp123.456" />

                            <TextView
                                android:id="@+id/summary_btn"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="?attr/selectableItemBackground"
                                android:drawablePadding="@dimen/_8dp"
                                android:fontFamily="@font/roboto"
                                android:gravity="center_vertical"
                                android:paddingStart="@dimen/_16dp"
                                android:paddingTop="12dp"
                                android:paddingEnd="16dp"
                                android:paddingBottom="12dp"
                                android:text="@string/cash_transaction_report"
                                android:textColor="@color/black_80"
                                android:textSize="14sp"
                                app:drawableEndCompat="@drawable/ic_chevron_right_black_60"
                                app:drawableStartCompat="@drawable/new_report_icon"
                                app:layout_constraintTop_toBottomOf="@id/under_paid_customers" />

                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <include layout="@layout/cash_transaction_search_layout" />

            <RelativeLayout
                android:id="@+id/searchFilterLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingTop="4dp"
                android:paddingBottom="4dp">

                <include layout="@layout/layout_transaction_grouping" />

                <TextView
                    android:id="@+id/searchContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_toStartOf="@id/sortMenu"
                    android:paddingStart="4dp"
                    android:paddingEnd="2dp"
                    android:text="@string/search_label"
                    android:textAlignment="center"
                    android:textColor="@color/black_80"
                    android:textSize="10sp"
                    app:drawableTopCompat="@drawable/ic_icon_search_new"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/sortMenu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_toStartOf="@id/filterMenu"
                    android:text="@string/sort"
                    android:textAlignment="center"
                    android:textColor="@color/black_80"
                    android:textSize="10sp"
                    app:drawableTopCompat="@drawable/ic_icon_sort_new"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/filterMenu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/tampilan_label"
                    android:textColor="@color/black_80"
                    android:textSize="10sp"
                    app:drawableTopCompat="@drawable/ic_grouping"
                    tools:ignore="SmallSp" />


            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/vrDivider" />

            <!-- Header for Tanggal Pemasukkan Pengeluaran -->
            <LinearLayout
                android:id="@+id/header_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eff4ff"
                android:paddingTop="4dp"
                android:paddingBottom="4dp">

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingStart="16dp"
                    tools:ignore="RtlSymmetry">

                    <TextView
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:fontFamily="@font/roboto"
                        android:lineSpacingExtra="6sp"
                        android:maxLines="1"
                        android:paddingEnd="8dp"
                        android:text="@string/date"
                        android:textColor="#5C5C5C"
                        android:textSize="12sp"
                        tools:ignore="RtlSymmetry" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/incomeAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto"
                    android:gravity="end"
                    android:lineSpacingExtra="6sp"
                    android:maxLines="1"
                    android:paddingEnd="18dp"
                    android:text="@string/sales"
                    android:textColor="#5C5C5C"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/expenseAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto"
                    android:gravity="end"
                    android:lineSpacingExtra="6sp"
                    android:maxLines="1"
                    android:paddingStart="4dp"
                    android:paddingEnd="18dp"
                    android:text="@string/expense_label"
                    android:textColor="#5C5C5C"
                    android:textSize="12sp" />
            </LinearLayout>

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/ptrExpenseCategory"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:backgroundTint="@color/colorPrimary">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/transactionRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/arrowArea"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="76dp"
        android:layout_toStartOf="@id/addCashTransactionBtn"
        android:visibility="gone"
        >

        <ImageView
            android:id="@+id/tutorarrow"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginEnd="20dp"
            android:src="@drawable/ic_pointer"
            app:tint="#DC770E" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/type_rg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="70dp"
        android:orientation="horizontal"
        android:visibility="gone"
        >


        <TextView
            android:id="@+id/text_expense"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:background="@drawable/type_exp_selected_bg"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="@string/expense_label"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_income"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/type_inc_selected_bg"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="@string/sales"
            android:textColor="@color/white"
            android:textStyle="bold" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/arrowAreaForNoTabMerge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="30dp"
        android:layout_toStartOf="@id/addCashTransactionNoTabMergeBtn"
        android:visibility="gone"
        >

        <ImageView
            android:id="@+id/tutorarrowForNoTabMerge"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginEnd="20dp"
            android:src="@drawable/ic_pointer"
            app:tint="#DC770E" />
    </LinearLayout>
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/addCashTransactionNoTabMergeBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/_8dp"
        android:layout_marginBottom="24dp"
        android:backgroundTint="@color/buku_CTA"
        android:elevation="8dp"
        android:fontFamily="@font/roboto"
        android:text="@string/add_notes"
        android:textStyle="bold"
        android:visibility="gone"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@mipmap/ic_plus_white_24dp"
        app:iconGravity="start"
        />
</RelativeLayout>