<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_10dp"
    android:paddingEnd="@dimen/_12dp">

    <TextView
        android:id="@+id/trx_no_title_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/trx_no"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/trx_no_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_12dp"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toStartOf="@id/copy_img"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/trx_no" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/copy_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        app:srcCompat="@drawable/ic_copy"
        app:layout_constraintBottom_toBottomOf="@id/trx_no_txt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/trx_no_txt" />

    <View
        android:id="@+id/account_detail_divider1"
        style="@style/Divider.Horizontal"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/trx_no_txt" />

    <TextView
        android:id="@+id/bank_title_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/label_bank"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_detail_divider1" />

    <TextView
        android:id="@+id/bank_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_detail_divider1"
        tools:text="@string/label_bank" />

    <View
        android:id="@+id/account_detail_divider2"
        style="@style/Divider.Horizontal"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bank_title_txt" />

    <TextView
        android:id="@+id/account_title_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/bank_account_no"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_detail_divider2" />

    <TextView
        android:id="@+id/account_number_txt"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_detail_divider2"
        tools:text="@string/bank_account_no" />

    <TextView
        android:id="@+id/account_name_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_number_txt"
        tools:text="@string/bank_account_no" />

    <View
        android:id="@+id/account_detail_divider3"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@color/black_10"
        app:layout_constraintTop_toBottomOf="@+id/account_name_txt" />

    <TextView
        android:id="@+id/va_title_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/label_virtual_account"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_detail_divider3" />

    <TextView
        android:id="@+id/va_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toStartOf="@id/copy_va_img"
        app:layout_constraintTop_toBottomOf="@id/account_detail_divider3"
        tools:text="@string/trx_no" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/copy_va_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        app:srcCompat="@drawable/ic_copy"
        app:layout_constraintBottom_toBottomOf="@id/va_txt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/va_txt" />

    <View
        android:id="@+id/va_divider4"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@color/black_10"
        app:layout_constraintTop_toBottomOf="@+id/va_txt" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/va_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="va_title_txt,va_txt,copy_va_img,va_divider4" />

</androidx.constraintlayout.widget.ConstraintLayout>