<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    tools:context=".activities.pos.PosActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="28dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:src="@drawable/ic_close" />

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="35dp"
                android:layout_marginRight="35dp"
                android:layout_toEndOf="@id/closeBtn"
                android:alpha="1"
                android:fontFamily="@font/roboto"
                android:text="@string/cashier_mode"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <com.bukuwarung.activities.productcategory.view.ProductCategoryFilterView
        android:id="@+id/filter_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/toolbar"/>

    <View
        android:id="@+id/gray_layer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_8dp"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@id/filter_view" />

    <TextView
        android:id="@+id/tv_search_product_name"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginRight="@dimen/_16dp"
        app:layout_constraintEnd_toStartOf="@+id/btn_add_product"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gray_layer" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_add_product"
        style="@style/ButtonFill.Blue"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_0dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:insetTop="@dimen/_0dp"
        android:insetBottom="@dimen/_0dp"
        android:text="@string/add_product"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gray_layer" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_search_product_name, btn_add_product" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/btn_add_product_grp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_search_product_name, btn_add_product, btn_add_product_divider"
        tools:visibility="visible" />

    <View
        android:id="@+id/btn_add_product_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_10"
        app:layout_constraintTop_toBottomOf="@id/barrier" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/stock_unit_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/nextLinearLayout"
        app:layout_constraintTop_toBottomOf="@id/btn_add_product_divider"
        tools:listitem="@layout/pos_product_item" />

    <LinearLayout
        android:id="@+id/nextLinearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <View
            android:id="@+id/button_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:background="#EEEEEE"
            app:layout_constraintTop_toTopOf="@id/nextLinearLayout" />

        <RelativeLayout
            android:id="@+id/price_summary_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:background="@drawable/pos_rounded_light_blue_rect"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_product_summary_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_12dp"
                android:paddingBottom="@dimen/_12dp"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                tools:text="1 Barang" />

            <TextView
                android:id="@+id/tv_product_price_summary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@id/iv_right_arrow"
                android:paddingTop="@dimen/_12dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_12dp"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="Rp50.000" />

            <ImageView
                android:id="@+id/iv_right_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                app:tint="@color/blue_60"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_centerVertical="true"
                app:srcCompat="@drawable/ic_right_arrow" />
        </RelativeLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:enabled="false"
            android:text="@string/next"
            android:textSize="@dimen/text_16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone">

        <include layout="@layout/empty_pos_store_front_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/_16dp"
            android:id="@+id/empty_screen_layout" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>