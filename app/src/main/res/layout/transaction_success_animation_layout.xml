<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

<merge>

    <androidx.constraintlayout.widget.ConstraintLayout

        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/success_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="visible">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_success"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/trx_success" />

        <TextView
            android:id="@+id/tv_trx_success"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/transaksi_berhasil_dicatat"
            android:textColor="@color/green_80"
            android:textSize="@dimen/text_24sp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>

    </layout>