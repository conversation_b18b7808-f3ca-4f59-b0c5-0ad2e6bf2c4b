<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Heading2"
        android:text="@string/download_pdf"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <ImageView
        android:id="@+id/closeBtn"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:src="@mipmap/close_grey"
        app:tint="@color/black_40"
        android:layout_marginEnd="21dp"
        android:layout_marginTop="29dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>



    <View
        android:id="@+id/dateRangeBottomBorder"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="24dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="@+id/title"/>

    <TextView
        style="@style/Body2"
        android:id="@+id/formatMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/format_types"
        android:textColor="@color/black_60"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="@+id/reportBtnLayout"
        app:layout_constraintBottom_toBottomOf="@+id/reportBtnLayout"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="@+id/title"
        />



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/reportBtnLayout"
        android:background="@drawable/grey_drawable_round_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@+id/dateRangeBottomBorder"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/pdfBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/pdf"
            android:textSize="18sp"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:fontFamily="@font/roboto"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:background="@drawable/blue_drawable_round_20dp"
            android:drawableStart="@drawable/ic_pdf_report"
            android:drawablePadding="@dimen/_8dp"
            />
        <TextView
            android:id="@+id/excelBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/excel"
            android:textSize="18sp"
            android:fontFamily="@font/roboto"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:background="@drawable/grey_drawable_round_20dp"
            android:drawableStart="@drawable/ic_excel_report"
            android:drawablePadding="@dimen/_8dp"
            app:layout_constraintStart_toEndOf="@+id/pdfBtn"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/reportBottomBorder"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@+id/reportBtnLayout"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnShare"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:backgroundTint="@color/white"
        android:fontFamily="@font/roboto"
        android:padding="12dp"
        android:text="@string/share"
        android:textAllCaps="false"
        android:textColor="@color/black_60"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="visible"
        app:cornerRadius="4dp"
        app:icon="@drawable/ic_share"
        app:iconGravity="textStart"
        app:iconTint="@color/black_60"
        app:iconTintMode="src_in"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@id/btnReportDownload"
        app:layout_constraintEnd_toStartOf="@id/btnReportDownload"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btnReportDownload"
        app:rippleColor="@color/black_40"
        app:strokeColor="@color/black_60"
        app:strokeWidth="1dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnReportDownload"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:padding="12dp"
        android:text="@string/download_str"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="16sp"
        android:textStyle="bold"
        app:backgroundTint="@color/new_yellow"
        app:cornerRadius="4dp"
        app:icon="@drawable/ic_download"
        app:iconGravity="textStart"
        app:iconTint="@color/black_80"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnShare"
        app:layout_constraintTop_toTopOf="@id/reportBottomBorder" />


</androidx.constraintlayout.widget.ConstraintLayout>