<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/vw_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_name"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/input_customer_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider" />

    <TextView
        android:id="@+id/tv_name_value"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/_4dp"
        app:layout_constraintStart_toEndOf="@+id/tv_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_name"
        tool:text="Agus Santoso" />

    <TextView
        android:id="@+id/tv_area"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/pdam_area"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_name" />

    <TextView
        android:id="@+id/tv_number"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/customer_number"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_area" />

    <TextView
        android:id="@+id/tv_number_value"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        android:textColor="@color/blue_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_number"
        tool:text="01102533773" />

    <TextView
        android:id="@+id/tv_periode"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/period"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_number" />

    <TextView
        android:id="@+id/tv_periode_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_periode"
        tool:text="2 Bulan" />


    <TextView
        android:id="@+id/tv_fine"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/fine"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_periode" />

    <TextView
        android:id="@+id/tv_fine_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_fine"
        tool:text="Rp20.000" />

    <TextView
        android:id="@+id/tv_area_name"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:layout_marginStart="@dimen/_4dp"
        app:layout_constraintStart_toEndOf="@id/tv_area"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_area"
        tool:text="Kota Bandung (PDAM TIRTAWENING)" />

    <TextView
        android:id="@+id/tv_favourite"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/favourite_contact"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_fine" />

    <TextView
        android:id="@+id/tv_favourite_value"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        app:drawableEndCompat="@drawable/ic_favourite_fill"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_favourite"
        app:layout_constraintTop_toTopOf="@+id/tv_favourite"
        tool:text="Dea Clarissa" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tool:visibility="visible"
        app:constraint_referenced_ids="tv_favourite, tv_favourite_value" />

    <View
        android:id="@+id/vw_divider_1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:layout_marginTop="@dimen/_20dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_favourite" />

    <TextView
        android:id="@+id/tv_total_tagihan"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginBottom="@dimen/_18dp"
        android:text="@string/total_bill"
        android:textColor="@color/black_80"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider_1" />

    <TextView
        android:id="@+id/tv_total_tagihan_value"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18dp"
        android:textColor="@color/red_80"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider_1"
        tool:text="Rp300.00" />

</androidx.constraintlayout.widget.ConstraintLayout>
