<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/Toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/TootleTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_0dp"
                android:paddingEnd="@dimen/_24dp"
                android:text="@string/create_payment"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/barrier"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="left"
                app:constraint_referenced_ids="tv_help_icon" />

            <TextView
                android:id="@+id/tv_help_icon"
                style="@style/Label2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_12dp"
                android:text="@string/help"
                android:textColor="@color/white"
                app:drawableTopCompat="@drawable/ic_baseline_help_outline_white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/black_5"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/include_payment_method"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/gray_barrier"
                app:layout_constraintTop_toTopOf="parent" />

            <RadioGroup
                android:id="@+id/rg_trx_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checkedButton="@id/rb_selling"
                android:orientation="horizontal"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_in"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:layout_weight="1"
                    android:background="@drawable/pay_type_green_bg_selector_light"
                    android:buttonTint="@drawable/trx_type_text_color_selector"
                    android:minHeight="@dimen/_40dp"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_10dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:text="@string/payment_label_payment_in"
                    android:textColor="@drawable/trx_type_text_color_selector" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_out"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_weight="1"
                    android:background="@drawable/trx_type_red_bg_selector_light"
                    android:buttonTint="@drawable/trx_type_text_color_selector"
                    android:minHeight="@dimen/_40dp"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_10dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:text="@string/payment_label_payment_out"
                    android:textColor="@drawable/trx_type_text_color_selector" />
            </RadioGroup>

            <View
                android:id="@+id/vw_separator_1"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/black_20"
                app:layout_constraintTop_toBottomOf="@id/rg_trx_type" />

            <include
                android:id="@+id/include_payment_categories"
                layout="@layout/payment_categories_layout"
                app:layout_constraintTop_toBottomOf="@id/vw_separator_1" />

            <TextView
                android:id="@+id/tv_category_error_message"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/payment_category_error_message"
                android:textColor="@color/red_80"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@+id/include_payment_categories"
                app:layout_constraintTop_toBottomOf="@+id/include_payment_categories" />

            <View
                android:id="@+id/vw_separator_2"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="@color/black_5"
                app:layout_constraintTop_toBottomOf="@id/tv_category_error_message" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/gr_categories_input"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="vw_separator_1, include_payment_categories, vw_separator_2" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_type_img"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:layout_marginBottom="@dimen/_16dp"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_separator_2"
                app:srcCompat="@drawable/ic_utang_credit_icon" />

            <TextView
                android:id="@+id/tv_amount_title_txt"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:text="@string/enter_amount_in_message"
                app:layout_constraintBottom_toBottomOf="@id/iv_type_img"
                app:layout_constraintStart_toEndOf="@id/iv_type_img"
                app:layout_constraintTop_toTopOf="@id/iv_type_img" />

            <com.bukuwarung.baseui.CurrencyEditText
                android:id="@+id/et_input_nominal"
                style="@style/Heading1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@color/white"
                android:gravity="end"
                android:hint="@string/zero_amount"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:paddingTop="@dimen/_6dp"
                android:paddingBottom="@dimen/_6dp"
                android:singleLine="true"
                android:text="@string/currency"
                app:layout_constraintBottom_toBottomOf="@id/tv_amount_title_txt"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_amount_title_txt"
                app:layout_constraintTop_toTopOf="@id/tv_amount_title_txt" />

            <TextView
                android:id="@+id/tv_nominal_error_txt"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/error_minimum_payment_limit"
                android:textColor="@color/red_80"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/et_input_nominal" />

            <View
                android:id="@+id/vw_divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_4dp"
                android:background="@color/black_10"
                app:layout_constraintTop_toBottomOf="@id/tv_nominal_error_txt" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/contact_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="52dp"
                app:layout_constraintTop_toBottomOf="@id/vw_divider">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_contact_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_utang_contact_icon_green" />

                <TextView
                    android:id="@+id/tv_modal_main_title1"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_8dp"
                    android:text="@string/collect_money_from"
                    app:layout_constraintBottom_toBottomOf="@id/iv_contact_img"
                    app:layout_constraintStart_toEndOf="@id/iv_contact_img"
                    app:layout_constraintTop_toTopOf="@id/iv_contact_img" />

                <TextView
                    android:id="@+id/tv_name_hint_txt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_16dp"
                    android:gravity="end"
                    android:text="@string/type_customer_name"
                    android:textColor="@color/black_20"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="@id/iv_contact_img"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_modal_main_title1"
                    app:layout_constraintTop_toTopOf="@id/iv_contact_img" />

                <TextView
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:id="@+id/tv_name_txt"
                    style="@style/Heading2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:gravity="end"
                    android:maxLength="18"
                    app:layout_constraintBottom_toBottomOf="@id/iv_contact_img"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_modal_main_title1"
                    app:layout_constraintTop_toTopOf="@id/iv_contact_img" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_layout_input_note"
                android:visibility="gone"
                tools:visibility="visible"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="0.5dp"
                app:hintEnabled="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contact_layout"
                app:startIconDrawable="@drawable/ic_edit_square"
                app:startIconTint="@color/black_40">

                <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                    android:id="@+id/et_input_note"
                    style="@style/OutlineTextInputStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:completionThreshold="1"
                    android:fontFamily="@font/roboto"
                    android:hint="@string/write_notes_optional"
                    android:imeOptions="actionDone"
                    android:inputType="textAutoComplete|textAutoCorrect"
                    android:lines="2"
                    android:minHeight="?attr/actionBarSize"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_0dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:paddingBottom="@dimen/_0dp"
                    android:singleLine="true"
                    android:textColor="@color/black_80"
                    android:textColorHint="@color/hint_color"
                    android:textSize="14sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:visibility="gone"
                android:id="@+id/bg_blue_shimmer"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/solitude_blue"
                app:layout_constraintBottom_toBottomOf="@id/shimmer_layout"
                app:layout_constraintTop_toTopOf="@id/shimmer_layout" />

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/solitude_blue"
                app:layout_constraintTop_toBottomOf="@id/til_layout_input_note">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <View
                        android:id="@+id/shimmer_header"
                        android:layout_width="153dp"
                        android:layout_height="9dp"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_20dp"
                        android:background="@drawable/shimmer_gradient_blue"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:layout_width="33dp"
                        android:layout_height="9dp"
                        android:layout_marginTop="@dimen/_20dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:background="@drawable/shimmer_gradient_blue"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/shimmer_img"
                        android:layout_width="56dp"
                        android:layout_height="44dp"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_20dp"
                        android:layout_marginBottom="@dimen/_12dp"
                        android:background="@drawable/shimmer_gradient_blue"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/shimmer_header" />

                    <View
                        android:id="@+id/shimmer_txt1"
                        android:layout_width="0dp"
                        android:layout_height="9dp"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_10dp"
                        android:layout_marginEnd="@dimen/_50dp"
                        android:background="@drawable/shimmer_gradient_blue"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/shimmer_img"
                        app:layout_constraintTop_toTopOf="@id/shimmer_img" />

                    <View
                        android:id="@+id/shimmer_txt2"
                        android:layout_width="98dp"
                        android:layout_height="9dp"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="@drawable/shimmer_gradient_blue"
                        app:layout_constraintBottom_toBottomOf="@id/shimmer_img"
                        app:layout_constraintStart_toEndOf="@id/shimmer_img"
                        app:layout_constraintTop_toBottomOf="@id/shimmer_txt1" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.facebook.shimmer.ShimmerFrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/add_bank_layout"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_10dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@id/til_layout_input_note">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="@drawable/bg_edittext_default"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_add_bank_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginBottom="@dimen/_10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_add_card" />

                <TextView
                    android:id="@+id/tv_add_bank_title_txt"
                    style="@style/SubHeading2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/add_bank_account"
                    app:layout_constraintEnd_toStartOf="@id/iv_add_bank_plus_img"
                    app:layout_constraintStart_toEndOf="@id/iv_add_bank_img"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_add_bank_subtitle_txt"
                    style="@style/Label1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:layout_marginBottom="@dimen/_10dp"
                    android:text="@string/add_bank_account_subtitle_in"
                    android:textColor="@color/black_40"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/iv_add_bank_plus_img"
                    app:layout_constraintStart_toEndOf="@id/iv_add_bank_img"
                    app:layout_constraintTop_toBottomOf="@id/tv_add_bank_title_txt" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_add_bank_plus_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_plus_blue" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bank_account_layout"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/solitude_blue"
                android:paddingBottom="@dimen/_12dp"
                app:layout_constraintTop_toBottomOf="@id/add_bank_layout">

                <View
                    android:id="@+id/white_space_view"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:background="@color/white"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_safe_bank_account_img"
                    android:layout_width="@dimen/_20dp"
                    android:layout_height="@dimen/_20dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:src="@drawable/ic_safe"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/white_space_view" />

                <TextView
                    android:id="@+id/tv_label_account_txt"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:text="@string/label_customer_account"
                    app:layout_constraintBottom_toBottomOf="@id/iv_safe_bank_account_img"
                    app:layout_constraintStart_toEndOf="@id/iv_safe_bank_account_img"
                    app:layout_constraintTop_toTopOf="@id/iv_safe_bank_account_img" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/layout_bank_image"
                    android:layout_width="56dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:padding="@dimen/_2dp"
                    app:cardBackgroundColor="@color/white"
                    app:layout_constraintStart_toStartOf="@id/iv_safe_bank_account_img"
                    app:layout_constraintTop_toBottomOf="@id/iv_safe_bank_account_img">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/iv_image_bank"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:srcCompat="@drawable/ic_bank" />

                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/tv_txt_bank_title"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    app:layout_constraintBottom_toTopOf="@+id/tv_txt_account_number"
                    app:layout_constraintEnd_toStartOf="@id/tv_button_change_account"
                    app:layout_constraintStart_toEndOf="@id/layout_bank_image"
                    app:layout_constraintTop_toTopOf="@id/layout_bank_image"
                    tools:text="Bank Name" />

                <TextView
                    android:id="@+id/tv_txt_account_number"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_2dp"
                    android:textColor="@color/black_40"
                    app:layout_constraintBottom_toBottomOf="@id/layout_bank_image"
                    app:layout_constraintStart_toEndOf="@id/layout_bank_image"
                    app:layout_constraintTop_toBottomOf="@id/tv_txt_bank_title"
                    tools:text="Bank Account" />

                <TextView
                    android:id="@+id/tv_button_change_account"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:text="@string/label_change"
                    android:textColor="@color/blue_60"
                    app:layout_constraintBottom_toBottomOf="@id/iv_safe_bank_account_img"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/iv_safe_bank_account_img" />

                <TextView
                    android:id="@+id/tv_info"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@drawable/bg_yellow_outline"
                    android:paddingHorizontal="@dimen/_16dp"
                    android:paddingVertical="@dimen/_4dp"
                    android:text="@string/blocked_info_text"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/iv_safe_bank_account_img"
                    app:layout_constraintTop_toBottomOf="@id/layout_bank_image"
                    tools:visibility="visible" />

                <com.bukuwarung.ui_component.component.alert.BukuAlert
                    android:id="@+id/ba_info"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/iv_safe_bank_account_img"
                    app:layout_constraintTop_toBottomOf="@id/layout_bank_image" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/bank_barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="add_bank_layout,bank_account_layout,shimmer_layout" />

            <View
                android:id="@+id/bg_blue"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/solitude_blue"
                app:layout_constraintBottom_toBottomOf="@id/safe_txt"
                app:layout_constraintTop_toTopOf="@id/safe_txt" />

            <TextView
                android:id="@+id/safe_txt"
                style="@style/SubHeading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_10dp"
                android:drawablePadding="@dimen/_10dp"
                android:paddingTop="@dimen/_18dp"
                android:paddingBottom="@dimen/_8dp"
                android:text="@string/guaranteed_safe"
                android:textColor="@color/blue_60"
                app:drawableStartCompat="@drawable/ic_tick_safe_blue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bank_barrier" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/safe_and_free_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="bg_blue, safe_txt" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/gray_barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="safe_txt, til_layout_input_note" />

            <TextView
                android:id="@+id/tv_transaction_detail_message"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/payment_detail"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/safe_txt" />

            <TextView
                android:id="@+id/tv_nominal_receive_message_txt"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:text="@string/total_received"
                android:textColor="@color/black_60"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_transaction_detail_message" />

            <TextView
                android:id="@+id/tv_amount_received_txt"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/black_80"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_nominal_receive_message_txt"
                tools:text="Rp250.000" />

            <TextView
                android:id="@+id/tv_admin_fee_title_txt"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:drawablePadding="@dimen/_6dp"
                android:text="@string/platform_fee"
                android:textColor="@color/black_60"
                app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_nominal_receive_message_txt" />

            <TextView
                android:id="@+id/tv_admin_fee_txt"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_4dp"
                android:background="@drawable/strike_through"
                android:text="@string/free"
                android:visibility="gone"
                android:textColor="@color/black_40"
                app:layout_constraintEnd_toStartOf="@+id/tv_discounted_fee"
                app:layout_constraintTop_toTopOf="@+id/tv_admin_fee_title_txt" />

            <TextView
                android:id="@+id/tv_discounted_fee"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/free"
                android:visibility="gone"
                android:textColor="@color/blue60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_admin_fee_title_txt" />

            <TextView
                android:id="@+id/tv_remaining_free_count"
                style="@style/Label2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_6dp"
                android:background="@drawable/type_selected_bg"
                android:fontFamily="@font/roboto_bold"
                android:paddingHorizontal="@dimen/_4dp"
                android:paddingVertical="@dimen/_2dp"
                android:textColor="@color/white"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/tv_discounted_fee"
                app:layout_constraintEnd_toStartOf="@+id/tv_discounted_fee"
                app:layout_constraintTop_toTopOf="@+id/tv_discounted_fee"
                tools:text="Sisa 2x lagi" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/br_fee"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="tv_admin_fee_txt, tv_discounted_fee" />

            <TextView
                android:id="@+id/tv_loyalty_tier"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:textColor="@color/black_60"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/br_fee" />

            <TextView
                android:id="@+id/tv_loyalty_discount"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/blue60"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_loyalty_tier" />

            <TextView
                android:id="@+id/tv_total_all_txt"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:text="@string/nominal_you_receive"
                android:textColor="@color/black_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_loyalty_tier" />

            <TextView
                android:id="@+id/tv_amount_total_all_txt"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/black_80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_total_all_txt"
                tools:text="Rp250.000" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/detail_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tv_transaction_detail_message,tv_admin_fee_title_txt,tv_total_all_txt,tv_amount_total_all_txt, tv_admin_fee_txt, tv_discounted_fee" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/info_record_payment_in_trx"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/rounded_blue_rect_4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_total_all_txt">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_ic_info"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="@dimen/_12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_money_save_info" />

                <TextView
                    android:id="@+id/tv_info_record_payment_in_trx"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_12dp"
                    android:text="@string/trx_will_be_recorded_after_success"
                    android:textColor="@color/blue_60"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_ic_info"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/warning_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_8dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_8dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/info_record_payment_in_trx">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_warning_img"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:layout_constraintBottom_toBottomOf="@id/tv_warning_txt"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tv_warning_txt" />

                <TextView
                    style="@style/Label1"
                    android:id="@+id/tv_warning_txt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/iv_warning_img"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_kyc_upgrade_info"
                style="@style/Body3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:background="@drawable/bg_solid_yellow_warning_corner_4dp_stroke_yellow80"
                android:drawablePadding="@dimen/_10dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/_10dp"
                android:paddingVertical="@dimen/_6dp"
                android:textColor="@color/black_60"
                android:visibility="gone"
                app:drawableStartCompat="@drawable/ic_info_yellow"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/warning_layout"
                tools:text="Kamu harus Verifikasi Akun untuk menggunakan fitur Saldo BukuWarung. Pelajari lebih lanjut"
                tools:visibility="visible" />
            
            <com.bukuwarung.widget.PoweredByFooterView
                android:id="@+id/powered_by_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_kyc_upgrade_info"
                app:layout_constraintVertical_bias="1" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <include
        android:id="@+id/include_payment_method"
        layout="@layout/layout_order_form_payment_method"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_share"
        style="@style/Button.Outline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/label_share_invoice_link"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:strokeColor="@color/colorAccent" />

    <FrameLayout
        android:id="@+id/contact_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="gone" />

    <include
        android:id="@+id/loading_overlay"
        layout="@layout/loading_overlay"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
