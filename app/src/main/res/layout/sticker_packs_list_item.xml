<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/sticker_store_row_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:background="@color/white"
    android:foreground="?android:attr/selectableItemBackground"
    android:orientation="vertical"
    android:paddingBottom="10dp">

    <ImageView
        android:id="@+id/add_button_on_list"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="8dp"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        android:background="?attr/selectableItemBackground"
        android:contentDescription="@string/add_button_content_description"
        android:scaleType="center"
        android:src="@drawable/ic_check_mark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/sticker_pack_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:background="#eff4ff"
        android:paddingLeft="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/sticker_pack_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="@dimen/_16dp"
            android:ellipsize="end"
            android:layout_toLeftOf="@id/action_button"
            android:layout_marginRight="@dimen/_16dp"
            android:layout_alignParentLeft="true"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            tools:text="Add 5 more tranasctions to unlock" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/action_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="@dimen/_16dp"
            style="@style/Heading3"
            android:gravity="center"
            android:maxWidth="160dp"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:maxLines="1"
            android:text="download"
            android:textColor="@color/white"
            app:cornerRadius="4dp"
            android:textSize="12sp"
            app:autoSizeMaxTextSize="14sp"
            android:backgroundTint="@color/buku_CTA_New"
            app:autoSizeTextType="uniform"
            app:backgroundTint="@color/white" />

        <TextView
            android:id="@+id/sticker_pack_publisher"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:ellipsize="end"
            android:maxWidth="120dp"
            android:visibility="gone"
            tools:text="petellison" />

        <TextView
            android:id="@+id/sticker_pack_list_item_dot"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/sticker_pack_list_item_row_dot_horizontal_padding"
            android:paddingLeft="@dimen/sticker_pack_list_item_row_dot_horizontal_padding"
            android:paddingEnd="@dimen/sticker_pack_list_item_row_dot_horizontal_padding"
            android:visibility="gone"
            android:paddingRight="@dimen/sticker_pack_list_item_row_dot_horizontal_padding"
            android:text="" />


        <TextView
            android:id="@+id/sticker_pack_filesize"
            style="@style/sticker_packs_list_item_author_style"
            android:layout_width="wrap_content"
            android:visibility="gone"
            android:layout_height="match_parent"
            tools:text="700kb" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/sticker_packs_list_item_image_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_below="@+id/sticker_pack_info"
        android:layout_marginEnd="16dp"
        android:layout_marginRight="16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@id/add_button_on_list"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sticker_pack_info" />

    <LinearLayout
        android:id="@+id/sticker_packs_list_item_image_list_second"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:layout_below="@+id/sticker_packs_list_item_image_list"
        android:layout_marginRight="16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@id/add_button_on_list"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sticker_pack_info" />


</RelativeLayout>
