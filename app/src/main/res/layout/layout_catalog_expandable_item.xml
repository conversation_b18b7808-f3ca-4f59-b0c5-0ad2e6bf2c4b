<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_expand"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:layout_marginBottom="@dimen/_1dp">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_40dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:contentDescription="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_bank" />

    <TextView
        android:id="@+id/tv_name"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingTop="@dimen/_8dp"
        android:paddingBottom="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_arrow"
        app:layout_constraintStart_toEndOf="@id/iv_logo"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Telkomsel" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_arrow"
        android:layout_width="@dimen/_35dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:contentDescription="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/arrow"
        app:tint="@color/black" />
</androidx.constraintlayout.widget.ConstraintLayout>
