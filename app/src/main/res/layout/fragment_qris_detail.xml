<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ScrollView
        android:id="@+id/sv_view"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/btn_download_qris"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?attr/actionBarSize">

            <include
                android:id="@+id/include_kyb_layout"
                layout="@layout/layout_kyb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_qris_poster_bg_dummy"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="@id/qris_code_container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/qris_code_container"
                tools:srcCompat="@drawable/qris_poster_bg" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/qris_code_container"
                android:layout_width="360dp"
                android:layout_height="511dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_kyb_layout">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_poster_bg"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:scaleType="centerCrop"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="@id/iv_qris_partner_banks"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:srcCompat="@drawable/qris_poster_bg" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_bg"
                    android:layout_width="270dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="@dimen/_45dp"
                    android:layout_marginTop="@dimen/_18dp"
                    android:layout_marginEnd="@dimen/_45dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintBottom_toTopOf="@id/iv_qris_partner_buku"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/qris_qr_bg" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_logo"
                    android:layout_width="150dp"
                    android:layout_height="36dp"
                    android:layout_marginStart="@dimen/_15dp"
                    android:layout_marginTop="@dimen/_4dp"
                    app:layout_constraintStart_toStartOf="@id/iv_qris_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_qris_bg"
                    app:srcCompat="@drawable/qris_logo" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_gpn_logo"
                    android:layout_width="26dp"
                    android:layout_height="32dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:layout_marginEnd="@dimen/_20dp"
                    app:layout_constraintEnd_toEndOf="@id/iv_qris_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_qris_bg"
                    app:srcCompat="@drawable/logo_gpn" />

                <!-- dp is used for texts inside the QRIS poster to avoid overlapping in case device font is set to Large -->
                <TextView
                    android:id="@+id/tv_qris_merchant_name"
                    style="@style/Heading2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_30dp"
                    android:gravity="center"
                    android:maxLines="2"
                    android:textAllCaps="true"
                    android:textSize="22dp"
                    app:layout_constraintEnd_toEndOf="@id/iv_qris_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_qris_bg"
                    app:layout_constraintTop_toBottomOf="@id/iv_qris_logo"
                    tools:ignore="SpUsage"
                    tools:text="MERCHANT NAME" />

                <TextView
                    android:id="@+id/tv_qris_unique_name"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_2dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textAllCaps="true"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="@id/iv_qris_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_qris_bg"
                    app:layout_constraintTop_toBottomOf="@id/tv_qris_merchant_name"
                    tools:ignore="SpUsage"
                    tools:text="PA25638349348" />

                <TextView
                    android:id="@+id/tv_qris_nmid"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_2dp"
                    android:gravity="center"
                    android:maxLines="2"
                    android:textAllCaps="true"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toEndOf="@id/iv_qris_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_qris_bg"
                    app:layout_constraintTop_toBottomOf="@id/tv_qris_unique_name"
                    tools:ignore="SpUsage"
                    tools:text="NMID: ID12345XXXX" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_qr"
                    android:layout_width="178dp"
                    android:layout_height="178dp"
                    android:layout_marginTop="@dimen/_14dp"
                    android:scaleType="fitXY"
                    app:layout_constraintEnd_toEndOf="@id/iv_qris_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_qris_bg"
                    app:layout_constraintTop_toBottomOf="@id/tv_qris_nmid"
                    tools:srcCompat="@color/white" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_partner_bank_bni"
                    android:layout_width="90dp"
                    android:layout_height="35dp"
                    app:layout_constraintBottom_toBottomOf="@id/iv_qris_partner_buku"
                    app:layout_constraintEnd_toStartOf="@id/iv_qris_partner_separator"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="@id/iv_qris_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_qris_partner_buku"
                    app:srcCompat="@drawable/logo_bni" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_partner_buku"
                    android:layout_width="46dp"
                    android:layout_height="46dp"
                    android:layout_marginBottom="@dimen/_4dp"
                    app:layout_constraintBottom_toTopOf="@id/tv_qris_payment_options"
                    app:layout_constraintEnd_toEndOf="@id/iv_qris_bg"
                    app:layout_constraintStart_toEndOf="@id/iv_qris_partner_separator"
                    app:srcCompat="@drawable/app_logo" />

                <View
                    android:id="@+id/iv_qris_partner_separator"
                    android:layout_width="@dimen/_2dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:background="@color/white"
                    app:layout_constraintBottom_toBottomOf="@id/iv_qris_partner_buku"
                    app:layout_constraintEnd_toStartOf="@id/iv_qris_partner_buku"
                    app:layout_constraintStart_toEndOf="@id/iv_qris_partner_bank_bni"
                    app:layout_constraintTop_toTopOf="@id/iv_qris_partner_buku" />

                <TextView
                    android:id="@+id/tv_qris_payment_options"
                    style="@style/Label2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_6dp"
                    android:gravity="center"
                    android:text="@string/payment_options_via_ewallet"
                    android:textColor="@color/white"
                    app:layout_constraintBottom_toTopOf="@id/iv_qris_partner_banks"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_partner_banks"
                    android:layout_width="328dp"
                    android:layout_height="38dp"
                    android:paddingBottom="@dimen/_10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:srcCompat="@drawable/qris_banks_logos" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.bukuwarung.payments.widget.QrisBankAccountView
                android:id="@+id/qris_selected_bank"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/qris_code_container" />

            <TextView
                android:id="@+id/tv_qris_charging_info"
                style="@style/Body3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:background="@drawable/bg_solid_blue15_corner_4dp_stroke_blue40"
                android:drawablePadding="@dimen/_10dp"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/_2dp"
                android:paddingHorizontal="@dimen/_12dp"
                android:paddingVertical="@dimen/_8dp"
                android:textColor="@color/black_60"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/close"
                app:drawableStartCompat="@drawable/qris_info"
                app:layout_constraintTop_toBottomOf="@id/qris_selected_bank"
                tools:text="@string/qris_charging_info"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_qris_download_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_22dp"
                android:paddingEnd="@dimen/_26dp"
                android:paddingBottom="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@id/tv_qris_charging_info">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_qris_stand"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_qris_stand" />

                <TextView
                    android:id="@+id/tv_qris_stand_title"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_20dp"
                    android:text="@string/download_qris_poster"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/iv_qris_stand"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_qris_stand_message"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/qris_download_message"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tv_qris_stand_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_qris_stand_title" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_qris_info_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black_5"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/cl_qris_download_info">

                <com.bukuwarung.payments.widget.ExpandableSection
                    android:id="@+id/es_charging"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.bukuwarung.payments.widget.ExpandableSection
                    android:id="@+id/es_processing_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/es_charging" />

                <com.bukuwarung.payments.widget.ExpandableSection
                    android:id="@+id/es_account_change_limit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    app:layout_constraintTop_toBottomOf="@id/es_processing_time" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/inaccessible_qris_overlay"
                layout="@layout/inaccessible_qris"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/qris_code_container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/qris_code_container"
                tools:visibility="visible" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/ba_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_24dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_download_qris"
        style="@style/ButtonOutline"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_18dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:paddingStart="@dimen/_4dp"
        android:paddingTop="@dimen/_12dp"
        android:paddingEnd="@dimen/_4dp"
        android:paddingBottom="@dimen/_12dp"
        android:text="@string/download_qris"
        android:textAllCaps="false"
        android:visibility="gone"
        app:cornerRadius="@dimen/_4dp"
        app:icon="@drawable/ic_feather_download"
        app:iconPadding="@dimen/_8dp"
        app:iconTint="@color/web_orange"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_share_qris"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sv_view"
        app:rippleColor="@color/black_40"
        tools:visibility="visible" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_share_qris"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_18dp"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:paddingStart="@dimen/_4dp"
        android:paddingTop="@dimen/_12dp"
        android:paddingEnd="@dimen/_4dp"
        android:paddingBottom="@dimen/_12dp"
        android:text="@string/share"
        android:textAllCaps="false"
        android:visibility="gone"
        app:cornerRadius="@dimen/_4dp"
        app:icon="@drawable/ic_share_new"
        app:iconPadding="@dimen/_8dp"
        app:iconTint="@color/black_80"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_download_qris"
        app:layout_constraintTop_toBottomOf="@+id/sv_view"
        app:rippleColor="@color/black_40"
        tools:visibility="visible" />

    <View
        android:id="@+id/vw_buttons"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sv_view" />

    <include
        android:id="@+id/inaccessible_qris"
        layout="@layout/inaccessible_qris"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/loading_state_lottie"
        android:layout_width="100dp"
        android:layout_height="100dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/loading_payment" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/snackbar_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>