<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_date"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="@string/date"
        android:gravity="center"
        android:padding="@dimen/_10dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintWidth_percent=".3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_penjualan"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="@string/total_penjualan"
        android:gravity="center"
        android:padding="@dimen/_10dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintWidth_percent=".28"
        app:layout_constraintStart_toEndOf="@id/tv_date"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_pengeluaran"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:gravity="center"
        android:padding="@dimen/_10dp"
        android:text="@string/total_expense_label"
        app:layout_constraintWidth_percent="0.3"
        app:layout_constraintStart_toEndOf="@id/tv_penjualan"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_date_val"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_8dp"
        android:fontFamily="@font/roboto_bold"
        android:textColor="@color/black_60"
        android:gravity="center"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_8dp"
        android:paddingBottom="@dimen/_8dp"
        android:textSize="@dimen/text_14sp"
        app:drawableEndCompat="@drawable/ic_dropdown"
        app:layout_constraintStart_toStartOf="@id/tv_date"
        app:layout_constraintEnd_toEndOf="@id/tv_date"
        app:layout_constraintTop_toBottomOf="@id/tv_date"
        tools:ignore="RtlSymmetry"
        tools:text="11/11/2021" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_penjualan_val"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_8dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_8dp"
        android:paddingBottom="@dimen/_8dp"
        android:textSize="@dimen/text_14sp"
        android:textColor="@color/green_80"
        tools:ignore="RtlSymmetry"
        app:layout_constraintTop_toBottomOf="@+id/tv_penjualan"
        app:layout_constraintStart_toStartOf="@+id/tv_penjualan"
        app:layout_constraintEnd_toEndOf="@id/tv_penjualan" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_pengeluaran_val"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_8dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_8dp"
        android:paddingBottom="@dimen/_8dp"
        android:textSize="@dimen/text_14sp"
        android:textColor="@color/red_80"
        tools:ignore="RtlSymmetry"
        app:layout_constraintTop_toBottomOf="@+id/tv_pengeluaran"
        app:layout_constraintStart_toStartOf="@+id/tv_pengeluaran"
        app:layout_constraintEnd_toEndOf="@id/tv_pengeluaran" />


</androidx.constraintlayout.widget.ConstraintLayout>