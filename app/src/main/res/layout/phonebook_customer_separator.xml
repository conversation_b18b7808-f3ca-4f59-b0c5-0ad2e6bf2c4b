<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/contactLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center">

    <TextView
        android:id="@+id/old_contact"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black_e2"
        android:ellipsize="end"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="4sp"
        android:maxLines="1"
        android:paddingLeft="@dimen/_16dp"
        android:paddingTop="8dp"
        android:paddingRight="@dimen/_16dp"
        android:paddingBottom="@dimen/_8dp"
        android:text="@string/existing_customer_contact"
        android:textColor="@color/black_60"
        android:textSize="16sp"
        android:visibility="visible" />

</RelativeLayout>
