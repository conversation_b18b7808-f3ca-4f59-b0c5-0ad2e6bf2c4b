<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:id="@+id/cl_home"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:weightSum="1"
    android:background="@drawable/white_background_radius_8"
    android:layout_height="wrap_content"
    android:orientation="horizontal">
    <androidx.viewpager2.widget.ViewPager2
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_54dp"
        android:layout_weight="0.95"
        android:id="@+id/vp_entry_point"
        android:orientation="vertical" />
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tb_entry_point"
        android:layout_width="24dp"
        android:layout_height="@dimen/_4dp"
        android:layout_weight="0.05"
        android:layout_gravity="center"
        android:rotation="90"
        android:background="@color/colorGreyLight"
        app:tabBackground="@drawable/selector_banner"
        app:tabIndicatorHeight="0dp"
        app:tabSelectedTextColor="@android:color/transparent" />
</LinearLayout>