<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/edit_printer_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_24dp"
        android:fontFamily="@font/roboto"
        android:text="@string/nama_printer"
        android:textColor="@color/black_20"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_printer_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:backgroundTint="@color/black_20"
        android:fontFamily="@font/roboto"
        android:inputType="text"
        android:textColor="@color/black_80"
        android:textSize="@dimen/dimen_16sp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_name"
        tools:text="Eppos Printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/black_20"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_printer_name"
        tools:text="86:67:23:00:4A:C2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_disconnect_printer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_40dp"
        android:fontFamily="@font/roboto_bold"
        android:padding="@dimen/_12dp"
        android:text="@string/disconnect"
        android:textColor="@color/bar_dashboard_ppob_1"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_address" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_save"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_48dp"
        android:layout_margin="@dimen/_16dp"
        android:background="@drawable/button_enabled_bg"
        android:enabled="false"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/simpan"
        android:textAllCaps="false"
        android:textColor="@color/black_800"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>