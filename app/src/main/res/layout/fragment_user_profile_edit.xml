<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">
  <androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/white">


        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:layout_alignParentTop="true"
            android:background="@color/colorPrimary"
            android:paddingStart="@dimen/_0dp"
            android:paddingEnd="@dimen/_0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:theme="@style/ToolbarTheme"
            app:titleTextColor="@color/white">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/back_btn"
                    android:layout_width="@dimen/_25dp"
                    android:layout_height="@dimen/_25dp"
                    android:layout_centerVertical="true"
                    android:fontFamily="@font/roboto"
                    android:src="@drawable/ic_back" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_24dp"
                    android:layout_alignParentTop="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_24dp"
                    android:layout_toEndOf="@+id/back_btn"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto"
                    android:gravity="top"
                    android:lineHeight="@dimen/_26dp"
                    android:lineSpacingExtra="@dimen/text_8sp"
                    android:maxLines="1"
                    android:text="@string/edit_profile"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_18sp"
                    android:textStyle="bold"
                    app:drawableLeftCompat="@drawable/dot_highlighter"
                    tools:ignore="UnusedAttribute" />

            </RelativeLayout>

        </androidx.appcompat.widget.Toolbar>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            app:layout_constraintTop_toBottomOf="@+id/toolbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_60dp"
                android:scaleType="fitXY"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@color/colorPrimary" />

            <ImageView
                android:id="@+id/profilePic"
                android:layout_width="@dimen/_80dp"
                android:layout_height="@dimen/_80dp"
                android:layout_marginTop="@dimen/_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/imageView"
                app:srcCompat="@drawable/ic_icon_shop" />

            <ImageView
                android:id="@+id/editImageIcon"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:background="@drawable/background_circular_black20"
                android:padding="@dimen/_4dp"
                android:src="@drawable/ic_camera_edit"
                app:layout_constraintBottom_toBottomOf="@id/profilePic"
                app:layout_constraintEnd_toEndOf="@id/profilePic" />

            <TextView
                android:id="@+id/your_profile_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/your_profile_pic"
                style="@style/Body2"
                android:layout_marginTop="@dimen/_8dp"
                app:layout_constraintStart_toStartOf="@+id/profilePic"
                app:layout_constraintEnd_toEndOf="@+id/profilePic"
                app:layout_constraintTop_toBottomOf="@+id/profilePic"
                />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/profileLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:layout_marginTop="@dimen/_10dp"
        android:orientation="vertical"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/your_profile_label">
        <TextView
            android:id="@+id/name_label"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:text="@string/your_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/user_name_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5dp"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/name_label"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/user_name_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:hint="@string/your_name"
                android:singleLine="true"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_16sp"
                android:maxLength="18"
                />

        </com.google.android.material.textfield.TextInputLayout>
        <TextView
            android:id="@+id/phone_label"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:text="@string/nomor_hp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/user_name_layout" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/user_phone_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black_5"
            android:enabled="false"
            android:hint="@string/mobile_phone_label"
            android:textColorHint="@color/hint_color"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="@dimen/_1dp"
            android:layout_marginTop="@dimen/_5dp"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/phone_label"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/user_phone_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:textColor="@color/black_40"
                android:textSize="@dimen/text_16sp"
                tools:text="96544347652" />

        </com.google.android.material.textfield.TextInputLayout>
        <TextView
            android:id="@+id/email_label"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:text="@string/email"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/user_phone_layout" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/user_email_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5dp"
            app:boxStrokeWidth="@dimen/_1dp"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/email_label"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/user_email_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:singleLine="true"

                android:hint="@string/email_label"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/date_of_birth_label"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:text="@string/date_of_birth_label"
            app:layout_constraintStart_toStartOf="@+id/user_email_layout"
            app:layout_constraintTop_toBottomOf="@+id/user_email_layout" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/day_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5dp"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintEnd_toStartOf="@+id/month_layout"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/date_of_birth_label"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/day_tv"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:maxLength="2"
                android:hint="@string/date"
                android:textColor="@color/black"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/month_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"

            android:inputType="number"
            app:boxStrokeWidth="@dimen/_1dp"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"
            app:layout_constraintBottom_toBottomOf="@+id/day_layout"
            app:layout_constraintEnd_toStartOf="@+id/year_layout"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/day_layout"
            app:layout_constraintTop_toTopOf="@+id/day_layout"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/month_tv"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:inputType="number"
                android:maxLength="2"
                android:hint="@string/month"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_16sp" />


        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/year_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"

            android:inputType="number"
            android:textColorHint="@color/black"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintBottom_toBottomOf="@+id/month_layout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/month_layout"
            app:layout_constraintTop_toTopOf="@+id/month_layout"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/year_tv"
                style="@style/Body2"
                android:hint="@string/year"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:inputType="number"
                android:maxLength="4"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_16sp" />


        </com.google.android.material.textfield.TextInputLayout>
        <TextView
            android:id="@+id/gender_label"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:text="@string/jenis_kelamin"
            app:layout_constraintStart_toStartOf="@+id/user_email_layout"
            app:layout_constraintTop_toBottomOf="@+id/month_layout" />

        <com.skydoves.powerspinner.PowerSpinnerView
            android:layout_width="@dimen/_0dp"
            android:layout_marginTop="@dimen/_5dp"
            android:id="@+id/spinner_gender"
            android:layout_height="wrap_content"
            android:background="@drawable/button_round_cornerd_stroke_pop_up"
            app:spinner_item_array="@array/jenis_kelamin"
            app:spinner_arrow_tint="@color/black_60"
            tools:text="@string/pilih_jenis_kelamin"
            android:hint="@string/pilih_jenis_kelamin"
            android:padding="@dimen/_15dp"
            android:textColor="@color/black_80"
            android:textColorHint="@color/black_80"
            android:textSize="14sp"
            app:spinner_arrow_gravity="end"
            app:spinner_arrow_padding="8dp"
            app:spinner_divider_show="true"
            app:spinner_divider_color="@color/light_gray"
            app:spinner_divider_size="0.4dp"
            app:spinner_popup_animation="dropdown"
            app:spinner_popup_height="@dimen/_100dp"
            app:spinner_popup_background="@color/white"
            app:spinner_popup_elevation="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gender_label"
            />




    </androidx.constraintlayout.widget.ConstraintLayout>



            <com.google.android.material.button.MaterialButton
                android:id="@+id/save"
                style="@style/DefaultMaterialButtonStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:layout_constraintVertical_bias="1"
                android:enabled="true"
                android:gravity="center"
                android:paddingTop="@dimen/_10dp"
                android:paddingBottom="@dimen/_10dp"
                android:text="@string/save"
                android:layout_margin="@dimen/_16dp"
                android:textAllCaps="false"
                android:textColor="@color/cta_button_text"
                android:textSize="@dimen/text_16sp"
                android:textStyle="bold"
                app:cornerRadius="@dimen/_2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
               />



        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <include layout="@layout/user_profile_saved_success_animation_layout" />


</androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>