<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="#F1F1F1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/container_row_1"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginTop="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_1"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="0dp"
            android:text="1"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_2"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:text="2"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_3"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:text="3"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/container_row_2"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginTop="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/container_row_1">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_4"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="0dp"
            android:text="4"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_5"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:text="5"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_6"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:text="6"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/container_row_3"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginTop="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/container_row_2">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_7"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="0dp"
            android:text="7"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_8"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:text="8"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_9"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:text="9"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/container_row_4"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginTop="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/container_row_3">

        <com.google.android.material.button.MaterialButton
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:enabled="false"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="0dp"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_0"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:text="0"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_delete"
            style="@style/Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:height="24dp"
            android:backgroundTint="@color/white"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:textColor="@color/black_60"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="0dp"
            app:icon="@drawable/ic_backspace"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconTint="@color/black_60"
            app:rippleColor="@color/black_40" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
