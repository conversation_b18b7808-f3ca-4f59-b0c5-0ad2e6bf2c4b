<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:title="@string/social_media_poster"
        app:titleTextColor="@color/white" />


    <ScrollView

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_5">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingBottom="@dimen/_16dp"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black_5"
                app:layout_constraintBottom_toTopOf="@id/btn_share"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/viewpager_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/_16dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.viewpager.widget.ViewPager
                        android:id="@+id/card_preview_viewpager"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        tools:layout_height="100dp" />

                    <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
                        android:id="@+id/dots_indicator"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginBottom="12dp"
                        app:dotsColor="@color/switch_off_color"
                        app:dotsCornerRadius="8dp"
                        app:dotsSize="10dp"
                        app:dotsSpacing="8dp"
                        app:dotsWidthFactor="2"
                        app:progressMode="true"
                        app:selectedDotColor="@color/colorPrimary" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_16dp"
                    android:background="@drawable/spindle_blue_background"
                    android:drawablePadding="@dimen/_16dp"
                    android:padding="@dimen/_8dp"
                    android:text="@string/social_media_poster_msg"
                    app:drawableStartCompat="@drawable/ic_social_media_post"
                    app:layout_constraintTop_toBottomOf="@id/viewpager_container" />


                <LinearLayout
                    android:id="@+id/input_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_info">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:paddingStart="@dimen/_16dp"
                        android:paddingTop="12dp"
                        android:paddingEnd="@dimen/_16dp"
                        android:paddingBottom="12dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/SubHeading1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Judul" />

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            app:counterEnabled="true"
                            app:counterMaxLength="30">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/title"
                                style="@style/Body2"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:importantForAutofill="no"
                                android:inputType="textMultiLine"
                                android:maxLength="30"
                                android:maxLines="2"
                                android:padding="12dp" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/SubHeading1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:text="Deskripsi" />

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            app:counterEnabled="true"
                            app:counterMaxLength="82">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/discription"
                                style="@style/Body2"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="start"
                                android:inputType="textMultiLine|textNoSuggestions|textVisiblePassword"
                                android:lines="3"
                                android:maxLines="3" />
                        </com.google.android.material.textfield.TextInputLayout>


                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/SubHeading1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Penutup" />

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            app:counterEnabled="true"
                            app:counterMaxLength="30">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/closing"
                                style="@style/Body2"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:imeOptions="actionNext"
                                android:inputType="textPersonName|textNoSuggestions|textVisiblePassword"
                                android:maxLength="30"
                                android:maxLines="1" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_share"
                style="@style/ButtonFill"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/share"
                app:icon="@drawable/ic_share"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</LinearLayout>