<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cv_home"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    android:layout_marginTop="@dimen/_10dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="6dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_12dp"
        android:paddingBottom="@dimen/_12dp"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        >


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_info_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:textFontWeight="700"
            android:gravity="start"
            android:textColor="@color/black_80"
            android:text="@string/info_lengkap_laporan_bulanan"
            app:autoSizeStepGranularity="2sp"
            app:autoSizeTextType="uniform"
            app:layout_constraintHorizontal_weight="7"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

<ImageView
    android:layout_width="@dimen/_6dp"
    android:layout_height="@dimen/_12dp"
    android:src="@drawable/vector"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>

