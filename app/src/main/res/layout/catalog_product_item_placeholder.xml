<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_marginBottom="@dimen/_8dp"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_8dp">


        <View
            android:id="@+id/image_product"
            android:layout_width="65dp"
            android:layout_height="65dp"
            android:background="@color/black_5"
            android:contentDescription="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_product_name"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:background="@color/black_5"
            android:ellipsize="end"
            android:maxEms="12"
            android:layout_marginEnd="@dimen/_16dp"
            android:maxLines="2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/image_product"
            app:layout_constraintTop_toTopOf="@id/image_product"
            tools:text="SSD Samsung 500GB" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_product_price"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:background="@color/black_5"
            android:layout_marginEnd="64dp"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/image_product"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_name"
            tools:text="Rp123.000" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_product_desc"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="@dimen/_24dp"
            android:background="@color/black_5"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/image_product"
            app:layout_constraintTop_toBottomOf="@id/tv_product_price"
            tools:text="Rp123.000" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</com.facebook.shimmer.ShimmerFrameLayout>