<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"

    >


    <androidx.appcompat.widget.AppCompatRadioButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textSize="@dimen/text_14sp"
        android:textColor="@color/black_60"
        android:id="@+id/unit_radio_btn"
        android:text=""
        android:layout_marginTop="@dimen/_12dp"
        style="@style/RadioButtonStyle"
        />
    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:background="@color/black_5"
        app:layout_constraintStart_toStartOf="@id/unit_radio_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/unit_radio_btn"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginRight="@dimen/_12dp"
        android:layout_marginStart="@dimen/_12dp"
        />


</androidx.constraintlayout.widget.ConstraintLayout>