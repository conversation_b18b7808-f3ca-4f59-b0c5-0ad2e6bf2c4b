<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    tools:showIn="@layout/tab_layout_expense">

    <com.google.android.material.chip.Chip
        android:id="@+id/chipTransactionGroupByDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/Daily"
        android:textColor="@color/black_80"
        android:textStyle="bold"
        app:checkedIconEnabled="false"
        app:chipBackgroundColor="@color/black_2"
        app:closeIcon="@drawable/ic_cevron_down_white"
        app:closeIconEnabled="true"
        app:closeIconTint="@color/black_80"
        app:closeIconVisible="true" />

    <com.google.android.material.chip.Chip
        android:id="@+id/chipTransactionGroupByCategory"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:visibility="gone"
        android:text="@string/all"
        android:textColor="@color/black_80"
        android:textStyle="bold"
        app:checkedIconEnabled="false"
        app:chipBackgroundColor="@color/black_2"
        app:closeIcon="@drawable/ic_cevron_down_white"
        app:closeIconEnabled="true"
        app:closeIconTint="@color/black_80"
        app:closeIconVisible="true" />
</merge>