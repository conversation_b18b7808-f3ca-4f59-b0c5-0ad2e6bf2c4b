<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/_50dp"
        android:layout_height="@dimen/_50dp"
        android:layout_margin="@dimen/_5dp"
        android:elevation="@dimen/_1dp"
        app:srcCompat="@drawable/close_new"
        app:layout_constraintEnd_toEndOf="@id/iv_preview_image"
        app:layout_constraintTop_toTopOf="@id/iv_preview_image" />

    <ImageView
        android:id="@+id/iv_preview_image"
        android:layout_width="@dimen/_300dp"
        android:layout_height="@dimen/_300dp"
        android:scaleType="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat = "@drawable/ic_camera" />

</androidx.constraintlayout.widget.ConstraintLayout>
