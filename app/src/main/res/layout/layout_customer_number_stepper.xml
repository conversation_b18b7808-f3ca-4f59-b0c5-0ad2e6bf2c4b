<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/btnDecrease"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_remove_circle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/blue_60" />

    <EditText
        android:id="@+id/etNumber"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:enabled="false"
        android:hint="0"
        android:imeOptions="actionDone"
        android:textColorHint="@color/black_20"
        android:inputType="number"
        android:digits="0123456789,"
        android:minWidth="48dp"
        android:textAlignment="center"
        android:textColor="@color/cash_chip_text_color_state"
        app:layout_constraintBottom_toBottomOf="@id/btnDecrease"
        app:layout_constraintEnd_toStartOf="@id/btnIncrease"
        app:layout_constraintStart_toEndOf="@id/btnDecrease"
        app:layout_constraintTop_toTopOf="@id/btnDecrease"
        android:maxLength="7"
        />

    <ImageView
        android:id="@+id/btnIncrease"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_add_circle"
        app:layout_constraintStart_toEndOf="@id/etNumber"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/blue_60" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="5dp"
        android:background="@color/blue_60"
        app:layout_constraintEnd_toEndOf="@id/btnIncrease"
        app:layout_constraintStart_toStartOf="@id/btnDecrease"
        app:layout_constraintTop_toBottomOf="@id/btnDecrease" />

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>