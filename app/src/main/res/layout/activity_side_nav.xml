<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:clickable="true"
    android:background="#fffbfa">

    <View
        android:id="@+id/top_margin"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_30dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/premium_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/top_margin"
        android:background="@color/blue_80">

        <ImageView
            android:id="@+id/iv_verified_badge"
            android:layout_width="@dimen/_50dp"
            android:layout_height="@dimen/_50dp"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginBottom="@dimen/_5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_kyc_badge_standard" />

        <TextView
            android:id="@+id/verified_title_txt"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:layout_marginTop="@dimen/_12dp"
            tools:text="@string/premium_account"
            android:textColor="@color/white"
            app:layout_constraintBottom_toTopOf="@id/verified_subtitle_txt"
            app:layout_constraintStart_toEndOf="@id/iv_verified_badge"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/verified_subtitle_txt"
            style="@style/Label1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:layout_marginBottom="@dimen/_5dp"
            tools:text="@string/verify_data_complete_prioritas"
            android:textColor="#B3FFFFFF"
            android:lineSpacingMultiplier="0.6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_verified_badge"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/verified_title_txt" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/blue_bg"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_20dp"
        android:layout_below="@id/premium_layout"
        android:background="@color/blue_80" />

    <LinearLayout
        android:id="@+id/layoutHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/premium_layout"
        android:background="@drawable/rounded_rectangle_white_20dp"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="51dp"
            android:layout_height="51dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:background="@color/white"
            android:src="@drawable/logo_transparent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="19dp"
            android:layout_marginTop="20dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/appName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="4sp"
                android:text="@string/app_name"
                android:textColor="@color/heading_text"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/appVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:paddingBottom="18dp"
                android:text=""
                android:textColor="@color/body_text"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@id/layoutHeader"
        android:background="#eeeeee" />
    <ImageView
        android:id="@+id/img_nav_payment_banner"
        android:layout_below="@id/divider"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginRight="@dimen/_16dp"
        tools:src="@drawable/image_hamburger_payment_banner" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/bookHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff"
        android:layout_below="@+id/img_nav_payment_banner">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/lang_divider"
            android:layout_below="@+id/layoutHeader"
            android:layout_marginBottom="70dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="16dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/business_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentTop="true"
                    tools:listitem="@layout/navigation_item" />

            </RelativeLayout>
        </androidx.core.widget.NestedScrollView>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="70dp"
            android:background="#eeeeee" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_new_business_btn"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_gravity="bottom"
            android:layout_marginLeft="28dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="28dp"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/roboto"
            android:text="@string/create_new_ledger"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            app:backgroundTint="@color/colorPrimary"
            app:rippleColor="@color/toolbar_item_ripple" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>
