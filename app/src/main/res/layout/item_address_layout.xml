<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="top|start"
    android:orientation="horizontal"
    android:background="?attr/selectableItemBackground"
    android:padding="@dimen/_16dp">

    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_location_outline" />

    <TextView
        android:id="@+id/tv_address"
        style="@style/Body1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:ellipsize="end"
        android:maxLines="2"
        tools:text="GGWP ASDF GGWP ASDF GGWP ASDF GGWP ASDF " />

</LinearLayout>