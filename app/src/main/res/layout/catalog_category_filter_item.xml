<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:background="@drawable/bg_button_outline"
    android:layout_height="95dp"
    android:layout_margin="16dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCategory"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/app_logo" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="26dp"
        android:textColor="@color/black_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivCategory"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Makanan dan Minuman" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivImageAddCatelogueNext"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_margin="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_chevron_right_black_60" />

</androidx.constraintlayout.widget.ConstraintLayout>