<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_pin_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="Jelajahi fitur BukuWarung"
        style="@style/Heading3"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_pin_subtitle"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="Maksimalkan usahamu #UntungPakeBukuWarung"
        android:layout_marginTop="@dimen/_8dp"
        style="@style/Body4"
        app:layout_constraintTop_toBottomOf="@id/tv_pin_title"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_pin_lock"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:layout_constraintTop_toTopOf="@id/tv_pin_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/ic_lock_big" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_profile_pin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:clipToPadding="false"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_pin_subtitle" />

</androidx.constraintlayout.widget.ConstraintLayout>