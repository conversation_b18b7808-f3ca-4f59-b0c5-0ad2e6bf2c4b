<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <!--header starts-->

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            >

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/header_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent">

                <include
                    android:id="@+id/header"
                    layout="@layout/transaction_receipt_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent" />


                <include
                    android:id="@+id/product_header_layout"
                    layout="@layout/layout_product_list_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    app:layout_constraintTop_toBottomOf="@id/header"
                    tools:visibility="visible" />


                <LinearLayout
                    android:id="@+id/product_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@id/product_header_layout"
                    tools:background="@color/black_10"
                    tools:layout_height="96dp" />

                <View
                    android:id="@+id/product_line"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/horizontal_dashed_line"
                    app:layout_constraintTop_toBottomOf="@id/product_container" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/product_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="product_line, product_header_layout, product_container"
                    tools:layout_editor_absoluteX="16dp"
                    tools:layout_editor_absoluteY="16dp" />

                <!--product section ends-->
                <!--transaction total section starts-->

                <TextView
                    android:id="@+id/tv_transaction_total_label"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/label_total_payment"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/product_line" />

                <TextView
                    android:id="@+id/tv_transaction_total_amount"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/product_line"
                    tools:text="Rp.500.000" />

                <TextView
                    android:id="@+id/tv_service_fee"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/service_fee"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_transaction_total_label" />

                <TextView
                    android:id="@+id/tv_service_fee_amount"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_transaction_total_label"
                    tools:text="Rp.500.000" />

                <View
                    android:id="@+id/transaction_line"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/horizontal_dashed_line"
                    app:layout_constraintTop_toBottomOf="@id/tv_service_fee" />

                <TextView
                    android:id="@+id/tv_pos_total"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/total_cash"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transaction_line"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_pos_total_amount"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transaction_line"
                    tools:text="Rp.500.000"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_pos_change"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/change_to_give"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_pos_total"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_post_change_amount"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_pos_total"
                    tools:text="Rp.500.000"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_sender"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/sender"
                    android:textColor="@color/black_40"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_pos_change" />

                <TextView
                    android:id="@+id/tv_sender_name"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_sender"
                    tools:text="Rohendy" />

                <TextView
                    android:id="@+id/tv_payment_method"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black_40"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_sender_name"
                    tools:text="Via e-Wallet OVO" />

                <View
                    android:id="@+id/payment_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="8dp"
                    android:background="@color/black_10"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/tv_payment_method" />

                <!--transaction total section ends-->
                <!--note section starts-->
                <TextView
                    android:id="@+id/note_label"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/label_note"
                    android:textColor="@color/black_40"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/payment_line" />

                <TextView
                    android:id="@+id/tv_note"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/black_80"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/note_label"
                    tools:text="Very long note, but we only show this section if user has inputted any" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_payment_catatan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@color/black_5"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/tv_note"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_pdt_name"
                        style="@style/Body1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Pulsa Telkomsel 100.000" />

                    <TextView
                        android:id="@+id/tv_number"
                        style="@style/Heading2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_8dp"
                        android:textColor="@color/blue_80"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_pdt_name"
                        tools:text="081275598545" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/ppob_message"
                    style="@style/Label1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/electricity_token_hint"
                    android:textColor="@color/black_40"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/layout_payment_catatan" />

                <View
                    android:id="@+id/payment_divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@color/black_10"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/ppob_message" />

                <TextView
                    android:id="@+id/tv_payment_safe_message"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/payment_safe_message"
                    android:textAlignment="center"
                    android:textColor="@color/black_40"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/payment_divider" />


                <androidx.constraintlayout.widget.Group
                    android:id="@+id/note_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="note_label, tv_note"
                    tools:layout_editor_absoluteX="16dp"
                    tools:layout_editor_absoluteY="16dp" />

                <!--note section ends-->
                <!--footer section starts-->

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_payment_safe_message"
                    android:layout_marginTop="38dp"
                    android:id="@+id/bukuwarung_watermark_layout"
                    >

                    <ImageView
                        android:id="@+id/img_app_logo"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/app_logo"
                        app:layout_constraintDimensionRatio="H, 1:1"
                        app:layout_constraintEnd_toStartOf="@id/tv_footer_one"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/tv_footer_one"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_bukuwarung_url"
                        />

                    <TextView
                        android:id="@+id/tv_footer_one"
                        style="@style/Label1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginTop="38dp"
                        android:text="@string/made_with_bukuwarung_app"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/img_app_logo"
                        app:layout_constraintBottom_toTopOf="@+id/tv_bukuwarung_url"
                        />

                    <TextView
                        android:id="@+id/tv_bukuwarung_url"
                        style="@style/Label1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginTop="4dp"
                        android:text="@string/bukuwarung_url"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toEndOf="@id/img_app_logo"
                        app:layout_constraintTop_toBottomOf="@id/tv_footer_one"
                        tools:ignore="SmallSp" />

                </androidx.constraintlayout.widget.ConstraintLayout>



                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_footer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="tv_bukuwarung_url, tv_footer_one, img_app_logo, transaction_line" />
            </androidx.constraintlayout.widget.ConstraintLayout>




            <!--footer section ends-->

            <LinearLayout
                android:id="@+id/banner_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black_5"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/header_layout"
                >

                <ImageView
                    android:id="@+id/img_mission_banner"
                    android:layout_width="match_parent"
                    android:layout_height="134dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_18dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_40dp"
                    />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>


    </ScrollView>




</androidx.constraintlayout.widget.ConstraintLayout>
