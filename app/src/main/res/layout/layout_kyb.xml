<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_10dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/white"/>

    <TextView
        android:id="@+id/tv_text"
        app:layout_constraintTop_toTopOf="@id/btn_action"
        app:layout_constraintBottom_toBottomOf="@id/btn_action"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_action"
        android:drawablePadding="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:layout_width="@dimen/_0dp"
        android:lineSpacingMultiplier="0.8"
        style="@style/Body3"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>