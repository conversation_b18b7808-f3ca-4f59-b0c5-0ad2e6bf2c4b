<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:layout_marginTop="@dimen/_12dp"
    android:background="@drawable/bg_rounded_rectangle_white_8dp">

    <TextView
        android:id="@+id/tv_biller_name"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintEnd_toStartOf="@id/btn_check"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Telkomsel 1.000" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_4dp"
        android:paddingEnd="@dimen/_10dp"
        android:textColor="@color/red_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_biller_name"
        tools:text="Rp2.300" />

    <TextView
        android:id="@+id/tv_profit"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_14dp"
        android:textColor="@color/green_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_amount"
        tools:text="Untung +700" />

    <TextView
        android:id="@+id/tv_last_amount"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/strike_through"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toEndOf="@id/tv_amount"
        app:layout_constraintTop_toTopOf="@id/tv_amount"
        tools:text="Rp 1.800" />

    <TextView
        android:id="@+id/tv_selling_price"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:text="@string/selling_price"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Harga Jual" />

    <TextView
        android:id="@+id/tv_selling_amount"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_price"
        tools:text="Rp3.000" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_check"
        style="@style/Button.OutlinePrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_14dp"
        android:paddingHorizontal="@dimen/_4dp"
        android:text="@string/ubah_harga"
        android:textStyle="bold"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_amount"
        app:rippleColor="@color/black_40" />
</androidx.constraintlayout.widget.ConstraintLayout>