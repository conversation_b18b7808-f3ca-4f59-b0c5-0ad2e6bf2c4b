<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_top_rounded_corner">
    <View
        android:id="@+id/divider"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/black_5"
        />
    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Jualan Produk Digital di Bukuwarung"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_30dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider"/>

    <TextView
        android:id="@+id/tv_body"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_ok"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_20dp"
        android:text="@string/understand"
        android:textAllCaps="false"
        app:layout_constraintTop_toBottomOf="@id/tv_body"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>