<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.expense.category.CategoryDescriptionActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbarMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/Toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/TootleTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_0dp"
                android:paddingEnd="@dimen/_24dp"
                android:text="@string/category_description"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>


    <TextView
        android:id="@+id/tv_category_header"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="@string/category_header"
        android:layout_marginTop="@dimen/_20dp"
        style="@style/SubHeading1"
        android:layout_marginHorizontal="@dimen/_12dp"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/toolbarMain"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_category_details"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:visibility="visible"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginHorizontal="@dimen/_12dp"
        app:layout_constraintTop_toBottomOf="@id/tv_category_header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>