<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_customer_payment_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/customer_payment_method"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_pos_payment_wallet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_customer_payment_method" />

    <RelativeLayout
        android:id="@+id/rl_buying_price_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/round_corner_info_message"
        android:orientation="horizontal"
        android:padding="@dimen/_12dp"
        app:layout_constraintTop_toBottomOf="@id/rv_pos_payment_wallet">

        <ImageView
            android:id="@+id/iv_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_info_icon" />

        <TextView
            android:id="@+id/txt_info_message"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_toEndOf="@+id/iv_info"
            android:text="@string/pos_payment_wallet_info"
            android:textColor="@color/black_60" />
    </RelativeLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:enabled="false"
        android:text="@string/save"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/success_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_success"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/trx_success" />

        <TextView
            android:id="@+id/tv_trx_success"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/transaksi_berhasil_dicatat"
            android:textColor="@color/green_80"
            android:textSize="@dimen/text_24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>