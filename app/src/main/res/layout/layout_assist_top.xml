<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_16dp"
    android:elevation="@dimen/_4dp"
    android:background="@drawable/bg_rounded_rectangle_white_8dp"
    android:paddingBottom="@dimen/_10dp">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_listrik_circle_bg" />

    <TextView
        android:id="@+id/tv_product_name"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_4dp"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        tools:text="Token Listrik" />

    <TextView
        android:id="@+id/tv_date"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="@id/tv_product_name"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="13 Agu 2021, 14:16" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:textColor="@color/red_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Rp101.500" />

    <TextView
        android:id="@+id/tv_status"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_10dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_amount"
        tools:text="Pembelian - Gagal" />

    <TextView
        android:id="@+id/tv_transaction"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        android:paddingHorizontal="@dimen/_9dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_date"
        tools:text="IYYMMDDXXXXXXXX" />

    <TextView
        android:id="@+id/tv_app_version"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_10dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_transaction"
        tools:text="App Version 3.14.0" />

</androidx.constraintlayout.widget.ConstraintLayout>