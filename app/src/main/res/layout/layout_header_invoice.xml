<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/tv_date"
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        layout="@layout/item_invoice"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="@dimen/_16dp"/>

    <include
        android:id="@+id/tv_note_code"
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        layout="@layout/item_invoice"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintTop_toBottomOf="@id/tv_date"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="@dimen/_16dp"/>

    <include
        android:id="@+id/tv_customer_details"
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        layout="@layout/item_invoice"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintTop_toBottomOf="@id/tv_note_code"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="@dimen/_16dp"/>
    
    <View
        android:id="@+id/view_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:background="@drawable/horizontal_dashed_line"
        android:layout_marginTop="@dimen/_8dp"
        android:foregroundGravity="center"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_customer_details"/>

</androidx.constraintlayout.widget.ConstraintLayout>