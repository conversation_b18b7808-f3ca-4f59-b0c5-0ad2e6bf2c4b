<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/white_background_radius_8">

    <ImageView
        android:id="@+id/profilePic"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/bg_bordered_circle"
        android:scaleType="centerInside"
        app:srcCompat="@drawable/ic_store_grey" />

    <ImageView
        android:id="@+id/editImageIcon"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:background="@drawable/background_circular_black20"
        android:padding="@dimen/_8dp"
        android:src="@drawable/ic_camera_edit"
        app:layout_constraintBottom_toBottomOf="@id/profilePic"
        app:layout_constraintEnd_toEndOf="@id/profilePic" />

<TextView
    android:id="@+id/tv_success_shop"
    android:layout_width="@dimen/_0dp"
    android:layout_height="wrap_content"
    app:layout_constraintStart_toStartOf="@+id/profilePic"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    android:layout_marginStart="@dimen/_80dp"
    tools:text="@string/success_shop"
    android:background="@color/white"
    android:textSize="@dimen/text_18sp"
    android:layout_marginTop="@dimen/_18dp"
    style="@style/Heading2"
    android:textColor="@color/black_000000"/>


    <ProgressBar
        android:id="@+id/progress_profile"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="0dp"
        android:layout_height="@dimen/_16dp"
        android:max="100"
        android:background="@color/white"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:progressTint="@color/buku_CTA"
        android:progressBackgroundTint="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_success_shop"
        app:layout_constraintStart_toEndOf="@+id/profilePic"
        android:progress="50" />

    <TextView
        android:id="@+id/completion_badge"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_20dp"
        android:max="100"
        android:textSize="@dimen/text_14sp"
        android:gravity="center_vertical"
        android:text="Sudah Lengkap"
        android:visibility="gone"
        android:paddingLeft="@dimen/_6dp"
        android:paddingRight="@dimen/_6dp"
        android:drawableLeft="@drawable/ic_check_circle"
        android:drawablePadding="@dimen/_6dp"
        android:background="@drawable/green_badge_bg"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_24dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_18dp"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tv_success_shop"
        app:layout_constraintStart_toEndOf="@+id/profilePic"
        app:layout_constraintBottom_toBottomOf="parent"
        android:progress="50" />

    <TextView
        android:id="@+id/tv_profile_percentage"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/profilePic"
        app:layout_constraintTop_toBottomOf="@id/progress_profile"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_18dp"
        android:layout_marginStart="@dimen/_16dp"
        android:textSize="@dimen/text_12sp"
        android:background="@color/white"
        android:text="Info usaha baru 35%. Yuk, lengkapi!"
        android:layout_marginTop="@dimen/_6dp"
        style="@style/Body3"
        android:textColor="@color/black_40"/>


</androidx.constraintlayout.widget.ConstraintLayout>