<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_parent"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- TODO: will refactor using new style for toolbar-->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_24dp"
                android:layout_toEndOf="@+id/back_btn"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/edit_business_profile"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:drawableLeftCompat="@drawable/dot_highlighter"
                tools:ignore="UnusedAttribute" />

            <TextView
                android:id="@+id/tv_delete_btn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/_16dp"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/delete"
                android:textColor="@color/white"
                android:textSize="@dimen/text_16sp"
                android:textStyle="bold"
                app:drawableLeftCompat="@drawable/dot_highlighter"
                tools:ignore="UnusedAttribute" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:id="@+id/form"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:fillViewport="true"
        android:scrollbars="none"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@id/button_divider"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                style="@style/Divider.Horizontal.LightBlue"
                android:id="@+id/progress_bg"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_16dp"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:background="@color/colorPrimary"
                android:paddingLeft="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                />
            <TextView
                android:id="@+id/progress_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:textSize="@dimen/_12dp"
                android:text="Lengkapi profilmu, isi 3 pertanyaan lagi"
                android:paddingLeft="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="@id/progress_bg"
                app:layout_constraintBottom_toBottomOf="@id/progress_bg"
                app:layout_constraintStart_toStartOf="@id/progress_bg"
                />

            <TextView
                android:id="@+id/progress_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:text="20%"
                android:textSize="@dimen/_12dp"
                android:textStyle="bold"
                android:paddingRight="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="@id/progress_bg"
                app:layout_constraintBottom_toBottomOf="@id/progress_bg"
                app:layout_constraintEnd_toEndOf="@id/progress_bg"
                />

            <ProgressBar
                android:id="@+id/progress_profile"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_16dp"
                android:max="100"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:background="@color/colorPrimary"
                android:progressTint="@color/buku_CTA"
                android:progressBackgroundTint="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/progress_bg"
                android:progress="50" />

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_48dp"
            android:scaleType="fitXY"
            app:layout_constraintTop_toBottomOf="@+id/progress_profile"
            app:srcCompat="@color/colorPrimary" />

        <ImageView
            android:id="@+id/profilePic"
            android:layout_width="@dimen/_80dp"
            android:layout_height="@dimen/_80dp"
            android:layout_marginTop="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView"
            android:background="@drawable/bg_bordered_circle"
            android:scaleType="centerInside"
            app:srcCompat="@drawable/ic_store_grey" />

        <ImageView
            android:id="@+id/editImageIcon"
            android:layout_width="@dimen/_25dp"
            android:layout_height="@dimen/_25dp"
            android:background="@drawable/background_circular_black20"
            android:padding="@dimen/_4dp"
            android:src="@drawable/ic_camera_edit"
            app:layout_constraintBottom_toBottomOf="@id/profilePic"
            app:layout_constraintEnd_toEndOf="@id/profilePic" />

        <TextView
            android:id="@+id/your_profile_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/business_profile_pic_label"
            style="@style/Body2"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintStart_toStartOf="@+id/profilePic"
            app:layout_constraintEnd_toEndOf="@+id/profilePic"
            app:layout_constraintTop_toBottomOf="@+id/profilePic"
            />

            <TextView
                style="@style/Divider.Horizontal.LightBlue"
                android:id="@+id/hr_business_info"
                android:layout_height="34dp"
                android:fillColor="#EAF6FF"
                android:gravity="center_vertical"
                android:text="@string/label_business_info"
                android:paddingLeft="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/your_profile_label"
                />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/user_name_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:hint="@string/business_name"
                android:layout_marginTop="@dimen/_16dp"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/hr_business_info"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/user_name_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:textSize="@dimen/text_16sp"
                    android:maxLength="100"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_business_category_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/business_category_hint"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_name_layout"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_business_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:textSize="@dimen/text_16sp"
                    android:editable="false"
                    android:focusableInTouchMode="false"
                    android:focusable="false"
                    android:drawableTint="@color/green_80"
                    android:drawableEnd="@drawable/ic_chevron_right" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/user_phone_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:hint="@string/mobile_phone_label"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_business_category_layout"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/user_phone_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black_40"
                    android:textSize="@dimen/text_16sp"
                    android:inputType="number"
                    tools:text="***********" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/Divider.Horizontal.LightBlue"
                android:id="@+id/hr_business_operation_info"
                android:layout_height="34dp"
                android:fillColor="#EAF6FF"
                android:gravity="center_vertical"
                android:text="@string/business_operation_info_lable"
                android:paddingLeft="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_phone_layout"
                />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/business_address_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:hint="@string/business_address_hint"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/hr_business_operation_info"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_business_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:textSize="@dimen/text_16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_business_hour_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/business_hour_hint"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/business_address_layout"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_business_hour"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:textSize="@dimen/text_16sp"
                    android:focusableInTouchMode="false"
                    android:focusable="false"
                    android:drawableTint="@color/green_80"
                    android:drawableEnd="@drawable/ic_chevron_right"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/Divider.Horizontal.LightBlue"
                android:id="@+id/hr_business_stat_info"
                android:layout_height="34dp"
                android:fillColor="#EAF6FF"
                android:gravity="center_vertical"
                android:text="@string/business_stat_info_label"
                android:paddingLeft="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_business_hour_layout"
                />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/business_establishment_year_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:hint="@string/business_est_year"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/hr_business_stat_info"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_business_establishment_year"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:inputType="number"
                    android:textSize="@dimen/text_16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/outlet_count_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:hint="@string/outlet_count_hint"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintHorizontal_weight="1"
                android:layout_margin="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/emp_count_layout"
                app:layout_constraintTop_toBottomOf="@+id/business_establishment_year_layout"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_outlet_count"
                    style="@style/Body2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:maxLength="6"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:textSize="@dimen/text_16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/emp_count_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginRight="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:hint="@string/emp_count_hint"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="@dimen/_1dp"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@+id/outlet_count_layout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/business_establishment_year_layout"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_emp_count"
                    style="@style/Body2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:inputType="number"
                    android:maxLength="6"
                    android:textColorHint="@color/hint_color"
                    android:textSize="@dimen/text_16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/save"
                style="@style/DefaultMaterialButtonStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:layout_constraintVertical_bias="1"
                android:enabled="true"
                android:gravity="center"
                android:paddingTop="@dimen/_10dp"
                android:paddingBottom="@dimen/_10dp"
                android:text="@string/save"
                android:textAllCaps="false"
                android:textColor="@color/cta_button_text"
                android:textSize="@dimen/text_16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:cornerRadius="@dimen/_2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/emp_count_layout" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <View
        android:id="@+id/saveOnboarding"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@+id/button_divider" />

    <View
        android:id="@+id/button_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#EEEEEE"
        app:layout_constraintBottom_toTopOf="@id/btn_save" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:text="@string/save"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintStart_toEndOf="@id/btn_bulk_transaksi"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <include layout="@layout/user_profile_saved_success_animation_layout" />

    <FrameLayout
        android:id="@+id/fragment_container_contact"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_save"
        />

</androidx.constraintlayout.widget.ConstraintLayout>