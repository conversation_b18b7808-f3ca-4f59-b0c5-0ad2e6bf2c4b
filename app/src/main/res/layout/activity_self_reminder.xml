<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    android:fitsSystemWindows="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_alignParentStart="true"
                android:layout_width="25dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_height="25dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:layout_centerVertical="true"
                android:src="@mipmap/back_white"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/title"
                android:layout_marginStart="24dp"
                android:layout_width="wrap_content"
                android:drawableStart="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:layout_height="24dp"
                android:layout_toEndOf="@+id/backBtn"
                android:layout_alignParentTop="true"
                android:fontFamily="@font/roboto"
                android:textStyle="bold"
                android:layout_centerVertical="true"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:lineSpacingExtra="8sp"
                android:lineHeight="26dp"
                android:gravity="top"
                android:maxLines="1"
                android:text="@string/self_remainder"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/emptyView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_below="@+id/toolbar"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="32dp"
        android:paddingBottom="32dp"
        android:visibility="gone"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <ImageView
            android:id="@+id/clock_image"
            android:layout_width="150dp"
            android:layout_height="160dp"
            android:contentDescription="@null"
            android:src="@drawable/clock_icon" />

        <TextView
            style="@style/Heading3"
            android:id="@+id/selfReminderTitle"
            android:layout_marginTop="32dp"
            android:text="@string/self_reminder_title"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            />

        <TextView
            android:id="@+id/selfReminderMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:fontFamily="@font/roboto"
            android:text="@string/self_reminder_message"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            android:textSize="@dimen/text_18sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/dataView"
        android:layout_below="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="top"
        android:visibility="visible"
        android:background="@color/white"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/selfReminderRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingBottom="@dimen/_8dp" />

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/addReminderBtnBottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/_8dp"
        android:layout_marginBottom="24dp"
        android:fontFamily="@font/roboto"
        android:text="@string/create_remainder"
        android:textStyle="bold"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@mipmap/ic_plus_white_24dp"
        app:iconGravity="start" />
</RelativeLayout>