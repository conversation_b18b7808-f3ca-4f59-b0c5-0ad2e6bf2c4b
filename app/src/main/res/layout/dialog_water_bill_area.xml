<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:tools="http://schemas.android.com/tools">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et_search"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:hint="@string/search_region"
        android:layout_marginTop="@dimen/_8dp"
        android:paddingHorizontal="@dimen/_16dp"
        android:singleLine="true"
        android:textColor="@color/blue_60"
        android:textColorHint="@color/black_10"
        app:drawableStartCompat="@drawable/ic_search"
        android:drawablePadding="@dimen/_20dp"
        app:drawableTint="@color/black_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar" />

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/sfl_layout"
        android:background="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_search"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <include
                layout="@layout/layout_shimmer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <include
                layout="@layout/layout_shimmer"
                android:layout_marginTop="@dimen/_25dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <include
                layout="@layout/layout_shimmer"
                android:layout_marginTop="@dimen/_25dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <include
                layout="@layout/layout_shimmer"
                android:layout_marginTop="@dimen/_25dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <include
                layout="@layout/layout_shimmer"
                android:layout_marginTop="@dimen/_25dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </com.facebook.shimmer.ShimmerFrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_item"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:paddingBottom="?attr/actionBarSize"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_search"
        tools:itemCount="12"
        tools:listitem="@layout/layout_water_bill_item" />

    <TextView
        android:id="@+id/tv_empty"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:gravity="center"
        android:visibility="gone"
        android:text="@string/empty_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"
        tools:text="Wilayah yang kamu cari tidak ditemukan..." />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_search"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>