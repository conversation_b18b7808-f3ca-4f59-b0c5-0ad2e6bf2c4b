<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <View
        android:id="@+id/vw_coachmark"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toTopOf="@id/cl_home"
        app:layout_constraintBottom_toBottomOf="@id/cl_home"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_home"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/home_blue_top_gradient_background"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_send_bill_text"
            style="@style/Heading3"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_22dp"
            android:layout_marginTop="8dp"
            android:textColor="@color/white"
            android:textSize="@dimen/text_18sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Kirim dan Tagih Uang Antar Bank" />

        <ImageView
            android:id="@+id/iv_help_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:srcCompat="@drawable/ic_info_blue"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_26dp"/>

        <include
            android:id="@+id/layout_learn_account_levels"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            layout="@layout/layout_home_learn_account_levels"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintWidth_percent="0.9"
            app:layout_constraintTop_toBottomOf="@id/tv_send_bill_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <include
            android:id="@+id/layout_payment_in_out"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            layout="@layout/layout_home_payment_in_out"
            android:layout_marginTop="@dimen/_8dp"
            android:paddingBottom="@dimen/_20dp"
            app:layout_constraintTop_toBottomOf="@id/layout_learn_account_levels"
            app:layout_constraintWidth_percent="0.9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>


        <TextView
            android:id="@+id/tv_kyc_prompt_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/Body3"
            android:gravity="center"
            android:layout_marginTop="-20dp"
            android:padding="@dimen/_10dp"
            android:textColor="@color/white"
            android:background="@drawable/home_top_kyc_prompt_info"
            app:layout_constraintStart_toStartOf="@+id/layout_payment_in_out"
            app:layout_constraintEnd_toEndOf="@id/layout_payment_in_out"
            app:layout_constraintTop_toBottomOf="@+id/layout_payment_in_out"
            android:text="@string/kyc_limit_info" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/kyb_upgrade_layout"
        layout="@layout/fragment_upgrade"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_home"/>


    <include
        android:id="@+id/inc_cta_view"
        layout="@layout/view_cta_homepage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/cl_home"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
