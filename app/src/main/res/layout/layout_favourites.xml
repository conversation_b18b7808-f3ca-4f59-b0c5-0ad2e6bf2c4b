<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/_12dp"
    android:background="@drawable/bg_rounded_yellow_8dp">

    <TextView
        android:id="@+id/tv_add_favourite"
        style="@style/SubHeading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/add_favourite_customer"
        app:layout_constraintEnd_toStartOf="@id/tv_favourite"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Label1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/favourite_subtitle"
        app:layout_constraintEnd_toStartOf="@id/tv_favourite"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_add_favourite" />

    <TextView
        android:id="@+id/tv_favourite"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:background="@drawable/bg_yellow_outline"
        android:drawablePadding="@dimen/_10dp"
        android:padding="@dimen/_4dp"
        android:text="@string/favourite"
        android:textColor="@color/yellow_60"
        app:drawableStartCompat="@drawable/ic_favourites"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>