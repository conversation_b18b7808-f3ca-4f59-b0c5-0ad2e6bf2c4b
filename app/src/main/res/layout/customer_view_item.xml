<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_marginEnd="@dimen/_20dp"
    android:foreground="?android:attr/selectableItemBackground"
    android:layout_height="72dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_pic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="16dp">

        <LinearLayout
            android:id="@+id/pic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/photo"
                android:layout_width="40dp"
                android:layout_height="40dp" />

            <TextView
                android:id="@+id/nameInitials"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/oval_0"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:text="A"
                android:maxLength="1"
                android:textColor="@color/white"
                android:textSize="22sp"
                android:visibility="gone" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/nameLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@+id/balanceLayout"
        android:layout_toRightOf="@+id/cl_pic"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="bkwarung"
            android:textColor="@color/heading_text"
            android:textSize="16dp"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="12sp"
            app:autoSizeStepGranularity="1sp"/>

        <TextView
            android:id="@+id/tvAddress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lineSpacingExtra="0sp"
            android:maxLines="1"
            android:text="indo"
            android:textColor="@color/body_text"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/tv_reminder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lineSpacingExtra="0sp"
            android:maxLines="1"
            android:text="@string/jatuh_tempo"
            android:textColor="@color/black_40"
            android:textSize="@dimen/text_10sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/balanceLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:layout_alignWithParentIfMissing="true"
        android:gravity="end"
        android:orientation="vertical">

        <TextView
            android:id="@+id/balanceTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:gravity="end"
            android:text="Rp100000"
            android:fontFamily="@font/roboto"
            android:textStyle="bold"
            android:textColor="@color/out_red"
            android:textSize="16dp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="right|center_vertical|center_horizontal|center|end"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/balanceState"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical|center_horizontal|center|end"
                android:gravity="right|center_vertical|center_horizontal|center|end"
                android:maxWidth="120dp"
                android:maxLines="1"
                android:text="owes you"
                android:textSize="@dimen/text_10sp"
                android:textColor="@color/body_text"
                app:autoSizeMaxTextSize="12sp"
                app:autoSizeMinTextSize="12sp"
                app:autoSizeStepGranularity="2sp" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignLeft="@+id/nameLayout"
        android:layout_alignParentBottom="true"
        android:background="@color/count_divider"
        android:visibility="visible" />
</RelativeLayout>
