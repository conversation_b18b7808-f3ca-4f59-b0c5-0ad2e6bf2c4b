<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingStart="@dimen/_32dp"
    android:paddingEnd="@dimen/_32dp">

    <ImageView
        android:id="@+id/img_logo"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:src="@drawable/app_logo" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:textAlignment="center"
        tools:text="Koneksi internet putus" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textAlignment="center"
        tools:text="Pastikan data internet atau sinyal kamu tersedia, lalu coba lagi." />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_action"
        style="@style/Button.OutlinePrimary"
        android:layout_width="wrap_content"
        android:textAllCaps="false"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/reload" />

</LinearLayout>