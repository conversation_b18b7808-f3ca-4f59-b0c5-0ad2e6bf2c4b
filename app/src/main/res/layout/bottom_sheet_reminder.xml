<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/vw_divider"
            style="@style/Divider"
            android:layout_width="@dimen/_48dp"
            android:layout_height="@dimen/_3dp"
            android:layout_marginTop="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_divider"
            tools:text="Detail Tagihan Listrik" />

        <View
            android:id="@+id/divider1"
            style="@style/Divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_margin="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <include
            android:id="@+id/include_listrik_detail"
            layout="@layout/ppob_confirmation_listrik_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_6dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/divider1"
            tools:visibility="visible" />

        <include
            android:id="@+id/pulsa_postpaid_detail"
            layout="@layout/layout_pulsa_postpaid_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_6dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/divider1" />

        <include
            android:id="@+id/pdam_detail"
            layout="@layout/layout_pdam_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_6dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/divider1" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_details"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="include_listrik_detail, pulsa_postpaid_detail, pdam_detail" />

        <TextView
            android:id="@+id/tv_selling_price"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/selling_price"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/br_details" />

        <TextView
            android:id="@+id/tv_opsional"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/opsional"
            android:textColor="@color/black_20"
            app:layout_constraintBottom_toBottomOf="@+id/tv_selling_price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_selling_price" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_amount"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="0.5dp"
            app:hintEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_selling_price"
            app:passwordToggleDrawable="@null">

            <com.bukuwarung.baseui.CurrencyEditText
                android:id="@+id/input_nominal"
                style="@style/Heading3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/indonesian_rupee"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:singleLine="true"
                android:textColorHint="@color/black_20" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/tv_harga_modal"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/customer_bill"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/til_amount" />

        <TextView
            android:id="@+id/tv_amount"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:textColor="@color/red_80"
            app:layout_constraintBottom_toBottomOf="@+id/tv_harga_modal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_harga_modal"
            tools:text="Rp 96.000" />

        <View
            android:id="@+id/vw_divider_2"
            android:layout_width="wrap_content"
            android:layout_height="2dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:background="@drawable/horizontal_dotted_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_total_payment" />

        <TextView
            android:id="@+id/tv_transaction_fees_message"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/transaction_fees"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_harga_modal" />

        <TextView
            android:id="@+id/tv_admin_fee"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_4dp"
            android:background="@drawable/strike_through"
            android:text="@string/free"
            android:textColor="@color/black_20"
            app:layout_constraintBottom_toBottomOf="@id/tv_transaction_fees_message"
            app:layout_constraintEnd_toStartOf="@id/tv_discounted_fee"
            app:layout_constraintTop_toTopOf="@id/tv_transaction_fees_message" />

        <TextView
            android:id="@+id/tv_discounted_fee"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/free"
            app:layout_constraintBottom_toBottomOf="@+id/tv_transaction_fees_message"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_transaction_fees_message" />

        <TextView
            android:id="@+id/tv_discount"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/discount"
            android:textColor="@color/blue_60"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_fees_message" />

        <TextView
            android:id="@+id/tv_discount_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:textColor="@color/blue_60"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tv_discount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_discount" />

        <TextView
            android:id="@+id/tv_total_payment"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/label_total_payment"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_discount" />

        <TextView
            android:id="@+id/tv_total_amount"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_total_payment" />

        <TextView
            android:id="@+id/tv_profit_text"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_4dp"
            android:text="@string/profit_text"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider_2" />

        <TextView
            android:id="@+id/tv_profit"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tv_profit_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_profit_text"
            tools:text="Rp4.000" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_primary_action"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/send_reminder"
            app:icon="@drawable/ic_whatsapp_black"
            app:layout_constraintTop_toBottomOf="@id/tv_profit_text" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_secondary_action"
            style="@style/ButtonOutline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:text="@string/make_transaction"
            app:iconTint="@color/new_yellow"
            app:layout_constraintTop_toBottomOf="@id/btn_primary_action" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>