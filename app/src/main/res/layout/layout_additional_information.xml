<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="@dimen/_25dp"
            android:layout_height="@dimen/_25dp"
            android:layout_marginTop="@dimen/_20dp"
            android:fontFamily="@font/roboto"
            android:src="@drawable/ic_back"
            android:layout_gravity="top"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_24dp"
            android:layout_marginStart="@dimen/_24dp"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_toEndOf="@+id/back_btn"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:layout_gravity="top"
            android:lineHeight="@dimen/_26dp"
            android:lineSpacingExtra="@dimen/text_8sp"
            android:maxLines="1"
            android:text="@string/additional_info"
            android:textColor="@color/white"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/back_btn"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UnusedAttribute" />

    </androidx.appcompat.widget.Toolbar>


        <View
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_20dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintStart_toStartOf="parent"
            android:background="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"/>
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:id="@+id/content_scroll_view"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toTopOf="@+id/button_divider"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/white_background_top_radius_16"
            >

            <!-- production -->
        <include
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:id="@+id/tv_make_your_own_product"
            layout="@layout/additional_info_section_heading"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_margin="@dimen/_16dp"/>

            <!-- productBuyer -->
        <include
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/self_production"
            layout="@layout/layout_additional_info_button_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_make_your_own_product"
            android:layout_margin="@dimen/_16dp"/>

        <include
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/buy_resell"
            app:layout_constraintEnd_toEndOf="parent"
            layout="@layout/layout_additional_info_button_item"
            android:layout_marginStart="@dimen/_8dp"
            app:layout_constraintStart_toEndOf="@id/self_production"
            app:layout_constraintTop_toBottomOf="@id/tv_make_your_own_product"
            android:layout_margin="@dimen/_16dp"/>

        <include
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/sell_from_manufactures"
            layout="@layout/layout_additional_info_button_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/self_production"
            android:layout_margin="@dimen/_16dp"/>

        <include
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:id="@+id/tv_who_buys"
            layout="@layout/additional_info_section_heading"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sell_from_manufactures"
            android:layout_margin="@dimen/_16dp"/>

        <include
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/other_sellers"
            layout="@layout/layout_additional_info_button_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_who_buys"
            android:layout_margin="@dimen/_16dp"/>

        <include
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/direct_buyers"
            layout="@layout/layout_additional_info_button_item"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/other_sellers"
            app:layout_constraintTop_toBottomOf="@id/tv_who_buys"
            android:layout_margin="@dimen/_16dp"/>

        <include
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_margin="@dimen/_16dp"
            android:id="@+id/monthly_turn_over"
            layout="@layout/additional_info_section_heading"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/other_sellers"/>

        <include
            android:id="@+id/monthly_turn_over_dropdown"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            layout="@layout/layout_turn_over_dropdown"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/monthly_turn_over"/>

        <include
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_margin="@dimen/_16dp"
            android:id="@+id/layout_since_when"
            layout="@layout/additional_info_section_heading"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/monthly_turn_over_dropdown"/>

        <include
            android:id="@+id/layout_since_when_dropdown"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            layout="@layout/layout_additional_info_dropdown_item"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_since_when"/>

        <include
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_margin="@dimen/_16dp"
            android:id="@+id/no_of_branch"
            layout="@layout/additional_info_section_heading"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_since_when_dropdown"/>

        <include
            android:id="@+id/no_of_branch_dropdown"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            layout="@layout/layout_branch_store"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/no_of_branch"/>

        <include
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_margin="@dimen/_16dp"
            android:id="@+id/no_of_employees"
            layout="@layout/additional_info_section_heading"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/no_of_branch_dropdown"/>

        <include
            android:id="@+id/no_of_employees_dropdown"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_80dp"
            layout="@layout/layout_number_of_employees"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/no_of_employees"/>
         </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

        <View
            android:id="@+id/button_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            app:layout_constraintBottom_toTopOf="@id/btn_save" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="@string/save"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>