<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner">

    <TextView
        android:id="@+id/change_category_label"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginTop="24dp"
        android:text="@string/select_expenditure_category"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/closeDialog"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        android:layout_marginTop="29dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:src="@drawable/close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/category_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="20dp"
        android:paddingTop="32dp"
        android:paddingRight="20dp"
        tools:listitem="@android:layout/simple_list_item_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/change_category_label" />


</androidx.constraintlayout.widget.ConstraintLayout>