<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mainContainer"
    android:background="@color/white"
    android:layout_width="300dp"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:paddingTop="@dimen/_16dp"
    android:paddingBottom="@dimen/_16dp">

    <TextView
        android:id="@+id/customerCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Pelanggan 1/3"
        android:textColor="#5C5C5C"
        android:textSize="16sp"
        android:layout_marginBottom="@dimen/_16dp"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        app:cardCornerRadius="8dp"
        android:elevation="@dimen/_8dp">

        <LinearLayout
            android:id="@+id/dateContainer"
            android:background="#F1F1F1"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="@dimen/_16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp">

                <LinearLayout
                    android:id="@+id/pic"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/photo"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/firstLetter"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/oval_0"
                        android:backgroundTint="#66BDFF"
                        android:fontFamily="@font/roboto"
                        android:gravity="center"
                        android:text="A"
                        android:textColor="@color/white"
                        android:textSize="22sp"
                        android:visibility="gone" />
                </LinearLayout>

<!--                <TextView-->
<!--                    android:id="@+id/firstLetter"-->
<!--                    android:layout_width="40dp"-->
<!--                    android:layout_height="40dp"-->
<!--                    android:background="@drawable/oval_0"-->
<!--                    android:backgroundTint="#66BDFF"-->
<!--                    android:fontFamily="@font/roboto"-->
<!--                    android:gravity="center"-->
<!--                    android:text="@string/default_placeholder"-->
<!--                    android:maxLength="2"-->
<!--                    android:textAllCaps="true"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="16px"-->
<!--                    android:textStyle="bold" />-->

                <TextView
                    android:id="@+id/nameText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="Agus Yusuf"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="bkwarung"
                    android:textColor="@color/heading_text"
                    android:textSize="16dp"
                    app:autoSizeMaxTextSize="16sp"
                    app:autoSizeMinTextSize="12sp"
                    app:autoSizeStepGranularity="1sp"
                    android:layout_marginStart="@dimen/_16dp"/>

            </LinearLayout>

            <TextView
                android:id="@+id/customerDebtAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Utang: Rp 200.000"
                android:textColor="#8D8D8D"
                android:textSize="14sp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"/>

            <TextView
                android:id="@+id/customerDebtInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="#FF4343"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="Akan ditagihkan pada 20 Mei 2020" />

            <CalendarView
                android:id="@+id/calendarView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:theme="@style/CalendarViewStyle"
                android:dateTextAppearance="@style/CalenderViewDate"
                android:weekDayTextAppearance="@style/CalenderViewDate"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:layout_marginBottom="@dimen/_8dp"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>