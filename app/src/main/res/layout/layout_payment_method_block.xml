<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/blue_80">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_bank"
        android:layout_width="@dimen/_56dp"
        android:layout_height="@dimen/_45dp"
        app:cardCornerRadius="@dimen/_4dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_bank"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srcCompat="@drawable/ic_bank" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_bank"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body3"
        android:layout_marginStart="@dimen/_10dp"
        android:textColor="@color/white"
        tools:text="Bayar via E-Wallet - OVO"
        android:text="@string/pay_via"
        app:layout_constraintEnd_toStartOf="@id/iv_arrow"
        app:layout_constraintStart_toEndOf="@+id/cv_bank"
        app:layout_constraintTop_toTopOf="@id/cv_bank"/>

    <TextView
        android:id="@+id/tv_bank_subtext"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/SubHeading1"
        android:textColor="@color/white"
        android:layout_marginStart="@dimen/_10dp"
        tools:text="Klik di sini untuk bayar"
        android:layout_marginTop="@dimen/_2dp"
        app:layout_constraintEnd_toStartOf="@+id/iv_arrow"
        app:layout_constraintStart_toEndOf="@+id/cv_bank"
        app:layout_constraintTop_toBottomOf="@id/tv_bank"/>

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_arrow_white"
        android:layout_marginEnd="@dimen/_20dp"
        android:layout_marginTop="@dimen/_6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_bank"/>

</androidx.constraintlayout.widget.ConstraintLayout>