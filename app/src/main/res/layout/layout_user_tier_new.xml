<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_home"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black5"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider"
        android:layout_width="@dimen/_1dp"
        android:layout_height="@dimen/_0dp"
        android:layout_marginVertical="@dimen/_8dp"
        android:background="@color/black_10"
        app:layout_constraintStart_toStartOf="@+id/layout_points"
        app:layout_constraintTop_toTopOf="@+id/layout_points"
        app:layout_constraintBottom_toBottomOf="@+id/layout_points"
        />

    <include
        android:id="@+id/layout_membership_status"
        layout="@layout/layout_user_tier_item_new"
        android:layout_width="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.5"
        android:stateListAnimator="@null" />

    <include
        android:id="@+id/layout_points"
        layout="@layout/layout_user_tier_item_new"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.5"
        android:stateListAnimator="@null" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_10dp"
        app:srcCompat="@mipmap/next_grey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/layout_points"
        app:layout_constraintBottom_toBottomOf="@id/layout_points"
        android:stateListAnimator="@null"
        android:clickable="false"/>

    <View
        android:id="@+id/vw_home"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/layout_membership_status"
        />

</androidx.constraintlayout.widget.ConstraintLayout>