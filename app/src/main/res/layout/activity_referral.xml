<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ActionBarAppTheme"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="fill_parent"
            android:layout_height="?actionBarSize"
            app:contentInsetLeft="0.0dip"
            app:contentInsetStart="0.0dip"
            app:contentInsetStartWithNavigation="0.0dip">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/backBtn"
                    android:layout_width="25dp"
                    android:layout_height="@dimen/_0dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="16dp"

                    android:gravity="center"
                    android:src="@mipmap/back_white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/screen_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:text="@string/referral_program"
                    android:textColor="#ffffff"
                    android:textSize="18.0dip"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/backBtn"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:background="@drawable/bg_rounded_rectangle_blue_90"
                    android:drawablePadding="@dimen/_16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:paddingLeft="@dimen/_16dp"
                    android:paddingRight="@dimen/_2dp"
                    android:text="@string/referral_text_tnc_popup"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_16sp"
                    app:drawableEndCompat="@drawable/ic_info_referral"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/ib_info"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_0dp"
                    android:background="@null"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/tv_info"
                    app:layout_constraintStart_toStartOf="@id/tv_info"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_buku_point" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:id="@+id/content_scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/_16dp">

            <ImageView
                android:id="@+id/referralImage"
                android:layout_width="match_parent"
                android:layout_height="140dp"
                android:scaleType="fitCenter"
                android:src="@drawable/referral_illustration"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/referral_header_txt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:gravity="center_horizontal"
                android:text="@string/referral_title"
                android:textAlignment="center"
                android:textColor="@color/black_80"
                android:textSize="@dimen/_18dp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/referralImage" />

            <TextView
                android:id="@+id/referral_subtxt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_20dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="@dimen/_20dp"
                android:layout_marginBottom="10dp"
                android:gravity="center_horizontal"
                android:text="@string/referral_subtext"
                android:textAlignment="center"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintTop_toBottomOf="@+id/referral_header_txt" />

            <TextView
                android:id="@+id/tv_tnc"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/see_bonus"
                android:textColor="@color/blue_60"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/referral_subtxt" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/referral_blue_gradient_bg"
                android:elevation="-1dp"
                app:layout_constraintBottom_toBottomOf="@id/referralLayout"
                app:layout_constraintBottom_toTopOf="@id/referralLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/referralImagePreview"
                android:layout_width="500dp"
                android:layout_height="500dp"
                android:scaleType="fitCenter"
                android:src="@drawable/referral_share_payment"
                android:visibility="invisible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/check"
                android:layout_width="match_parent"
                android:layout_height="126dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="110dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/white_drawable_round_8dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/referralLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:paddingBottom="20dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_tnc">

                <TextView
                    android:id="@+id/referralTitle"
                    style="@style/SubHeading1"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:text="@string/your_referral_code"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/referralBonusMessage"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/referral_bonus"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/referralBonusAmount"
                    style="@style/Heading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:drawableLeft="@drawable/ic_coin_symbol"
                    android:drawablePadding="4dp"
                    android:text="100.000"
                    android:textColor="@color/green_80"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/referralBonusMessage" />

                <TextView
                    android:id="@+id/referralCodeMessage"
                    style="@style/Heading3"
                    android:background="@drawable/bg_yellow_corner_8dp_stroke_yellow_dotted"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:textColor="@color/blue_60"
                    tools:text="BWDL2022"
                    android:padding="@dimen/_10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/br_referral"
                    app:drawableRightCompat="@drawable/ic_copy" />

                <androidx.constraintlayout.widget.Barrier
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:id="@+id/br_referral"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="referralBonusMessage,referralTitle"/>


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/inviteFriendsBtn"
                    style="@style/DefaultMaterialButtonStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="20dp"
                    android:fontFamily="@font/roboto"
                    android:letterSpacing="0.05"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="@string/send_referral"
                    android:textAllCaps="false"
                    android:textColor="@color/cta_text_button"
                    android:textSize="16sp"
                    app:icon="@drawable/ic_share"
                    app:iconTint="@color/cta_text_button"
                    app:iconGravity="textStart"
                    android:textStyle="bold"
                    app:backgroundTint="@color/buku_CTA"
                    app:layout_constraintTop_toBottomOf="@+id/referralCodeMessage" />

                <TextView
                    android:id="@+id/tv_invited"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="Teman yang Diundang"
                    android:textColor="@color/blue_60"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/inviteFriendsBtn" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_referral"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="@id/referralLayout"
                app:layout_constraintStart_toStartOf="@id/referralLayout"
                app:layout_constraintTop_toBottomOf="@id/referralLayout">

                <EditText
                    android:id="@+id/enterReferral"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@drawable/layout_color_change"
                    android:hint="@string/enter_referral_hint"
                    android:padding="@dimen/_16dp"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="0.5dp"
                    app:layout_constraintEnd_toStartOf="@+id/confirmationBtn"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_goneMarginStart="@dimen/_16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/confirmationBtn"
                    style="@style/DefaultMaterialButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginBottom="20dp"
                    android:fontFamily="@font/roboto"
                    android:letterSpacing="0.05"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:text="@string/confirmation"
                    android:textAllCaps="false"
                    android:textColor="@color/cta_text_button"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/black_20"
                    app:layout_constraintBottom_toBottomOf="@id/enterReferral"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/enterReferral" />

                <TextView
                    android:id="@+id/tv_successful_referral_message"
                    style="@style/Body3"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:gravity="center"
                    android:text="Yey! Kamu dapat 10 poin karena sudah input referral"
                    android:textColor="@color/referral_success"
                    app:drawableStartCompat="@drawable/ic_buku_point"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/confirmationBtn" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/img_phone_book"
                android:layout_width="128dp"
                android:layout_height="128dp"
                android:layout_marginTop="@dimen/_16dp"
                android:src="@drawable/ic_phonebook_new"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_referral" />

            <TextView
                android:id="@+id/invite_friends_faster"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:gravity="center"
                android:text="@string/invite_friends_faster"
                android:textColor="@color/black"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/img_phone_book" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_allow_contact_access"
                style="@style/ButtonFill"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:text="@string/allow_contact_access"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/invite_friends_faster" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/group_phonebook"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="img_phone_book, btn_allow_contact_access"
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_search_contact"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:boxStrokeColor="@color/black_20"
                app:endIconDrawable="@drawable/close"
                app:endIconMode="clear_text"
                app:endIconTint="@color/black_40"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_referral"
                app:startIconDrawable="@drawable/ic_icon_search_new"
                app:startIconTint="@color/black_40">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search_contact"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/find_friend"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/black_80"
                    android:textColorHint="@color/black_40" />

            </com.google.android.material.textfield.TextInputLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_contact"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:minHeight="200dp"
                app:layout_constraintTop_toBottomOf="@id/til_search_contact" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/group_contact_list"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="rv_contact, til_search_contact"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</LinearLayout>