<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.button.MaterialButton xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/btn_view_all"
    style="@style/ButtonOutline.White"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_16dp"
    android:text="@string/view_all"
    android:background="@color/black_5"
    app:icon="@drawable/ic_chevron_down"
    app:iconGravity="textEnd"
    app:iconPadding="@dimen/_16dp"
    app:iconTint="@color/black" />