<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:titleTextAppearance="@style/Heading2"
        app:titleTextColor="@color/white"
        tools:title="@string/item_list" />


    <LinearLayout
        android:id="@+id/ll_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_8dp">

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_8dp"
            android:layout_weight="1"
            app:boxBackgroundColor="@color/black_5"
            app:boxCornerRadiusBottomEnd="@dimen/_4dp"
            app:boxCornerRadiusBottomStart="@dimen/_4dp"
            app:boxCornerRadiusTopEnd="@dimen/_4dp"
            app:boxCornerRadiusTopStart="@dimen/_4dp"
            app:boxStrokeWidth="@dimen/_0dp"
            app:boxStrokeWidthFocused="@dimen/_0dp"
            app:endIconDrawable="@drawable/ic_mtrl_chip_close_circle"
            app:endIconMode="clear_text"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:passwordToggleDrawable="@null"
            app:hintTextAppearance="@style/Body2"
            app:hintTextColor="@color/black_40"
            app:startIconDrawable="@drawable/ic_icon_search_new"
            app:startIconTint="@color/black_60">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/search_product_from_catalog"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/btn_filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/_8dp"
            android:padding="@dimen/_8dp"
            android:text="@string/filter"
            app:drawableTint="@color/black_60"
            app:drawableTopCompat="@drawable/ic_filter" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_5" />

    <TextView
        android:id="@+id/tv_sub_category"
        style="@style/SubHeading1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_24dp"
        android:layoutDirection="rtl"
        android:padding="@dimen/_16dp"
        tools:text="Makanan dan Minuman" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_5" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_product"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone"
        tools:itemCount="5"
        tools:listitem="@layout/catalog_product_item" />

    <include
        android:id="@+id/placeholder"
        layout="@layout/list_item_placeholder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.bukuwarung.activities.addcustomer.ErrorScreen
        android:id="@+id/error_screen"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:background="@color/new_divider" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_submit"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:enabled="false"
            android:text="@string/add_product"
            tools:enabled="true" />

    </LinearLayout>

</androidx.appcompat.widget.LinearLayoutCompat>