<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingTop="@dimen/_10dp"
    android:paddingBottom="@dimen/_20dp">

    <View
        android:id="@+id/vw_close_bar"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:background="@drawable/background_filled_black10_border_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_26dp"
        android:text="@string/saldo_balance_limit_details"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_close_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_limits"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@drawable/bg_solid_black5_corner_8dp"
        android:paddingHorizontal="@dimen/_12dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <TextView
            android:id="@+id/tv_remaining_daily_limits"
            style="@style/Body3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black_80"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Limit harian tersisa: Rp147.500.000" />

        <TextView
            android:id="@+id/tv_remaining_monthly_limits"
            style="@style/Body3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:textColor="@color/black_80"
            app:layout_constraintTop_toBottomOf="@id/tv_remaining_daily_limits"
            tools:text="Limit bulanan tersisa: Rp5.000.500.000" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/saldo_balance_limits_message"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_limits" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_advanced_limits_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_solid_blue90_corner_top_8dp"
        android:paddingHorizontal="@dimen/_20dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_advanced_limits_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_advanced_limits_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_advanced_limits_title"
            app:srcCompat="@drawable/ic_kyc_badge_premium" />

        <TextView
            android:id="@+id/tv_advanced_limits_title"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:text="@string/advanced_saldo_limits"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_advanced_limits_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_advanced_limits"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_solid_blue5_corners_bottom_8dp"
        android:paddingHorizontal="@dimen/_8dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_advanced_limits_title">

        <TextView
            android:id="@+id/tv_advanced_limits_daily_title"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/daily_limit"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toStartOf="@id/tv_advanced_limits_monthly_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_advanced_limits_daily_value"
            style="@style/SubHeading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_4dp"
            android:gravity="center"
            app:layout_constraintEnd_toStartOf="@id/tv_advanced_limits_monthly_value"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_advanced_limits_daily_title"
            tools:text="Rp1,5 Juta" />

        <TextView
            android:id="@+id/tv_advanced_limits_monthly_title"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/monthly_limit"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toStartOf="@id/tv_advanced_limits_hold_title"
            app:layout_constraintStart_toEndOf="@id/tv_advanced_limits_daily_title"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_advanced_limits_monthly_value"
            style="@style/SubHeading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_4dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_advanced_limits_hold_value"
            app:layout_constraintStart_toEndOf="@id/tv_advanced_limits_daily_value"
            app:layout_constraintTop_toBottomOf="@id/tv_advanced_limits_monthly_title"
            tools:text="Rp50 Juta" />

        <TextView
            android:id="@+id/tv_advanced_limits_hold_title"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/saldo_hold_limit"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_advanced_limits_monthly_title"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_advanced_limits_hold_value"
            style="@style/SubHeading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_4dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_advanced_limits_monthly_value"
            app:layout_constraintTop_toBottomOf="@id/tv_advanced_limits_hold_title"
            tools:text="Rp10 Juta" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_supreme_limits_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_solid_yellow60_corner_top_8dp"
        android:paddingHorizontal="@dimen/_20dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_advanced_limits">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_supreme_limits_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_supreme_limits_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_supreme_limits_title"
            app:srcCompat="@drawable/ic_kyc_badge_priority" />

        <TextView
            android:id="@+id/tv_supreme_limits_title"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:text="@string/supreme_saldo_limits"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_supreme_limits_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_supreme_limits"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_solid_yellow5_corners_bottom_8dp"
        android:paddingHorizontal="@dimen/_8dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_supreme_limits_title">

        <TextView
            android:id="@+id/tv_supreme_limits_daily_title"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/daily_limit"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toStartOf="@id/tv_supreme_limits_monthly_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_supreme_limits_daily_value"
            style="@style/SubHeading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_4dp"
            android:gravity="center"
            app:layout_constraintEnd_toStartOf="@id/tv_supreme_limits_monthly_value"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_supreme_limits_daily_title"
            tools:text="Rp200 Juta" />

        <TextView
            android:id="@+id/tv_supreme_limits_monthly_title"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/monthly_limit"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toStartOf="@id/tv_supreme_limits_hold_title"
            app:layout_constraintStart_toEndOf="@id/tv_supreme_limits_daily_title"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_supreme_limits_monthly_value"
            style="@style/SubHeading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_4dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_supreme_limits_hold_value"
            app:layout_constraintStart_toEndOf="@id/tv_supreme_limits_daily_value"
            app:layout_constraintTop_toBottomOf="@id/tv_supreme_limits_monthly_title"
            tools:text="Rp20 Miliar" />

        <TextView
            android:id="@+id/tv_supreme_limits_hold_title"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/saldo_hold_limit"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_supreme_limits_monthly_title"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_supreme_limits_hold_value"
            style="@style/SubHeading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_4dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_supreme_limits_monthly_value"
            app:layout_constraintTop_toBottomOf="@id/tv_supreme_limits_hold_title"
            tools:text="Rp200 Juta" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_understand"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_32dp"
        android:text="@string/understand"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_supreme_limits" />

</androidx.constraintlayout.widget.ConstraintLayout>
