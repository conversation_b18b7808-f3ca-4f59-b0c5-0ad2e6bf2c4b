<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="beforeDescendants"
  >


    <EditText
        android:id="@+id/search_input"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/rounded_rectangle_customer_name"
        android:drawableStart="@drawable/ic_pelanggan"
        android:drawablePadding="@dimen/_8dp"
        android:hint="@string/add_contact_hint"
        android:textColorHint="@color/black_20"
        android:maxLength="100"
        android:padding="12dp"
        android:textColor="@color/black_80"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@id/add_contact_parent_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:focusable="false"
        android:clickable="false" />



    <LinearLayout
        android:id="@+id/add_contact_parent_layout"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/search_input"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/search_input"

        >

        <ImageView
            android:id="@+id/add_contact_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/_8dp"
            android:background="@drawable/background_circular_blue"
            android:padding="@dimen/_8dp"
            android:src="@drawable/ic_contact" />

        <TextView
            android:id="@+id/add_contact"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="4dp"
            android:gravity="center"
            android:text="@string/import_contact"
            android:textColor="@color/blue_60"
            android:textSize="@dimen/text_14sp"

            />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>