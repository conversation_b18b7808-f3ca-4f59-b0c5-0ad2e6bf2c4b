<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorBackground"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/transaction_detail_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary">

            <ImageView
                android:id="@+id/close"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_centerVertical="true"
                android:padding="12dp"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/transaction_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="12dp"
                android:layout_toRightOf="@+id/close"
                android:fontFamily="@font/roboto"
                android:gravity="center_horizontal"
                android:textColor="#ffffff"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraint_layout_view_transaction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:scrollbars="vertical"
                android:visibility="visible">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/round_corners_bottom_white_bg"
                    android:paddingBottom="@dimen/_16dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:id="@+id/formLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <!--Amount Input-->
                        <include
                            android:id="@+id/transaction_input_amount_layout"
                            layout="@layout/credit_amount_input"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp" />

                        <!--Transaction Note-->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:background="@drawable/new_edittext_bg"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/transaction_note_icon"
                                android:layout_width="@dimen/form_icon_size"
                                android:layout_height="@dimen/form_icon_size"
                                android:layout_marginEnd="@dimen/_8dp"
                                android:src="@drawable/ic_pencil_note_green"
                                app:tint="@color/colorPrimary" />

                            <EditText
                                android:id="@+id/transaction_note"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@null"
                                android:fontFamily="@font/roboto"
                                android:hint="@string/transaction_note_hint"
                                android:inputType="none|text|textCapSentences|textMultiLine"
                                android:lineSpacingExtra="5sp"
                                android:scrollbars="vertical"
                                android:textColor="@color/black_80"
                                android:textColorHint="@color/black_20"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <!--Date Input-->
                        <LinearLayout
                            android:id="@+id/rl_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:background="@drawable/new_edittext_bg"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/transaction_date_icon"
                                android:layout_width="@dimen/form_icon_size"
                                android:layout_height="@dimen/form_icon_size"
                                android:layout_marginEnd="@dimen/_8dp"
                                android:src="@drawable/ic_date_range_green"
                                app:tint="@color/colorPrimary" />

                            <TextView
                                android:id="@+id/transaction_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@null"
                                android:fontFamily="@font/roboto"
                                android:lineSpacingExtra="5sp"
                                android:maxLines="5"
                                android:scrollbars="vertical"
                                android:textColor="#666666"
                                android:textSize="16.3sp" />

                        </LinearLayout>

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cbSendNewTransaction"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:text="@string/send_sms_notification_to"
                            android:textColor="@color/black_80" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:background="#EEE" />

                        <!--Preview message-->
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/svSMSPreview"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/tvPreviewLabel"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto"
                                android:text="@string/sms_notification_that_will_be_received_by_the_customer"
                                android:textColor="@color/black_60"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.constraintlayout.widget.Guideline
                                android:id="@+id/guideline"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                app:layout_constraintGuide_percent="0.25" />

                            <TextView
                                android:id="@+id/tvSmsPreview"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/_8dp"
                                android:background="@drawable/sms_bubble"
                                android:fontFamily="@font/roboto"
                                android:paddingStart="32dp"
                                android:paddingTop="@dimen/_16dp"
                                android:paddingEnd="32dp"
                                android:paddingBottom="@dimen/_16dp"
                                android:textColor="@color/black_80"
                                android:textSize="14sp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="@id/guideline"
                                app:layout_constraintTop_toBottomOf="@id/tvPreviewLabel"
                                tools:text="Bapak/Ibu Rano Setiawan, Anda masih memiliki transaksi belum lunas sebesar Rp200.000. Silakan lakukan pembayaran.\n\nPesan ini dikirim melalui Aplikasi BukuWarung https://bukuwarung.com/y?ae=k068GM31J9" />

                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </LinearLayout>


                </RelativeLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_save_transaction"
                style="@style/DefaultMaterialButtonStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_8dp"
                android:gravity="center"
                android:textAllCaps="false"
                android:padding="12dp"
                android:text="@string/save"
                android:textColor="@color/cta_button_text"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="4dp"
                app:rippleColor="@color/black_40" />

            <!--action button-->
            <LinearLayout
                android:id="@+id/action_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_8dp"
                android:orientation="vertical"
                android:visibility="gone">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/shareBtn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:layout_marginBottom="8dp"
                    android:gravity="center"
                    android:padding="12dp"
                    android:text="@string/share_transaction"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/buku_CTA"
                    app:cornerRadius="4dp"
                    app:icon="@drawable/ic_whatsapp_new"
                    app:iconGravity="textStart"
                    app:iconSize="20dp"
                    app:iconTint="@color/white" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/deleteBtn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:gravity="center"
                    android:padding="12dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/out_red"
                    app:cornerRadius="4dp"
                    app:icon="@drawable/delete_red"
                    app:iconGravity="textStart"
                    app:iconTint="@color/white" />
            </LinearLayout>


            <com.bukuwarung.keyboard.CustomKeyboardView
                android:id="@+id/keyboardView"
                android:layout_width="match_parent"
                android:layout_height="245dp"
                android:visibility="gone" />

        </FrameLayout>

    </LinearLayout>

    <include layout="@layout/transaction_creation_success_animation_layout" />

</FrameLayout>