<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/entriesLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:foreground="?attr/selectableItemBackground"
    android:background="?attr/selectableItemBackground">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16dp"
        >


        <ImageView
            android:id="@+id/nav_icon"
            android:layout_width="@dimen/_50dp"
            android:layout_height="@dimen/_50dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="@drawable/background_circular_white48"
            android:padding="@dimen/_12dp"
            android:src="@drawable/ic_business_big"
            app:tint="@color/heading_text" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/lengkapi_profil_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_8dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_centerInParent="true"
            android:layout_toStartOf="@+id/iv_daily_update_icon"
            android:layout_toEndOf="@+id/nav_icon">

            <TextView
                android:id="@+id/name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                style="@style/Heading3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/businessProfileEntryLayout"
                tools:text="test" />


            <LinearLayout
                android:id="@+id/businessProfileEntryLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="@+id/name"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:visibility="visible"
                >

                <TextView
                    android:id="@+id/tv_profile_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginRight="@dimen/_8dp"
                    android:padding="@dimen/_2dp"
                    style="@style/Body3"
                    android:background="@drawable/round_rectangle_4dp"
                    app:tint="@color/black_80"
                    tools:backgroundTint="@color/yellow"
                    tools:text="50%" />

                <TextView
                    android:id="@+id/tv_business_profile_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:textColor="@color/black_60"
                    android:textSize="@dimen/text_12sp"
                    app:tint="@color/heading_text"
                    android:text="@string/complete_profile"
                    android:drawablePadding="@dimen/_16dp"
                    />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/forward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_margin="16dp"
            android:layout_toStartOf="@+id/iv_daily_update_icon"
            android:background="@drawable/oval_0"
            android:backgroundTint="@color/subheader"
            android:scaleType="center"
            app:tint="@color/subheader" />

        <ImageView
            android:id="@+id/iv_daily_update_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            app:srcCompat="@drawable/ic_daily_update_icon" />
    </RelativeLayout>

</FrameLayout>
