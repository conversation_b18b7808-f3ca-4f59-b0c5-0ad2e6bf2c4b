<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/ll_pin"
        layout="@layout/layout_enter_pin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/ll_confirm_pin"
        layout="@layout/layout_enter_pin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_pin" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_verify"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:paddingVertical="@dimen/dimen_11dp"
        android:text="@string/simpan"
        android:textColor="@color/black_80"
        android:textAppearance="@style/Heading3"
        app:cornerRadius="@dimen/dimen_10dp"
        app:layout_constraintTop_toBottomOf="@id/ll_confirm_pin" />

</androidx.constraintlayout.widget.ConstraintLayout>