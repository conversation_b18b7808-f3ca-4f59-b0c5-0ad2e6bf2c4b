<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottomsheet_rounded"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/consent_statement"
        app:layout_constraintEnd_toStartOf="@id/btn_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/btn_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/close"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        style="@style/Body2"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="@string/terms_and_conditions" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_agree"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/i_agree"
        app:layout_constraintTop_toBottomOf="@id/tv_content" />

</androidx.constraintlayout.widget.ConstraintLayout>