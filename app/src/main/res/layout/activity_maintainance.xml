<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_height="match_parent"
    android:background="@color/white"
    >


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingBottom="@dimen/_16dp"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="500dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                android:background="@color/white"
                >


                <ImageView
                    android:id="@+id/imageView5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/reminder_app_logo"
                    android:layout_marginTop="@dimen/_40dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/imageView6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_75dp"
                    android:src="@drawable/ic_maintenance"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imageView5" />


                <TextView
                    android:id="@+id/maintenance_title"
                    style="@style/Heading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/maintenance_title"
                    android:layout_marginTop="@dimen/_26dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imageView6" />

                <TextView
                    android:id="@+id/maintenance_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/maintenance_subtitle"
                    android:layout_marginTop="@dimen/_4dp"
                    android:paddingHorizontal="@dimen/_16dp"
                    android:textSize="@dimen/_14dp"
                    android:gravity="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/maintenance_title" />

            </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>


    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_alignParentBottom="true"
        >


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_return_home"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:text="@string/return_to_home" />
    </RelativeLayout>

</RelativeLayout>