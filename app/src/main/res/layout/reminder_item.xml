<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_category"
        android:layout_width="@dimen/_45dp"
        android:layout_height="@dimen/_45dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_electricity" />

    <TextView
        android:id="@+id/tv_name"
        style="@style/Body1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:layout_marginTop="@dimen/_16dp"
        android:drawablePadding="@dimen/_10dp"
        android:ellipsize="end"
        android:maxLines="1"
        app:drawableStartCompat="@drawable/ic_favourite_fill"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintStart_toEndOf="@id/iv_category"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_due_date"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toTopOf="@id/tv_error"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintStart_toEndOf="@id/iv_category"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />


    <TextView
        android:id="@+id/tv_error"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/rounded_red_rect_4dp"
        android:drawablePadding="@dimen/_10dp"
        android:padding="@dimen/_8dp"
        android:text="@string/failed_transaction_info"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_error"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_due_date" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="btn_remind, btn_check, btn_pay" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_remind"
        style="@style/ButtonFill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingTop="@dimen/_4dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_4dp"
        android:text="@string/remind_button"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@color/black_40" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_check"
        style="@style/Button.OutlinePrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingTop="@dimen/_4dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_4dp"
        android:text="@string/bt_cek"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@color/black_40" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_pay"
        style="@style/ButtonFill.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingTop="@dimen/_4dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_4dp"
        android:text="@string/pay"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@color/black_40" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <View
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:background="@color/black_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>