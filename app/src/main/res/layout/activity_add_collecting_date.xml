<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_alignParentLeft="true"
                android:layout_width="25dp"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_height="25dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_close" />

            <TextView
                android:id="@+id/title"
                android:layout_marginLeft="24dp"
                android:layout_width="wrap_content"
                android:drawableLeft="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:layout_height="24dp"
                android:layout_toRightOf="@+id/backBtn"
                android:layout_alignParentTop="true"
                android:fontFamily="@font/roboto"
                android:textStyle="bold"
                android:layout_centerVertical="true"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:lineSpacingExtra="8sp"
                android:lineHeight="26dp"
                android:gravity="top"
                android:maxLines="1"
                android:text="@string/collection_add_title"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bannerContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/blue_80"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingVertical="12dp"
                android:visibility="visible"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent">

                <ImageView
                    android:id="@+id/tempoIcon"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:src="@drawable/ic_tempo"
                    android:layout_marginBottom="12dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/tempoTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="Buat tempo utang 3 pelanggan"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:layout_marginStart="@dimen/_16dp"
                    app:layout_constraintStart_toEndOf="@id/tempoIcon"
                    app:layout_constraintTop_toTopOf="parent"/>

                <TextView
                    android:id="@+id/tempoSubtitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="Dapatkan pembayaran utang Rp 600.000 yang lebih cepat"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginBottom="12dp"
                    app:layout_constraintStart_toEndOf="@id/tempoIcon"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tempoTitle"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/dateRV"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:foregroundGravity="center_horizontal"
                app:layout_constraintTop_toBottomOf="@id/bannerContainer"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:visibility="gone"/>

            <ProgressBar
                android:id="@+id/loadingBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:backgroundTint="@color/colorPrimary"/>

        </LinearLayout>

    </ScrollView>

</LinearLayout>