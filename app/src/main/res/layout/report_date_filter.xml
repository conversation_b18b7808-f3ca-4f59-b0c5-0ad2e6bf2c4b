<LinearLayout android:id="@+id/filterLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#F8F9FE"
    android:orientation="vertical"
    android:layout_below="@+id/toolbar"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/dateLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_16dp"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@+id/viewType">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="@dimen/_16dp"
            android:weightSum="2">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/roboto"
                android:gravity="center_vertical"
                android:textColor="@color/body_text"
                android:textSize="14sp"
                android:layout_weight="1"
                android:text="@string/date_caption"
                android:layout_marginLeft="@dimen/_16dp"
                android:textStyle="normal" />


        <RelativeLayout
            android:id="@+id/singleDayLayout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginRight="16dp"
            android:layout_weight="1"
            android:background="@drawable/date_range_background"
            android:orientation="horizontal"
            android:paddingLeft="8dp"
            android:paddingRight="16dp">

            <TextView
                android:id="@+id/specificDate"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/roboto"
                android:gravity="center_vertical"
                android:textColor="#4a4a4a"
                android:textSize="14sp"
                android:drawableLeft="@mipmap/calender_blue"
                android:drawablePadding="6dp"
                android:visibility="gone"
                android:textStyle="normal" />
            <TextView
                android:id="@+id/singleDateDummy"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/roboto"
                android:gravity="center_vertical"
                android:textColor="#4a4a4a"
                android:textSize="14sp"
                android:layout_marginRight="-16dp"
                android:drawablePadding="6dp"
                android:textStyle="normal" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_gravity="right"
                android:tint="@color/colorPrimary"
                android:rotation="90"
                android:src="@drawable/ic_chevron_right" />
            <Spinner
                android:id="@+id/spinner"
                android:background="@color/white"
                android:backgroundTint="@color/white"
                android:theme="@style/spinner_dialog"
                android:layout_marginLeft="-10dp"
                android:layout_marginRight="-16dp"
                android:layout_marginTop="-3dp"
                android:layout_below="@id/singleDateDummy"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </RelativeLayout>

        </LinearLayout>
        <include layout="@layout/report_start_end_date" />

    </LinearLayout>
</LinearLayout>