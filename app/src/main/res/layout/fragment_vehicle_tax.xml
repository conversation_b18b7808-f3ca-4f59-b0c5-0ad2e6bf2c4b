<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/payment_tab_gradient_bg"
            app:layout_constraintBottom_toTopOf="@+id/vw_divider"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_input_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_rounded_rectangle_white_8dp"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent">

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_biller"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:hint="@string/select_region"
                app:layout_constraintTop_toTopOf="parent"
                app:title="@string/select_vehicle_region" />

            <TextView
                android:id="@+id/tv_info"
                style="@style/Body3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_fill_blue_60_outline_solitude"
                android:drawablePadding="@dimen/_10dp"
                android:maxLines="2"
                android:padding="@dimen/_10dp"
                android:textColor="@color/black_60"
                android:visibility="gone"
                app:drawableStartCompat="@drawable/ic_info_blue"
                app:layout_constraintTop_toBottomOf="@+id/biv_biller"
                tools:text="Silakan cek panduan membayar Pajak STNK untuk wilayah Jawa Timur. Lihat panduan" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_payment_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:inputType="text"
                android:visibility="gone"
                app:bottomText="@string/pay_code_hint"
                app:hint="@string/pay_code_example"
                app:layout_constraintTop_toBottomOf="@+id/tv_info"
                app:title="@string/pay_code" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:visibility="gone"
                app:bottomText="@string/vehicle_tax_number_hint"
                app:hint="@string/enter_phone_number_hint"
                app:layout_constraintEnd_toStartOf="@+id/iv_contact"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/biv_payment_code"
                app:title="@string/customer_phone" />

            <ImageView
                android:id="@+id/iv_contact"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/biv_number"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/biv_number"
                app:srcCompat="@drawable/ic_contact_book" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_policy_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:inputType="text"
                app:bottomText="@string/policy_number_hint"
                app:hint="@string/policy_number_example"
                app:layout_constraintTop_toBottomOf="@+id/biv_number"
                app:title="@string/policy_number" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_machine_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:inputType="text"
                app:bottomText="@string/machine_number_hint"
                app:hint="@string/machine_number_example"
                app:layout_constraintTop_toBottomOf="@+id/biv_policy_number"
                app:title="@string/machine_number" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_frame_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:inputType="text"
                app:bottomText="@string/frame_number_hint"
                app:hint="@string/frame_number_example"
                app:layout_constraintTop_toBottomOf="@+id/biv_machine_number"
                app:title="@string/frame_number" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/grp_form1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="biv_policy_number, biv_machine_number, biv_frame_number" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cek"
                style="@style/ButtonFill"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:padding="@dimen/_12dp"
                android:text="@string/bt_cek"
                android:visibility="gone"
                app:cornerRadius="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@+id/biv_frame_number" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/vw_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_5dp"
            android:layout_marginTop="@dimen/_12dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@+id/cl_input_area" />

        <FrameLayout
            android:id="@+id/fl_recent_and_fav"
            android:layout_width="@dimen/_0dp"
            android:layout_height="600dp"
            android:background="@color/black_5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>