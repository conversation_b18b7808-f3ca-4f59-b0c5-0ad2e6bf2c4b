<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    android:id="@+id/mainContainer"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/bg_referral_share"
        android:scaleType="centerCrop"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="30dp"
        android:paddingEnd="30dp"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/mainFrameLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="108dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/mainCardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:cardCornerRadius="@dimen/_8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/referral_share_card_bg"
                    android:orientation="vertical"
                    android:paddingStart="20dp"
                    android:paddingTop="24dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="12dp">

                    <TextView
                        android:id="@+id/businessName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:textColor="@color/white"
                        tools:text="Laundry Rojali"
                        android:text="@string/default_placeholder"
                        android:textSize="24sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/businessOwnerName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:textColor="@color/white"
                        tools:text="Rojali"
                        android:text="@string/default_placeholder"
                        android:textSize="18sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="2dp"
                        android:background="#F1F1F1"
                        android:layout_marginTop="12dp"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingStart="32dp"
                        android:paddingEnd="32dp"
                        android:gravity="center_vertical"
                        android:layout_marginTop="12dp">

                        <ImageView
                            android:id="@+id/trophy"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_trophy_first"/>

                        <TextView
                            android:id="@+id/pointText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/main_referral_point"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            android:layout_marginStart="@dimen/_8dp"
                            app:layout_constraintStart_toEndOf="@id/trophy"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"/>

                        <TextView
                            android:id="@+id/userPoint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            tools:text="120"
                            android:text="@string/default_placeholder"
                            android:textColor="@color/white"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"/>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/oval_0"
                android:backgroundTint="#046BB9"
                android:gravity="center"
                android:padding="@dimen/_8dp"
                android:elevation="8dp">

                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:src="@drawable/ic_business_big"
                    android:tint="@color/white" />

            </LinearLayout>

        </FrameLayout>

        <TextView
            android:id="@+id/downloadText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:text="@string/referral_share_subtitle"
            android:textSize="16sp"
            android:textColor="#222222"
            android:layout_marginTop="20dp"
            android:textAlignment="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mainFrameLayout"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/uploadBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            app:cornerRadius="24dp"
            android:layout_marginBottom="24dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:background="#046BB9"
            android:backgroundTint="#046BB9"
            android:textStyle="bold"
            android:fontFamily="sans-serif"
            android:lineSpacingExtra="6sp"
            android:textColor="@color/white"
            app:icon="@drawable/ic_share_invert"
            app:iconTint="@color/white"
            style="@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon"
            android:text="@string/referral_share_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/downloadText"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>