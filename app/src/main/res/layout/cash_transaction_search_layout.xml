<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/searchLayout"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:layout_gravity="bottom"
    android:background="@color/colorBackground"
    android:fitsSystemWindows="true"
    android:gravity="center_vertical"
    android:visibility="gone"
    app:layout_collapseMode="pin"
    tools:showIn="@layout/tab_layout_expense"
    tools:visibility="visible">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp">

        <ImageView
            android:id="@+id/img_search_icon"
            android:layout_width="24dp"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:src="@drawable/ic_icon_search_new" />

        <EditText
            android:id="@+id/searchQueryBox"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginEnd="8dp"
            android:layout_toStartOf="@+id/clear"
            android:layout_toEndOf="@id/img_search_icon"
            android:hint="@string/search_by_date_hint"
            android:maxLines="1"
            android:textColor="@color/main_text_color"
            android:textColorHint="@color/hint_color"
            android:textSize="16sp"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/clear"
            android:layout_width="40dp"
            android:layout_height="44dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="16dp"
            android:background="@color/white"
            android:padding="6dp"
            android:src="@mipmap/close_grey"
            android:visibility="visible" />
    </RelativeLayout>
</RelativeLayout>

            