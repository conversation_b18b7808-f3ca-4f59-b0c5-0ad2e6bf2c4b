<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="@dimen/_16dp"
    style="@style/BottomSheetDialogTheme.RoundCorner"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_32dp"
        style="@style/Heading2"
        android:gravity="center"
        android:text="@string/tnc_heding_referral"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_point_1_referral"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        app:srcCompat="@drawable/ic_like"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_header" />

    <TextView
        android:id="@+id/tv_sub_header"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="@string/tnc_subheading_referral"
        android:layout_marginTop="@dimen/_16dp"
        style="@style/SubHeading1"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_header"
        app:layout_constraintStart_toEndOf="@+id/iv_point_1_referral" />

    <TextView
        android:id="@+id/tv_subheader_text"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/tnc_subheader_text_referral"
        style="@style/Body2"
        app:layout_constraintStart_toStartOf="@id/tv_sub_header"
        app:layout_constraintTop_toBottomOf="@id/tv_sub_header"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/iv_smartphone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        app:srcCompat="@drawable/ic_smartphone_referral"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subheader_text" />
    
    <TextView
        android:id="@+id/tv_smartphone_heading"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="@string/smartphone_heading_referral"
        app:layout_constraintStart_toStartOf="@id/tv_sub_header"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subheader_text"
        android:layout_marginTop="@dimen/_16dp"
        style="@style/SubHeading1"/>
    
    <TextView
        android:id="@+id/tv_bullet_smartphone"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/referral_smartphone"
        app:layout_constraintTop_toBottomOf="@id/tv_smartphone_heading"
        app:layout_constraintStart_toStartOf="@id/tv_smartphone_heading"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/Body2"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_tnc_referral"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_bullet_smartphone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="@string/tnc_text_referral"
        android:textColor="@color/blue_60"
        style="@style/Button.Text" />

</androidx.constraintlayout.widget.ConstraintLayout>