<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="com.bukuwarung.utils.Utility"
            alias="utility"
            />
        <variable
            name="item"
            type="com.bukuwarung.database.entity.ProductEntity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:theme="@style/ToolbarTheme">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/closeBtn"
                    android:layout_width="28dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center"
                    android:layout_marginLeft="35dp"
                    android:layout_marginRight="35dp"
                    android:layout_toEndOf="@id/closeBtn"
                    android:alpha="1"
                    android:fontFamily="@font/roboto"
                    android:text="@string/product_detail"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/editStockBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_toStartOf="@id/deleteStockBtn"
                    android:drawableTop="@drawable/ic_edit"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/edit"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:tint="@color/white"
                    android:visibility="visible"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/deleteStockBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:drawableTop="@drawable/delete_red"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="visible"
                    tools:ignore="SmallSp" />

            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>


        <RelativeLayout

            android:id="@+id/loadingContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/toolbar"
            android:background="@color/white"
            android:paddingStart="@dimen/_16dp"
            android:paddingEnd="@dimen/_16dp"
            android:visibility="visible">

            <ProgressBar
                android:id="@+id/loadingBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:progressTint="@color/colorPrimary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/loadingBar"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/_16dp"
                android:text="Mohon Tunggu..."
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/mainContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/toolbar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/productDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="@dimen/_16dp"
                android:paddingTop="@dimen/_12dp"
                android:paddingRight="@dimen/_16dp"
                android:paddingBottom="@dimen/_12dp">

                <TextView
                    android:id="@+id/productName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto"
                    android:maxLines="2"
                    android:text="@{item.name}"
                    android:textAlignment="textStart"
                    android:textColor="@color/black_80"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/stockInitialValue"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Ikan cupang Ikan cupang Ikan cupang Ikan cupang Ikan cupang Ikan cupang Ikan cupang " />

                <TextView
                    android:id="@+id/stockInitialValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="3dp"
                    android:fontFamily="@font/roboto"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="Stok: 50"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />
<!--                <TextView-->
<!--                    android:id="@+id/unitValue"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:textColor="@{item.stock>=0 ? @color/black_40 : @color/out_red}"-->
<!--                    android:fontFamily="@font/roboto"-->
<!--                    android:visibility="@{item.measurementName!=null?View.VISIBLE:View.INVISIBLE}"-->
<!--                    android:text="@{item.measurementName}"-->
<!--                    android:textSize="16sp"-->
<!--                    app:layout_constraintTop_toTopOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    tools:text="Pcs"-->

<!--                    />-->


                <TextView
                    android:id="@+id/productCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto"
                    android:textAllCaps="true"
                    android:textColor="@color/black_40"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/productName"
                    tools:text="@{item.code}"
                    />

                <TextView
                    android:id="@+id/stockMinValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto"
                    android:text="@{`Stok min: `+ item.minimumStock}"
                    android:textColor="@color/black_40"
                    android:textSize="12sp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/productCode"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:text="Stok min: 5" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/label_favourite"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@drawable/button_round_cornerd_edit_stroke_pop_up"
                    android:drawableStart="@drawable/ic_fav_grey"
                    android:drawablePadding="@dimen/_4dp"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:paddingVertical="@dimen/_8dp"
                    android:text="@string/favourite_label"
                    android:textColor="@color/black_40"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/stockMinValue" />




                <TextView
                    android:id="@+id/selling_price_label_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/selling_price_2"
                    android:textColor="@color/black_60"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/productCode"
                    tools:text="@string/selling_price_2" />

                <TextView
                    android:id="@+id/selling_price_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto"
                    android:text="@{`Rp `+ utility.formatCurrency(item.unitPrice)}"
                    android:textColor="@color/green_100"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@id/selling_price_label_txt"
                    app:layout_constraintTop_toBottomOf="@id/productCode"
                    tools:text="Rp1.000" />
                <!--                ` /` + item.measurementName}-->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto"
                    android:text="@{` /` + item.measurementName}"
                    android:textColor="@color/black_40"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@id/selling_price_txt"
                    app:layout_constraintTop_toBottomOf="@id/productCode"
                    tools:text="/Pcs" />



                <TextView
                    android:id="@+id/buying_price_label_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginTop="@dimen/_10dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/buying_price_2"
                    android:textColor="@color/black_60"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="@+id/label_favourite"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/selling_price_label_txt"
                    tools:text="@string/buying_price_2" />

                <TextView
                    android:id="@+id/buying_price_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:fontFamily="@font/roboto"
                    android:text="@{`Rp `+ utility.formatCurrency(item.buyingPrice)}"
                    android:textColor="@color/red_100"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="@+id/label_favourite"
                    app:layout_constraintStart_toEndOf="@id/selling_price_label_txt"
                    app:layout_constraintTop_toBottomOf="@id/selling_price_txt"
                    tools:text="Rp1.000" />
                <!--                ` /` + item.measurementName}-->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/productName"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:fontFamily="@font/roboto"
                    android:text="@{` /` + item.measurementName}"
                    android:textColor="@color/black_40"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="@+id/label_favourite"
                    app:layout_constraintStart_toEndOf="@id/buying_price_txt"
                    app:layout_constraintTop_toBottomOf="@id/selling_price_txt"
                    tools:text="/Pcs" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="12dp"
                android:layout_below="@+id/productDetails"
                android:background="#F1F1F1"
                android:orientation="vertical">

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/customerRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/divider"
                android:background="@color/white"
                tools:listitem="@layout/row_item_inventory_detail" />

            <LinearLayout
                android:gravity="center"
                android:id="@+id/notFoundContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_above="@+id/manageStockBtn"
                android:layout_below="@+id/divider"
                android:paddingStart="@dimen/_20dp"
                android:paddingEnd="@dimen/_20dp"
                >
                <ImageView
                    android:layout_width="wrap_content"
                    android:src="@drawable/stock_list_empty_image"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/inventory_hostory_detail_empty_screen_message"
                    android:textAlignment="center"
                    android:layout_marginTop="@dimen/_10dp"
                    android:textColor="@color/black_60"
                    android:textSize="15sp"
                    android:fontFamily="@font/roboto" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/emptyUnitPriceContaienr"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/manageStockBtn"
                android:layout_below="@+id/divider"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingStart="@dimen/_20dp"
                android:paddingEnd="@dimen/_20dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/stok_menipis_empty_image" />

                <TextView
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/empty_unit_price_message"
                    android:textAlignment="center"
                    android:textStyle="bold" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_manage_price"
                    style="@style/ButtonFill"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:paddingStart="@dimen/_32dp"
                    android:paddingEnd="@dimen/_32dp"
                    android:text="@string/manage_price_label" />

            </LinearLayout>

            <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
                android:id="@+id/manageStockBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="@dimen/_8dp"
                android:layout_marginBottom="24dp"
                android:fontFamily="@font/roboto"
                android:text="@string/modify_stock"
                android:textStyle="bold"
                app:backgroundTint="@color/buku_CTA"
                app:icon="@drawable/ic_tabler_adjustments"
                app:iconGravity="start" />
        </RelativeLayout>
    </RelativeLayout>
</layout>