<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_10dp"
    android:id="@+id/parent_container"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rl_category"
        android:layout_width="@dimen/_75dp"
        android:layout_height="@dimen/_75dp"
        android:background="@drawable/circle_gray_category"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <ImageView
            android:id="@+id/iv_category"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            app:srcCompat = "@drawable/ic_camera"
            android:layout_centerInParent="true" />

        <ImageView
            android:id="@+id/iv_category_selected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            app:srcCompat = "@drawable/category_selected_icon" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tv_category"
        android:layout_width="@dimen/_75dp"
        android:layout_height="wrap_content"
        style="@style/Body3"
        android:gravity="center"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@id/rl_category"
        app:layout_constraintStart_toStartOf="@id/rl_category"
        app:layout_constraintEnd_toEndOf="@id/rl_category" />

</androidx.constraintlayout.widget.ConstraintLayout>