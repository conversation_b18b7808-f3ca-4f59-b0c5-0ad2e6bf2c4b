<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    android:layout_marginTop="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_heading"
            android:layout_margin="@dimen/_16dp"
            android:text="Utang"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/iv_dropdown"
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:src="@drawable/ic_chevron_down"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_marginEnd="@dimen/_24dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_heading">

        <ImageView
            android:id="@+id/utangImage"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:src="@drawable/ic_utang_book"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="28dp"
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/utang_kamu_ke_orang_lain"
            app:layout_constraintTop_toBottomOf="@+id/balanceStatus" />

        <TextView
            android:id="@+id/utang_kamu_ke_orang_lain"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_24dp"
            android:fontFamily="@font/roboto"
            android:text="@string/utang_kamu_ke_orang_lain"
            android:textColor="@color/grey_91"
            android:textStyle="bold"
            app:layout_constraintHorizontal_weight="8"
            app:layout_constraintStart_toEndOf="@+id/utangImage"
            app:layout_constraintTop_toBottomOf="@+id/balanceStatus" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_22dp"
            android:src="@drawable/info"
            android:visibility="invisible"
            app:layout_constraintHorizontal_weight="8"
            app:layout_constraintStart_toEndOf="@+id/utang_kamu_ke_orang_lain"
            app:layout_constraintTop_toBottomOf="@+id/balanceStatus" />

        <TextView
            android:id="@+id/debitBalance"
            style="@style/Heading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_5dp"
            android:fontFamily="@font/roboto"
            android:paddingEnd="@dimen/_10dp"
            android:text="@string/rp1_000_000"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:textSize="@dimen/text_16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="8"
            app:layout_constraintStart_toEndOf="@+id/utangImage"
            app:layout_constraintTop_toBottomOf="@+id/utang_kamu_ke_orang_lain"
            tools:ignore="RtlSymmetry" />



        <ImageView
            android:id="@+id/utangImageTwo"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:src="@drawable/ic_utang_book"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_25dp"
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/utang_orang_lain_ke_kamu"
            app:layout_constraintTop_toBottomOf="@+id/debitBalance" />

        <TextView
            android:id="@+id/utang_orang_lain_ke_kamu"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_15dp"
            android:layout_marginTop="@dimen/_20dp"
            android:fontFamily="@font/roboto"
            android:text="@string/utang_orang_lain_ke_kamu"
            android:textColor="@color/grey_91"
            android:textStyle="bold"
            app:layout_constraintHorizontal_weight="8"
            app:layout_constraintStart_toEndOf="@+id/utangImageTwo"
            app:layout_constraintTop_toBottomOf="@+id/debitBalance" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_22dp"
            android:src="@drawable/info"
            android:visibility="invisible"
            app:layout_constraintHorizontal_weight="8"
            app:layout_constraintStart_toEndOf="@+id/utang_orang_lain_ke_kamu"
            app:layout_constraintTop_toBottomOf="@+id/debitBalance" />

        <TextView
            android:id="@+id/creditBalance"
            style="@style/Heading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_15dp"
            android:layout_marginTop="@dimen/_5dp"
            android:fontFamily="@font/roboto"
            android:paddingEnd="@dimen/_10dp"
            android:text="@string/rp1_000_000"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:textSize="@dimen/text_16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="8"
            app:layout_constraintStart_toEndOf="@+id/utangImage"
            app:layout_constraintTop_toBottomOf="@+id/utang_orang_lain_ke_kamu"
            tools:ignore="RtlSymmetry" />
        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_marginTop="@dimen/_24dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@id/creditBalance" />

        <TextView
            android:id="@+id/btn_detail"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_bold"
            android:layout_margin="@dimen/_16dp"
            android:text="@string/credit_debit_report"
            android:textAlignment="center"
            android:textSize="@dimen/_14dp"
            android:textColor="@color/blue_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider1" />

        <include layout="@layout/layout_business_dashboard_empty_info"
            android:id="@+id/empty_info"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/creditBalance"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_margin="@dimen/_16dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>

