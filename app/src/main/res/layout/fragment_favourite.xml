<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/choose_favourite"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_favourite"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <include
        android:id="@+id/include_loading"
        layout="@layout/layout_favourite_loading"
        android:layout_width="@dimen/_0dp"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <include
        android:id="@+id/include_empty"
        layout="@layout/layout_favourite_empty"
        android:layout_width="@dimen/_0dp"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/_10dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dimen_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/pb_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>