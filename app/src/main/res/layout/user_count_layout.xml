<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/img_user1"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:background="@drawable/bg_circle"
        android:padding="@dimen/_2dp"
        app:layout_constraintBottom_toBottomOf="@id/chip_user_count"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/chip_user_count" />


    <ImageView
        android:id="@+id/img_user2"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:background="@drawable/bg_circle"
        android:padding="@dimen/_2dp"
        app:layout_constraintBottom_toBottomOf="@id/chip_user_count"
        app:layout_constraintStart_toStartOf="@id/img_user1"
        app:layout_constraintTop_toTopOf="@id/chip_user_count" />

    <ImageView
        android:id="@+id/img_user3"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:background="@drawable/bg_circle"
        android:padding="@dimen/_2dp"
        app:layout_constraintBottom_toBottomOf="@id/chip_user_count"
        app:layout_constraintStart_toStartOf="@id/img_user2"
        app:layout_constraintTop_toTopOf="@id/chip_user_count" />

    <com.google.android.material.chip.Chip
        android:id="@+id/chip_user_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:padding="@dimen/_0dp"
        android:textColor="@color/white"
        android:textAppearance="@style/SubHeading1"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:chipBackgroundColor="@color/blue_80"
        app:chipStrokeColor="@color/white"
        app:chipStrokeWidth="@dimen/_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/img_user3"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="5.000.000++" />
</androidx.constraintlayout.widget.ConstraintLayout>