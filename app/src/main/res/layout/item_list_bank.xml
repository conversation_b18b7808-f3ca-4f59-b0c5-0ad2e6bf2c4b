<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/cl_layout"
    android:background="?attr/selectableItemBackground">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_bank_image"
        android:layout_width="56dp"
        android:layout_height="46dp"
        android:layout_marginStart="@dimen/_16dp"
        app:cardBackgroundColor="@color/white"
        android:layout_marginVertical="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/txt_bank_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image_bank"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            android:contentDescription="@null"
            app:srcCompat="@drawable/ic_bank" />
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/txt_bank_title"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_4dp"
        android:textColor="@color/heading_text"
        app:layout_constraintBottom_toTopOf="@id/tv_pay_method_error"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layout_bank_image"
        app:layout_constraintTop_toTopOf="@id/layout_bank_image"
        tools:text="Bank Amar Indonesia (formerly Anglomas International Bank)" />

    <TextView
        android:id="@+id/tv_pay_method_error"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/red_40"
        android:maxLines="1"
        android:paddingStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/txt_bank_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txt_bank_title"
        tools:text="Tidak tersedia" />

    <View
        style="@style/Divider.Horizontal"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_bank_image" />

</androidx.constraintlayout.widget.ConstraintLayout>
