<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_item_key"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_item_value"
        style="@style/Body2"
        android:textColor="@color/black_40"
        android:text="@string/business_name_label"/>

    <TextView
        android:id="@+id/tv_item_value"
        android:layout_width="0dp"
        android:paddingStart="@dimen/_24dp"
        android:textAlignment="textEnd"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_percent="0.56"
        android:maxLines="4"
        style="@style/Body2"
        android:textColor="@color/black_80"
        tools:text="tcycycythhvjhvgvuvuvxctyrfcgfcgfcxgfxcgfxgxgxtgfxgxx"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_separator"
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_item_key, tv_item_value" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@id/barrier_separator"
        android:layout_marginTop="@dimen/_12dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>