<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/_8dp"
    app:cardElevation="@dimen/_0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_12dp">

        <TextView
            android:id="@+id/total_balance_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black60"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.cardview.widget.CardView
            android:id="@+id/currency_capsule"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/_6dp"
            app:cardElevation="@dimen/_0dp"
            app:layout_constraintBottom_toBottomOf="@id/balance_input"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/balance_input">

            <TextView
                android:id="@+id/currency_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/_4dp"
                android:layout_marginVertical="@dimen/_2dp"
                android:textSize="@dimen/text_14sp" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/balance_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:textAlignment="textEnd"
            android:textSize="@dimen/text_24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/cursor"
            app:layout_constraintStart_toEndOf="@id/currency_capsule"
            app:layout_constraintTop_toBottomOf="@id/total_balance_label" />

        <View
            android:id="@+id/cursor"
            android:layout_width="@dimen/_2dp"
            android:layout_height="@dimen/dimen_24dp"
            android:layout_marginBottom="@dimen/_4dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/balance_input"
            app:layout_constraintEnd_toEndOf="parent" />

        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@color/black5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/balance_input" />

        <LinearLayout
            android:id="@+id/expression_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider">

            <TextView
                android:id="@+id/expression_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAlignment="textEnd"
                android:textColor="@color/black_20"
                android:textSize="@dimen/text_14sp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
