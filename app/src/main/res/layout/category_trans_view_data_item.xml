<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include layout="@layout/category_transaction_header_item" />
    <LinearLayout
        android:id="@+id/main_container"
        android:layout_below="@+id/listHeader"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="75dp"
        android:paddingStart="@dimen/_16dp"
        >

        <CheckBox
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/selectorSingle"
            android:layout_gravity="center"
            />

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingLeft="16dp">

            <TextView
                android:id="@+id/date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="7"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="10sp"
                android:maxLines="1"
                android:paddingRight="8dp"
                android:text="23232.0"
                android:textColor="@color/body_text"
                android:textSize="15sp"
                app:autoSizeMaxTextSize="15sp"
                app:autoSizeMinTextSize="10sp"
                app:autoSizeStepGranularity="1sp"
                app:autoSizeTextType="uniform" />

            <TextView
                android:id="@+id/note"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/date"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:text="Balance till 3rd December"
                android:textColor="#9dacb4"
                android:textSize="12dp" />
        </RelativeLayout>

        <TextView
            android:id="@+id/amount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:fontFamily="sans-serif-medium"
            android:lineSpacingExtra="10sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:paddingLeft="4dp"
            android:text="2000.9"
            android:gravity="right"
            android:layout_marginEnd="18dp"
            android:textColor="@color/out_red"
            android:textSize="@dimen/amount_text_size"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="14sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform" />

        <TextView
            android:id="@+id/buying_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:fontFamily="sans-serif-medium"
            android:gravity="right"
            android:lineSpacingExtra="10sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:paddingLeft="4dp"
            android:paddingRight="18dp"
            android:text="2000.9"
            android:textColor="@color/out_red"
            android:textSize="@dimen/amount_text_size"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="14sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform" />
    </LinearLayout>

    <TextView
        android:id="@+id/section"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_below="@+id/main_container"
        android:background="@color/section_end" />
</RelativeLayout>
