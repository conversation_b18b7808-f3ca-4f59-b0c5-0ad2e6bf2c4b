<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activities.printer.PrintBillActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/tbBill"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:theme="@style/ToolbarTheme"
            app:title="Cetak Struk"
            app:titleTextColor="@color/white" />
    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/black_0"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                app:cardBackgroundColor="@color/white"
                app:contentPaddingBottom="25dp"
                app:contentPaddingLeft="20dp"
                app:contentPaddingRight="20dp"
                app:contentPaddingTop="25dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/groupUtangTransaction"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="tvCustomerName, tvCustomerPhone, flNominal" />

                    <TextView
                        android:id="@+id/tvWarungName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Warung Chimay" />

                    <TextView
                        android:id="@+id/tvWarungAddress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_40"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvWarungName"
                        tools:text="Jl.Alamat warung no xx" />

                    <LinearLayout
                        android:id="@+id/tvNoAddress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvWarungName">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:text="@string/printer_receipt_no_address"
                            android:textColor="@color/black_40" />

                        <TextView
                            android:id="@+id/tvSetAddress"
                            android:layout_marginStart="4dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:text="@string/business_card"
                            android:textColor="@color/colorPrimary" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvWarungPhone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_40"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvWarungAddress"
                        tools:text="***********" />


                    <View
                        android:id="@+id/line1"
                        android:layout_width="match_parent"
                        android:layout_height="2dp"
                        android:layout_marginTop="8dp"
                        android:background="@color/black_10"
                        app:layout_constraintTop_toBottomOf="@id/tvWarungPhone" />

                    <TextView
                        android:id="@+id/tvVerified"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:drawablePadding="@dimen/_8dp"
                        android:fontFamily="@font/roboto"
                        android:gravity="center"
                        android:text="@string/printer_verified_by_app"
                        android:textColor="@color/black_60"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/line1" />

                    <TextView
                        android:id="@+id/tvReceiptDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvVerified"
                        tools:text="Dicetak : 23/06/2020" />


                    <TextView
                        android:id="@+id/tvCustomerName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        app:layout_constraintTop_toBottomOf="@+id/tvReceiptDate"
                        tools:text="Nama: Zulfakar" />

                    <TextView
                        android:id="@+id/tvCustomerPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        app:layout_constraintTop_toBottomOf="@+id/tvCustomerName"
                        tools:text="Nomor HP: 08xxxxxxx" />

                    <TextView
                        android:id="@+id/tvTransactionDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        app:layout_constraintTop_toBottomOf="@+id/tvCustomerPhone"
                        tools:text="@string/transaction_date" />

                    <!--Only visible for utang transaction-->
                    <FrameLayout
                        android:id="@+id/flNominal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="@color/black_5"
                        android:orientation="horizontal"
                        android:padding="@dimen/_8dp"
                        app:layout_constraintTop_toBottomOf="@id/tvTransactionDate">

                        <!--Menerima/Memberi-->
                        <TextView
                            android:id="@+id/tvOperation"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start"
                            android:fontFamily="@font/roboto"
                            android:textColor="@color/black_80"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="Menerima" />

                        <TextView
                            android:id="@+id/tvTransactionNominal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:fontFamily="@font/roboto"
                            android:textColor="@color/black_80"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="Rp.50.000" />
                    </FrameLayout>

                    <TextView
                        android:id="@+id/tvTransactionNoteLabel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/notes"
                        android:textColor="@color/black_60"
                        android:textStyle="bold"
                        app:layout_constraintTop_toBottomOf="@+id/flNominal" />

                    <TextView
                        android:id="@+id/tvTransactionNote"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_60"
                        app:layout_constraintTop_toBottomOf="@+id/tvTransactionNoteLabel"
                        tools:text="disini catatan kalo ada" />

                    <!--only visible for cash transaction-->
                    <LinearLayout
                        android:id="@+id/layoutProductDetails"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:orientation="vertical"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/tvTransactionNote"
                        tools:visibility="visible">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:text="@string/rincian_produk"
                            android:textColor="@color/black_60"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!--"Sisa (Utang/Piutang) Anda" for utang transaction-->
                    <!--"Total Bayar" for cash transaction-->
                    <TextView
                        android:id="@+id/tvSummary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="@color/black_5"
                        android:gravity="end|center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/_8dp"
                        android:textColor="@color/black_80"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintTop_toBottomOf="@id/layoutProductDetails"
                        tools:text="Total Bayar : Rp50.000" />

                    <View
                        android:id="@+id/line2"
                        android:layout_width="match_parent"
                        android:layout_height="2dp"
                        android:layout_marginTop="16dp"
                        android:background="@color/black_10"
                        app:layout_constraintTop_toBottomOf="@id/tvSummary" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/print_receipt_footer"
                        android:textAlignment="center"
                        android:textColor="@color/black_40"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/line2" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutPrint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingTop="12dp"
        android:paddingBottom="12dp">


        <TextView
            android:id="@+id/tvPrinterLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:text="Printer"
            android:textColor="@color/black_80"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvNoPrinterWarning" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/cgNoPrinter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tvNoPrinterWarning, tvNoPrinterMessage, btnSetupPrinter"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/tvNoPrinterWarning"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:background="#33FF4343"
            android:drawableStart="@drawable/ic_baseline_warning"
            android:drawablePadding="@dimen/_8dp"
            android:fontFamily="@font/roboto"
            android:gravity="center_vertical"
            android:padding="4dp"
            android:text="@string/no_printer_installed"
            android:textColor="@color/out_red"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/tvPrinterLabel"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tvNoPrinterMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:text="@string/no_printer_message"
            android:textColor="@color/black_60"
            android:textSize="14sp"
            app:layout_constraintTop_toBottomOf="@id/tvPrinterLabel" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/cgPrinter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tvChangePrinter, layoutSelectedPrinter, btnPrint"
            tools:visibility="visible" />


        <include
            android:id="@+id/layoutSelectedPrinter"
            layout="@layout/printer_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tvPrinterLabel" />

        <TextView
            android:id="@+id/tvChangePrinter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:text="@string/change"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/layoutSelectedPrinter"
            app:layout_constraintTop_toTopOf="@id/layoutSelectedPrinter" />


        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tvNoPrinterMessage,layoutSelectedPrinter" />


        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_marginTop="12dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@id/barrier" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSetupPrinter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:padding="12dp"
            android:text="@string/setup_printer"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnPrint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:padding="12dp"
            android:text="@string/cetak"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@color/buku_CTA"
            app:layout_constraintTop_toBottomOf="@id/line" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
