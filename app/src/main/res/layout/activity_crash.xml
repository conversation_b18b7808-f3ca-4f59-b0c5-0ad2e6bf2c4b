<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".BukuCrashingActivity">

    <ImageView
        android:id="@+id/iv_bug"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/logo_bw"
        android:layout_marginTop="150dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_error_message"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:text="Aplikasi mendapat masalah tak terduga"
        android:textColor="#000"
        android:textSize="22sp"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_bug"/>

    <TextView
        android:id="@+id/tv_error_message_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:alpha=".7"
        android:gravity="center"
        android:maxEms="11"
        android:text="Maaf atas ketidaknyamanan ini."
        android:textColor="#000"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_error_message"/>

    <Button
        android:id="@+id/bReport"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:minHeight="50dp"
        android:paddingHorizontal="24dp"
        android:text="hapus data dan mulai ulang"
        android:textAllCaps="false"
        android:textSize="22sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_error_message_detail"/>

    <LinearLayout
        android:id="@+id/bRestartApp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bReport">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ulang aplikasi"
            android:textColor="#000"
            android:textSize="16sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="-3dp"
            android:background="#000" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>