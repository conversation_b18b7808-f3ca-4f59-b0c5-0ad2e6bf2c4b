<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="@dimen/_16dp"
    android:background="@drawable/bg_rounded_rectangle_white_8dp">

    <TextView
        android:id="@+id/tv_detail_transaksi"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/label_payment_detail"
        tools:drawableEnd="@drawable/ic_chevron_down"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_check_details"
        style="@style/Label2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:text="@string/check_details_message"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_transaksi" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_bill_detail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_check_details" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gp_expandable"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rv_bill_detail, tv_check_details" />

</androidx.constraintlayout.widget.ConstraintLayout>

