<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainContainer"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingTop="12dp"
    android:paddingBottom="12dp">

<!--    <TextView-->
<!--        android:id="@+id/firstLetter"-->
<!--        android:layout_width="36dp"-->
<!--        android:layout_height="36dp"-->
<!--        android:background="@drawable/oval_0"-->
<!--        android:backgroundTint="@color/colorPrimary"-->
<!--        android:fontFamily="@font/roboto"-->
<!--        android:gravity="center"-->
<!--        android:text="@string/default_placeholder"-->
<!--        android:maxLength="1"-->
<!--        android:textAllCaps="true"-->
<!--        android:textColor="@color/white"-->
<!--        android:textSize="18sp"-->
<!--        android:textStyle="bold"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent" />-->

    <LinearLayout
        android:id="@+id/pic"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:orientation="vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/photo"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:visibility="gone" />

        <TextView
            android:id="@+id/firstLetter"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/oval_0"
            android:backgroundTint="#66BDFF"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="A"
            android:textColor="@color/white"
            android:textSize="22sp"
            android:visibility="gone" />
    </LinearLayout>

    <TextView
        android:id="@+id/nameText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="Agus Yusuf"
        android:textSize="16dp"
        app:autoSizeMaxTextSize="16sp"
        app:autoSizeMinTextSize="12sp"
        app:autoSizeStepGranularity="1sp"
        android:ellipsize="end"
        android:maxLines="1"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintLeft_toRightOf="@id/pic"
        app:layout_constraintRight_toLeftOf="@id/amount"
        app:layout_constraintTop_toTopOf="parent"
        android:textColor="@color/heading_text"/>

    <TextView
        android:id="@+id/collectingText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/collecting_calendar_not_paid"
        android:textSize="14sp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintLeft_toRightOf="@id/pic"
        app:layout_constraintRight_toLeftOf="@id/setNewDate"
        app:layout_constraintTop_toBottomOf="@id/nameText"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="#5C5C5C"/>

    <TextView
        android:id="@+id/amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Rp 20.000"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textColor="#FF4343"/>

    <TextView
        android:id="@+id/setNewDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/collecting_calendar_new_date"
        android:textSize="14sp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/amount"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textColor="#025FA5"/>

</androidx.constraintlayout.widget.ConstraintLayout>