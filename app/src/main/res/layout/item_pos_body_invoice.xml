<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_product_header"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="@dimen/_4dp"
    android:paddingBottom="@dimen/_4dp">

    <!--Width ratio is 3:1:2-->

    <TextView
        android:id="@+id/tv_product_name"
        style="@style/Body2"
        android:layout_width="0dp"
        android:textColor="@color/black_60"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:textAlignment="viewStart"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Martabak 312 rasa susu keju diskon seratus ribu jhvjhvjvjvcxjvsjchvsajchvasjcvasjgcvjsacvjsavcjhsavjhcvsajhvcjashcvjsahvcjcsvjhvscjhvsajchvsajcvjsaxxxxxx" />

    <TextView
        android:id="@+id/tv_price_per_unit"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        android:layout_marginTop="@dimen/_4dp"
        tools:text="1000 / PCS"
        tools:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/tv_product_name"
        app:layout_constraintHorizontal_weight="3"
        app:layout_constraintEnd_toStartOf="@id/tv_product_count"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name" />

    <TextView
        android:id="@+id/tv_product_count"
        style="@style/Body2"
        android:textColor="@color/black_60"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewEnd"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintEnd_toStartOf="@id/tv_product_price"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/tv_price_per_unit"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="1" />

    <TextView
        android:id="@+id/tv_product_count_top"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewEnd"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/tv_product_name"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/normal_product_info_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_product_count, tv_price_per_unit" />

    <!--could be selling or buying price-->
    <TextView
        android:id="@+id/tv_product_price"
        style="@style/Body2"
        android:textColor="@color/black_60"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewEnd"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="2"
        app:layout_constraintStart_toEndOf="@id/tv_product_count"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="Rp.120.000" />

</androidx.constraintlayout.widget.ConstraintLayout>