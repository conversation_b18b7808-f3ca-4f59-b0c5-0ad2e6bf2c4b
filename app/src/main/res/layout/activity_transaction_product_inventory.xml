<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black5">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="@color/blue60"
        app:navigationIcon="@drawable/ic_back"
        app:title="@string/item_list"
        app:titleTextColor="@color/white" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/confirmation_container"
        android:layout_below="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/search_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:drawableStart="@drawable/ic_icon_search_new"
                android:drawablePadding="@dimen/_12dp"
                android:drawableTint="@color/black40"
                android:hint="@string/search_items"
                android:inputType="text"
                android:padding="@dimen/_16dp"
                android:textColor="@color/black60"
                android:textColorHint="@color/black40"
                android:textSize="@dimen/text_14sp" />

            <LinearLayout
                android:id="@+id/add_product_suggestion_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingTop="@dimen/_12dp"
                android:paddingBottom="@dimen/_4dp">

                <TextView
                    android:id="@+id/product_suggestion_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_weight="1"
                    android:textColor="@color/black80"
                    android:textSize="@dimen/text_14sp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/add_product"
                    style="@style/ButtonFill.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_0dp"
                    android:text="@string/product_add"
                    app:cornerRadius="@dimen/_4dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/product_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_8dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/empty_state_container"
        layout="@layout/item_empty_state"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="@dimen/_32dp" />

    <LinearLayout
        android:id="@+id/confirmation_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/black5" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginVertical="@dimen/_12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/total_selected_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/black60"
                android:textSize="@dimen/text_14sp" />

            <TextView
                android:id="@+id/total_price_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textAlignment="textEnd"
                android:textColor="@color/black80"
                android:textSize="@dimen/text_16sp"
                android:textStyle="bold" />

        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/confirmation_button"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:text="@string/next" />

    </LinearLayout>

</RelativeLayout>
