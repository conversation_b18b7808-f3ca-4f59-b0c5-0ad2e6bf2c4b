<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dimen_24dp"
            app:layout_constraintTop_toBottomOf="@+id/iv_top_pic"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_top_pic"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:scaleType="fitXY"
                android:src="@drawable/business_top"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/businessNameMessage"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="@string/business_name_label"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_top_pic" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/businessNameLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="21dp"
                android:hint="@string/business_name_with_asterisk"
                android:paddingEnd="20dp"
                app:boxStrokeColor="@color/black_40"
                app:boxStrokeErrorColor="@color/red_80"
                app:boxStrokeWidth="1dp"
                app:errorIconTint="@color/red_80"
                app:errorTextColor="@color/red_80"
                app:hintTextColor="@color/black_40"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/businessNameMessage">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/businessName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:cursorVisible="true"
                    android:focusedByDefault="false"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textCursorDrawable="@null"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/ownerNameError"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@+id/businessNameLayout"
                app:layout_constraintTop_toBottomOf="@+id/businessNameLayout">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_warning"
                    app:tint="@color/red_error" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/owner_name_error"
                    android:textColor="@color/red_error" />
            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/bizLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:hint="@string/select_business_hint_business_category"
                android:paddingEnd="20dp"
                app:boxStrokeColor="@color/black"
                app:boxStrokeErrorColor="@color/red_80"
                app:boxStrokeWidth="1dp"
                app:endIconCheckable="true"
                app:endIconDrawable="@drawable/ic_chevron_down_blue"
                app:endIconMode="custom"
                app:endIconTint="@color/colorPrimaryDark"
                app:errorIconTint="@color/red_80"
                app:errorTextColor="@color/red_80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ownerNameError">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/businessTypeET"
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    android:ellipsize="end"
                    android:focusable="false"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/customerLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:hint="@string/pilih_pelanggan_kamu_with_asterisk"
                android:paddingEnd="20dp"
                app:boxStrokeColor="@color/black"
                app:boxStrokeErrorColor="@color/red_80"
                app:boxStrokeWidth="1dp"
                app:endIconCheckable="true"
                app:endIconDrawable="@drawable/ic_chevron_down_blue"
                app:endIconMode="custom"
                app:endIconTint="@color/colorPrimaryDark"
                app:errorIconTint="@color/red_80"
                app:errorTextColor="@color/red_80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/bizLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/customerTypeEt"
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    android:ellipsize="end"
                    android:focusable="false"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/hint_color"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>


            <LinearLayout
                android:id="@+id/businessTypeError"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@+id/bizLayout"
                app:layout_constraintTop_toBottomOf="@+id/bizLayout">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_warning"
                    app:tint="@color/red_error" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/business_type_error"
                    android:textColor="@color/red_error" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_selling_method"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="12dp"
                android:text="@string/selling_method_with_asterisk"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/customerLayout" />

            <TextView
                android:id="@+id/tv_selling_method_helper"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/mutliple_choice_allowed"
                android:textColor="@color/black_60"
                android:textSize="@dimen/text_10sp"
                app:layout_constraintBottom_toBottomOf="@id/tv_selling_method"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_selling_method" />

            <CheckBox
                android:id="@+id/cb_selling_offline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:button="@drawable/rounded_checkbox_selector"
                android:paddingStart="6dp"
                android:text="@string/selling_method_offline"
                android:textAlignment="textStart"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_selling_method" />

            <View
                android:id="@+id/line_offline"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@color/dot_default"
                app:layout_constraintTop_toBottomOf="@id/cb_selling_offline" />

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/cb_selling_online"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:button="@drawable/rounded_checkbox_selector"
                android:paddingStart="6dp"
                android:text="@string/online"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line_offline" />

            <View
                android:id="@+id/line_online"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@color/dot_default"
                app:layout_constraintTop_toBottomOf="@id/cb_selling_online" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_referral"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="21dp"
                android:hint="@string/ref_code_hint"
                android:paddingEnd="20dp"
                app:boxStrokeColor="@color/black_40"
                app:boxStrokeErrorColor="@color/red_80"
                app:boxStrokeWidth="1dp"
                app:errorIconTint="@color/red_80"
                app:errorTextColor="@color/red_80"
                app:hintTextColor="@color/black_40"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:endIconMode="clear_text"
                app:layout_constraintTop_toBottomOf="@+id/line_online">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/tie_referral"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:cursorVisible="true"
                    android:focusedByDefault="false"
                    android:imeOptions="actionDone"
                    android:inputType="textCapCharacters"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textCursorDrawable="@null"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/br_referral"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="til_referral"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/line_online"/>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_info"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_20dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_20dp"
                android:layout_marginBottom="@dimen/_12dp"
                android:background="@drawable/payment_filter_bg"
                android:paddingStart="@dimen/_8dp"
                android:paddingTop="@dimen/_8dp"
                android:paddingBottom="@dimen/_8dp"
                app:layout_constraintEnd_toEndOf="@+id/bizLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_referral">

                <ImageView
                    android:id="@+id/iv_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/_8dp"
                    android:src="@drawable/ic_info_icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:text="@string/info_business_category"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_info"
                    app:layout_constraintTop_toTopOf="@id/iv_info" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.Group
                android:id="@+id/full_question_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="customerLayout, cb_selling_offline, cb_selling_online, tv_selling_method, tv_selling_method_helper, line_offline, line_online" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSkip"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:backgroundTint="@color/white"
                android:fontFamily="@font/roboto"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="@string/skip"
                android:textAllCaps="false"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/buttonSubmit"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_info"
                app:strokeColor="@color/colorPrimary"
                app:strokeWidth="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSubmit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="20dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:backgroundTint="@color/buku_CTA_New"
                android:fontFamily="@font/roboto"
                android:text="@string/label_submit"
                android:textColor="@color/black_80"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/buttonSkip"
                app:layout_constraintTop_toBottomOf="@id/cl_info" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>