<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <androidx.cardview.widget.CardView
        android:id="@+id/card_container"
        android:layout_width="match_parent"
        android:layout_height="420dp"
        app:cardBackgroundColor="@color/black"
        app:cardCornerRadius="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/backgroundImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="80dp"
                android:layout_marginRight="40dp"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:minLines="2"
                android:fontFamily="sans-serif"
                android:textFontWeight="800"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Diskon Chinese New Year 2021" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="48dp"
                android:layout_marginRight="16dp"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textFontWeight="600"
                android:fontFamily="sans-serif"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle"
                tools:text="Nikmati diskon imlek dengan 50% diskon untuk semua produk yang ada di sini semua" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStoreLinkMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="4dp"
                android:gravity="center"
                android:padding="2dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:fontFamily="sans-serif"
                android:textFontWeight="400"
                app:layout_constraintBottom_toTopOf="@+id/tvStoreLink"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="ddr6575675" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStoreLink"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp"
                android:layout_marginBottom="70dp"
                android:background="@drawable/background_radius_4dp"
                android:gravity="center"
                android:fontFamily="sans-serif"
                android:padding="2dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textFontWeight="700"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="ddr6575675" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>