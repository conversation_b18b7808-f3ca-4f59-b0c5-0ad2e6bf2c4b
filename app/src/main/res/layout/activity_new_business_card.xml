<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="match_parent"
    tools:context=".activities.card.newcard.NewBusinessCardActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb"
        android:layout_width="match_parent"
        app:title="@string/business_card"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:titleTextAppearance="@style/Heading2"
        app:titleTextColor="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/_16dp">

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/card_pager"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:layout_height="250dp" />

            <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
                android:id="@+id/dots_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginBottom="12dp"
                app:dotsColor="@color/black_10"
                app:dotsCornerRadius="8dp"
                app:dotsSize="8dp"
                app:dotsSpacing="4dp"
                app:dotsWidthFactor="2"
                app:selectedDotColor="@color/colorPrimary" />


            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                android:background="@color/black_10" />


            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                app:hintEnabled="false"
                android:layout_marginTop="@dimen/_16dp"
                app:boxStrokeColor="@color/black_10"
                app:startIconTint="@color/black_80"
                android:layout_marginEnd="@dimen/_16dp"
                app:startIconDrawable="@drawable/ic_business_big">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_business_name"
                    style="@style/Body1"
                    android:layout_width="match_parent"
                    android:hint="@string/your_name_optional"
                    android:inputType="text"
                    android:layout_height="wrap_content"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                app:hintEnabled="false"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                app:boxStrokeColor="@color/black_10"
                app:startIconTint="@color/black_80"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:startIconDrawable="@drawable/ic_person">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_owner_name"
                    android:layout_width="match_parent"
                    style="@style/Body1"
                    android:hint="@string/nama_kamu_opsional"
                    android:inputType="textPersonName"
                    android:layout_height="wrap_content"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:boxStrokeColor="@color/black_10"
                app:startIconTint="@color/black_80"
                android:layout_marginStart="@dimen/_16dp"
                app:hintEnabled="false"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:startIconDrawable="@drawable/ic_call_blue">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_phone"
                    style="@style/Body1"
                    android:hint="@string/hint_phone"
                    android:inputType="phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:hintEnabled="false"
                android:layout_marginStart="@dimen/_16dp"
                app:boxStrokeColor="@color/black_10"
                app:startIconTint="@color/black_80"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:startIconDrawable="@drawable/ic_baseline_message_24">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_slogan"
                    style="@style/Body1"
                    android:layout_width="match_parent"
                    android:hint="@string/business_tag_line"
                    android:inputType="text"
                    android:maxLength="80"
                    android:layout_height="wrap_content"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:boxStrokeColor="@color/black_10"
                app:startIconTint="@color/black_80"
                app:hintEnabled="false"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:startIconDrawable="@drawable/location_icon">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_address"
                    style="@style/Body1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/ic_chevron_right_black_60"
                    android:focusable="false"
                    android:hint="@string/location_optional"
                    android:inputType="text"
                    android:maxLength="80"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

    </ScrollView>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:background="@color/black_10" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="@string/save" />

    </FrameLayout>

</LinearLayout>