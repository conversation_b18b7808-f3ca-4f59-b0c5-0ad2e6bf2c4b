<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="@dimen/_100dp"
        android:layout_height="@dimen/_100dp"
        android:layout_marginTop="@dimen/_20dp"
        app:srcCompat= "@drawable/blank_fav"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_heading"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:gravity="center"
        android:text="@string/empty_fav_heading"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_image" />

    <TextView
        android:id="@+id/tv_sub_heading"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:gravity="center"
        android:text="@string/empty_fav_sub_heading"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_heading" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_favourite"
        style="@style/ButtonFill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:padding="@dimen/_12dp"
        android:text="@string/add_favourite"
        android:textAllCaps="false"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_sub_heading"
        app:rippleColor="@color/black_40" />

</androidx.constraintlayout.widget.ConstraintLayout>