<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:background="@drawable/rounded_black_10_10dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_qris_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_kyc_in_progress" />

    <TextView
        android:id="@+id/tv_qris_status_heading"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_12dp"
        app:layout_constraintBottom_toTopOf="@id/tv_qris_status_body"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/iv_qris_status"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Verifikasi Toko sedang dalam proses" />

    <TextView
        android:id="@+id/tv_qris_status_body"
        style="@style/Label2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:lineSpacingExtra="3dp"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_qris_status_heading"
        app:layout_constraintTop_toBottomOf="@+id/tv_qris_status_heading"
        tools:text="Proses verifikasi membutuhkan maksimal 7 hari kerja" />

</androidx.constraintlayout.widget.ConstraintLayout>