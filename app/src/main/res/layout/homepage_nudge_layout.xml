<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ScrollView
        android:id="@+id/sv_nudge_layout"
        android:layout_width="@dimen/_0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/nudge_bottom_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_title"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:drawableStart="@drawable/ic_upgrade"
                android:drawablePadding="@dimen/_6dp"
                android:text="Upgrade Akun, Nikmati Fiturnya"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_banner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:src="@drawable/bayar_nudge_image"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title" />

            <TextView
                android:id="@+id/tv_subtitle"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:text="Layani pelanggan Bayar antar bank"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_banner" />

            <TextView
                android:id="@+id/tv_body_text"
                style="@style/Body2"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:text="Atur komisimu tiap bantu pelanggan bayar ke bank manapun dengan biaya admin rendah dan limit hingga Rp1 Miliar/hari."
                android:textColor="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_subtitle" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_benefits"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:background="@drawable/bg_rectangle_gray_12"
                android:elevation="@dimen/_4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_body_text">

                <TextView
                    android:id="@+id/tv_title_benefits"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="Dapatkan keuntungan lainnya secara gratis"
                    app:layout_constraintEnd_toEndOf="@id/cl_benefits"
                    app:layout_constraintStart_toStartOf="@id/cl_benefits"
                    app:layout_constraintTop_toTopOf="@id/cl_benefits" />

                <ImageView
                    android:id="@+id/iv_benefits"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_benefits"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_title_benefits" />

                <TextView
                    android:id="@+id/tv_learn_about_benefits"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:text="Pelajari keuntungan fitur lainnya"
                    android:textColor="@color/blue_60"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_benefits" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <include
    android:id="@+id/nudge_bottom_layout"
    layout="@layout/homepage_nudge_bottom_layout"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    android:elevation="@dimen/_6dp"
    android:layout_width="@dimen/_0dp"
    android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>