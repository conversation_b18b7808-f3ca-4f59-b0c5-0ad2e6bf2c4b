<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/firstLetter"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:background="@drawable/oval_0"
        android:backgroundTint="@color/colorPrimary"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/default_placeholder"
        android:maxLength="2"
        android:textAllCaps="true"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/text_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/default_placeholder"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintRight_toLeftOf="@id/text_point"
        app:layout_constraintLeft_toRightOf="@id/firstLetter"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/text_point"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/default_placeholder"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold"
        android:textSize="18sp"
        android:layout_marginStart="@dimen/_8dp"
        app:layout_constraintLeft_toRightOf="@id/text_name"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
