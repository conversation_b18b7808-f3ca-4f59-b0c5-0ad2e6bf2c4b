<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_marginStart="24dp"
                android:drawableStart="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineSpacingExtra="8sp"
                android:maxLines="1"
                android:text="@string/notifications"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/notificationRv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/_16dp"
        android:layout_below="@id/toolbar"/>

    <TextView
        android:id="@+id/view_empty_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/no_notification"
        android:textColor="@color/body_text"
        android:visibility="gone"
        tools:visibility="visible" />

</RelativeLayout>
