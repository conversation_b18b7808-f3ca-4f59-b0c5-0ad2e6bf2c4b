<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_status_transaction"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Body2"
        android:textAllCaps="true"
        android:gravity="center"
        android:textColor="@color/red_80"
        android:layout_marginVertical="@dimen/_8dp"
        android:text="@string/transaction_status"/>

    <View
        android:id="@+id/view_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:background="@drawable/horizontal_dashed_line"
        android:layout_marginTop="@dimen/_8dp"
        android:foregroundGravity="center"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_status_transaction"/>

</androidx.constraintlayout.widget.ConstraintLayout>