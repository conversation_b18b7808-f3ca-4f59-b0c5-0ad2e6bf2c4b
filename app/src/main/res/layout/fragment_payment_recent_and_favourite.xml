<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tb_layout"
        style="@style/TabLayout.Theme.PrimaryNew"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabMaxWidth="0dp"
        app:tabGravity="fill"
        app:tabMode="fixed"
        app:tabTextAppearance="@style/Tab.Theme.Primary.TextAppearance" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>