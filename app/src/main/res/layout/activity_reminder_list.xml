<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/selfRemainder"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:title="@string/self_remainder" />
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="32dp"
        android:paddingBottom="32dp"
        android:background="@color/white"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/RemainderView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/addCashTransactionBtn"
            style="@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom|right|center_vertical|center_horizontal|center"
            android:layout_marginRight="16dp"

            android:background="@color/buku_CTA"
            android:backgroundTint="@color/buku_CTA"
            android:elevation="8dp"
            android:text="BUAT PENGINGAT"
            android:textAllCaps="true"
            android:textColor="@color/cta_button_text"
            android:textStyle="bold"
            app:icon="@mipmap/ic_plus_white_24dp"
            app:iconGravity="textStart"
            app:iconTint="@color/cta_button_text" />

    </LinearLayout>
</LinearLayout>
<!--</androidx.coordinatorlayout.widget.CoordinatorLayout>-->