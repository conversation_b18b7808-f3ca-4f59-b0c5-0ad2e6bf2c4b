<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:fitsSystemWindows="true">
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_alignParentTop="true"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ActionBarAppTheme">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="fill_parent"
            android:layout_height="?actionBarSize"
            app:contentInsetLeft="0.0dip"
            app:contentInsetStart="0.0dip"
            app:contentInsetStartWithNavigation="0.0dip">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_alignParentLeft="true"
                android:layout_width="25dp"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_height="25dp"
                android:gravity="center"
                android:layout_centerVertical="true"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/screen_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/new_business_title"
                android:fontFamily="@font/roboto"
                android:textStyle="bold"
                android:layout_marginLeft="24dp"
                android:textColor="@color/white"
                android:textSize="18.0dip" />
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
<ScrollView
    android:layout_width="match_parent"
    android:id="@+id/formArea"
    android:layout_below="@+id/appbar"
    android:paddingTop="@dimen/_8dp"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:layout_marginRight="@dimen/_16dp"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/ownerLayout"
            android:layout_marginTop="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ownerNameIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:tint="@color/colorPrimary"
                android:src="@drawable/ic_person" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/outlinedTextField"
                android:layout_width="match_parent"
                android:layout_toRightOf="@+id/ownerNameIcon"
                android:layout_height="wrap_content"
                android:hint="@string/business_owner_hint"
                app:passwordToggleDrawable="@null"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black"
                    android:id="@+id/ownerNameEt"
                    android:textSize="16sp"
                    />

            </com.google.android.material.textfield.TextInputLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/businessLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <ImageView
                android:id="@+id/businessIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:tint="@color/colorPrimary"
                android:src="@drawable/ic_business_big" />
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_toRightOf="@+id/businessIcon"
                android:layout_height="wrap_content"
                android:hint="@string/empty_business_name"
                android:textColorHint="@color/black"
                app:passwordToggleDrawable="@null"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="1dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:id="@+id/businessNameEt"
                    android:textSize="16sp"
                    />

            </com.google.android.material.textfield.TextInputLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/businessTypeLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <ImageView
                android:id="@+id/businessTypeIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:src="@drawable/ic_list_category"
                android:tint="@color/colorPrimary" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:hint="@string/select_business_hint"
                app:passwordToggleDrawable="@null"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="1dp"
                app:endIconCheckable="true"
                android:layout_toRightOf="@+id/businessTypeIcon"
                android:id="@+id/custom_end_icon"
                app:endIconDrawable="@drawable/ic_chevron_down_blue"
                app:endIconMode="custom"
                app:endIconTint="@color/colorPrimaryDark">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:id="@+id/businessTypeTv"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:inputType="text"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:focusable="false"
                    tools:text="Toko Minuman / Warkop / Thai Tea / Bubble"
                    android:layout_height="52dp" />

            </com.google.android.material.textfield.TextInputLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/refLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <ImageView
                android:id="@+id/refIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:src="@drawable/ic_referral"
                android:tint="@color/colorPrimary" />

            <TextView
                android:id="@+id/refLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@+id/refIcon"
                android:lineSpacingExtra="3.8sp"
                android:text="@string/ref_code_header"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="12.2sp" />

            <EditText
                android:id="@+id/refCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/refLabel"
                android:layout_toRightOf="@+id/refIcon"
                android:hint="@string/ref_code_hint"
                android:lineSpacingExtra="7.7sp"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="16.3sp" />
        </RelativeLayout>


    </LinearLayout>
</ScrollView>
    <com.google.android.material.button.MaterialButton
        android:id="@+id/next"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        app:backgroundTint="@color/buku_CTA"
        android:text="@string/save"
        android:elevation="6dp"
        android:paddingTop="10dp"
        android:textStyle="bold"
        android:textSize="16sp"
        android:paddingBottom="10dp"
        app:cornerRadius="2dp"
        android:layout_margin="@dimen/_16dp"
        android:layout_alignParentBottom="true"
        android:textColor="@color/cta_button_text"
        app:rippleColor="@color/black_40"/>
</RelativeLayout>