<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_16dp"
    android:background="@drawable/white_background_radius_8">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_cashback"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_24dp"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_cashback" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/total_cashback"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toEndOf="@id/iv_cashback"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_amount_total"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_4dp"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toEndOf="@id/iv_cashback"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="Rp200.000" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/info_cashback"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_36dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/rounded_blue_border_rectangle_4dp"
        android:drawableStart="@drawable/ic_info"
        android:drawablePadding="@dimen/_10dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/_12dp"
        android:text="@string/cashback_yang"
        android:textColor="@color/black60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_amount_total" />

</androidx.constraintlayout.widget.ConstraintLayout>