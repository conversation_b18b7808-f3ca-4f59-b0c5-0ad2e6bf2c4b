<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/label_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:drawableLeftCompat="@drawable/ic_yellow_fav"
        android:text="Barang Favorit"
        android:textColor="@color/black_60"
        android:visibility="gone"
        android:drawablePadding="@dimen/_4dp"
        android:paddingVertical="@dimen/_2dp"
        android:paddingHorizontal="@dimen/_4dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/bg_rounded_rectangle_fff5d9"/>

    <FrameLayout
        android:id="@+id/bg"
        style="@style/Heading3"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/initial_product_name_bg"
        android:textAllCaps="true"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/label_favourite" />

    <TextView
        android:id="@+id/tv_initial"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="@id/bg"
        app:layout_constraintEnd_toEndOf="@id/bg"
        app:layout_constraintStart_toStartOf="@id/bg"
        app:layout_constraintTop_toTopOf="@id/bg"
        tools:text="M" />


    <TextView
        android:id="@+id/tv_product_name"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintEnd_toStartOf="@id/ll_product_count"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toTopOf="@+id/bg"
        app:layout_constraintVertical_chainStyle="spread"
        tools:text="Beras Beras Beras Beras Beras Beras " />

    <TextView
        android:id="@+id/selling_price_currency_txt"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:ellipsize="end"
        android:text="@string/currency"
        android:textColor="@color/green_100"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_name"
        app:layout_constraintStart_toEndOf="@id/bg"
        android:layout_marginTop="@dimen/_5dp" />
    <TextView
        android:id="@+id/selling_price_txt"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="2"
        android:maxLength="20"
        tools:text="50.000"
        android:textColor="@color/green_100"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_name"
        app:layout_constraintStart_toEndOf="@id/selling_price_currency_txt"
        android:layout_marginTop="@dimen/_5dp" />


    <TextView
        android:id="@+id/tv_subtext"
        style="@style/Body2"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:textColor="@color/out_red"
        app:layout_constraintBottom_toBottomOf="@+id/bg"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="" />

    <TextView
        android:id="@+id/tv_measurement"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:textColor="@color/black_40"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        app:layout_constraintStart_toEndOf="@id/selling_price_txt"
        android:layout_marginTop="@dimen/_5dp"
        tools:text="/Karung" />

    <View
        android:id="@+id/view_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ll_product_count"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <LinearLayout
        android:id="@+id/ll_product_count"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        app:layout_constraintWidth_percent="0.25"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_stock_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Heading1"
            android:textColor="@color/blue_60"
            android:layout_marginEnd="@dimen/_16dp"
            tools:text="1"
            android:layout_gravity="end"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/_10dp" />

        <TextView
            android:id="@+id/tv_unit_name"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:textColor="@color/blue_60"
            android:visibility="gone"
            android:layout_gravity="end"
            tools:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/tv_stock_number"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/_16dp"
            tools:text="Karung"
            android:paddingBottom="@dimen/_12dp" />
    </LinearLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_4dp"
        android:background="@color/black_10"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintTop_toBottomOf="@id/selling_price_txt" />

</androidx.constraintlayout.widget.ConstraintLayout>
