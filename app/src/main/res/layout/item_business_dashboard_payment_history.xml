<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_80"
        style="@style/SubHeading1"
        tools:text="Penagihan Hutang"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/label_business_personal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/label_bisnis"
        android:text="Bisnis"
        style="@style/Label2"
        android:paddingHorizontal="@dimen/_6dp"
        android:layout_marginStart="@dimen/_4dp"
        app:layout_constraintStart_toEndOf="@id/tv_item_title"
        app:layout_constraintTop_toTopOf="@id/tv_item_title"/>

    <TextView
        android:id="@+id/tv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="100 Transaksi"
        android:textColor="@color/black_40"
        style="@style/Body3"
        app:layout_constraintStart_toStartOf="@id/tv_item_title"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title"
        android:layout_marginTop="@dimen/_4dp"/>

    <TextView
        android:id="@+id/tv_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Rp 10.000.000"
        android:textColor="@color/black_80"
        style="@style/Body3"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title"
        android:layout_marginTop="@dimen/_4dp"/>

    <View
        android:id="@+id/vw_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_5"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_count"
        app:layout_constraintStart_toStartOf="@id/tv_item_title"
        app:layout_constraintEnd_toEndOf="@id/tv_amount"/>


</androidx.constraintlayout.widget.ConstraintLayout>