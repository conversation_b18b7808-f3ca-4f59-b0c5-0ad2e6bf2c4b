<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contactLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center"
    android:paddingStart="16dp"
    android:paddingTop="@dimen/_12dp"
    android:paddingEnd="@dimen/_16dp">

    <FrameLayout
        android:id="@+id/pic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/view_bottom_line"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/contact_photo"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:gravity="center" />

        <TextView
            android:id="@+id/nameInitials"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/oval_0"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:visibility="gone"
            tools:text="A" />
    </FrameLayout>

    <TextView
        android:id="@+id/name"
        style="@style/ContactTextStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="4sp"
        android:maxLines="1"
        android:textAlignment="textStart"
        android:textColor="@color/body_text"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/mobile"
        app:layout_constraintEnd_toStartOf="@id/btn_invite"
        app:layout_constraintStart_toEndOf="@id/pic"
        app:layout_constraintTop_toTopOf="@id/pic"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Customer" />

    <TextView
        android:id="@+id/mobile"
        style="@style/ContactTextStyle"
        android:layout_width="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/body_text"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="@id/pic"
        app:layout_constraintStart_toEndOf="@id/pic"
        app:layout_constraintTop_toBottomOf="@id/name"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="+628087888899" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_invite"
        style="@style/ButtonFill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:padding="@dimen/_8dp"
        android:text="@string/invite_label"
        app:layout_constraintBottom_toBottomOf="@id/pic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/pic" />


    <View
        android:id="@+id/view_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/pic"
        android:layout_alignStart="@+id/pic"
        android:layout_marginTop="@dimen/_12dp"
        android:background="#e8eef1"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pic" />
</androidx.constraintlayout.widget.ConstraintLayout>
