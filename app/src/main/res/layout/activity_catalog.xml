<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:layout_marginBottom="@dimen/_56dp"
    android:fitsSystemWindows="true">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <ImageView
                android:id="@+id/iv_banner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="?attr/actionBarSize"
                android:scaleType="fitXY"
                app:srcCompat="@drawable/catalog_banner" />


            <include
                android:id="@+id/include_tool_bar"
                layout="@layout/layout_activity_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                app:layout_collapseMode="pin" />
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.bukuwarung.ui_component.component.error_view.BukuErrorView
            android:id="@+id/buku_error_view"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="@dimen/dimen_0dp"
            android:layout_marginTop="@dimen/dimen_10dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_filter"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_13dp"
            android:background="@drawable/bg_solid_grey_corner_16_stroke_1"
            android:paddingHorizontal="@dimen/_14dp"
            android:paddingVertical="@dimen/_9dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_category_filter" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_filter"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_13dp"
            android:layout_marginEnd="@dimen/_10dp"
            android:background="@color/white"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_filter"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_banner"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:visibility="gone"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_filter"
            tools:visibility="visible" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tb_banner"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_6dp"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:background="@color/transparent"
            android:foregroundGravity="bottom"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/vp_banner"
            app:layout_constraintTop_toBottomOf="@+id/vp_banner"
            app:tabBackground="@drawable/selector_banner"
            app:tabGravity="center"
            app:tabIndicatorHeight="@dimen/_0dp"
            app:tabPaddingEnd="@dimen/_8dp"
            app:tabPaddingStart="@dimen/_8dp"
            app:tabSelectedTextColor="@android:color/transparent"
            app:tabTextColor="@android:color/transparent"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Group
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/grp_banner"
            app:constraint_referenced_ids="tb_banner, vp_banner"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_0dp"
            android:layout_marginTop="@dimen/_20dp"
            android:background="@color/black_5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tb_banner" />

        <com.bukuwarung.payments.compoundviews.EnterPPOBAmountView
            android:id="@+id/amount_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_filter" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
