<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="20dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_profile_payment_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text= "Profil Tidak Bisa Dihapus"
        style="@style/Heading2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_dialog_payment_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:layout_marginTop="20dp"
        android:text= "Profil ini tidak bisa dihapus karena sudah terintegrasi dengan aplikasi BukuWarung di mesin EDC kamu."
        app:layout_constraintTop_toBottomOf="@id/tv_profile_payment_title" />
    
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_ok"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/ButtonFill.Blue"
        android:text="Mengerti"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_payment_details"
        android:layout_marginTop="20dp" />


</androidx.constraintlayout.widget.ConstraintLayout>