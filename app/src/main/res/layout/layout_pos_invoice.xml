<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            >

    <include
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:id="@+id/layout_store_detail"
        layout="@layout/layout_store_detail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <include
        android:id="@+id/layout_header_pos"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_store_detail"
        layout="@layout/layout_header_pos"/>

    <include
        android:id="@+id/layout_transaction_status"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        layout="@layout/layout_transaction_status"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_header_pos"/>

    <LinearLayout
        android:id="@+id/product_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/layout_transaction_status"
        tools:background="@color/black_10"
        tools:layout_height="96dp" />

    <include
        android:id="@+id/layout_payment_detail"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        layout="@layout/layout_payment_detail_pos"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/product_container"/>


    <include
        android:id="@+id/layout_footer_pos"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        layout="@layout/layout_footer_invoice"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_payment_detail"/>

        <LinearLayout
                android:id="@+id/banner_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black_5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_footer_pos">

                <ImageView
                    android:id="@+id/img_mission_banner"
                    android:layout_width="match_parent"
                    android:layout_height="134dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_18dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_40dp" />
        </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>