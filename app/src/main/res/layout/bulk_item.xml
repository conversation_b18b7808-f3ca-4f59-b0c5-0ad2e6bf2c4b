<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_parent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <RadioGroup
        android:id="@+id/rg_status"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/dimen_24dp"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_total_penjualan_select"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/trx_type_green_bg_selector_normal"
            android:button="@null"
            android:paddingStart="@dimen/dimen_24dp"
            android:paddingEnd="@dimen/dimen_24dp"
            android:text="@string/sales"
            android:textColor="@color/white"
            android:layout_weight="1"/>

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_total_pengeluaran_select"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/trx_type_red_bg_selector_normal"
            android:layout_weight="1"
            android:layout_marginStart="-5dp"
            android:button="@null"
            android:paddingStart="@dimen/dimen_24dp"
            android:paddingEnd="@dimen/dimen_24dp"
            android:text="@string/expense_label"
            android:textColor="@color/white" />
    </RadioGroup>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_bulk_delete"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_0dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@id/rg_status"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/rg_status"
        app:srcCompat="@drawable/ic_delete" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_total_penjualan"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/_16dp"
        android:drawablePadding="@dimen/_16dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center"
        android:text="@string/total_penjualan"
        android:textSize="@dimen/text_12sp"
        app:drawableStartCompat="@drawable/ic_debit"
        app:layout_constraintStart_toStartOf="@id/rg_status"
        app:layout_constraintTop_toBottomOf="@id/rg_status" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_total_pengeluaran"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:drawablePadding="@dimen/_16dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center"
        android:text="@string/harga_modal"
        android:textSize="@dimen/text_12sp"
        app:drawableStartCompat="@drawable/ic_credit"
        app:layout_constraintStart_toEndOf="@id/vw_divider"
        app:layout_constraintTop_toTopOf="@id/tv_total_penjualan" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_penjualan_rp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:autoSizeTextType="uniform"
        app:autoSizeMaxTextSize="100sp"
        app:autoSizeMinTextSize="12sp"
        app:autoSizeStepGranularity="2sp"
        android:fontFamily="@font/roboto_bold"
        android:text="Rp"
        android:textColor="@color/green_80"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintStart_toStartOf="@id/tv_total_penjualan"
        app:layout_constraintTop_toBottomOf="@id/tv_total_pengeluaran" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/et_balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        app:autoSizeTextType="uniform"
        app:autoSizeMaxTextSize="100sp"
        app:autoSizeMinTextSize="12sp"
        app:autoSizeStepGranularity="2sp"
        android:fontFamily="@font/roboto_bold"
        android:hint="0"
        android:inputType="number"
        android:maxLength="10"
        android:textColor="@color/green_80"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintBottom_toBottomOf="@id/tv_penjualan_rp"
        app:layout_constraintStart_toEndOf="@+id/tv_penjualan_rp"
        app:layout_constraintTop_toTopOf="@id/tv_penjualan_rp" />

    <View
        android:id="@+id/vw_balance"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="@id/tv_total_penjualan"
        app:layout_constraintEnd_toEndOf="@id/tv_total_penjualan"
        app:layout_constraintTop_toTopOf="@id/et_balance"
        app:layout_constraintBottom_toBottomOf="@id/et_balance" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="@dimen/_1dp"
        android:layout_height="@dimen/_0dp"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:background="@color/black_10"
        app:layout_constraintBottom_toBottomOf="@+id/et_balance"
        app:layout_constraintStart_toEndOf="@+id/tv_total_penjualan"
        app:layout_constraintTop_toTopOf="@+id/tv_total_penjualan" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_pengeluaran_rp"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:autoSizeMaxTextSize="100sp"
        android:autoSizeMinTextSize="12sp"
        android:autoSizeStepGranularity="2sp"
        android:autoSizeTextType="uniform"
        android:fontFamily="@font/roboto_bold"
        android:text="Rp"
        android:textColor="@color/red_80"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintStart_toStartOf="@id/tv_total_pengeluaran"
        app:layout_constraintTop_toBottomOf="@id/tv_total_pengeluaran" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/et_harga"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        app:autoSizeTextType="uniform"
        app:autoSizeMaxTextSize="100sp"
        app:autoSizeMinTextSize="12sp"
        app:autoSizeStepGranularity="2sp"
        android:fontFamily="@font/roboto_bold"
        android:hint="0"
        android:inputType="number"
        android:maxLength="10"
        android:textColor="@color/red_80"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintBottom_toBottomOf="@id/tv_pengeluaran_rp"
        app:layout_constraintStart_toEndOf="@id/tv_pengeluaran_rp"
        app:layout_constraintTop_toBottomOf="@id/tv_total_pengeluaran"
        app:layout_constraintTop_toTopOf="@id/tv_pengeluaran_rp" />

    <View
        android:id="@+id/vw_harga"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="@id/tv_total_pengeluaran"
        app:layout_constraintEnd_toEndOf="@id/tv_total_pengeluaran"
        app:layout_constraintTop_toTopOf="@id/et_harga"
        app:layout_constraintBottom_toBottomOf="@id/et_harga" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_profit"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:background="@color/green_80_transparent_10"
        android:padding="@dimen/_8dp"
        app:layout_constraintBottom_toTopOf="@+id/iv_note"
        app:layout_constraintEnd_toEndOf="@id/iv_bulk_delete"
        app:layout_constraintStart_toStartOf="@id/rg_status"
        app:layout_constraintTop_toBottomOf="@id/et_balance">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_profit"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:fontFamily="@font/roboto"
            android:text="@string/profit_kamu"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_profit_val"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:maxLength="10"
            android:text="Rp100.000"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_note"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:paddingTop="@dimen/_8dp"
        app:layout_constraintBottom_toTopOf="@id/vw_bottom"
        app:layout_constraintStart_toStartOf="@id/rg_status"
        app:srcCompat="@drawable/ic_note_icon" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_note"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:hint="@string/notes_new"
        android:imeOptions="actionDone"
        android:inputType="text"
        android:paddingTop="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="@id/iv_bulk_delete"
        app:layout_constraintStart_toEndOf="@+id/iv_note"
        app:layout_constraintTop_toTopOf="@+id/iv_note" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gp_credit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tv_total_penjualan, tv_total_pengeluaran, cl_profit, et_balance, et_harga, tv_pengeluaran_rp, tv_penjualan_rp, vw_divider, vw_harga, vw_balance" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_pengeluaran"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:drawablePadding="@dimen/_8dp"
        android:paddingTop="@dimen/_2dp"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/total_expense_label"
        android:textSize="@dimen/text_14sp"
        app:drawableStartCompat="@drawable/ic_credit"
        app:layout_constraintBottom_toTopOf="@+id/iv_note"
        app:layout_constraintStart_toStartOf="@id/rg_status"
        app:layout_constraintTop_toBottomOf="@+id/rg_status"
        app:layout_constraintWidth_percent="0.6" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_pengeluaran_r"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoSizeMaxTextSize="100sp"
        android:autoSizeMinTextSize="12sp"
        android:autoSizeStepGranularity="2sp"
        android:autoSizeTextType="uniform"
        android:fontFamily="@font/roboto_bold"
        android:text="Rp"
        android:textColor="@color/red_80"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintStart_toEndOf="@id/rg_status"
        app:layout_constraintTop_toBottomOf="@+id/rg_status"
        app:layout_constraintTop_toTopOf="@id/tv_pengeluaran" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/et_pengeluaran_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        app:autoSizeTextType="uniform"
        app:autoSizeMaxTextSize="100sp"
        app:autoSizeMinTextSize="12sp"
        app:autoSizeStepGranularity="2sp"
        android:fontFamily="@font/roboto_bold"
        android:hint="0"
        android:inputType="number"
        android:maxLength="10"
        android:textColor="@color/red_80"
        android:textSize="@dimen/text_18sp"
        app:fontFamily="@font/roboto_bold"
        app:layout_constraintBottom_toBottomOf="@id/tv_pengeluaran_r"
        app:layout_constraintStart_toEndOf="@+id/tv_pengeluaran_r"
        app:layout_constraintTop_toTopOf="@+id/tv_pengeluaran_r" />

    <View
        android:id="@+id/vw_pengeluaran"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="@id/et_pengeluaran_text"
        app:layout_constraintEnd_toEndOf="@id/iv_bulk_delete"
        app:layout_constraintTop_toTopOf="@id/et_pengeluaran_text"
        app:layout_constraintBottom_toBottomOf="@id/et_pengeluaran_text" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gp_debit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="et_pengeluaran_text, tv_pengeluaran, tv_pengeluaran_r, vw_pengeluaran" />

    <View
        android:id="@+id/vw_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_10"
        app:layout_constraintTop_toBottomOf="@+id/iv_note" />

    <LinearLayout
        android:id="@+id/ll_result"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_expr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!--cursors-->

    <View
        android:id="@+id/balance_cursor"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:background="@color/green_80"
        app:layout_constraintBottom_toBottomOf="@id/et_balance"
        app:layout_constraintStart_toEndOf="@id/et_balance"
        app:layout_constraintTop_toTopOf="@id/et_balance" />

    <View
        android:id="@+id/harga_cursor"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:background="@color/red_80"
        app:layout_constraintBottom_toBottomOf="@id/et_harga"
        app:layout_constraintStart_toEndOf="@id/et_harga"
        app:layout_constraintTop_toTopOf="@id/et_harga" />

    <View
        android:id="@+id/pengeluaran_cursor"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:background="@color/red_80"
        app:layout_constraintBottom_toBottomOf="@id/et_pengeluaran_text"
        app:layout_constraintStart_toEndOf="@id/et_pengeluaran_text"
        app:layout_constraintTop_toTopOf="@id/et_pengeluaran_text" />

</androidx.constraintlayout.widget.ConstraintLayout>