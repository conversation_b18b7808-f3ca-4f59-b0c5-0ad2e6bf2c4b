<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".feature.login.password.screen.PasswordActivity">

    <com.bukuwarung.ui_component.component.appbar.SimpleAppBar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:backButton="true"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="@string/enter"
        app:titleTextAppearance="@style/Heading2"
        android:id="@+id/toolbar"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        android:id="@+id/successLayout"
        >

        <TextView
            android:id="@+id/tv_password_label"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_40dp"
            android:text="@string/password"
            android:textSize="@dimen/dimen_14sp"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/password_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5dp"
            app:boxStrokeWidth="@dimen/_1dp"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:endIconMode="password_toggle"
            app:layout_constraintStart_toStartOf="@+id/tv_password_label"
            app:layout_constraintTop_toBottomOf="@+id/tv_password_label"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/tv_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/enter_password"
                android:inputType="textPassword"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:maxLength="18"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/tv_error"
            style="@style/Body3"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/password_does_not_match"
            android:textColor="@color/red80"
            android:textSize="@dimen/dimen_14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/password_layout" />

        <TextView
            android:id="@+id/tv_forgot_password_label"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginStart="16dp"
            android:textColor="@color/blue40"
            android:layout_marginTop="@dimen/_24dp"
            android:text="@string/forgot_password"
            android:textSize="@dimen/dimen_14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_error" />

        <!--    app:drawableStartCompat="@drawable/ic_warning_red"-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/btn_enter"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:visibility="gone"
            android:background="@drawable/bg_warning_login"
            android:paddingVertical="@dimen/_14dp"
            android:paddingHorizontal="@dimen/_14dp"
            android:id="@+id/alert_layout"
            >

            <TextView
                android:id="@+id/tv_warning_title"
                style="@style/SubHeading1"
                android:textColor="@color/out_red"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                app:layout_constraintTop_toTopOf="parent"
                android:text="@string/only_two_attempts_remaining"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />
            <TextView
                android:id="@+id/tv_warning_txt"
                style="@style/Body3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/_4dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_warning_title"
                android:text="@string/account_will_be_frozen_message"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.bukuwarung.ui_component.component.button.BukuButton
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_margin="@dimen/_16dp"
            android:id="@+id/btn_enter"
            android:textSize="@dimen/text_16sp"
            android:textAllCaps="false"
            app:buttonText="@string/enter"
            app:buttonType="disableGrey"
            android:padding="@dimen/_14dp"
            app:layout_constraintStart_toStartOf="parent" />



    </androidx.constraintlayout.widget.ConstraintLayout>


    <ProgressBar
        android:id="@+id/progressbar"
        android:visibility="visible"
        tools:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/serverErrorLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/errorImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="70dp"
            android:layout_marginTop="@dimen/_40dp"
            android:src="@mipmap/brick_diconnected_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/errorMessage"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_30dp"
            android:gravity="center"
            android:text="@string/error_state_message"
            app:layout_constraintEnd_toEndOf="@+id/errorImage"
            app:layout_constraintStart_toStartOf="@+id/errorImage"
            app:layout_constraintTop_toBottomOf="@+id/errorImage" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRetry"
            style="@style/ButtonOutline.Blue"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_40dp"
            android:layout_marginBottom="@dimen/_50dp"
            android:enabled="true"
            android:paddingTop="@dimen/_10dp"
            android:paddingBottom="@dimen/_10dp"
            android:text="@string/retry"
            android:textAllCaps="false"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold"
            app:cornerRadius="@dimen/_2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/errorMessage"
            app:layout_constraintStart_toStartOf="@+id/errorMessage"
            app:layout_constraintTop_toBottomOf="@+id/errorMessage" />


    </androidx.constraintlayout.widget.ConstraintLayout>





</androidx.constraintlayout.widget.ConstraintLayout>