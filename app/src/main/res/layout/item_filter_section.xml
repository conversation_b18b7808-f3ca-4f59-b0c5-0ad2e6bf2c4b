<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_filter_section"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_12dp"
        android:textColor="@color/black80"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Pembayaran" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_filters"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:itemCount="3"
        tools:listitem="@layout/item_filter" />

</androidx.constraintlayout.widget.ConstraintLayout>
