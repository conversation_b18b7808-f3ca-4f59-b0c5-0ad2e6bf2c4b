<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_query"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:paddingHorizontal="@dimen/_16dp"
            app:hintAnimationEnabled="false"
            app:endIconMode="clear_text"
            app:hintEnabled="false">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/tv_query"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/geolocation_search_hint"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_2dp"
            android:background="@color/black_5" />

        <TextView
            android:id="@+id/tv_current_location"
            style="@style/Body2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:background="?attr/selectableItemBackground"
            android:drawablePadding="@dimen/_8dp"
            android:padding="@dimen/_16dp"
            android:text="@string/use_current_location"
            android:textColor="@color/colorPrimary"
            app:drawableStartCompat="@drawable/ic_current_location"
            app:drawableTint="@color/colorPrimary" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_2dp"
            android:background="@color/black_5" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_not_found"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:padding="@dimen/_16dp"
                android:text="@string/no_address_found_message"
                android:textColor="@color/black_40"
                android:visibility="gone"
                tools:visibility="visible" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_address"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:itemCount="5"
                tools:listitem="@layout/item_address_layout" />

            <com.bukuwarung.activities.addcustomer.ErrorScreen
                android:id="@+id/error_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                tools:visibility="visible" />

        </FrameLayout>


    </LinearLayout>

    <FrameLayout
        android:id="@+id/pb_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:background="@color/dialog_transparent"
        tools:visibility="visible">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true" />
    </FrameLayout>

</FrameLayout>