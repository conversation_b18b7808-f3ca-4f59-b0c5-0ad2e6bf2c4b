<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp">

    <View
        android:id="@+id/vw_divider"
        style="@style/Divider"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30dp"
        android:adjustViewBounds="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider"
        app:srcCompat="@drawable/onboarding_bg" />


    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/bukuwarung_onboarding_bottomsheet"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_18dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_bg" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_16dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:text="@string/bukuwarung_onboarding_bottomsheet_subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_18dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_next"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_38dp"
        android:text="@string/start_explore"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle" />

</androidx.constraintlayout.widget.ConstraintLayout>