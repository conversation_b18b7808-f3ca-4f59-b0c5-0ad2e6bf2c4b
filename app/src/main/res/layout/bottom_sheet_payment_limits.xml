<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_10dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingBottom="@dimen/_20dp">

    <View
        android:id="@+id/view_close_bar"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:background="@drawable/background_filled_black10_border_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30dp"
        android:gravity="center"
        android:text="@string/want_to_continue_transaction"
        app:layout_constraintTop_toBottomOf="@id/view_close_bar" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:textColor="@color/black_80"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="Batas tiap transaksi kamu hanya Rp2.000.000. Untuk melanjutkan, kamu harus verifikasi KTP." />

    <TextView
        android:id="@+id/tv_message_2"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/kyc_account_benefits"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

    <TextView
        android:id="@+id/tv_account_verify_info"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/more"
        android:textColor="@color/colorPrimary"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_messages"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_message, tv_message_2, tv_account_verify_info" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_verify_ktp"
        style="@style/ButtonFill.Blue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/account_verification"
        app:layout_constraintTop_toBottomOf="@id/br_messages" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_verify_kyc_full"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_38dp"
        android:text="@string/account_verification_now"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/br_messages" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_later"
        style="@style/ButtonOutline.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_38dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:text="@string/cancel_btn"
        app:layout_constraintEnd_toStartOf="@id/btn_verify_kyc_half"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/br_messages" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_verify_kyc_half"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_38dp"
        android:text="@string/account_verification"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_later"
        app:layout_constraintTop_toBottomOf="@id/br_messages" />

</androidx.constraintlayout.widget.ConstraintLayout>
