<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_top_rounded_corner"
    android:padding="@dimen/_16dp">

    <ImageView
        android:id="@+id/closeDialog"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        app:srcCompat="@drawable/close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="10dp"/>

    <ImageView
        android:id="@+id/iv_processing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_bank_account_used"
        android:layout_marginTop="22dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tv_data_sent"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bank_account_used_title"
        android:textAlignment="center"
        android:layout_marginTop="@dimen/_14dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_processing"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/subtitle_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bank_account_used_subtitle"
        android:textColor="@color/black_60"
        android:textAlignment="center"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_data_sent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/ButtonOutline.Blue1"
        android:textSize="@dimen/text_16sp"
        android:text="@string/try_other_account"
        android:textColor="@color/blue_60"
        android:paddingVertical="@dimen/_8dp"
        android:paddingHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@id/subtitle_txt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />
</androidx.constraintlayout.widget.ConstraintLayout>