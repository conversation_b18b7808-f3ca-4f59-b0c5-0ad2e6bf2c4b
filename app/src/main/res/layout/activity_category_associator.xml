<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context=".activities.productcategory.view.CategoryAssociatorActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:theme="@style/Toolbar"
        app:layout_constraintTop_toTopOf="parent"
        app:titleTextAppearance="@style/Heading2"
        app:navigationIcon="@drawable/ic_close"
        app:title="Category Name should be here"
        app:titleTextColor="@color/white" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_search"
            style="@style/OutlineTextInputStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:endIconDrawable="@drawable/ic_mtrl_chip_close_circle"
            app:endIconMode="clear_text"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:startIconDrawable="@drawable/ic_icon_search">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:imeOptions="actionSearch"
                android:layout_height="wrap_content"
                android:hint="@string/search_product" />

        </com.google.android.material.textfield.TextInputLayout>

    </FrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_product"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_5"
        app:layout_constraintBottom_toTopOf="@id/btn_container" />


    <FrameLayout
        android:id="@+id/btn_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_confirm"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/save_changes" />

    </FrameLayout>

</LinearLayout>