<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/txt_title"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/stock_tab_popup_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txt_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/txt_title"
        android:text="@string/stock_tab_disable_warning"
        />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_cancel"
        style="@style/Button.OutlinePrimary"
        android:layout_width="@dimen/_0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="4dp"
        android:text="@string/cancel"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="@+id/btn_save"
        app:layout_constraintEnd_toStartOf="@id/btn_save"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btn_save" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill.Blue"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/ya"
        android:textAllCaps="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_cancel"
        app:layout_constraintTop_toBottomOf="@id/txt_description" />
</androidx.constraintlayout.widget.ConstraintLayout>