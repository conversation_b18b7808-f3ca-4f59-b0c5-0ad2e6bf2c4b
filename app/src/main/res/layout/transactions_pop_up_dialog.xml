<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/transactions_dialog_layout"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:backgroundTint="@color/black_40"
    android:layout_height="wrap_content"
    android:padding="@dimen/_8dp"
    android:background="@color/fui_transparent">

    <LinearLayout
        android:id="@+id/close_dialog"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:background="@drawable/circle_with_white_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:elevation="8dp"
        tools:ignore="MissingConstraints">
        <ImageView
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:src="@drawable/close"
            app:tint="@color/black_60"
            android:layout_margin="7dp" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/fui_transparent"
        android:padding="14dp"
        tools:ignore="MissingConstraints">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_white_radius16"
            tools:ignore="MissingConstraints">

        <ImageView
            android:id="@+id/transactions_image"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:scaleType="fitXY"
            android:src="@drawable/fifth_pop_up_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/transactions_heading"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/transactions_heading"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="heading"
            android:textColor="@color/black_80"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="@font/roboto"
            android:lineHeight="24dp"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/transactions_image"
            app:layout_constraintBottom_toTopOf="@+id/transactions_body"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/transactions_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="body"
            android:lineHeight="20dp"
            android:textColor="@color/black_60"
            android:textSize="14sp"
            android:fontFamily="@font/roboto"
            app:layout_constraintTop_toBottomOf="@+id/transactions_heading"
            app:layout_constraintBottom_toTopOf="@+id/button"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            tools:ignore="MissingConstraints" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button"
            style="@style/DefaultMaterialButtonStyleAdjacent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:layout_marginBottom="@dimen/_16dp"
            android:padding="12dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:gravity="center_horizontal"
            android:textColor="@color/cta_button_text"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="4dp"
            android:text="@string/save"
            android:textAllCaps="false"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/transactions_body"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="MissingConstraints" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
