<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/_16dp">

    <View
        android:id="@+id/vw_divider"
        android:layout_width="@dimen/_48dp"
        android:layout_height="4dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_heading"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:text="@string/why_trx_pending"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider" />

    <TextView
        android:id="@+id/tv_network_issue"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_20dp"
        android:drawablePadding="@dimen/_16dp"
        android:text="@string/network_problem_message"
        app:drawableStartCompat="@drawable/ic_bank_building"
        app:layout_constraintTop_toBottomOf="@id/tv_heading" />

    <TextView
        android:id="@+id/tv_refund_time"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_20dp"
        android:drawablePadding="@dimen/_16dp"
        android:text="@string/wait_pending_trx_message"
        app:drawableStartCompat="@drawable/ic_stop_watch"
        app:layout_constraintTop_toBottomOf="@id/tv_network_issue" />

    <TextView
        android:id="@+id/tv_automatic_refund"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_20dp"
        android:drawablePadding="@dimen/_16dp"
        android:text="@string/automatic_refund_message"
        app:drawableStartCompat="@drawable/ic_hand_cash"
        app:layout_constraintTop_toBottomOf="@id/tv_refund_time" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_confirm"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_48dp"
        android:text="@string/confirm_label"
        app:layout_constraintTop_toBottomOf="@+id/tv_automatic_refund" />

</androidx.constraintlayout.widget.ConstraintLayout>
