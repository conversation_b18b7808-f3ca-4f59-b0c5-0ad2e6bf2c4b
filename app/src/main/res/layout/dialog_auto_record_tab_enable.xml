<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/txt_title"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/auto_record_tab_popup_title_question"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txt_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:gravity="center"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:text="@string/auto_record_tab_popup_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txt_title" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/buttonLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txt_description">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill.Red"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/auto_record_tab_popup_title"
            android:textAllCaps="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Button.OutlineRed"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:text="@string/cancel_selectable"
            android:textAllCaps="false"
            android:textColor="@color/red_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_save" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>