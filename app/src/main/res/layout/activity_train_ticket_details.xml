<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/include_timer"
        layout="@layout/layout_ppob_timer"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include_tool_bar"
        tools:visibility="visible" />


    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_view"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:background="@color/black_5"
        app:layout_constraintBottom_toTopOf="@+id/btn_complete_payment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_timer">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?attr/actionBarSize">

            <ImageView
                android:id="@+id/iv_logo"
                android:layout_width="70dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginVertical="@dimen/dimen_8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_label"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:layout_marginVertical="@dimen/dimen_8dp"
                android:gravity="center_vertical"
                android:text="@string/train_ticket_label"
                app:layout_constraintStart_toEndOf="@id/iv_logo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/iv_logo"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/vw_train_time_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dimen_16dp"
                android:background="@drawable/bg_rounded_rectangle_white_8dp"
                android:padding="@dimen/dimen_8dp"
                app:layout_constraintTop_toBottomOf="@id/iv_logo">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/vw_train_details"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_rounded_rectangle_blue_5"
                    android:padding="@dimen/dimen_8dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tv_train_name"
                        style="@style/Body3"
                        android:layout_width="@dimen/dimen_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dimen_8dp"
                        android:textColor="@color/blue80"
                        app:layout_constraintEnd_toStartOf="@id/tv_train_class"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Argo Parahyangan" />

                    <TextView
                        android:id="@+id/tv_train_class"
                        style="@style/SubHeading2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dimen_8dp"
                        android:textColor="@color/blue80"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Eksekutif(A)" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_src_station"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_train_details"
                    tools:text="Gambir" />

                <TextView
                    android:id="@+id/tv_src_station_code"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_4dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    app:layout_constraintStart_toEndOf="@id/tv_src_station"
                    app:layout_constraintTop_toBottomOf="@id/vw_train_details"
                    tools:text="(GMR)" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    app:srcCompat="@drawable/ic_arrow_right_black"
                    app:layout_constraintBottom_toBottomOf="@id/tv_src_station"
                    app:layout_constraintStart_toEndOf="@id/tv_src_station_code"
                    app:layout_constraintTop_toTopOf="@id/tv_src_station" />

                <TextView
                    android:id="@+id/tv_dest_station"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    app:layout_constraintStart_toEndOf="@id/iv_arrow"
                    app:layout_constraintTop_toBottomOf="@id/vw_train_details"
                    tools:text="Bandung" />

                <TextView
                    android:id="@+id/tv_dest_station_code"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_4dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    app:layout_constraintStart_toEndOf="@id/tv_dest_station"
                    app:layout_constraintTop_toBottomOf="@id/vw_train_details"
                    tools:text="(BD)" />

                <TextView
                    android:id="@+id/tv_depart_time"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_src_station"
                    tools:text="Jum, 25 Nov 2021 ・14:00" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.bukuwarung.payments.compoundviews.BillDetailExpandableView
                android:id="@+id/bill_detail_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vw_train_time_detail" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/tv_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dimen_16dp"
                app:alertText="@string/eticket_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bill_detail_view"
                app:type="information" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/vw_passenger_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dimen_16dp"
                android:background="@drawable/bg_rounded_rectangle_white_8dp"
                android:padding="@dimen/dimen_8dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_warning">

                <TextView
                    android:id="@+id/tv_detail_transaksi"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    android:text="@string/detail_passenger"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_check_details"
                    style="@style/Label2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    android:layout_marginTop="@dimen/_2dp"
                    android:text="@string/check_details_message"
                    android:textColor="@color/black_40"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_detail_transaksi" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_passenger_detail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_check_details" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/pb_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        tools:visibility="visible" />


    <com.bukuwarung.ui_component.component.button.BukuButton
        android:id="@+id/btn_complete_payment"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:padding="@dimen/_12dp"
        app:buttonText="@string/go_on"
        app:buttonType="yellow60"
        android:textAllCaps="false"
        android:visibility="gone"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sv_view"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>