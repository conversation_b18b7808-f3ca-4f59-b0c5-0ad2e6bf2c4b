<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/round_corners_top_white_bg"
    android:paddingTop="@dimen/_10dp">

    <View
        android:id="@+id/sheet_anchor"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_4dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/_10dp"
        android:background="@color/black_10"
        android:gravity="center_horizontal"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toBottomOf="@id/sheet_anchor"        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <RadioGroup
            android:id="@+id/radio_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="visible"
            android:weightSum="2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RadioButton
                android:id="@+id/button1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="0dp"
                android:layout_weight="1"
                android:background="@drawable/tab_radio_selector"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                android:paddingBottom="@dimen/_10dp"

                android:text="Pemasukan" />

            <RadioButton
                android:id="@+id/button2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="0dp"
                android:layout_weight="1"
                android:background="@drawable/tab_radio_selector"
                android:button="@null"
                android:gravity="center"
                android:paddingBottom="@dimen/_10dp"
                android:text="Pengeluaran" />
        </RadioGroup>

        <TextView
            android:id="@+id/sheetheadingId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal"
            android:text="Produk paling laris di tokomu"
            style="@style/Body2"
            android:fontFamily="@font/roboto_bold"
            android:textColor="@color/black_80"
            android:textSize="18sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_sheet_subheading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:gravity="center_horizontal"
            android:paddingStart="@dimen/_10dp"
            android:paddingEnd="@dimen/_10dp"
            android:paddingBottom="@dimen/_20dp"
            android:layout_marginEnd="@dimen/_20dp"
            android:layout_marginStart="@dimen/_20dp"
            android:text="Ada 10 daftar produk jualan yang paling sering dibeli oleh pelanggan kamu"
            android:textColor="@color/black_60"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sheetheadingId" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/list_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:layout_centerHorizontal="true"
        android:background="@color/black_10"
        android:gravity="center_horizontal"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/header"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/listRecyclerView"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_0dp"
        android:background="@color/white"
        app:layout_constraintHeight_default="wrap"
        android:clipToPadding="false"
        android:fadeScrollbars="false"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        app:layout_constraintBottom_toTopOf="@id/btn_dismiss"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/list_divider"
        tools:listitem="@layout/item_bottom_sheet_list" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_dismiss"
        style="@style/ButtonFillNoPadding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_50dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:text="@string/button_dismiss"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

