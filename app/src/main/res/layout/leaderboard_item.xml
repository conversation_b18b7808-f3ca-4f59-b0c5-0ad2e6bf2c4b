<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/mainContainer"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp">

    <LinearLayout
        android:id="@+id/rankContainer"
        android:layout_width="48dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/text_rank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/default_placeholder"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:textSize="18sp"
            android:layout_marginEnd="@dimen/_8dp"/>

        <ImageView
            android:id="@+id/trophyImage"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:visibility="gone"
            tools:ignore="ContentDescription" />

    </LinearLayout>

    <TextView
        android:id="@+id/firstLetter"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/oval_0"
        android:gravity="center"
        android:text="@string/default_placeholder"
        tools:text="A"
        android:fontFamily="@font/roboto"
        android:maxLength="2"
        android:textAllCaps="true"
        android:textColor="@color/white"
        android:textSize="22sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/rankContainer"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/text_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/default_placeholder"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintRight_toLeftOf="@id/text_point"
        app:layout_constraintLeft_toRightOf="@id/firstLetter"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/text_point"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/default_placeholder"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold"
        android:textSize="18sp"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintLeft_toRightOf="@id/text_name"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
