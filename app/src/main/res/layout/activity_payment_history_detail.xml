<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_payment_history_detail"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

   <include
       android:id="@+id/include_tool_bar"
       android:layout_width="@dimen/_0dp"
       android:layout_height="wrap_content"
       layout="@layout/layout_activity_title"
       app:layout_constraintStart_toStartOf="parent"
       app:layout_constraintTop_toTopOf="parent"
       app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_view"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:background="@color/black_5"
        app:layout_constraintBottom_toTopOf="@+id/btn_complete_payment"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?attr/actionBarSize">

            <TextView
                android:id="@+id/tv_error"
                style="@style/SubHeading2"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:background="@color/red_5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingVertical="@dimen/_14dp"
                android:text="@string/kwh_error"
                android:textColor="@color/red_80"
                android:visibility="gone"
                app:drawableTint="@color/red_80"
                app:layout_constraintEnd_toStartOf="@id/tv_error_action"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_error_action"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_8dp"
                android:background="@drawable/bg_rounded_rectangle_red60_4dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/_20dp"
                android:paddingVertical="@dimen/_4dp"
                android:text="@string/select"
                android:textColor="@color/white"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tv_error"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_error"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_success"
                style="@style/SubHeading2"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:background="@color/green_5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingVertical="@dimen/_14dp"
                android:textColor="@color/green_80"
                android:visibility="gone"
                app:drawableTint="@color/green_80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Rekening penerima QRIS berhasil diubah. Silakan lakukan transaksi ulang dengan tekan coba lagi."
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_info"
                style="@style/SubHeading2"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:background="@color/blue_5"
                android:drawablePadding="@dimen/_4dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingVertical="@dimen/_14dp"
                android:textColor="@color/blue_90"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/ic_close"
                app:drawableTint="@color/blue_90"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Jika masih gagal, silakan coba lagi pada pukul 12:15"
                tools:visibility="visible" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/ba_refund_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/br_top_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="tv_error, tv_success, tv_info, ba_refund_info" />

            <View
                android:id="@+id/vw_top_background"
                android:layout_width="@dimen/_0dp"
                android:layout_height="@dimen/_150dp"
                android:background="@drawable/bg_blue_white_gradient"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/br_top_message" />

            <TextView
                android:id="@+id/tv_status"
                style="@style/SubHeading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingVertical="@dimen/_8dp"
                android:paddingEnd="@dimen/_8dp"
                android:paddingStart="@dimen/_6dp"
                android:drawablePadding="@dimen/_6dp"
                android:gravity="center_vertical"
                android:textColor="@color/white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/vw_top_background" />

            <TextView
                android:id="@+id/tv_date"
                style="@style/Body3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:textColor="@color/white"
                app:layout_constraintEnd_toStartOf="@+id/tv_edc_tag"
                app:layout_constraintTop_toTopOf="@+id/vw_top_background" />

            <TextView
                android:id="@+id/tv_edc_tag"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/bg_rounded_rectangle_white_8dp"
                android:padding="@dimen/_8dp"
                android:text="@string/edc_caps"
                android:textColor="@color/black_40"
                android:visibility="gone"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/tv_status"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_status"
                tools:visibility="visible" />

            <include
                android:id="@+id/include_favourite"
                layout="@layout/layout_favourites"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/add_customer_text_margin"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_payment_detail_top"
                tools:visibility="visible" />

            <include
                android:id="@+id/include_payment_detail_top"
                layout="@layout/layout_payment_detail_top"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_status" />

            <com.bukuwarung.payments.widget.DisbursalBankView
                android:id="@+id/disbursal_bank_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/include_favourite" />

            <include
                android:id="@+id/include_payment_method"
                layout="@layout/layout_payment_method"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/disbursal_bank_view"
                tools:visibility="visible" />

            <include
                android:id="@+id/include_payment_pending_info"
                layout="@layout/layout_pending_transaction_info"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/include_payment_method"
                android:visibility="gone"
                tools:visibility="visible"
                />


            <include
                android:id="@+id/include_payment_collection_pending"
                layout="@layout/layout_ppob_unpaid"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/include_payment_pending_info"
                tools:visibility="visible"
                />

            <include
                android:id="@+id/include_payment_collection"
                layout="@layout/layout_ppob_payment_link"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_payment_collection_pending"
                tools:visibility="visible"
                />

            <com.bukuwarung.ui_component.component.button.BukuButton
                android:id="@+id/bb_download_pdf"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:padding="@dimen/_8dp"
                android:textAllCaps="false"
                android:visibility="gone"
                app:buttonText="@string/download_e_ticket"
                app:buttonType="blue80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_payment_collection" />

            <include
                android:id="@+id/include_payment_receipt"
                layout="@layout/layout_payment_receipt"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bb_download_pdf" />

            <include
                android:id="@+id/include_cash_transaction"
                layout="@layout/layout_cash_transaction"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_payment_receipt"
                />

            <include
                android:id="@+id/include_refunded_success"
                layout="@layout/layout_cash_transaction"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_payment_status"
                />

            <include
                android:id="@+id/include_auto_refund"
                layout="@layout/layout_auto_refund_bank"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_cash_transaction" />

            <TextView
                android:id="@+id/tv_support_top"
                style="@style/SubHeading1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/payment_problem"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginStart="@dimen/_16dp"
                android:textColor="@color/black"
                android:visibility="gone"
                app:layout_constraintEnd_toStartOf="@id/tv_cs_support_top"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_auto_refund" />

            <TextView
                android:id="@+id/tv_cs_support_top"
                style="@style/SubHeading1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/label_help_wa_btn"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:drawableEndCompat="@drawable/ic_cs_icon"
                android:gravity="end"
                android:visibility="gone"
                android:drawablePadding="@dimen/_10dp"
                android:textColor="@color/blue_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toBottomOf="@id/include_auto_refund" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/grp_support_top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tv_support_top, tv_cs_support_top"/>

            <include
                android:id="@+id/include_warning_mid"
                layout="@layout/layout_warning"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_support_top" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/ba_msg"
                app:type="info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_warning_mid" />

            <include
                android:id="@+id/include_hold_message"
                layout="@layout/layout_payment_hold_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_support_top" />

            <include
                android:id="@+id/include_payment_status"
                layout="@layout/layout_status_transaction"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ba_msg" />

            <include
                android:id="@+id/include_autorecorded_info"
                layout="@layout/layout_cash_transaction"
                android:visibility="gone"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_payment_status"
                tools:visibility="visible"/>

            <include
                android:id="@+id/include_payment_transaction_detail"
                layout="@layout/layout_detail_transaction"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_autorecorded_info" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/ba_bnpl"
                app:type="info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_payment_transaction_detail" />

            <TextView
                android:id="@+id/tv_support"
                style="@style/SubHeading1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/payment_problem"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginStart="@dimen/_16dp"
                android:textColor="@color/black"
                app:layout_constraintEnd_toEndOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ba_bnpl" />

            <TextView
                android:id="@+id/tv_cs_support"
                style="@style/SubHeading1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/label_help_wa_btn"
                android:layout_marginTop="@dimen/_16dp"
                app:drawableEndCompat="@drawable/ic_cs_icon"
                android:gravity="end"
                android:layout_marginEnd="@dimen/_16dp"
                android:drawablePadding="@dimen/_10dp"
                android:textColor="@color/blue_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/guideline"
                app:layout_constraintTop_toBottomOf="@id/ba_bnpl" />

            <com.bukuwarung.widget.PoweredByFooterView
                android:id="@+id/powered_by_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/tv_support" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/grp_support"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tv_support, tv_cs_support"/>

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_complete_payment"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:layout_marginTop="@dimen/_20dp"
        android:padding="@dimen/_12dp"
        android:text="@string/label_payment"
        android:textAllCaps="false"
        android:visibility="gone"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintTop_toBottomOf="@+id/sv_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:rippleColor="@color/black_40"
        tools:visibility="visible" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_40dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include_tool_bar"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/contact_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="gone" />

    <ProgressBar
        android:id="@+id/pb_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <View
        android:id="@+id/vw_info"
        android:layout_width="0dp"
        android:layout_height="360dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>