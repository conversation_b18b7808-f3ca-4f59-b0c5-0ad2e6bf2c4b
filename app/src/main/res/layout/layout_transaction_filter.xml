<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:showIn="@layout/tab_layout_expense">

    <HorizontalScrollView
        android:id="@+id/chipScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/cgTrxFilter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:selectionRequired="true"
            app:singleLine="true"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_filter_ranged"
                style="@style/CashFilterChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:checkable="true"
                android:text="@string/set_range"
                app:checkedIconEnabled="false"
                app:closeIcon="@drawable/ic_cevron_down_white"
                app:closeIconTint="@color/cash_chip_text_color_state"
                app:closeIconVisible="true" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_filter_all"
                style="@style/CashFilterChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/all"
                android:checked="true"
                app:checkedIconEnabled="false" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_filter_current_month"
                style="@style/CashFilterChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checkable="true"
                android:text="@string/this_month_label"
                app:checkedIconEnabled="false" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_filter_last_month"
                style="@style/CashFilterChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Bulan lalu"
                app:checkedIconEnabled="false" />


            <com.google.android.material.chip.Chip
                android:id="@+id/chip_filter_this_week"
                style="@style/CashFilterChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Minggu ini"
                app:checkedIconEnabled="false" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_filter_today"
                style="@style/CashFilterChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="Hari ini"
                app:checkedIconEnabled="false" />
        </com.google.android.material.chip.ChipGroup>
    </HorizontalScrollView>
</merge>