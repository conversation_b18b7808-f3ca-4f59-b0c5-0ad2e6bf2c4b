<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_marginTop="@dimen/_8dp"
    android:layout_height="wrap_content">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_ppob"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_voucher_game" />

    <TextView
        android:id="@+id/tv_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_free_text"
        android:fontFamily="@font/roboto"
        android:paddingHorizontal="@dimen/_6dp"
        android:paddingVertical="@dimen/_2dp"
        android:text="@string/text_new"
        android:textColor="@color/white"
        android:textSize="@dimen/text_8sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/iv_ppob"
        app:layout_constraintStart_toStartOf="@+id/iv_ppob"
        app:layout_constraintTop_toTopOf="@+id/iv_ppob" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6dp"
        android:text="@string/postpaid_pulsa"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="@id/iv_ppob"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_ppob" />

</androidx.constraintlayout.widget.ConstraintLayout>