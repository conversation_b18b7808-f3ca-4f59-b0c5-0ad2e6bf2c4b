<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tb_layout"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        android:background="@color/blue60"
        app:tabIndicatorHeight="@dimen/_2dp"
        app:tabTextColor="@color/white"
        app:tabIndicatorColor="@color/white"/>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_pager"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>
