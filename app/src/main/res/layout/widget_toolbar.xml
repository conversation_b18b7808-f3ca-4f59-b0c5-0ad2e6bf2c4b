<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_toolbar"
    style="@style/Toolbar">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginStart="@dimen/_12dp"
            android:background="?android:attr/selectableItemBackground"
            android:src="@mipmap/back_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_toolbar_title"
            style="@style/Heading2"
            android:layout_marginStart="@dimen/_16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/back_btn"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/business_card" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.appcompat.widget.Toolbar>
