<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/cl_layout"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="@dimen/_16dp"
    android:background="@color/black_5">

    <TextView
        android:id="@+id/tv_merchant_id"
        style="@style/Body2"
        android:text="@string/merchant_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_merchant_id_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        tools:text="087775598545"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_merchant_id"/>

    <View
        android:id="@+id/vw_mechant_id"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_10dp"
        android:background="@color/black_10"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_merchant_id"/>

    <TextView
        android:id="@+id/tv_payment_method"
        style="@style/Body2"
        android:text="@string/label_payment_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_mechant_id"/>

    <TextView
        android:id="@+id/tv_payment_method_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        tools:text="Bank Central Asia (BCA)"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_payment_method"/>

    <View
        android:id="@+id/vw_tv_payment_method"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_10dp"
        android:background="@color/black_10"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_payment_method"/>

    <TextView
        android:id="@+id/tv_product"
        style="@style/Body2"
        android:text="@string/produk"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_tv_payment_method"/>

    <TextView
        android:id="@+id/tv_product_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        tools:text="Token Listrik 100.000"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_product"/>

    <View
        android:id="@+id/vw_product"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_10dp"
        android:background="@color/black_10"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_product"/>

    <TextView
        android:id="@+id/tv_number"
        style="@style/Body2"
        android:text="@string/customer_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_product"/>

    <TextView
        android:id="@+id/tv_number_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        tools:text="081275598545"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_number"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_payment_in"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_product, tv_product_value, vw_tv_payment_method"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_payment_out"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="vw_product, tv_number_value, tv_number"/>

</androidx.constraintlayout.widget.ConstraintLayout>