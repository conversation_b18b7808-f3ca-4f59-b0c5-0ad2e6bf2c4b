<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/info_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="4dp"
        app:cardElevation="4dp"
        app:cardPreventCornerOverlap="false"
        app:contentPadding="0dp">

        <RelativeLayout
            android:id="@+id/layout_info_main"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="16dp">

            <ImageView
                android:id="@+id/imageview_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/ic_check_circle"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/headerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/info_header"
                    style="@style/tut_heading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/stepIndicator"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="8dp"
                    android:fontFamily="sans-serif"
                    android:text="Header"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:id="@+id/stepIndicator"
                    android:layout_width="wrap_content"
                    android:layout_height="10dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="@dimen/_8dp">

                    <View
                        android:id="@+id/oval_1"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:background="@drawable/oval_active" />

                    <View
                        android:id="@+id/oval_2"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_marginLeft="4dp"
                        android:background="@drawable/oval_inactive" />

                    <View
                        android:id="@+id/oval_3"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_marginLeft="4dp"
                        android:background="@drawable/oval_inactive" />

                    <View
                        android:id="@+id/oval_4"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_marginLeft="4dp"
                        android:background="@drawable/oval_inactive" />

                    <View
                        android:id="@+id/oval_5"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_marginLeft="4dp"
                        android:background="@drawable/oval_inactive" />

                    <View
                        android:id="@+id/oval_6"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_marginLeft="4dp"
                        android:background="@drawable/oval_inactive"
                        android:visibility="gone" />
                </LinearLayout>

                <ImageView
                    android:src="@mipmap/close_grey"
                    android:layout_alignParentEnd="true"
                    android:id="@+id/btnClose"
                    android:layout_alignParentTop="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </RelativeLayout>

            <TextView
                android:id="@+id/textview_info"
                style="@style/tut_body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/headerLayout"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:layout_toRightOf="@id/imageview_icon"
                android:letterSpacing="0"
                android:text="Jika Anda belum memliki catatan transaksi, Anda bisa menekan tombol + untuk membuat catatan transaksi baru." />

            <TextView
                android:id="@+id/separator"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/textview_info"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="#eeeeee" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/separator"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp">

                <TextView
                    android:id="@+id/back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:background="@drawable/button_bg"
                    android:gravity="center"
                    android:lineSpacingExtra="1sp"
                    android:paddingLeft="16dp"
                    android:paddingTop="8dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="8dp"
                    android:text="@string/tut_back_btn"
                    android:textColor="#0091FF"
                    android:textStyle="bold"
                    android:translationY="-0.44sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:background="#1A0091FF"
                    android:gravity="center"
                    android:lineSpacingExtra="1sp"
                    android:paddingLeft="16dp"
                    android:paddingTop="8dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="8dp"
                    android:text="@string/cancel_btn"
                    android:textColor="#0091FF"
                    android:textStyle="bold"
                    android:translationY="-0.44sp" />

                <TextView
                    android:id="@+id/next"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:background="@drawable/button_next_bg"
                    android:fontFamily="@font/roboto"
                    android:gravity="center"
                    android:lineSpacingExtra="1sp"
                    android:paddingLeft="16dp"
                    android:paddingTop="8dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="8dp"
                    android:text="@string/tut_next_btn"
                    android:textColor="#FFFFFF"
                    android:textStyle="bold"
                    android:translationY="-0.44sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/startIntroBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:background="@drawable/button_next_bg"
                    android:gravity="center"
                    android:lineSpacingExtra="1sp"
                    android:paddingLeft="16dp"
                    android:paddingTop="8dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="8dp"
                    android:text="@string/start_btn"
                    android:textColor="#FFFFFF"
                    android:textStyle="bold"
                    android:translationY="-0.44sp"
                    android:visibility="visible" />

            </RelativeLayout>

        </RelativeLayout>

    </androidx.cardview.widget.CardView>


</RelativeLayout>
