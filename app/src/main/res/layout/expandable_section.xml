<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bank_account_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp_stroke_black10"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingVertical="@dimen/_12dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/iv_collapse"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Apakah pembayaran dengan QRIS dikenakan biaya?" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black60"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/iv_collapse"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="Kamu dikenakan biaya QRIS 0,7% dari total tagihan. Biaya QRIS atau MDR (Merchant Discount Rate) adalah biaya yang dibebankan ke pengguna selama transaksi dengan QRIS." />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_collapse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        app:srcCompat="@drawable/ic_chevron_down"
        app:tint="@color/black80" />

</androidx.constraintlayout.widget.ConstraintLayout>