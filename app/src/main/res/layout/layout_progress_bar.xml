<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/cl_home"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/colorGreyLight"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools">

    <include
        android:id="@+id/referee_entry_point"
        layout="@layout/layout_entry_point_refree"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_heading"/>

    <TextView
        android:id="@+id/tv_heading"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:text="Kelengkapan Informasi"
        style="@style/Heading3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/referee_entry_point"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginStart="@dimen/_16dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:id="@+id/rv_progress_bar"
        android:orientation="horizontal"
        android:padding="@dimen/_8dp"
        android:layout_marginTop="@dimen/_8dp"
        tools:listitem="@layout/item_progress_bar_lainnya_tab"
        android:layout_marginBottom="@dimen/_12dp"
        app:layout_constraintTop_toBottomOf="@id/tv_heading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>