<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.skydoves.powerspinner.PowerSpinnerView
        android:layout_width="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/spinner"
        android:layout_height="wrap_content"
        android:background="@drawable/button_round_cornerd_stroke_pop_up"
        app:spinner_item_array="@array/number_of_branch_stores"
        app:spinner_arrow_tint="@color/black_60"
        tools:text="Penambahan Stok"
        android:padding="@dimen/_12dp"
        android:textColor="@color/black_80"
        android:textColorHint="@color/black_80"
        android:textSize="14sp"
        app:spinner_arrow_gravity="end"
        app:spinner_arrow_padding="8dp"
        app:spinner_divider_show="true"
        app:spinner_divider_color="@color/light_gray"
        app:spinner_divider_size="0.4dp"
        app:spinner_popup_animation="dropdown"
        app:spinner_popup_height="@dimen/dimen_200dp"
        app:spinner_popup_background="@color/white"
        app:spinner_popup_elevation="14dp"
        />

</androidx.constraintlayout.widget.ConstraintLayout>