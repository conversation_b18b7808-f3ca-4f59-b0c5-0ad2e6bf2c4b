<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/_8dp"
    app:cardElevation="@dimen/_0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_12dp"
        android:paddingTop="@dimen/_16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/balance_primary_input_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_16dp">

            <TextView
                android:id="@+id/total_balance_primary_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black40"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintBottom_toBottomOf="@id/balance_primary_input"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/balance_primary_input" />

            <TextView
                android:id="@+id/balance_primary_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:textAlignment="textEnd"
                android:textSize="@dimen/text_16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@id/primary_cursor"
                app:layout_constraintStart_toEndOf="@id/total_balance_primary_label"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/primary_cursor"
                android:layout_width="@dimen/_2dp"
                android:layout_height="@dimen/dimen_16dp"
                android:layout_marginBottom="@dimen/_2dp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@id/balance_primary_input"
                app:layout_constraintEnd_toEndOf="parent" />

            <View
                android:id="@+id/primary_divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/balance_primary_input" />

            <LinearLayout
                android:id="@+id/primary_expression_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/primary_divider">

                <TextView
                    android:id="@+id/primary_expression_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAlignment="textEnd"
                    android:textColor="@color/black_20"
                    android:textSize="@dimen/text_14sp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/balance_secondary_input_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_16dp">

            <TextView
                android:id="@+id/total_balance_secondary_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black40"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintBottom_toBottomOf="@id/balance_secondary_input"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/balance_secondary_input" />

            <TextView
                android:id="@+id/balance_secondary_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:textAlignment="textEnd"
                android:textSize="@dimen/text_16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@id/secondary_cursor"
                app:layout_constraintStart_toEndOf="@id/total_balance_secondary_label"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/secondary_cursor"
                android:layout_width="@dimen/_2dp"
                android:layout_height="@dimen/dimen_16dp"
                android:layout_marginBottom="@dimen/_2dp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@id/balance_secondary_input"
                app:layout_constraintEnd_toEndOf="parent" />

            <View
                android:id="@+id/secondary_divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/balance_secondary_input" />

            <LinearLayout
                android:id="@+id/secondary_expression_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/secondary_divider">

                <TextView
                    android:id="@+id/secondary_expression_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAlignment="textEnd"
                    android:textColor="@color/black_20"
                    android:textSize="@dimen/text_14sp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.cardview.widget.CardView
            android:id="@+id/result_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_16dp"
            app:cardBackgroundColor="@color/neutral50"
            app:cardCornerRadius="@dimen/_6dp"
            app:cardElevation="@dimen/_0dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/_12dp"
                android:paddingVertical="@dimen/_8dp">

                <TextView
                    android:id="@+id/result_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/result_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:textAlignment="textEnd"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/result_label"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</androidx.cardview.widget.CardView>
