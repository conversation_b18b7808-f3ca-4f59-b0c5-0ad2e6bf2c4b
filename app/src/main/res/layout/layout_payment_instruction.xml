<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_black_outline"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:layout_width="match_parent">

    <TextView
        android:id="@+id/tv_instruction"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:paddingVertical="@dimen/_12dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingEnd="@dimen/_16dp"
        android:text="@string/payment_guide"
        app:drawableEndCompat="@drawable/ic_chevron_down"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tv_expanded"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="@dimen/dimen_28dp"
        android:visibility="gone"
        android:textColor="@color/black_40"
        app:layout_constraintTop_toBottomOf="@+id/tv_instruction"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>