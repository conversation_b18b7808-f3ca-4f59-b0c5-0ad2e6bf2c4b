<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_title_txt"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/saldo_tutorial_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_img1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_txt"
        app:srcCompat="@drawable/saldoicon_1" />

    <TextView
        android:id="@+id/tv_point1_title_txt"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_9dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/saldo_tutorial_pt1_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_img1"
        app:layout_constraintTop_toBottomOf="@id/tv_title_txt" />

    <TextView
        android:id="@+id/tv_point1_subtitle_txt"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_9dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/saldo_tutorial_pt1_subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_img1"
        app:layout_constraintTop_toBottomOf="@id/tv_point1_title_txt" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_img2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_point1_subtitle_txt"
        app:srcCompat="@drawable/ic_styled_shield" />

    <TextView
        android:id="@+id/tv_point2_title_txt"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_9dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/saldo_tutorial_pt2_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ic_img2"
        app:layout_constraintTop_toBottomOf="@id/tv_point1_subtitle_txt" />

    <TextView
        android:id="@+id/tv_point2_subtitle_txt"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_9dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/saldo_tutorial_pt2_subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ic_img2"
        app:layout_constraintTop_toBottomOf="@id/tv_point2_title_txt" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_img3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_point2_subtitle_txt"
        app:srcCompat="@drawable/saldoicon_3" />

    <TextView
        android:id="@+id/tv_point3_title_txt"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_9dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/saldo_tutorial_pt3_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ic_img3"
        app:layout_constraintTop_toBottomOf="@id/tv_point2_subtitle_txt" />

    <TextView
        android:id="@+id/tv_point3_subtitle_txt"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_9dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/saldo_tutorial_pt3_subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ic_img3"
        app:layout_constraintTop_toBottomOf="@id/tv_point3_title_txt" />

    <TextView
        android:id="@+id/tv_check_tutorial_txt"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_9dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/saldo_tutorial_vid"
        android:textColor="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ic_img3"
        app:layout_constraintTop_toBottomOf="@id/tv_point3_subtitle_txt" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_topup"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:text="@string/topup_saldo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_check_tutorial_txt" />

</androidx.constraintlayout.widget.ConstraintLayout>
