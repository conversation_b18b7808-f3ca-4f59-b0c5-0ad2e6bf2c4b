<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:paddingVertical="@dimen/_20dp">

            <!--chosen address-->
            <LinearLayout
                android:id="@+id/ll_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_status_black_5"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_chosen_address"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    tools:text="Pagedangan, Tangerang, BantenPagedangan, Tangerang, Banten" />

                <TextView
                    android:id="@+id/tv_change_address"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/change"
                    android:textColor="@color/colorPrimary" />
            </LinearLayout>

            <TextView
                android:id="@+id/province_header"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/province"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ll_address" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_province"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:enabled="false"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintTop_toBottomOf="@id/province_header">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_province"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:inputType="textCapWords"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/city_header"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/city"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_province" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_city"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:enabled="false"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintTop_toBottomOf="@id/city_header">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_city"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:inputType="textCapWords"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/district_header"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/district"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_city" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_district"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:enabled="false"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintTop_toBottomOf="@id/district_header">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_district"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:inputType="textCapWords"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/ward_header"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/sub_district"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_district" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_ward"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="@dimen/_4dp"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintEnd_toStartOf="@id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ward_header">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_ward"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/sub_district_hint"
                    android:inputType="textCapWords"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.6" />


            <TextView
                android:id="@+id/postal_code_header"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4dp"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/postal_code"
                app:layout_constraintStart_toEndOf="@id/guideline"
                app:layout_constraintTop_toBottomOf="@id/til_district" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_postal_code"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4dp"
                android:layout_marginTop="4dp"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/guideline"
                app:layout_constraintTop_toBottomOf="@id/postal_code_header">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_postal_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:inputType="number"
                    android:maxLines="1"
                    android:scrollHorizontally="true"
                    tools:text="Purbalingga" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/address_header"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/full_address"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_ward" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_address"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintTop_toBottomOf="@id/address_header">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top|start"
                    android:hint="@string/full_address_hint"
                    android:maxLines="5"
                    android:minHeight="100dp" />

            </com.google.android.material.textfield.TextInputLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_2dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toTopOf="parent" />

        <!--https://stackoverflow.com/questions/49247301/fix-android-studio-warning-about-layout-marginhorizontal-->
        <!--Did you find out why your layout_marginHorizontal  worked on pre-26 devices? I have the same "problem" now. Actually, layout_marginHorizontal even works on Android 4.4-->

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginVertical="@dimen/_8dp"
            android:enabled="false"
            android:text="@string/save"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:enabled="true" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>