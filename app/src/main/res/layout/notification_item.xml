<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:elevation="4dp"
        android:layout_height="wrap_content">
<RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="62dp"
        android:background="@color/white"
        android:layout_alignParentTop="true">

        <ImageView
            android:id="@+id/thumbnail"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:padding="@dimen/_8dp"
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="@dimen/_16dp"
            android:background="@drawable/circle_with_border"
            app:srcCompat="@drawable/reminder_app_logo" />
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_toRightOf="@+id/thumbnail"
            android:layout_centerVertical="true"
            android:orientation="vertical"
            android:layout_marginLeft="@dimen/_16dp"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:textColor="@color/heading_text"
                android:textSize="16sp"
                android:lineHeight="26dp"
                android:textStyle="bold"
                android:layout_marginRight="76dp"
                android:text=""/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:textColor="@color/body_text"
                android:text="3 hrs ago"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:orientation="horizontal"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:layout_height="match_parent">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/round_corner_light_border_rectangle"
            android:tint="@color/colorPrimary"
            android:layout_centerVertical="true"
            android:layout_marginRight="4dp"
            android:textSize="12dp"
            android:paddingLeft="12dp"
            android:fontFamily="@font/roboto"
            android:paddingRight="12dp"
            android:alpha="0.8"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:textColor="@color/colorPrimary"
            android:id="@+id/share_notification"
            android:text="@string/share" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:tint="#BEBEBE"
                android:visibility="gone"
                android:layout_centerVertical="true"
                android:id="@+id/action_menu"
                android:src="@drawable/ic_main_menu_dot" />
        </LinearLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/bodyImg"
        android:layout_below="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:visibility="gone"
        android:src="@drawable/slide1"
        android:scaleType="centerCrop"/>

    <TextView
        android:layout_below="@+id/bodyImg"
        android:textColor="@color/body_text"
        android:background="@color/white"
        android:id="@+id/bodyText"
        android:paddingLeft="16dp"
        android:paddingRight="40dp"
        android:textSize="12dp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:gravity="center_vertical"
        android:text=""
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</RelativeLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>
