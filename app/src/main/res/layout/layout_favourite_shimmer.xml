<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/vw_fav"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_22dp"
        android:background="@drawable/shimmer_grey_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vw_icon"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@drawable/shimmer_grey_8dp"
        app:layout_constraintStart_toEndOf="@id/vw_fav"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vw_title"
        android:layout_width="110dp"
        android:layout_height="20dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@drawable/shimmer_grey_8dp"
        app:layout_constraintStart_toEndOf="@id/vw_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vw_sub_title"
        android:layout_width="65dp"
        android:layout_height="12dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:background="@drawable/shimmer_grey_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/vw_icon"
        app:layout_constraintTop_toBottomOf="@id/vw_title" />

    <View
        android:id="@+id/vw_button"
        android:layout_width="80dp"
        android:layout_height="28dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@drawable/shimmer_grey_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>