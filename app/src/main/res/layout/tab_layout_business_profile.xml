<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:contentInsetStartWithNavigation="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/menuIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingEnd="@dimen/_5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@mipmap/ic_menu_white_24dp" />

            <ImageView
                android:id="@+id/notification_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="?android:attr/selectableItemBackground"
                android:src="@drawable/ic_alert"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/notify_highlighter_exp"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:background="@drawable/oval_pink"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/notification_icon"
                app:layout_constraintTop_toTopOf="@id/notification_icon"
                tools:text="2"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/screenTitle"
                style="@style/Heading2"
                android:layout_width="@dimen/_0dp"
                android:layout_marginStart="@dimen/_5dp"
                android:layout_marginEnd="@dimen/_5dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/profile"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/notification_icon"
                app:layout_constraintStart_toEndOf="@id/menuIcon"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include
                android:id="@+id/incentiveLayout"
                layout="@layout/layout_progress_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/loyalty_container"
                tools:visibility="gone" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/profileHeader"
                android:orientation="vertical"
                android:id="@+id/loyalty_container"
                >

                <com.bukuwarung.widget.LoyaltyWidget
                    android:id="@+id/loyalty_widget"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    />

                <com.bukuwarung.widget.LoyaltyWidgetWithSaldoBonus
                    android:id="@+id/loyalty_widget_with_saldo_bonus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    />

            </LinearLayout>


            <View
                android:id="@+id/gradient_bg_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_0dp"
                android:background="@drawable/payment_tab_gradient_bg"
                app:layout_constraintBottom_toTopOf="@id/divider"
                app:layout_constraintTop_toBottomOf="@id/incentiveLayout" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/register_bank_layout2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_register_bank_new_all_radius"
                android:padding="@dimen/_12dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/divider"
                app:layout_constraintTop_toBottomOf="@id/incentiveLayout">

                <TextView
                    android:id="@+id/register_bank_txt2"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginBottom="@dimen/_10dp"
                    android:text="@string/register_bank_message"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/register_bank_btn2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_12dp"
                    android:src="@drawable/ic_arrow_white_bg"
                    app:layout_constraintBottom_toBottomOf="@+id/register_bank_txt2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/register_bank_txt2" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/payment_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:background="@color/white"
                app:cardCornerRadius="@dimen/_8dp"
                app:layout_constraintTop_toBottomOf="@+id/incentiveLayout"
                tools:ignore="MissingConstraints">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/paymentLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/_12dp">

                    <TextView
                        android:id="@+id/txt_label_digital_payment"
                        style="@style/Heading3"
                        android:layout_marginStart="@dimen/_16dp"
                        android:text="@string/payment_card_layout_label_digital_payment_2"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.bukuwarung.widget.SafeGuaranteeAnimView
                        android:id="@+id/safe_anim_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/button_history"
                        style="@style/Body2"
                        android:layout_width="0dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:drawablePadding="5dp"
                        android:paddingBottom="@dimen/_12dp"
                        android:text="@string/payment_label_history"
                        android:textAlignment="center"
                        android:textColor="@color/black_80"
                        android:visibility="gone"
                        app:drawableTopCompat="@drawable/ic_riwayat"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/button_payment_in"
                        app:layout_constraintTop_toBottomOf="@id/txt_label_digital_payment"
                        tools:ignore="MissingConstraints"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/txt_history_dot"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        android:text="@string/payment_card_dot"
                        android:textColor="#FFDD7E"
                        android:textSize="24sp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toTopOf="@id/button_history"
                        app:layout_constraintEnd_toEndOf="@id/button_history"
                        app:layout_constraintHorizontal_bias="0.58"
                        app:layout_constraintStart_toStartOf="@id/button_history"
                        app:layout_constraintTop_toTopOf="@id/button_history" />

                    <TextView
                        android:id="@+id/button_payment_in"
                        style="@style/Body2"
                        android:layout_width="0dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:drawableTop="@drawable/ic_tagih"
                        android:drawablePadding="5dp"
                        android:paddingBottom="@dimen/_12dp"
                        android:text="@string/payment_label_payment_in"
                        android:textAlignment="center"
                        android:textColor="@color/black_80"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/button_history"
                        app:layout_constraintStart_toEndOf="@id/button_payment_out"
                        app:layout_constraintTop_toBottomOf="@id/txt_label_digital_payment"
                        tools:ignore="MissingConstraints" />

                    <TextView
                        android:id="@+id/button_payment_out"
                        style="@style/Body2"
                        android:layout_width="0dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:drawableTop="@drawable/ic_bayar"
                        android:drawablePadding="5dp"
                        android:paddingBottom="@dimen/_12dp"
                        android:text="@string/payment_label_payment_out"
                        android:textAlignment="center"
                        android:textColor="@color/black_80"
                        app:layout_constraintEnd_toStartOf="@id/button_payment_in"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/txt_label_digital_payment"
                        tools:ignore="MissingConstraints" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/register_bank_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_register_bank_new"
                        android:padding="@dimen/_12dp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/button_payment_out">

                        <TextView
                            android:id="@+id/register_bank_txt"
                            style="@style/Body3"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_12dp"
                            android:layout_marginTop="@dimen/_10dp"
                            android:layout_marginEnd="@dimen/_8dp"
                            android:layout_marginBottom="@dimen/_10dp"
                            android:text="@string/register_bank_message"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/register_bank_btn"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/register_bank_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_12dp"
                            android:src="@drawable/ic_arrow_white_bg"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <include
                android:id="@+id/paymentInfoMessage"
                layout="@layout/layout_money_saved_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/payment_card" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier_payment_register_bank"
                android:layout_width="wrap_content"
                android:layout_height="1dp"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="paymentInfoMessage,register_bank_layout2"
                app:layout_constraintStart_toStartOf="parent" />

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_8dp"
                android:background="@color/black_0"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/barrier_payment_register_bank" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp_profile_banner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tb_profile_banner"
                android:layout_width="0dp"
                android:layout_height="@dimen/_6dp"
                android:layout_alignParentBottom="true"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@color/transparent"
                android:foregroundGravity="bottom"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/vp_profile_banner"
                app:layout_constraintTop_toBottomOf="@+id/vp_profile_banner"
                app:tabBackground="@drawable/selector_banner"
                app:tabGravity="center"
                app:tabIndicatorHeight="0dp"
                app:tabSelectedTextColor="@android:color/transparent"
                app:tabTextColor="@android:color/transparent" />

            <include
                android:id="@+id/in_profile_pins"
                layout="@layout/profile_pins"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vp_profile_banner" />


            <View
                android:id="@+id/divider_pin_bottom"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_8dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black_0"
                app:layout_constraintTop_toBottomOf="@id/in_profile_pins" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/profileHeader"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:paddingVertical="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/profilePic"
                    android:layout_width="@dimen/profile_layout_width"
                    android:layout_height="@dimen/profile_layout_width"
                    android:src="@drawable/ic_icon_shop"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/editImageIcon"
                    android:layout_width="@dimen/_20dp"
                    android:layout_height="@dimen/_20dp"
                    android:background="@drawable/background_circular_black20"
                    android:padding="@dimen/_4dp"
                    android:src="@drawable/ic_camera_edit"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/profilePic"
                    app:layout_constraintEnd_toEndOf="@id/profilePic" />

                <TextView
                    android:id="@+id/txt_owner_name"
                    style="@style/Heading2"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    app:layout_constraintBottom_toTopOf="@id/phone_number"
                    app:layout_constraintEnd_toStartOf="@id/seeProfileBtn"
                    app:layout_constraintStart_toEndOf="@id/profilePic"
                    app:layout_constraintTop_toTopOf="@id/profilePic"
                    tools:text="Usaha 123" />

                <TextView
                    android:id="@+id/phone_number"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    app:layout_constraintBottom_toBottomOf="@id/profilePic"
                    app:layout_constraintEnd_toStartOf="@id/seeProfileBtn"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@id/profilePic"
                    app:layout_constraintTop_toBottomOf="@id/txt_owner_name"
                    tools:text="987654321" />

                <TextView
                    android:id="@+id/phone_number2"
                    style="@style/Heading2"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/profilePic"
                    app:layout_constraintEnd_toStartOf="@id/seeProfileBtn"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@id/profilePic"
                    app:layout_constraintTop_toTopOf="@+id/profilePic"
                    tools:text="987654321" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/seeProfileBtn"
                    style="@style/ButtonOutline"
                    android:text="@string/edit"
                    app:layout_constraintBottom_toBottomOf="@id/profilePic"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/profilePic" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/mission_achievements"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_14dp"
                android:background="@color/blue_60"
                android:drawableRight="@drawable/ic_right_arrow"
                android:drawableTint="@color/white"
                android:paddingLeft="@dimen/_16dp"
                android:paddingTop="@dimen/_14dp"
                android:paddingRight="@dimen/_20dp"
                android:paddingBottom="@dimen/_14dp"
                android:text="@string/mission_achievements"
                android:textColor="@color/white"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider_pin_bottom" />

            <View
                android:id="@+id/divider1"
                style="@style/Divider.Black5"
                android:layout_marginTop="@dimen/_14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mission_achievements" />

            <View
                android:id="@+id/self_remainder_btn"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="?attr/selectableItemBackground"
                app:layout_constraintBottom_toTopOf="@id/divider2"
                app:layout_constraintTop_toBottomOf="@id/divider1" />

            <TextView
                android:id="@+id/selfRemainderLabel"
                style="@style/Body1"
                android:layout_marginStart="@dimen/_16dp"
                android:drawableStart="@drawable/ic_pengingat_pencatatan"
                android:drawablePadding="14dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/self_remainder"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider1" />

            <TextView
                android:id="@+id/new_label"
                style="@style/SubHeading2"
                android:layout_marginStart="@dimen/_8dp"
                android:background="@drawable/bg_text_badge_red"
                android:padding="4dp"
                android:text="@string/new_label"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/selfRemainderLabel"
                app:layout_constraintStart_toEndOf="@+id/selfRemainderLabel"
                app:layout_constraintTop_toTopOf="@id/selfRemainderLabel" />

            <View
                android:id="@+id/divider2"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/selfRemainderLabel" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/self_reminder_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="selfRemainderLabel,self_remainder_btn,new_label,divider2" />


            <TextView
                android:id="@+id/business_card_btn"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_kartu_nama"
                android:drawableEnd="@drawable/ic_chevron_down"
                android:drawablePadding="14dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/business_card"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider2" />

            <View
                android:id="@+id/divider3"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/business_card_btn" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/card_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="business_card_btn,divider3" />

            <View
                android:id="@+id/bg_card"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/light_blue"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/bottom_button_line"
                app:layout_constraintTop_toBottomOf="@id/divider3" />

            <include
                android:id="@+id/business_card_preview"
                layout="@layout/business_card_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider3" />

            <androidx.cardview.widget.CardView
                android:id="@+id/blur_card"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:elevation="11dp"
                android:visibility="gone"
                app:cardCornerRadius="@dimen/_8dp"
                app:layout_constraintBottom_toBottomOf="@id/business_card_preview"
                app:layout_constraintEnd_toEndOf="@id/business_card_preview"
                app:layout_constraintStart_toStartOf="@id/business_card_preview"
                app:layout_constraintTop_toTopOf="@id/business_card_preview">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/blurred_img"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_extra_below_card"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="@+id/business_card_preview"
                app:layout_constraintStart_toStartOf="@+id/business_card_preview"
                app:layout_constraintTop_toBottomOf="@id/business_card_preview">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_blurred"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:src="@drawable/onboarding_great"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_create_card_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:src="@drawable/create_business_card_info"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@+id/iv_blurred"
                    app:layout_constraintTop_toTopOf="@id/iv_blurred" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_share"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:gravity="center_vertical"
                    android:text="@string/share"
                    android:textColor="@color/black_80"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/ll_share"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:weightSum="1"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_share"
                    app:layout_constraintEnd_toStartOf="@+id/btn_download"
                    app:layout_constraintStart_toEndOf="@+id/tv_share"
                    app:layout_constraintTop_toTopOf="@+id/tv_share">

                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/iv_instagram"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.3"
                        android:background="@drawable/share_back"
                        android:src="@drawable/insta" />

                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/iv_whatsapp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:layout_marginEnd="@dimen/_10dp"
                        android:layout_weight="0.3"
                        android:background="@drawable/share_back"
                        android:src="@mipmap/ic_whatsapp_white_24dp" />

                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/iv_share"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.3"
                        android:background="@drawable/share_back"
                        android:src="@drawable/share_white" />

                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_download"
                    style="@style/ButtonOutline.Blue"
                    android:layout_width="0dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/download"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/tv_share"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tv_share" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/create_card_btn"
                style="@style/ButtonFill"
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:drawablePadding="@dimen/_8dp"
                android:elevation="12dp"
                android:text="@string/create_free_card"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/business_card_preview"
                app:layout_constraintEnd_toEndOf="@id/business_card_preview"
                app:layout_constraintStart_toStartOf="@id/business_card_preview"
                app:layout_constraintTop_toTopOf="@id/business_card_preview" />

            <View
                android:id="@+id/bottom_button_line"
                android:layout_width="0dp"
                android:layout_height="1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_extra_below_card"
                app:layout_goneMarginTop="0dp" />

            <TextView
                android:id="@+id/settings_header"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_baseline_settings"
                android:drawableEnd="@drawable/ic_chevron_down"
                android:drawablePadding="14dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/account_settings_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bottom_button_line" />

            <View
                android:id="@+id/divider4"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_header" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/settings_header_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="settings_header,divider4" />

            <TextView
                android:id="@+id/tvSetupPrinter"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/submenu_item_background"
                android:paddingStart="54dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/printer"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider4" />

            <TextView
                android:id="@+id/tv_printer_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_12dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_40"
                android:textSize="@dimen/dimen_14sp"
                android:textStyle="normal"
                app:layout_constraintBottom_toTopOf="@id/divider5"
                app:layout_constraintEnd_toEndOf="@id/tvSetupPrinter"
                app:layout_constraintTop_toBottomOf="@id/divider4"
                tools:text="@string/empty_printer" />

            <View
                android:id="@+id/divider5"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvSetupPrinter" />

            <TextView
                android:id="@+id/settings_layout"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/submenu_item_background"
                android:paddingStart="54dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/account_settings"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider5" />

            <View
                android:id="@+id/divider6"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_layout" />

            <LinearLayout
                android:id="@+id/invoice_setting_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/submenu_item_background"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider6">

                <TextView
                    android:id="@+id/invoice_setting"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="54dp"
                    android:paddingTop="@dimen/_16dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="@dimen/_16dp"
                    android:text="@string/invoice_settings" />

                <com.bukuwarung.widget.ViewTextLabel
                    android:id="@+id/invoice_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:labelColor="@color/out_red"
                    app:labelText="@string/new_label"
                    app:labelTextColor="@color/white"
                    app:layout_constraintBottom_toBottomOf="@id/invoice_setting"
                    app:layout_constraintStart_toEndOf="@id/invoice_setting"
                    app:layout_constraintTop_toTopOf="@id/invoice_setting" />
            </LinearLayout>

            <View
                android:id="@+id/divider16"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/invoice_setting_layout" />

            <!-- TODO: Add 3 layouts here -->

            <View
                android:id="@+id/list_bank_layout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/submenu_item_background"
                app:layout_constraintBottom_toTopOf="@id/divider_list_bank"
                app:layout_constraintTop_toBottomOf="@id/divider16"
                app:layout_constraintVertical_bias="0.0"
                tools:layout_editor_absoluteX="0dp" />

            <TextView
                android:id="@+id/list_bank_label"
                style="@style/Body1"
                android:drawablePadding="14dp"
                android:paddingStart="54dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/list_of_bank_account"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider16" />

            <TextView
                android:id="@+id/list_bank_new_label"
                style="@style/SubHeading2"
                android:layout_marginStart="@dimen/_8dp"
                android:background="@drawable/bg_text_badge_red"
                android:padding="4dp"
                android:text="@string/new_label"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/list_bank_label"
                app:layout_constraintStart_toEndOf="@+id/list_bank_label"
                app:layout_constraintTop_toTopOf="@id/list_bank_label" />

            <View
                android:id="@+id/divider_list_bank"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/list_bank_label" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/list_bank_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="list_bank_label,list_bank_layout,list_bank_new_label,divider_list_bank" />

            <LinearLayout
                android:id="@+id/ll_app_update"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/submenu_item_background"
                android:gravity="center_vertical"
                app:layout_constraintTop_toBottomOf="@id/divider_list_bank">

                <TextView
                    android:id="@+id/tv_update_app"
                    style="@style/Body1"
                    android:drawablePadding="@dimen/_14dp"
                    android:paddingStart="@dimen/_54dp"
                    android:paddingTop="@dimen/_16dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="@dimen/_16dp"
                    android:text="@string/update_app"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider_list_bank" />

                <com.bukuwarung.widget.ViewTextLabel
                    android:id="@+id/label_update_app"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:labelColor="@color/new_yellow"
                    app:labelText="@string/available"
                    app:labelTextColor="@color/black_80"
                    app:layout_constraintBottom_toBottomOf="@id/divider14"
                    app:layout_constraintStart_toEndOf="@id/tv_update_app"
                    app:layout_constraintTop_toBottomOf="@id/divider_list_bank" />
            </LinearLayout>

            <View
                android:id="@+id/divider14"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ll_app_update" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/settings_submenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="settings_layout,divider6,tvSetupPrinter,divider5,invoice_setting_layout,ll_app_update"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/help_header"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_baseline_help_outline"
                android:drawableEnd="@drawable/ic_chevron_down"
                android:drawablePadding="14dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/help_and_education"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider14" />

            <View
                android:id="@+id/divider7"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/help_header" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/help_header_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="help_header,divider7" />

            <TextView
                android:id="@+id/help_layout"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/submenu_item_background"
                android:paddingStart="54dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/label_help_wa_btn"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider7" />

            <View
                android:id="@+id/divider8"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/help_layout" />

            <TextView
                android:id="@+id/assist_layout"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/submenu_item_background"
                android:paddingStart="54dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/help_center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider8" />

            <View
                android:id="@+id/divider9"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/assist_layout" />

            <TextView
                android:id="@+id/feedback_layout"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/submenu_item_background"
                android:paddingStart="54dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/send_suggestions"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider9" />

            <View
                android:id="@+id/divider10"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/feedback_layout" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/help_submenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="help_layout,divider8,assist_layout,divider9, feedback_layout, divider10" />

            <TextView
                android:id="@+id/sticker_layout"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_baseline_style"
                android:drawablePadding="14dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/whatsapp_sticker"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider10" />

            <View
                android:id="@+id/vw_divider_11"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/sticker_layout" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/gp_sticker"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="sticker_layout, vw_divider_11" />

            <TextView
                android:id="@+id/stock_layout"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_stock_tab"
                android:drawablePadding="14dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/stock_tab_switch_label_active"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_divider_11" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/stock_feature"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                app:layout_constraintBottom_toBottomOf="@id/stock_layout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_divider_11"
                app:layout_constraintTop_toTopOf="@id/stock_layout"

                />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/gp_stock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="stock_layout, stock_feature, stock_divider" />

            <View
                android:id="@+id/stock_divider"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/stock_layout" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/auto_txn_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/stock_divider"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/auto_record_txn_layout"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false"
                    android:drawablePadding="14dp"
                    android:focusable="false"
                    android:paddingStart="@dimen/_16dp"
                    android:paddingTop="@dimen/_16dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="@dimen/_16dp"
                    android:text="@string/auto_record_label"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.bukuwarung.widget.ViewTextLabel
                    android:id="@+id/auto_record_new_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:labelColor="@color/out_red"
                    app:labelText="@string/new_label"
                    app:labelTextColor="@color/white"
                    app:layout_constraintBottom_toBottomOf="@id/auto_record_txn_layout"
                    app:layout_constraintStart_toEndOf="@id/auto_record_txn_layout"
                    app:layout_constraintTop_toTopOf="@id/auto_record_txn_layout" />


                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/auto_record_txn_feature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/auto_record_txn_layout"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/auto_record_txn_layout" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <View
                android:id="@+id/auto_record_txn_divider"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintBottom_toTopOf="@id/referral_btn"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/auto_txn_layout" />


            <View
                android:id="@+id/referral_btn"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="?attr/selectableItemBackground"
                android:visibility="visible"
                app:layout_constraintBottom_toTopOf="@id/referralDivider" />

            <TextView
                android:id="@+id/ReferralLabel"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/_16dp"
                android:drawableStart="@drawable/ic_referral_icon"
                android:drawablePadding="14dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/referral_program"
                android:visibility="visible"
                app:layout_constraintBottom_toTopOf="@+id/referralDivider"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/stock_divider" />

            <View
                android:id="@+id/referralDivider"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ReferralLabel" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/gp_referral"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="ReferralLabel, referral_btn, referralDivider" />

            <TextView
                android:id="@+id/share_app_layout"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_share"
                android:drawablePadding="14dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/share_app"
                android:visibility="gone"
                app:drawableTint="@color/black_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/referralDivider" />

            <View
                android:id="@+id/divider12"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/share_app_layout" />

            <View
                android:id="@+id/tokoko_btn"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="?attr/selectableItemBackground"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/tokokoDivider"
                app:layout_constraintTop_toBottomOf="@id/divider12" />

            <TextView
                android:id="@+id/tokokoLabel"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/_16dp"
                android:drawableStart="@drawable/ic_tokoko_icon"
                android:drawablePadding="14dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/download_tokoko_message"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/tokokoDivider"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider12" />

            <View
                android:id="@+id/tokokoDivider"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tokokoLabel" />

            <TextView
                android:id="@+id/tokoko_app_layout"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_share"
                android:drawablePadding="14dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/share_app"
                android:visibility="gone"
                app:drawableTint="@color/black_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tokokoDivider" />

            <View
                android:id="@+id/divider13"
                style="@style/Divider.Black5"
                android:layout_marginStart="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tokoko_app_layout" />


            <TextView
                android:id="@+id/txt_title_socmed"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:text="@string/bw_socmed"
                android:textColor="@color/black_60"
                app:layout_constraintTop_toBottomOf="@id/divider13" />

            <TextView
                android:id="@+id/ig_btn"
                style="@style/Body1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_instagram"
                android:drawablePadding="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="16dp"
                android:text="@string/ig_menu"
                app:layout_constraintEnd_toStartOf="@id/fb_btn"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_title_socmed" />

            <TextView
                android:id="@+id/fb_btn"
                style="@style/Body1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:drawableStart="@drawable/ic_facebook"
                android:drawablePadding="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="16dp"
                android:text="@string/fb_menu"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ig_btn"
                app:layout_constraintTop_toBottomOf="@id/txt_title_socmed" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/txt_join_fb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:backgroundTint="@color/black5"
                android:padding="@dimen/_12dp"
                android:text="@string/join_fb"
                android:textAllCaps="false"
                android:textColor="@color/blue40"
                android:textSize="@dimen/dimen_14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/fb_btn" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_undang_float_entry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
            android:id="@+id/ib_utang_float_entry"
            android:layout_width="@dimen/_80dp"
            android:layout_height="@dimen/_80dp"
            android:background="@null"
            android:scaleType="fitXY"
            android:src="@drawable/undang_float"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpeakableTextPresentCheck" />

        <View
            android:id="@+id/vw_undang_float_entry"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_24dp"
            app:layout_constraintEnd_toEndOf="@id/ib_utang_float_entry"
            app:layout_constraintStart_toStartOf="@id/ib_utang_float_entry"
            app:layout_constraintTop_toTopOf="@id/ib_utang_float_entry" />

        <TextView
            android:id="@+id/tv_undang_float_entry"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_24dp"
            android:backgroundTint="@color/transparent"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_cross_undang"
            app:layout_constraintEnd_toEndOf="@id/ib_utang_float_entry"
            app:layout_constraintStart_toStartOf="@id/ib_utang_float_entry"
            app:layout_constraintTop_toTopOf="@id/ib_utang_float_entry" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>