<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:layout_margin="@dimen/_8dp"
    android:padding="@dimen/_16dp">

    <TextView
        style="@style/Heading2"
        android:id="@+id/titleText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="@string/transaction_back_dialog_title"/>

    <TextView
        style="@style/Body2"
        android:id="@+id/contentText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        tools:text="@string/transaction_back_dialog_subtitle"
        android:textColor="@color/greyDisabled"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/_16dp">

        <com.google.android.material.button.MaterialButton
            style="@style/SubHeading1"
            android:id="@+id/btn_yes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/_8dp"
            android:backgroundTint="@color/white"
            android:enabled="true"
            android:gravity="center"
            android:padding="14dp"
            android:text="@string/yes"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            app:cornerRadius="4dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/colorPrimary"/>

        <com.google.android.material.button.MaterialButton
            style="@style/SubHeading1"
            android:id="@+id/btn_no"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:backgroundTint="@color/colorPrimary"
            android:enabled="true"
            android:gravity="center"
            android:padding="14dp"
            android:text="@string/no"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:cornerRadius="4dp"
            app:iconGravity="textStart"
            app:iconTint="@color/white" />

    </LinearLayout>

</LinearLayout>
