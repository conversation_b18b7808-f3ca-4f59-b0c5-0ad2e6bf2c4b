<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    tools:context=".activities.pos.PosPaymentFragment">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="28dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:src="@drawable/ic_arrow_back" />

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="35dp"
                android:layout_marginRight="35dp"
                android:layout_toEndOf="@id/closeBtn"
                android:alpha="1"
                android:fontFamily="@font/roboto"
                android:text="@string/pembayaran"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/payment_linear_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#EEF8FF"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/total_amount_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:gravity="center"
            android:text="@string/total_amount_to_be_paid"
            android:textColor="@color/black_80"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

        <TextView
            android:id="@+id/tv_total_amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:gravity="center"
            android:textColor="@color/blue_60"
            android:textSize="@dimen/text_28sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/total_amount_text"
            tools:text="Rp125.000" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_receive_money"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            app:strokeColor="@color/blue_60"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:gravity="center"
            android:paddingTop="@dimen/_10dp"
            android:paddingBottom="@dimen/_10dp"
            android:text="@string/receive_money"
            android:textAllCaps="false"
            android:textSize="@dimen/text_12sp"
            app:cornerRadius="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_total_amount" />
    </LinearLayout>


    <!--    <View-->
    <!--        android:id="@+id/total_amount_view"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="@dimen/_0dp"-->
    <!--        android:background="#EEF8FF"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/btn_receive_money"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/toolbar" />-->


    <!--    </LinearLayout>-->

    <!--    <LinearLayout-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:orientation="vertical"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/tv_receive_money">-->

    <TextView
        android:id="@+id/tv_enter_nominal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:gravity="center"
        android:text="@string/or_enter_a_nominal"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/payment_linear_layout" />

    <TextView
        android:id="@+id/tv_change_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:gravity="center"
        android:text="@string/change_calculated_automatically"
        android:textColor="@color/black_40"
        android:textSize="@dimen/text_10sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/tv_enter_nominal" />

    <TextView
        android:id="@+id/text_amount_calc"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:fontFamily="@font/roboto"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="6sp"
        android:textAlignment="center"
        android:textColor="@color/black_40"
        android:textSize="15sp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/result_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_custom_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/tv_change_info">

        <TextView
            android:id="@+id/currency_symbol"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="Rp"
            android:textColor="@color/black_80"
            android:textSize="@dimen/text_28sp" />

        <TextView
            android:id="@+id/selling_price_edit"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:autoSizeMaxTextSize="34dp"
            android:autoSizeMinTextSize="22dp"
            android:autoSizeStepGranularity="1dp"
            android:fontFamily="@font/roboto"
            android:hint="0"
            android:maxLength="14"
            android:textColor="@color/black_80"
            android:textColorHint="@color/black_20"
            android:textSize="@dimen/text_28sp"
            android:textStyle="bold" />

        <View
            android:id="@+id/cursor"
            android:layout_width="2.0dip"
            android:layout_height="28.0dip"
            android:layout_gravity="center_vertical"
            android:background="@color/black_60" />
    </LinearLayout>

    <!--    <LinearLayout-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        android:id="@+id/result_layout">-->

    <LinearLayout
        android:id="@+id/ll_change_to_give"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        android:orientation="horizontal"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/ll_custom_amount"
        tools:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/change_to_give"
            android:textColor="@color/black_60"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_change_to_give"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="end"
            android:textColor="@color/black_60"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            tools:text="Rp0" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/success_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_success"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/trx_success" />

        <TextView
            android:id="@+id/tv_trx_success"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/transaksi_berhasil_dicatat"
            android:textColor="@color/green_80"
            android:textSize="@dimen/text_24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/keyboardView"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>