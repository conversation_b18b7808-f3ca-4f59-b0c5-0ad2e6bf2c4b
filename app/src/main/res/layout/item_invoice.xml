<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <TextView
        android:id="@+id/tv_key"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="jgcvghchjvjhfvjyfjhjghfvjhgf"
        android:textColor="@color/black_40"
        android:layout_marginEnd="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline"/>

    <TextView
        android:id="@+id/tv_value"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="end"
        tools:text="jghcjhvhgcvghchjvjhfvjyfvj"
        android:textColor="@color/black_60"
        android:lineHeight="@dimen/_20dp"
        android:layout_marginStart="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/guideline"
        tools:targetApi="p" />

</androidx.constraintlayout.widget.ConstraintLayout>