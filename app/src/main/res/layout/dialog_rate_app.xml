<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#00000000">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="6dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white_shade_bg"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorPrimary"
                android:orientation="vertical"
                android:padding="25.0dip">

                <ImageView
                    android:layout_width="50.0dip"
                    android:layout_height="50.0dip"
                    android:layout_gravity="center_horizontal"
                    android:tint="@color/white"
                    app:srcCompat="@drawable/ic_people" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="7.0dip"
                    android:text="@string/rate_us_title"
                    android:textColor="@color/white_shade_bg"
                    android:textSize="16.0sp"
                    android:lineSpacingExtra="3dp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20.0dip"
                android:layout_marginTop="25.0dip"
                android:lineSpacingExtra="4dp"
                android:layout_marginRight="16.0dip"
                android:layout_marginBottom="12.0dip"
                android:text="@string/rate_us_msg"
                android:textColor="@color/main_text_color"
                android:textSize="16.0dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="16.0dip"
                android:layout_marginEnd="10.0dip"
                android:layout_marginRight="10.0dip"
                android:layout_marginBottom="10.0dip"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_later"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="10.0dip"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_gravity="start"
                    android:text="@string/later"
                    android:layout_weight="1"
                    android:textSize="@dimen/_16dp"
                    android:textAllCaps="true"
                    android:textColor="@color/colorPrimary" />

                <LinearLayout
                    android:layout_weight="1"
                    android:gravity="right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="20.0dip"
                    android:paddingTop="10.0dip"
                    android:paddingRight="20.0dip"
                    android:paddingBottom="10.0dip"
                    android:text="@string/sorry"
                    android:textAllCaps="true"
                    android:textSize="@dimen/_16dp"
                    android:textColor="@color/colorPrimary" />

                <TextView
                    android:id="@+id/btn_ok"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25.0dip"
                    android:paddingTop="10.0dip"
                    android:paddingRight="20.0dip"
                    android:paddingBottom="10.0dip"
                    android:textSize="@dimen/_16dp"
                    android:text="@string/rate"
                    android:textAllCaps="true"
                    android:textColor="@color/colorPrimary" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>