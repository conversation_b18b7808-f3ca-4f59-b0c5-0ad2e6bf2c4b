<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:padding="12.0dip">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/closeDialog"
            android:tint="@color/white"
            android:backgroundTint="@color/white"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_close"
            tools:ignore="ContentDescription"
            android:layout_marginEnd="@dimen/_16dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/closeDialog"
            android:layout_marginTop="7.0dip"
            android:text="@string/select_business"
            android:textColor="@color/white_shade_bg"
            android:textSize="14.0sp"
            android:textStyle="bold" />

    </RelativeLayout>

    <ListView
        android:id="@+id/businessCategoryList"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>