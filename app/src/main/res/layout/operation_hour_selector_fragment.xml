<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:clickable="true"
    android:focusable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                app:srcCompat="@drawable/ic_back" />

            <TextView
                android:id="@+id/tv_toolBar_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_24dp"
                android:layout_toEndOf="@+id/back_btn"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/business_hour_title"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:drawableLeftCompat="@drawable/dot_highlighter"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:layout_width="wrap_content"
        android:id="@+id/tv_operation_day_label"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        android:text="Pilih Hari"/>

    <TextView
        android:id="@+id/tv_subtitle_day"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/Body3"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginStart="@dimen/_16dp"
        android:text="Atur hari buka toko atau usaha kamu "
        app:layout_constraintTop_toBottomOf="@id/tv_operation_day_label"/>

    <RadioGroup
        android:id="@+id/rg_operation_days"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle_day"
        app:layout_constraintStart_toStartOf="parent"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/rb_daily"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/everyday"
            android:textSize="@dimen/_14dp"
            />

        <RadioButton
            android:id="@+id/rb_weekdays"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/_14dp"
            android:text="@string/weekdays" />

        <RadioButton
            android:id="@+id/radioMale"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/monday_to_saturday"
            android:textSize="@dimen/_14dp"/>

        <RadioButton
            android:id="@+id/radioFemale"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/_14dp"
            android:text="@string/tuesday_to_friday" />

    </RadioGroup>

    <TextView
        android:layout_width="wrap_content"
        android:id="@+id/tv_operation_time_label"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_16dp"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/_32dp"
        app:layout_constraintTop_toBottomOf="@id/rg_operation_days"
        app:layout_constraintStart_toStartOf="parent"
        android:text="Pilih Jam"/>

    <TextView
        android:id="@+id/tv_subtitle_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/Body3"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginStart="@dimen/_16dp"
        android:text="Atur waktu jam buka dan tutup toko atau usaha kamu"
        app:layout_constraintTop_toBottomOf="@id/tv_operation_time_label"/>

    <EditText
        android:layout_width="@dimen/_80dp"
        android:id="@+id/et_hour"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_16dp"
        style="@style/EditTextBordered"
        android:focusableInTouchMode="false"
        android:textStyle="bold"
        android:textAlignment="center"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle_time"
        app:layout_constraintStart_toStartOf="parent"
        android:text="08:00"/>

    <EditText
        android:layout_width="@dimen/_80dp"
        android:id="@+id/et_minute"
        style="@style/EditTextBordered"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_16dp"
        android:textStyle="bold"
        android:focusableInTouchMode="false"
        android:textAlignment="center"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle_time"
        app:layout_constraintStart_toEndOf="@id/et_hour"
        android:text="20:00"/>

    <CheckBox
        android:id="@+id/cb_always_open"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginStart="@dimen/_16dp"
        android:text="Buka 24 jam"
        android:buttonTint="@color/otp_field_stroke_color"
        app:layout_constraintTop_toBottomOf="@id/et_hour"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_gravity="top"
            android:background="@color/new_divider" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_action"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:enabled="false"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="@string/save" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>