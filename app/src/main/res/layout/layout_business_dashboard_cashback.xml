<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_0">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/back_btn"
            android:layout_width="@dimen/_25dp"
            android:layout_height="@dimen/_25dp"
            android:layout_gravity="top"
            android:layout_marginTop="@dimen/_20dp"
            android:fontFamily="@font/roboto"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_back" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_24dp"
            android:layout_gravity="top"
            android:layout_marginStart="@dimen/_24dp"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_toEndOf="@+id/back_btn"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:lineHeight="@dimen/_26dp"
            android:lineSpacingExtra="@dimen/text_8sp"
            android:maxLines="1"
            android:text="@string/riwayat_cashback"
            android:textColor="@color/white"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/back_btn"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UnusedAttribute" />

    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_cashback"
        android:layout_width="@dimen/_0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_no_cashback"
        android:layout_width="@dimen/_100dp"
        android:layout_height="120dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_no_cashback" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_no_cashback_title"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_32dp"
        android:text="@string/kamu_belum_ada_cashback"
        android:textColor="@color/black60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_no_cashback" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_no_cashback_subtitle"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="3"
        android:paddingHorizontal="@dimen/_16dp"
        android:text="@string/yuk_lakukan_lebih"
        android:textColor="@color/black60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_no_cashback_title" />

</androidx.constraintlayout.widget.ConstraintLayout>