<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_home"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/colorGreyLight"
    android:layout_height="wrap_content"
    android:orientation="vertical">

<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_refree_entry_point"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:layout_marginBottom="@dimen/_12dp"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    android:background="@drawable/white_background_radius_16">

    <ImageView
        android:id="@+id/iv_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_12dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_benefits_refree" />

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_blue_arrow"
        android:layout_marginStart="@dimen/_12dp"
        android:lines="2"
        android:layout_marginEnd="@dimen/_20dp"
        android:layout_marginVertical="@dimen/_12dp"
        style="@style/Body3"
        android:textColor="@color/black_80"/>

    <ImageView
        android:id="@+id/iv_blue_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_blue_right_arrow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/_20dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>