<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintEnd_toStartOf="@id/tv_product_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_product_name"
        style="@style/Body1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="1"
        android:layout_marginStart="@dimen/_10dp"

        app:layout_constraintEnd_toStartOf="@id/numberStepper"
        app:layout_constraintStart_toEndOf="@id/checkbox"
        app:layout_constraintTop_toTopOf="@id/checkbox"
        app:layout_constraintVertical_chainStyle="spread"
        tools:text="Nama produk yang sangat panjang" />

    <TextView
        android:id="@+id/price_added_label_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:ellipsize="end"
        android:fontFamily="@font/roboto"
        android:maxLength="20"
        android:maxLines="2"
        android:text="@string/selling_price"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_10sp"
        app:layout_constraintStart_toEndOf="@id/checkbox"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_name"
        tools:text="Harga Jual"

        />

    <TextView
        android:layout_marginStart="@dimen/_10dp"
        android:id="@+id/selling_price_currency_txt"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:text="@string/currency"
        android:textColor="@color/green_100"
        app:layout_constraintStart_toEndOf="@id/checkbox"
        app:layout_constraintTop_toBottomOf="@+id/price_added_label_text" />

    <TextView
        android:id="@+id/selling_price_txt"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:maxLength="20"
        android:maxLines="2"
        android:textColor="@color/green_100"
        app:layout_constraintStart_toEndOf="@id/selling_price_currency_txt"
        app:layout_constraintTop_toBottomOf="@+id/price_added_label_text"
        tools:text="0" />

    <TextView
        android:id="@+id/tv_stock_unit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/manage_product_stock"
        android:textColor="@color/black_40"
        android:textSize="@dimen/text_14sp"
        android:layout_marginStart="@dimen/_4dp"
        app:layout_constraintStart_toEndOf="@id/selling_price_txt"
        app:layout_constraintTop_toBottomOf="@id/price_added_label_text"
        tools:text="/PCS"
        tools:textColor="@color/black_40" />

    <TextView
        android:id="@+id/tv_stock"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/manage_product_stock"
        android:textColor="@color/green_100"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/numberStepper"
        tools:text="Stok:20 "
        tools:textColor="@color/black_80" />

    <TextView
        android:id="@+id/tv_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        style="@style/Body2"
        tools:visibility="visible"
        android:text="@string/edit"
        android:layout_marginEnd="@dimen/_14dp"
        android:visibility="gone"
        android:textColor="@color/blue_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_stock"
        tools:text="Ubah" />

    <com.bukuwarung.activities.expense.NumberStepper
        android:id="@+id/numberStepper"
        android:layout_marginEnd="@dimen/_14dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/checkbox"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/checkbox" />

    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:background="@color/black_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/selling_price_txt" />
</androidx.constraintlayout.widget.ConstraintLayout>