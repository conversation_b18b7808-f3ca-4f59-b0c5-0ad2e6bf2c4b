<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:maxHeight="350dp"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:maxHeight="350dp"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="24dp"
        android:paddingEnd="20dp"
        android:paddingBottom="@dimen/_16dp">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:text="@string/product_detail"
            android:textColor="#222222"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/productRv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/productRv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_8dp"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toTopOf="@id/btnContainer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_min="50dp"
            app:layout_constraintHeight_max="180dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"
            tools:itemCount="3"
            tools:listitem="@layout/item_product_entry" />

        <LinearLayout
            android:id="@+id/btnContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/add_product_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginEnd="@dimen/_8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/white"
                android:enabled="true"
                android:gravity="center"
                android:padding="14dp"
                android:text="@string/product_add"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                android:textStyle="bold"
                app:cornerRadius="4dp"
                app:strokeColor="@color/colorPrimary"
                app:strokeWidth="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/save_product_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:backgroundTint="@color/colorPrimary"
                android:enabled="true"
                android:gravity="center"
                android:padding="14dp"
                android:text="@string/product_save"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                app:cornerRadius="4dp"
                app:iconGravity="textStart"
                app:iconTint="@color/white" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>