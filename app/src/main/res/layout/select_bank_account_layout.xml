<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/label_account_txt"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="9dp"
        android:text="@string/label_customer_account"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/not_enough_saldo_txt"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/not_enough_saldo"
        android:textColor="@color/red_80"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_bank_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintTop_toBottomOf="@id/label_account_txt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SubHeading1"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:drawablePadding="@dimen/_10dp"
            android:text="@string/error_title"
            android:textColor="@color/black_80"
            app:drawableStartCompat="@drawable/ic_blocked_icon" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bank_details_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:padding="@dimen/_12dp"
        android:background="@drawable/edittext_bg_flat">

        <TextView
            android:id="@+id/enterAccountMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp"
            android:text="@string/enter_account_message"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            app:drawableEndCompat="@drawable/ic_right"
            app:drawableTint="@color/blue_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_bank_image"
            android:layout_width="56dp"
            android:layout_height="44dp"
            android:padding="2dp"
            app:cardBackgroundColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_bank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_bank" />
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/txt_bank_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="@color/black_60"
            android:textSize="16sp"
            android:visibility="gone"
            android:drawablePadding="@dimen/_10dp"
            app:layout_constraintBottom_toTopOf="@+id/txt_account_number"
            app:layout_constraintEnd_toStartOf="@id/button_change_account"
            app:layout_constraintStart_toEndOf="@id/layout_bank_image"
            app:layout_constraintTop_toTopOf="@id/layout_bank_image"
            tools:text="Bank Name" />

        <TextView
            android:id="@+id/txt_account_number"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="2dp"
            android:gravity="center_vertical"
            android:textColor="@color/black_40"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/layout_bank_image"
            app:layout_constraintStart_toEndOf="@id/layout_bank_image"
            app:layout_constraintTop_toBottomOf="@id/txt_bank_title"
            tools:text="Bank Account" />

        <TextView
            android:id="@+id/topup_txt"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_4dp"
            android:layout_marginTop="2dp"
            android:gravity="center_vertical"
            android:text="@string/topup_saldo"
            android:visibility="gone"
            android:textColor="@color/red_80"
            app:layout_constraintBottom_toBottomOf="@id/layout_bank_image"
            app:layout_constraintStart_toEndOf="@id/txt_account_number"
            app:layout_constraintTop_toBottomOf="@id/txt_bank_title" />

        <TextView
            android:id="@+id/button_change_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:fontFamily="@font/roboto"
            android:padding="8dp"
            android:paddingLeft="0dp"
            android:paddingRight="0dp"
            android:text="@string/label_change"
            android:textColor="@color/blue_80"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/bank_account_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="layout_bank_image,txt_bank_title,txt_account_number" />
    </androidx.constraintlayout.widget.ConstraintLayout>



        <TextView
            android:id="@+id/tv_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/Body3"
            android:visibility="gone"
            android:text="@string/blocked_info_text"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_4dp"
            android:textColor="@color/red_80"
            android:background="@drawable/bg_red_outline" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
