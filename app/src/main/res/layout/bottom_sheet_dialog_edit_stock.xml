<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="item"
            type="com.bukuwarung.database.entity.ProductEntity" />
    </data>

    <RelativeLayout
        android:background="@drawable/rounded_corner_background_bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:paddingLeft="@dimen/_16dp"
            android:paddingRight="@dimen/_16dp"
            android:paddingTop="30dp"
            android:paddingBottom="@dimen/_16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >
            <TextView
                android:id="@+id/productName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:textSize="18sp"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:text="@string/change_items" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_alignParentRight="true"
                android:layout_width="wrap_content"
                android:src="@drawable/ic_cross"
                android:layout_height="wrap_content"/>


            <LinearLayout
                android:id="@+id/productNameETLayout"
                android:layout_marginTop="@dimen/_18dp"
                android:layout_below="@+id/productName"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:weightSum="2">
            <EditText
                android:layout_width="wrap_content"
                android:id="@+id/productNameET"
                android:layout_weight="1.4"
                android:layout_height="match_parent"
                android:background="@drawable/button_round_cornerd_stroke_pop_up"
                android:padding="10dp"
                android:text="@{item.name}"
                android:textColor="@color/black_80"
                android:textSize="14sp"
                android:foreground="?attr/selectableItemBackground" />

                <LinearLayout
                    android:layout_weight="0.1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <LinearLayout
                    android:id="@+id/edit_measurement"
                    android:background="@drawable/button_round_cornerd_disable_btn"
                    android:layout_weight="0.5"
                    android:layout_gravity="right"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">
                <TextView
                    android:id="@+id/unitName"
                    android:layout_width="wrap_content"
                    android:text="@{item.measurementName}"
                    android:fontFamily="@font/roboto"
                    android:textSize="12sp"
                    android:layout_height="wrap_content"/>

                    <ImageView
                        android:layout_marginLeft="10dp"
                        android:layout_width="wrap_content"
                        android:src="@drawable/ic_right_arrow"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                </LinearLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/product_price_parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:layout_below="@+id/productNameETLayout"
                >

                <TextView
                    android:id="@+id/product_price_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/selling_price"
                    android:textSize="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:textColor="@color/black_80"
                    app:layout_constraintHorizontal_bias="1"
                    />

                <TextView
                    android:id="@+id/product_price_optional"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/optional"
                    android:textSize="@dimen/_16dp"
                    android:textColor="@color/black_20"
                    app:layout_constraintBaseline_toBaselineOf="@+id/product_price_label"
                    app:layout_constraintStart_toEndOf="@id/product_price_label" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.48"/>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toEndOf="@+id/guideline"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:id="@+id/selling_price_parent"
                    >
                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline_internal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.30"
                        />
                    <TextView
                        android:id="@+id/currency_symbol"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:paddingEnd="2dp"
                        android:text="Rp"
                        android:textColor="@color/green_100"
                        android:textSize="24sp"
                        style="@style/Heading3"
                        app:layout_constraintEnd_toStartOf="@+id/selling_price_edit"
                        app:layout_constraintTop_toTopOf="parent"
                        />



                    <View
                        android:id="@+id/cursor"
                        android:layout_width="2.0dip"
                        android:layout_height="28.0dip"
                        android:layout_gravity="center_vertical"
                        android:background="@color/black_60"
                        app:layout_constraintBottom_toBottomOf="@id/selling_price_edit"
                        app:layout_constraintEnd_toEndOf="@id/selling_price_edit"
                        app:layout_constraintTop_toTopOf="@id/selling_price_edit" />

                    <TextView
                        android:id="@+id/selling_price_edit"
                        style="@style/Heading3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_8dp"
                        android:autoSizeMaxTextSize="34dp"
                        android:autoSizeMinTextSize="22dp"
                        android:autoSizeStepGranularity="1dp"
                        android:fontFamily="@font/roboto"
                        android:hint="0"
                        android:maxLength="14"
                        android:textColor="@color/green_100"
                        android:textColorHint="@color/green_100"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                    <View
                        android:id="@+id/nominal_line"
                        style="@style/Divider.Horizontal"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="@dimen/_8dp"
                        android:background="@color/black_60"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@id/guideline_internal"
                        app:layout_constraintTop_toBottomOf="@id/selling_price_edit" />
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:id="@+id/result_layout"
                        >
                        <TextView
                            android:id="@+id/text_amount_calc"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:fontFamily="@font/roboto"
                            android:gravity="center_horizontal"
                            android:lineSpacingExtra="6sp"
                            android:textAlignment="center"
                            android:textColor="@color/black_40"
                            android:textSize="15sp"
                            android:textStyle="normal"
                            android:visibility="gone"

                             />
                    </LinearLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <RelativeLayout
                android:layout_marginTop="20dp"
                android:id="@+id/StockMinimumLayout"
                android:layout_below="@+id/product_price_parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/stockMinimu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:textSize="16sp"
                    android:drawablePadding="3dp"
                    android:drawableTint="@color/black_40"
                    android:drawableRight="@drawable/ic_info"
                    android:textColor="@color/black_80"
                    android:text="@string/stock_minimum" />
                <LinearLayout
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    >
                    <include
                        android:id="@+id/numberStepperLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        layout="@layout/layout_customer_number_stepper"/>
                </LinearLayout>
            </RelativeLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_confirm"
                style="@style/ButtonFill"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/StockMinimumLayout"
                android:layout_gravity="center"
                android:layout_marginTop="30dp"
                android:text="@string/confirmation"
                android:textAllCaps="true" />
                />
        </RelativeLayout>
        <com.bukuwarung.keyboard.CustomKeyboardView
            android:id="@+id/keyboardView"
            android:layout_width="match_parent"
            android:layout_height="245dp"
            android:visibility="gone"
            android:layout_alignParentBottom="true"/>
    </RelativeLayout>

</layout>