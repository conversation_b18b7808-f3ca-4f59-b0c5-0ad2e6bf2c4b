<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_receive_money"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:gravity="center"
        android:paddingTop="@dimen/_10dp"
        android:paddingBottom="@dimen/_10dp"
        android:text="@string/receive_money"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:cornerRadius="@dimen/_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/blue_60" />

    <TextView
        android:id="@+id/tv_enter_nominal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:gravity="center"
        android:text="@string/or_enter_a_nominal"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/btn_receive_money" />

    <TextView
        android:id="@+id/tv_change_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:gravity="center"
        android:text="@string/change_calculated_automatically"
        android:textColor="@color/black_40"
        android:textSize="@dimen/text_10sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/tv_enter_nominal" />

    <TextView
        android:id="@+id/text_amount_calc"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:fontFamily="@font/roboto"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="6sp"
        android:textAlignment="center"
        android:textColor="@color/black_40"
        android:textSize="15sp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/result_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_custom_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/tv_change_info">

        <TextView
            android:id="@+id/currency_symbol"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="Rp"
            android:textColor="@color/black_80"
            android:textSize="@dimen/text_28sp" />

        <TextView
            android:id="@+id/selling_price_edit"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:autoSizeMaxTextSize="@dimen/_35dp"
            android:autoSizeMinTextSize="@dimen/_22dp"
            android:autoSizeStepGranularity="@dimen/_1dp"
            android:fontFamily="@font/roboto"
            android:hint="0"
            android:maxLength="14"
            android:textColor="@color/black_80"
            android:textColorHint="@color/black_20"
            android:textSize="@dimen/text_28sp"
            android:textStyle="bold" />

        <View
            android:id="@+id/cursor"
            android:layout_width="2.0dip"
            android:layout_height="28.0dip"
            android:layout_gravity="center_vertical"
            android:background="@color/black_60" />
    </LinearLayout>

    <!--    <LinearLayout-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        android:id="@+id/result_layout">-->

    <LinearLayout
        android:id="@+id/ll_change_to_give"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_36dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        android:orientation="horizontal"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/ll_custom_amount"
        tools:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/change_to_give"
            android:textColor="@color/black_60"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_change_to_give"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="end"
            android:textColor="@color/black_60"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            tools:text="Rp0" />
    </LinearLayout>

    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/keyboardView"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/success_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_success"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/trx_success" />

        <TextView
            android:id="@+id/tv_trx_success"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/transaksi_berhasil_dicatat"
            android:textColor="@color/green_80"
            android:textSize="@dimen/text_24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>