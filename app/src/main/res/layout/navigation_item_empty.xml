<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="48dp"
    android:background="@color/white"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="48dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="24dp"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="4sp"
        android:text="@string/navigation_empty_text"
        android:textColor="#90a4ae"
        android:textSize="12sp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/add_first_business"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/create_new_ledger"
        app:strokeColor="#90a4ae"
        app:strokeWidth="1dp" />
</LinearLayout>
