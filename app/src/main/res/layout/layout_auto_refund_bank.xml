<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/cl_refund_layout"
    android:background="@drawable/bg_rounded_rectangle_blue_5"
    android:paddingBottom="@dimen/_16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_set_refund"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/SubHeading1"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/set_refund_method"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/tv_set_receiver"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:text="@string/enter_recipient_account"
        app:layout_constraintEnd_toStartOf="@+id/btn_choose_refund_method"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_set_refund" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_choose_refund_method"
        style="@style/ButtonFill.Red"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/choose"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_set_refund" />

    <include
        android:id="@+id/include_bank_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        layout="@layout/layout_bank_layout"
        android:visibility="gone"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_set_refund"/>

</androidx.constraintlayout.widget.ConstraintLayout>