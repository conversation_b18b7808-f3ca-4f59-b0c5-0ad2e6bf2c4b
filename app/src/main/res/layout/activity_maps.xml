<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".activities.geolocation.view.MapsActivity"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <fragment
        android:id="@+id/map"
        android:name="com.google.android.gms.maps.SupportMapFragment"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_address_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/background_floating_material_light"
        android:padding="@dimen/_16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_choose_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:text="@string/choose_location"
            style="@style/Heading3"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_location_blue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_choose_location"
            android:layout_marginTop="@dimen/_16dp"
            app:srcCompat="@drawable/ic_location_blue"/>

        <TextView
            android:id="@+id/tv_location"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@id/iv_location_blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_choose_location"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_16dp"
            android:lines="3"
            style="@style/SubHeading2"/>

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tv_location"
            android:layout_marginTop="@dimen/_10dp"
            style="@style/ButtonFill.Yellow"
            android:text="@string/confirmation"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

