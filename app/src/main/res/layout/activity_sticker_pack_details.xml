<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingBottom="@dimen/preview_side_margin">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="#0091ff">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="fill_parent"
            android:layout_height="?actionBarSize"
            app:contentInsetLeft="0.0dip"
            app:contentInsetStart="0.0dip"
            app:contentInsetStartWithNavigation="0.0dip">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:gravity="center"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/screen_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:fontFamily="@font/roboto"
                android:text="@string/unlock_sticker_title"
                android:textColor="#ffffff"
                android:textSize="18.0dip"
                android:textStyle="bold" />
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary">


        <ImageView
            android:id="@+id/tray_image"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="8dp"
            android:contentDescription="@string/tray_image_content_description"
            android:src="@color/white"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@color/white" />

        <TextView
            android:id="@+id/pack_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_from_tray_to_name"
            android:layout_marginLeft="@dimen/margin_from_tray_to_name"
            android:layout_marginTop="8dp"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@+id/tray_image"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Pack Name" />

        <TextView
            android:id="@+id/author"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_from_tray_to_name"
            android:layout_marginLeft="@dimen/margin_from_tray_to_name"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@+id/tray_image"
            app:layout_constraintTop_toBottomOf="@+id/pack_name"
            tools:text="Author" />

        <TextView
            android:id="@+id/pack_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_from_tray_to_name"
            android:layout_marginLeft="@dimen/margin_from_tray_to_name"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@+id/tray_image"
            app:layout_constraintTop_toBottomOf="@+id/author"
            tools:text="Size" />

        <TextView
            android:id="@+id/locked_sticker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="@string/almost_there"
            android:textColor="@color/white"
            android:textSize="22dp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/icLock"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_below="@id/locked_sticker"
            android:layout_marginTop="4dp"
            android:layout_toLeftOf="@+id/locked_sticker_subtext"
            android:src="@drawable/ic_locked"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/locked_sticker_subtext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/locked_sticker"
            android:layout_centerHorizontal="true"
            android:layout_marginLeft="4dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="40dp"
            android:alpha="0.6"
            android:text="@string/more_trans_more_gift"
            android:textColor="@color/white" />
    </RelativeLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.bukuwarung.activities.stickers.BottomFadingRecyclerView
            android:id="@+id/sticker_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="24dp"
            android:background="@color/white"
            android:clipToPadding="false"
            android:fadingEdgeLength="@dimen/fading_edge_length"
            android:paddingStart="@dimen/preview_side_margin"
            android:paddingLeft="@dimen/preview_side_margin"
            android:paddingEnd="@dimen/preview_side_margin"
            android:paddingRight="@dimen/preview_side_margin"
            android:requiresFadingEdge="vertical" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:alpha="0.1"
            android:background="#000000"
            android:visibility="invisible"
            tools:visibility="visible" />

    </FrameLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginStart="@dimen/preview_side_margin"
        android:layout_marginLeft="@dimen/preview_side_margin"
        android:layout_marginEnd="@dimen/preview_side_margin"
        android:layout_marginRight="@dimen/preview_side_margin">

        <TextView
            android:id="@+id/already_added_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="@string/details_pack_already_added"
            android:textColor="#9B9B9B"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/add_to_whatsapp_button2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/btn_green"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless"
            android:visibility="gone">

            <TextView
                style="@style/StickerPreviewButtonText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:clickable="false"
                android:drawableStart="@drawable/ic_whatsapp_new"
                android:drawableLeft="@drawable/ic_whatsapp_new"
                android:drawablePadding="4dp"
                android:drawableTint="@color/white"
                android:focusable="false"
                android:foreground="@android:color/transparent"
                android:text="@string/add_to_whatsapp" />
        </FrameLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_to_whatsapp_button"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_toRightOf="@id/btn_save"
            android:layout_weight="1"
            android:backgroundTint="@color/green_dark"
            android:fontFamily="@font/roboto"
            android:maxLines="1"
            android:text="@string/add_to_whatsapp"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="16dp"
            android:textStyle="bold"
            android:visibility="gone"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="12sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            app:cornerRadius="4dp"
            app:icon="@drawable/ic_whatsapp_new"
            app:iconGravity="textStart"
            app:iconTint="@color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_transaction_btn"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_toRightOf="@id/btn_save"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:maxLines="1"
            android:text="@string/add_more_trans"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="16dp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="12sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            app:backgroundTint="@color/buku_CTA"
            app:cornerRadius="4dp" />
    </FrameLayout>
</LinearLayout>
