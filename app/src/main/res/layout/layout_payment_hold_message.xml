<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dimen_16dp"
    android:background="@drawable/background_info"
    android:paddingHorizontal="@dimen/dimen_16dp"
    android:paddingVertical="@dimen/dimen_8dp">

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        android:drawablePadding="@dimen/_8dp"
        app:drawableStartCompat="@drawable/ic_info"
        app:layout_constraintEnd_toStartOf="@id/btn_hold_action"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Demi keamanan, pembayaranmu sedang diperiksa. Tunggu hingga proses pemeriksaan selesai." />

    <TextView
        android:id="@+id/btn_hold_action"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="@color/blue_60"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Pelajari" />
</androidx.constraintlayout.widget.ConstraintLayout>