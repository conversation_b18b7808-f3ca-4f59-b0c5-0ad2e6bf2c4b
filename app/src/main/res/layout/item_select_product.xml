<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingVertical="@dimen/_8dp"
    android:background="?attr/selectableItemBackground"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_initial"
        style="@style/Heading3"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/initial_product_name_bg"
        android:gravity="center"
        android:textAllCaps="true"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/tv_selling_price"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="A" />

    <TextView
        android:id="@+id/tv_product_name"
        app:layout_constraintStart_toEndOf="@id/tv_initial"
        android:layout_width="wrap_content"
        style="@style/SubHeading1"
        tools:text="Beras"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_selling_price_label"
        style="@style/Label1"
        android:layout_marginStart="@dimen/_8dp"
        tools:text="Harga Jual"
        android:text="@string/selling_price"
        app:layout_constraintStart_toEndOf="@id/tv_initial"
        android:layout_width="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_selling_price"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_price_label"
        app:layout_constraintStart_toEndOf="@id/tv_initial"
        android:layout_width="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        style="@style/SubHeading1"
        android:textColor="@color/green_100"
        tools:text="Harga Jual"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_unit"
        app:layout_constraintTop_toBottomOf="@id/tv_selling_price_label"
        app:layout_constraintStart_toEndOf="@id/tv_selling_price"
        android:layout_width="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        style="@style/Body2"
        android:textColor="@color/black_40"
        tools:text="/Pcs"
        android:layout_height="wrap_content" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checbox"
        android:clickable="false"
        android:layout_width="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>