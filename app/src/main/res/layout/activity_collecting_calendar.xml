<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_alignParentStart="true"
                android:layout_width="25dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_height="25dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:layout_centerVertical="true"
                android:src="@mipmap/back_white"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/title"
                android:layout_marginStart="24dp"
                android:layout_width="wrap_content"
                android:drawableStart="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:layout_height="24dp"
                android:layout_toEndOf="@+id/backBtn"
                android:layout_alignParentTop="true"
                android:fontFamily="@font/roboto"
                android:textStyle="bold"
                android:layout_centerVertical="true"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:lineSpacingExtra="8sp"
                android:lineHeight="26dp"
                android:gravity="top"
                android:maxLines="1"
                android:text="@string/collecting_calendar_title"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/completedContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/blue_80"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="12dp"
        android:visibility="gone">
        
        <ImageView
            android:id="@+id/confettiIcon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/ic_confetti"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/confettiTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/collection_complete_title"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginStart="@dimen/_16dp"
            app:layout_constraintStart_toEndOf="@id/confettiIcon"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/confettiSubtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/collection_complete_subtitle"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_marginStart="@dimen/_16dp"
            app:layout_constraintStart_toEndOf="@id/confettiIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/confettiTitle"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/confettiClose"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/ic_close"
            android:tint="@color/white"
            android:layout_marginBottom="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/confettiSubtitle"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/notCompletedContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/blue_80"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="12dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/tempoIcon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/ic_tempo"
            android:layout_marginBottom="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintBottom_toTopOf="@id/btnTempo"/>

        <TextView
            android:id="@+id/tempoTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:text="Buat tempo utang 3 pelanggan"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginStart="@dimen/_16dp"
            app:layout_constraintStart_toEndOf="@id/tempoIcon"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tempoSubtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:text="Dapatkan pembayaran utang Rp 600.000 yang lebih cepat"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginBottom="12dp"
            app:layout_constraintStart_toEndOf="@id/tempoIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tempoTitle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintBottom_toTopOf="@id/btnTempo"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnTempo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/collection_add_btn"
            android:textColor="@color/blue_80"
            android:textStyle="bold"
            android:textSize="16sp"
            app:backgroundTint="@color/white"
            android:gravity="center"
            android:textAllCaps="false"
            android:padding="11dp"
            app:icon="@drawable/ic_pointer"
            app:iconTint="@color/blue_80"
            app:iconGravity="textStart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
    
    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabIndicatorFullWidth="true"
            app:tabTextColor="#8D8D8D"
            app:tabPaddingStart="0dp"
            app:tabPaddingEnd="0dp"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabGravity="fill"
            app:tabBackground="@color/white"
            app:tabIndicatorHeight="1dp"
            android:elevation="4dp"
            android:background="@color/white"
            app:tabIndicatorColor="@color/colorPrimary"/>

    </androidx.viewpager.widget.ViewPager>

</LinearLayout>