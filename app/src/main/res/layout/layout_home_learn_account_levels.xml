<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_white_outline_rectangle">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:src="@drawable/ic_kyc_badge_premium"
        android:layout_marginVertical="@dimen/_8dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tv_title_level"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Level akun"
        style="@style/Label2"
        android:textColor="@color/blue_10"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginStart="@dimen/_10dp"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Premium"
        style="@style/SubHeading2"
        android:textColor="@color/white"
        android:layout_marginBottom="@dimen/_12dp"
        android:layout_marginStart="@dimen/_10dp"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/iv_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_right_white_arrow"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tv_learn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Pelajari Level Akun"
        style="@style/Body3"
        app:layout_constraintEnd_toStartOf="@id/iv_right"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/_6dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>