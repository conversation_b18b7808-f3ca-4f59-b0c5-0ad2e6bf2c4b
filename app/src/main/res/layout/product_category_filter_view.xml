<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:animateLayoutChanges="true"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingVertical="@dimen/_8dp">

    <LinearLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_category"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_8dp"
            android:layout_weight="1"
            android:hint="@string/product_category"
            android:textColorHint="@color/black_40"
            app:boxStrokeColor="@color/black_40"
            app:boxStrokeWidth="1dp"
            app:hintTextColor="@color/black_40"
            app:endIconTint="@color/black_80"
            app:placeholderTextColor="@color/black_80">

            <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                android:id="@+id/actv_category"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:dropDownHeight="wrap_content"
                android:ellipsize="end"
                android:focusable="false"
                android:inputType="textNoSuggestions"
                android:maxLines="1"
                android:textColor="@color/black_80"
                android:textStyle="bold" />

        </com.google.android.material.textfield.TextInputLayout>

        <ImageView
            android:id="@+id/btn_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            android:src="@drawable/ic_icon_search" />

        <ImageView
            android:id="@+id/secondary_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            android:src="@drawable/ic_icon_sort_new_28" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/search_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/btn_close_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/black_80" />


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_search"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:boxBackgroundColor="@color/black_5"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="1dp"
            app:endIconDrawable="@drawable/ic_mtrl_chip_close_circle"
            app:endIconMode="clear_text"
            app:endIconTint="@color/black_20"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:hintTextAppearance="@style/Body2"
            app:startIconTint="@color/black_60"
            app:startIconDrawable="@drawable/ic_icon_search_new">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:hint="@string/search_product"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

</FrameLayout>