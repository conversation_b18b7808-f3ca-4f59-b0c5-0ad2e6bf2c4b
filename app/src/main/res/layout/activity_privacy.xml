<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_view"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="60.0dip"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:textAlignment="center"
                    android:textColor="@color/dark_68"
                    android:textSize="13dp" />

                <TextView
                    android:id="@+id/privacy_url"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="3.0dip"
                    android:gravity="center"
                    android:text="@string/html_privacy"
                    android:textAlignment="center"
                    android:textColor="@color/dark_68"
                    android:textSize="13dp" />
                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="1.0px"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginRight="@dimen/_16dp"
                android:background="@color/dark_gray" />
        </LinearLayout>
    </LinearLayout>

    <ScrollView
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_below="@+id/appbar"
        android:padding="8dp"
        android:layout_marginBottom="70.0dip">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="60.0dip"
                android:layout_height="60.0dip"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="40.0dip"
                android:tint="@color/colorPrimary"
                app:srcCompat="@drawable/ic_secure_data" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40.0dip"
                android:orientation="horizontal"
                android:paddingLeft="16.0dip"
                android:paddingRight="16.0dip">

                <ImageView
                    android:layout_width="24.0dip"
                    android:layout_height="24.0dip"
                    android:layout_gravity="center_vertical"
                    android:tint="@color/dark_68"
                    app:srcCompat="@drawable/ic_store_data" />

                <TextView
                    android:id="@+id/storeDataTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16.0dip"
                    android:layout_marginLeft="16.0dip"
                    android:text="@string/store_data_question"
                    android:textColor="@color/black"
                    android:textSize="16.0sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="56.0dip"
                android:layout_marginTop="12.0dip"
                android:layout_marginRight="16.0dip"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/usedForRecovery"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/used_for_data_recovery"
                    android:textColor="@color/dark_68"
                    android:textSize="13dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="56.0dip"
                android:layout_marginTop="12.0dip"
                android:layout_marginRight="16.0dip"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/usedForSms"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/use_for_sms_service"
                    android:textColor="@color/dark_68"
                    android:textSize="13dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30.0dip"
                android:orientation="horizontal"
                android:paddingLeft="16.0dip"
                android:paddingRight="16.0dip">

                <ImageView
                    android:layout_width="24.0dip"
                    android:layout_height="24.0dip"
                    android:layout_gravity="center_vertical"
                    android:tint="@color/dark_68"
                    app:srcCompat="@drawable/ic_share_data" />

                <TextView
                    android:id="@+id/isDataSharedTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16.0dip"
                    android:layout_marginLeft="16.0dip"
                    android:text="@string/is_data_shared"
                    android:textColor="@color/dark_68"
                    android:textSize="16.0sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="56.0dip"
                android:layout_marginTop="12.0dip"
                android:layout_marginRight="16.0dip"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/weDontShareTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/we_dont_share_data"
                    android:textColor="@color/dark_68"
                    android:textSize="13dp" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ActionBarAppTheme">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="fill_parent"
            android:layout_height="?actionBarSize"
            app:contentInsetLeft="0.0dip"
            app:contentInsetStart="0.0dip"
            app:contentInsetStartWithNavigation="0.0dip"
            app:navigationIcon="?homeAsUpIndicator">

            <TextView
                android:id="@+id/screen_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/privacy_policy"
                android:textColor="@color/white"
                android:textSize="18.0dip" />
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
</RelativeLayout>