<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <TextView
        android:id="@+id/totalUtangTitle"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/money_owes_you"/>
    <TextView
        android:id="@+id/utangAmount"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Rp250.000"
        android:textColor="@color/red_80"/>

    <View
        android:id="@+id/newDivider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/new_divider"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintTop_toBottomOf="@+id/utangAmount"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/manualReminderContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/newDivider">
        <TextView
            style="@style/Body2"
            android:id="@+id/billReminderMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/bill_reminder_message"
            android:layout_marginTop="12dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            style="@style/SubHeading1"
            android:id="@+id/nominalBill"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/nominal_bill"
            android:layout_marginTop="12dp"
            android:layout_marginStart="@dimen/_16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/billReminderMessage"
            />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_input_nominal"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:passwordToggleDrawable="@null"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="0.5dp"
            app:hintEnabled="false"
            android:layout_marginTop="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/nominalBill">
            <com.bukuwarung.baseui.CurrencyEditText
                android:id="@+id/input_nominal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:singleLine="true"
                android:textColor="@color/black_80"
                android:textColorHint="@color/hint_color"
                android:textCursorDrawable="@drawable/blue_cursor"
                android:textSize="21sp"
                android:textStyle="bold"/>
        </com.google.android.material.textfield.TextInputLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:visibility="gone"
            android:id="@+id/minimumAmountErrorMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/error_message_drawable_round_4dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@+id/layout_input_nominal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">
            <TextView
                android:id="@+id/amount_error_message_txt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/minimum_amount_error_message"
                android:textSize="12sp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_80"
                app:drawableStartCompat="@drawable/ic_info"
                app:drawableTint="@color/yellow"
                android:drawablePadding="10dp"
                android:layout_margin="@dimen/_12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <CheckBox
            android:id="@+id/create_payment_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:layout_marginStart="@dimen/_20dp"
            android:layout_marginTop="@dimen/_20dp"
            android:buttonTint="@color/otp_field_stroke_color"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/minimumAmountErrorMessage" />

        <TextView
            android:id="@+id/create_payment_txt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/Body3"
            android:layout_marginEnd="@dimen/_20dp"
            android:text="@string/create_payment_checkbox_text"
            app:layout_constraintBottom_toBottomOf="@id/create_payment_checkbox"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/create_payment_checkbox"
            app:layout_constraintTop_toTopOf="@id/create_payment_checkbox" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/create_payment_txt"
            app:layout_constraintEnd_toStartOf="@+id/requestMoney"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="24dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginBottom="20dp"
            android:fontFamily="@font/roboto"
            android:text="@string/cancel"
            android:textAllCaps="false"
            android:textSize="14sp"
            android:textColor="@color/colorPrimary"
            android:textStyle="bold"
            app:strokeWidth="1dp"
            android:paddingTop="12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:paddingBottom="12dp"
            app:strokeColor="@color/colorPrimary"
            android:backgroundTint="@color/white"/>
        <com.google.android.material.button.MaterialButton
            android:id="@+id/requestMoney"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/create_payment_txt"
            app:layout_constraintStart_toEndOf="@+id/cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/_20dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:text="@string/label_request_payment"
            style="@style/ButtonFill.Blue"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        tools:visibility="gone"
        android:id="@+id/manualReminderLoader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/newDivider">
        <ProgressBar
            android:id="@+id/paymentLinkProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"
            android:scaleX="2"
            android:scaleY="2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />
        <TextView
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:text="@string/payment_tab_loading_state_message"
            android:layout_marginTop="64dp"
            android:layout_marginBottom="28dp"
            app:layout_constraintTop_toBottomOf="@+id/paymentLinkProgressBar"
            app:layout_constraintBottom_toBottomOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
