<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/new_contact_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/icon_contact_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="16dp"
            android:background="@drawable/background_circular_yellow"
            android:padding="8dp"
            android:tint="@color/white"
            android:src="@drawable/ic_contact_add" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/icon_contact_add"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/new_phone_number_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_toStartOf="@+id/next"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="7.7sp"
                android:maxLines="1"
                android:textStyle="bold"
                android:text="@string/new_phone_number"
                android:textColor="@color/buku_CTA"
                android:textSize="16.3sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_below="@id/new_phone_number_tv"
                android:layout_toLeftOf="@id/next"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="7.7sp"
                android:maxLines="2"
                android:text="@string/new_phone_number_info"
                android:textColor="@color/greyDisabled"
                android:textSize="12sp" />

            <ImageView
                android:id="@+id/next"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:scaleType="fitXY"
                android:src="@mipmap/next_grey"

                />
        </RelativeLayout>
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#e8eef1" />
</LinearLayout>
