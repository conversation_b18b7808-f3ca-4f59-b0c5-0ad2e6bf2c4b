<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/selected_tab"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/layout_heading"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        layout="@layout/layout_business_profile_section_heading"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_name"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/layout_heading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_category"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/layout_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_number"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/layout_category"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_8dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>