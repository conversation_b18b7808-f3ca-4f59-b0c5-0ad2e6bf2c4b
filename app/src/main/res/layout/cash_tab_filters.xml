<LinearLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/filterLayout"
    android:layout_below="@id/app_bar"
    android:orientation="vertical"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:paddingTop="10dp"
        android:id="@+id/filterLayoutDate"
        android:paddingBottom="10dp"
        android:background="@color/rv_bg"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_marginLeft="12dp"
            android:id="@+id/dailyBtn"
            android:layout_width="wrap_content"
            android:paddingBottom="6dp"
            android:paddingTop="6dp"
            android:layout_height="30dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:gravity="center"
            android:background="@drawable/rectangle_filter"
            >

            <TextView
                android:id="@+id/text_daily"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:textStyle="bold"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="@string/Daily"
                android:textColor="@color/white" />
        </LinearLayout>



        <LinearLayout
            android:id="@+id/weeklyBtn"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginLeft="@dimen/_8dp"
            android:paddingBottom="6dp"
            android:paddingTop="6dp"
            android:gravity="center"
            android:background="@drawable/rectangle_filter_unselected"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            >
            <TextView
                android:id="@+id/text_weekly"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:text="@string/weekly"
                android:fontFamily="sans-serif"
                android:layout_marginRight="8dp"
                android:layout_marginLeft="8dp"
                android:textAlignment="center"
                android:textColor="@color/filter_disable"
                />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/monthlyBtn"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:paddingBottom="6dp"
            android:paddingTop="6dp"
            android:layout_marginLeft="@dimen/_8dp"
            android:layout_alignParentLeft="true"
            android:gravity="center"
            android:background="@drawable/rectangle_filter_unselected"
            >
            <TextView
                android:id="@+id/text_monthly"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:textAlignment="center"
                android:text="@string/monthly"
                android:textAppearance="@style/harian_unselected"
                />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/filterLayoutCategory"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/rv_bg"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/allButton"
            android:layout_width="91dp"
            android:layout_height="30dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="12dp"
            android:background="@drawable/rectangle_filter"
            android:gravity="center"
            android:paddingTop="6dp"
            android:paddingBottom="6dp">

            <TextView
                android:id="@+id/text_all"
                android:layout_width="50dp"
                android:layout_height="20dp"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="12dp"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:text="@string/filter_all"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/expenseBtn"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="@dimen/_8dp"
            android:background="@drawable/rectangle_filter_unselected"
            android:gravity="center"
            android:paddingTop="6dp"
            android:paddingBottom="6dp">

            <TextView
                android:id="@+id/text_expense"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="12dp"
                android:fontFamily="sans-serif"
                android:text="@string/expense_label"
                android:textAlignment="center"
                android:textColor="@color/filter_disable" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/incomeBtn"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="@dimen/_8dp"
            android:background="@drawable/rectangle_filter_unselected"
            android:gravity="center"
            android:paddingTop="6dp"
            android:paddingBottom="6dp">

            <TextView
                android:id="@+id/text_income"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="12dp"
                android:text="@string/income_label"
                android:textAlignment="center"
                android:textAppearance="@style/harian_unselected" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>