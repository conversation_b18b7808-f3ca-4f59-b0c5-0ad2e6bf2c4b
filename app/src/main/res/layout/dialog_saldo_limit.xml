<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Title" />

    <TextView
        android:id="@+id/tv_body"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="long textlong textlong textlong textlong textlong textlong text" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_left"
        style="@style/Button.OutlinePrimary.Bold"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:text="@string/cancel"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="@+id/btn_right"
        app:layout_constraintEnd_toStartOf="@id/btn_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btn_right" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_right"
        style="@style/ButtonFill.Blue.Bold"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/yes_topup_saldo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_left"
        app:layout_constraintTop_toBottomOf="@id/tv_body" />

</androidx.constraintlayout.widget.ConstraintLayout>