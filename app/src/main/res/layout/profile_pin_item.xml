<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="@dimen/_4dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:layout_constraintDimensionRatio="1:1">

    <ImageView
        android:id="@+id/iv_profile_pin_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="@dimen/_50dp"
        android:minHeight="@dimen/_50dp"
        android:layout_margin="@dimen/_4dp"
        app:srcCompat="@drawable/ic_kartu_nama"
        android:padding="@dimen/_8dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="@id/tv_profile_pin_item"
        app:layout_constraintEnd_toEndOf="@id/tv_profile_pin_item"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_profile_pin_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Kartu Nama"
        android:paddingLeft="@dimen/_8dp"
        android:paddingRight="@dimen/_8dp"
        android:paddingBottom="@dimen/_4dp"
        style="@style/Body5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_profile_pin_item" />

    <TextView android:id="@+id/tv_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_free_text"
        android:fontFamily="@font/roboto"
        android:paddingHorizontal="@dimen/_6dp"
        android:paddingVertical="@dimen/_2dp"
        android:text="@string/text_new"
        android:textColor="@color/white"
        android:textSize="@dimen/text_8sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_profile_pin_item"/>

</androidx.constraintlayout.widget.ConstraintLayout>