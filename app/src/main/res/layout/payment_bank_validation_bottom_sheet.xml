<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_20dp"
    android:paddingBottom="@dimen/_16dp">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:srcCompat="@drawable/ic_arrow_left"
        app:tint="@color/black_000000" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:text="@string/account"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iv_cross"
        app:layout_constraintStart_toEndOf="@+id/iv_back"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_cross"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:srcCompat="@drawable/ic_cross_circle_bg" />

    <com.bukuwarung.payments.widget.PaymentBankAccountView
        android:id="@+id/bank_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_enter_account_number"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/label_bank_account_number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bank_view" />

    <com.bukuwarung.ui_component.component.inputview.BukuSearchView
        android:id="@+id/bsv_search"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        app:editTextHint="@string/account_number_hint"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_verify"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_enter_account_number" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_verify"
        style="@style/ButtonFill"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:fontFamily="@font/roboto_bold"
        android:paddingHorizontal="@dimen/_16dp"
        android:text="@string/label_verify"
        app:layout_constraintBottom_toTopOf="@+id/vw_reference"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/bsv_search" />

    <View
        android:id="@+id/vw_reference"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/_6dp"
        app:layout_constraintTop_toBottomOf="@+id/bsv_search" />

    <com.bukuwarung.payments.widget.BankAccountView
        android:id="@+id/bank_account_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_reference"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error_message"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textColor="@color/red_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bank_account_view"
        tools:text="Rekening tidak ditemukan. Pastikan nomor rekening benar dan coba lagi." />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_error_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="iv_back, tv_title, iv_cross, bank_view, tv_enter_account_number, bsv_search, btn_verify, vw_reference, tv_error_message"/>

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_32dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>