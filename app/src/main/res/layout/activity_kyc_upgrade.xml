<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_top_rounded_corner">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />

            <TextView
                android:id="@+id/title"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/upgrade_advantages"
                android:layout_marginStart="@dimen/_24dp"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/back_btn"
                app:layout_constraintTop_toTopOf="parent"
                />

            <ImageView
                android:id="@+id/notification_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="?android:attr/selectableItemBackground"
                android:src="@drawable/ic_alert"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/notify_highlighter_exp"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:background="@drawable/oval_pink"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/notification_icon"
                app:layout_constraintTop_toTopOf="@id/notification_icon"
                tools:text="2"
                tools:visibility="gone" />


            <TextView
                android:id="@+id/tv_help_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_12dp"
                android:text="@string/help"
                android:textColor="@color/white"
                android:visibility="gone"
                app:drawableTopCompat="@drawable/ic_help"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toTopOf="@+id/cl_bottom_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@id/iv_banner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:src="@drawable/kyc_upgrade_banner_new"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tv_title"
                style="@style/Heading3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/kyc_upgrade_title"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_banner" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bill_payment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title">

                <ImageView
                    android:id="@+id/iv_bill_payment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_payment_out_new_colored" />

                <TextView
                    android:id="@+id/tv_bill_payment"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/bill_payment"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_bill_payment"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_bill_payment"
                    app:layout_constraintTop_toTopOf="@+id/iv_bill_payment" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_set_commission"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/bill_payment">

                <ImageView
                    android:id="@+id/iv_set_commission"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_payment_in_new_colored" />

                <TextView
                    android:id="@+id/tv_save_account_message"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/set_your_commission"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_set_commission"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_set_commission"
                    app:layout_constraintTop_toTopOf="@+id/iv_set_commission" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_accept_payments"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_set_commission">

                <ImageView
                    android:id="@+id/iv_accept_payments"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_qris" />

                <TextView
                    android:id="@+id/tv_accept_payments"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/payment_using_qris"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_accept_payments"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_accept_payments"
                    app:layout_constraintTop_toTopOf="@+id/iv_accept_payments" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_business_capital"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_accept_payments">

                <ImageView
                    android:id="@+id/iv_business_capital"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/business_capital" />

                <TextView
                    android:id="@+id/tv_business_capital"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/business_capital"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_business_capital"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_business_capital"
                    app:layout_constraintTop_toTopOf="@+id/iv_business_capital" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_saving_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_business_capital">

                <ImageView
                    android:id="@+id/iv_saving_account"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/saving_account" />

                <TextView
                    android:id="@+id/tv_saving_account"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/saving_account"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_saving_account"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_saving_account"
                    app:layout_constraintTop_toTopOf="@+id/iv_saving_account" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/tv_learn_more"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_18dp"
                android:drawablePadding="@dimen/_12dp"
                android:text="@string/learn_more"
                android:textColor="@color/blue60"
                android:textSize="@dimen/_14dp"
                app:drawableEndCompat="@drawable/ic_right_arrow"
                app:drawableTint="@color/blue60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_saving_account" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </ScrollView>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginBottom="@dimen/_18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_upgrade"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:text="@string/verifikasi_account_premium"
            app:cornerRadius="@dimen/_8dp"
            android:padding="@dimen/_12dp"
            android:textAllCaps="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>