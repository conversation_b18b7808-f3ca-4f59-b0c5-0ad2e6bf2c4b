<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_layout_payment_methods_detail"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:visibility="gone"
    tools:visibility="visible">

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/label_payment_method"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_saldo_title"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/saldo"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/tv_saldo_value"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_saldo_value"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_4dp"
        android:gravity="end|center_vertical"
        android:textColor="@color/black_80"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/ic_chevron_right"
        app:drawableTint="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_saldo_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="Rp49.000"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_saldo_bonus_title"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/saldo_bonus"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/tv_saldo_bonus_value"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_saldo_title"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_saldo_bonus_value"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_4dp"
        android:gravity="end|center_vertical"
        android:textColor="@color/black_80"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/ic_chevron_right"
        app:drawableTint="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_saldo_bonus_title"
        app:layout_constraintTop_toTopOf="@+id/tv_saldo_bonus_title"
        tools:text="Rp49.000"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>