<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground"
    android:orientation="horizontal"
    android:paddingStart="@dimen/_16dp"
    android:paddingVertical="@dimen/_8dp"
    android:paddingEnd="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cb_category"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:clickable="false"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        android:paddingStart="@dimen/_8dp"
        android:paddingEnd="@dimen/_8dp"
        app:layout_constraintEnd_toStartOf="@id/iv_more" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:src="@drawable/ic_menu_dot_horizontal" />
</LinearLayout>