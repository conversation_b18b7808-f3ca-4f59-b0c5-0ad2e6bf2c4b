<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/pick_image_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pick_image_source"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/rl_gallery_option"/>

    <RelativeLayout
        android:id="@+id/rl_gallery_option"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        tools:ignore="MissingConstraints"
        app:layout_constraintTop_toBottomOf="@id/pick_image_title"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="16dp">

        <ImageView
            android:id="@+id/gallery_image"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginRight="20dp"
            android:src="@drawable/ic_gallery" />

        <TextView
            android:id="@+id/gallery_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/gallery_image"
            android:layout_marginTop="4dp"
            android:layout_toRightOf="@+id/gallery_image"
            android:lineSpacingExtra="11.7sp"
            android:text="@string/gallery"
            android:textColor="#442b2d"
            android:textSize="16.3sp" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_camera_option"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        app:layout_constraintTop_toBottomOf="@+id/rl_gallery_option"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="@dimen/_16dp">

        <ImageView
            android:id="@+id/camera_image"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginRight="20dp"
            android:src="@drawable/ic_camera" />

        <TextView
            android:id="@+id/camera_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/camera_image"
            android:layout_marginTop="4dp"
            android:layout_toRightOf="@+id/camera_image"
            android:lineSpacingExtra="11.7sp"
            android:text="@string/camera"
            android:textColor="#442b2d"
            android:textSize="16.3sp" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>