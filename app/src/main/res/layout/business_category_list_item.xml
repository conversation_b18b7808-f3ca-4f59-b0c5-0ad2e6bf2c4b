<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:orientation="vertical"

    android:layout_height="match_parent">

    <TextView
        android:id="@+id/businessTypeNm"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:paddingTop="8dp"
        android:layout_marginRight="24dp"
        android:clickable="false"
        android:focusable="false"
        android:textSize="12dp"
        android:focusableInTouchMode="false"
        android:textAllCaps="false"
        android:gravity="left"
        app:strokeColor="@color/subheader"
        app:strokeWidth="1dp" />
    <TextView
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_below="@+id/businessTypeNm"
        android:background="@color/white_shade_bg" />
</LinearLayout>
