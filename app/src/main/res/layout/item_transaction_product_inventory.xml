<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingBottom="@dimen/_16dp">

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/black5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/edit_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:src="@drawable/ic_edit_square"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider"
        app:tint="@color/neutral300" />

    <LinearLayout
        android:id="@+id/info_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/quantity_container"
        app:layout_constraintStart_toEndOf="@id/edit_icon"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <TextView
            android:id="@+id/name_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/black80"
            android:textSize="@dimen/text_14sp" />

        <TextView
            android:id="@+id/caption_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:textColor="@color/black_20"
            android:textSize="@dimen/text_12sp" />

        <TextView
            android:id="@+id/price_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:textColor="@color/black40"
            android:textSize="@dimen/text_14sp" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quantity_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <ImageView
            android:id="@+id/decrement_icon"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:src="@drawable/ic_remove_circle"
            app:layout_constraintBottom_toBottomOf="@id/quantity_input"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/quantity_input"
            app:tint="@color/blue60" />

        <EditText
            android:id="@+id/quantity_input"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:maxLength="4"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_12dp"
            android:text="0"
            android:textColor="@color/black80"
            android:textSize="@dimen/text_24sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/decrement_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/increment_icon"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:src="@drawable/ic_add_circle"
            app:layout_constraintBottom_toBottomOf="@id/quantity_input"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/quantity_input"
            app:layout_constraintTop_toTopOf="@id/quantity_input"
            app:tint="@color/blue60" />

        <TextView
            android:id="@+id/stock_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:textAlignment="center"
            android:textColor="@color/black40"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/quantity_input" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_button"
            style="@style/ButtonOutline.Blue1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/product_add"
            app:cornerRadius="@dimen/_6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:strokeWidth="@dimen/_1dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
