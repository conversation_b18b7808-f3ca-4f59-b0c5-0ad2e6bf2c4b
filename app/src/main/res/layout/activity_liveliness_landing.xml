<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/include_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        layout="@layout/toolbar"/>


    <androidx.core.widget.NestedScrollView
        android:id="@+id/scr_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_header"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_14dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_14dp"
                android:text="@string/cara_ambil_selfie_kamu"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_icon1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_14dp"
                android:layout_marginTop="@dimen/_18dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_header"
                app:srcCompat="@drawable/ic_liveliness_icon_1" />

            <TextView
                android:id="@+id/tv_point1"
                style="@style/Body1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_14dp"
                android:text="@string/liveliness_landing_point_1"
                android:textColor="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_icon1"
                app:layout_constraintTop_toBottomOf="@id/tv_header" />


            <View
                android:id="@+id/view_separator"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_15dp"
                android:background="@color/black_0"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_icon1"
                app:layout_constraintTop_toBottomOf="@id/tv_point1" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_icon2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_14dp"
                android:layout_marginTop="@dimen/_14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/view_separator"
                app:srcCompat="@drawable/ic_liveliness_icon_2" />

            <TextView
                android:id="@+id/tv_point2"
                style="@style/Body1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_14dp"
                android:text="@string/liveliness_landing_point_2"
                android:textColor="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_icon2"
                app:layout_constraintTop_toBottomOf="@id/view_separator" />


            <View
                android:id="@+id/view_separator_2"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_15dp"
                android:background="@color/black_0"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tv_point2"
                app:layout_constraintTop_toBottomOf="@id/tv_point2" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_icon3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_14dp"
                android:layout_marginTop="@dimen/_14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/view_separator_2"
                app:srcCompat="@drawable/ic_liveliness_icon_3" />

            <TextView
                android:id="@+id/tv_point3"
                style="@style/Body1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_14dp"
                android:text="@string/liveliness_landing_point_3"
                android:textColor="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_icon3"
                app:layout_constraintTop_toBottomOf="@id/view_separator_2" />

            <View
                android:id="@+id/view_separator_3"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_15dp"
                android:background="@color/black_0"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tv_point3"
                app:layout_constraintTop_toBottomOf="@id/tv_point3" />

            <TextView
                android:id="@+id/tv_contoh"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_14dp"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginEnd="@dimen/_14dp"
                android:text="@string/contoh"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/view_separator_3" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_correct"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_30dp"
                android:layout_marginTop="@dimen/_14dp"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_contoh"
                app:srcCompat="@drawable/liveliness_image_correct" />

            <TextView
                android:id="@+id/tv_correct"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="28dp"
                android:drawableLeft="@drawable/ic_check_circle_green"
                android:drawablePadding="@dimen/_6dp"
                android:text="@string/benar"
                android:textColor="@color/green_80"
                app:layout_constraintEnd_toEndOf="@+id/iv_correct"
                app:layout_constraintStart_toStartOf="@+id/iv_correct"
                app:layout_constraintTop_toBottomOf="@id/iv_correct"
                tools:ignore="UseCompatTextViewDrawableXml" />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_wrong"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@+id/iv_correct"
                app:layout_constraintTop_toTopOf="@id/iv_correct"
                app:srcCompat="@drawable/liveliness_wrong" />

            <TextView
                android:id="@+id/tv_wrong"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="28dp"
                android:drawableLeft="@drawable/ic_error"
                android:drawablePadding="@dimen/_6dp"
                android:text="@string/salah"
                android:textColor="@color/red_80"
                app:layout_constraintEnd_toEndOf="@+id/iv_wrong"
                app:layout_constraintStart_toStartOf="@+id/iv_wrong"
                app:layout_constraintTop_toBottomOf="@id/iv_wrong"
                tools:ignore="UseCompatTextViewDrawableXml" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_next"
        android:layout_width="@dimen/_0dp"
        style="@style/ButtonOutline"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:backgroundTint="@color/new_yellow"
        android:padding="@dimen/_12dp"
        android:text="@string/mulai"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintTop_toBottomOf="@+id/scr_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:rippleColor="@color/black_40" />



</androidx.constraintlayout.widget.ConstraintLayout>