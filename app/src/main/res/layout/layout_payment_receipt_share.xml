<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_8dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_share"
        android:layout_width="@dimen/_48dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_4dp"
        android:backgroundTint="@color/white"
        android:gravity="center"
        android:paddingVertical="@dimen/_8dp"
        app:cornerRadius="@dimen/_4dp"
        app:icon="@drawable/ic_share_20dp"
        app:iconTint="@color/black_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_print"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/black_10"
        app:strokeWidth="@dimen/_1dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_print"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:backgroundTint="@color/white"
        android:gravity="center"
        android:lines="1"
        android:padding="@dimen/_8dp"
        android:text="@string/transaction_print"
        android:textColor="@color/black_60"
        app:cornerRadius="@dimen/_4dp"
        app:icon="@drawable/ic_printer"
        app:iconGravity="textStart"
        app:iconTint="@color/black_60"
        app:iconTintMode="src_in"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_send"
        app:layout_constraintStart_toEndOf="@+id/btn_share"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/black_10"
        app:strokeWidth="@dimen/_1dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_send"
        style="@style/ButtonFill.Blue60"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:lines="1"
        android:padding="@dimen/_8dp"
        android:text="@string/send"
        android:textColor="@color/white"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:icon="@drawable/ic_send"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_print"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>