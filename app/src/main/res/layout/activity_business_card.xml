<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="callback"
            type="com.bukuwarung.activities.card.BusinessCardActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:layout_alignParentTop="true"
            android:background="@color/colorPrimary"
            app:contentInsetLeft="0dp"
            app:contentInsetStart="0dp"
            app:contentInsetStartWithNavigation="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:theme="@style/ToolbarTheme">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/backBtn"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="center"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:layout_alignParentTop="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="24dp"
                    android:layout_toRightOf="@+id/backBtn"
                    android:drawableLeft="@drawable/dot_highlighter"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto"
                    android:gravity="top"
                    android:lineHeight="26dp"
                    android:lineSpacingExtra="8sp"
                    android:maxLines="1"
                    android:text="@string/business_card"
                    android:textColor="@color/white"
                    android:textSize="18dp"
                    android:textStyle="bold" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>

        <ScrollView
            android:id="@+id/form_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:paddingBottom="@dimen/_12dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toTopOf="@id/separator"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/business_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#f1f1f1"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/_16dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingRight="@dimen/_16dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <include layout="@layout/business_card_layout"/>

                </LinearLayout>

                <HorizontalScrollView
                    android:id="@+id/horizontalScrollView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/business_card"
                    android:background="#f1f1f1"
                    android:paddingLeft="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingBottom="@dimen/_8dp"
                    app:layout_constraintTop_toBottomOf="@id/business_card">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="8dp"
                            android:elevation="10dp"
                            app:cardCornerRadius="4dp">

                            <ImageView
                                android:id="@+id/bcard1"
                                android:layout_width="66dp"
                                android:layout_height="36dp"
                                android:src="@drawable/bcard1" />
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="8dp"
                            android:elevation="10dp"
                            app:cardCornerRadius="4dp">

                            <ImageView
                                android:id="@+id/bcard2"
                                android:layout_width="66dp"
                                android:layout_height="36dp"
                                android:src="@drawable/bcard2" />
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="8dp"
                            android:elevation="10dp"
                            app:cardCornerRadius="4dp">

                            <ImageView
                                android:id="@+id/bcard3"
                                android:layout_width="66dp"
                                android:layout_height="36dp"
                                android:src="@drawable/bcard3" />
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="8dp"
                            android:elevation="10dp"
                            app:cardCornerRadius="4dp">

                            <ImageView
                                android:id="@+id/bcard4"
                                android:layout_width="66dp"
                                android:layout_height="36dp"
                                android:src="@drawable/bcard4" />
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="8dp"
                            android:elevation="10dp"
                            app:cardCornerRadius="4dp">

                            <ImageView
                                android:id="@+id/bcard5"
                                android:layout_width="66dp"
                                android:layout_height="36dp"
                                android:src="@drawable/bcard5" />
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>
                </HorizontalScrollView>

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/businessName"
                    style="@style/EditTextBordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:drawableStartCompat="@drawable/ic_business_big"
                    android:hint="@string/empty_business_name"
                    android:inputType="text"
                    app:drawableTint="@color/black_60"
                    app:layout_constraintTop_toBottomOf="@id/horizontalScrollView" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/ownerName"
                    style="@style/EditTextBordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:drawableStartCompat="@drawable/ic_person"
                    android:hint="@string/your_name_optional"
                    android:inputType="text"
                    app:drawableTint="@color/black_60"
                    app:layout_constraintTop_toBottomOf="@id/businessName" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/businessPhone"
                    style="@style/EditTextBordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:drawableStartCompat="@drawable/ic_call_blue"
                    android:hint="@string/hint_phone"
                    android:inputType="phone"
                    app:drawableTint="@color/black_60"
                    app:layout_constraintTop_toBottomOf="@id/ownerName" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/businessTagLine"
                    style="@style/EditTextBordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:hint="@string/business_tag_line"
                    android:inputType="text"
                    app:drawableStartCompat="@drawable/ic_sms"
                    app:drawableTint="@color/black_60"
                    app:layout_constraintTop_toBottomOf="@id/businessPhone" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/businessAddresss"
                    style="@style/EditTextBordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:drawableStartCompat="@drawable/location_icon"
                    android:hint="@string/location_optional"
                    android:inputType="text"
                    app:drawableTint="@color/black_60"
                    app:layout_constraintTop_toBottomOf="@id/businessTagLine" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/businessEmail"
                    style="@style/EditTextBordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:drawableStartCompat="@drawable/ic_email"
                    android:hint="@string/email_optional"
                    android:inputType="textEmailAddress"
                    app:drawableTint="@color/black_60"
                    app:layout_constraintTop_toBottomOf="@id/businessAddresss" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>

        <View
            android:id="@+id/separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/new_divider"
            app:layout_constraintBottom_toTopOf="@id/btn_save" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:layout_weight="1"
            app:backgroundTint="@color/buku_CTA"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/save"
            android:textAllCaps="true"
            android:textColor="@color/black_80"
            android:textSize="16sp"
            android:textStyle="bold"
            app:rippleColor="@color/black_40"
            app:cornerRadius="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_share"
            app:layout_constraintStart_toStartOf="parent"
            app:strokeColor="@color/buku_CTA"
            app:strokeWidth="1dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_share"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/share"
            android:textAllCaps="true"
            android:textColor="@color/black_80"
            android:textSize="16sp"
            android:visibility="gone"
            android:textStyle="bold"
            app:backgroundTint="@color/buku_CTA"
            app:cornerRadius="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_save" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
