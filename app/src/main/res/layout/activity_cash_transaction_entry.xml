<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_parent"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- TODO: will refactor using new style for toolbar-->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="28dp"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="35dp"
                android:layout_marginRight="35dp"
                android:layout_toStartOf="@+id/saveBtn"
                android:layout_toRightOf="@id/closeBtn"
                android:alpha="1"
                android:fontFamily="@font/roboto"
                android:text="@string/new_cash_transaction"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:id="@+id/tvOffline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        android:background="@color/yellow5"
        android:elevation="@dimen/_12dp"
        android:drawablePadding="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="12dp"
        android:paddingEnd="@dimen/_16dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:paddingBottom="12dp"
        android:text="@string/recordings_are_saved_in_offline_mode"
        android:textColor="@color/black_80"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_offline_msg" />

    <ScrollView
        android:id="@+id/form"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:fillViewport="true"
        android:scrollbars="none"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@id/button_divider"
        app:layout_constraintTop_toBottomOf="@id/tvOffline">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.Group
                android:id="@+id/ppob_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:constraint_referenced_ids="rg_trx_type, product_divider, btn_add_product, product_et"/>
            <RadioGroup
                android:id="@+id/rg_trx_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checkedButton="@id/rb_selling"
                android:orientation="horizontal"
                android:padding="@dimen/_16dp"
                android:background="@color/white"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_selling"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:background="@drawable/trx_type_green_bg_selector_light"
                    android:buttonTint="@color/white"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_10dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:text="@string/sales_new"
                    android:textColor="@drawable/trx_type_text_color_selector"
                    android:textStyle="bold" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_expense"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_weight="1"
                    android:background="@drawable/trx_type_red_bg_selector_light"
                    android:buttonTint="@color/white"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_10dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:text="@string/expense_label"
                    android:textColor="@drawable/trx_type_text_color_selector"
                    android:textStyle="bold" />
            </RadioGroup>



            <include
                android:id="@+id/product_header_layout"
                layout="@layout/layout_product_list_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/parent_category"
                tools:visibility="visible" />

            <ScrollView
                android:id="@+id/product_scrollview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:scrollbars="vertical"
                app:layout_constraintHeight_max="180dp"
                app:layout_constraintTop_toBottomOf="@id/product_header_layout"
                tools:background="@color/black_40"
                tools:layout_height="180dp">

                <LinearLayout
                    android:id="@+id/product_container"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />
            </ScrollView>

            <TextView
                android:id="@+id/btn_add_product"
                style="@style/Body3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/bg_custom_icon_button"
                android:drawablePadding="@dimen/_8dp"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingTop="10dp"
                android:paddingEnd="10dp"
                android:paddingBottom="10dp"
                android:text="@string/add_product"
                android:textAllCaps="false"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/text_12sp"
                app:drawableStartCompat="@mipmap/ic_plus_white_24dp"
                app:drawableTint="@color/colorPrimary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/product_scrollview" />

            <TextView
                android:id="@+id/product_et"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/product_sold_label_new"
                app:layout_constraintBottom_toBottomOf="@id/btn_add_product"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/btn_add_product" />

            <View
                android:id="@+id/view_barang_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@+id/product_divider"
                app:layout_constraintTop_toBottomOf="@+id/parent_category" />

            <View
                android:id="@+id/product_divider"
                style="@style/Divider.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/btn_add_product" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/gp_product"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="product_divider,view_status_bg,product_et,btn_add_product, product_scrollview" />
            <!--vertical guideline-->


            <View
                android:id="@+id/nominals_background"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_0dp"
                android:background="@color/blue_background"
                app:layout_constraintBottom_toTopOf="@id/invisible_bg_anchor"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/product_divider"
                app:layout_constraintVertical_bias="0.0" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/nominal_line_guideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.6" />

            <!--1st nominal fields starts-->

            <TextView
                android:id="@+id/txt_main_title"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/total_pemasukan"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/nominals_background" />

            <View
                android:id="@+id/cursor"
                android:layout_width="2.0dip"
                android:layout_height="28.0dip"
                android:layout_gravity="center_vertical"
                android:background="@color/black_60"
                app:layout_constraintBottom_toBottomOf="@id/balance"
                app:layout_constraintEnd_toEndOf="@id/balance"
                app:layout_constraintTop_toTopOf="@id/balance" />

            <TextView
                android:id="@+id/balance"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:autoSizeMaxTextSize="34dp"
                android:autoSizeMinTextSize="22dp"
                android:autoSizeStepGranularity="1dp"
                android:fontFamily="@font/roboto"
                android:hint="0"
                android:maxLength="20"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/nominals_background" />

            <TextView
                android:id="@+id/currency_symbol"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:maxLength="18"
                android:text="Rp"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/balance"
                app:layout_constraintEnd_toStartOf="@id/balance"
                app:layout_constraintTop_toTopOf="@id/balance" />

            <LinearLayout
                android:id="@+id/exprLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/_16dp"
                android:gravity="center"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/nominal_line"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/text_amount_calc"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="6sp"
                    android:textAlignment="center"
                    android:textColor="@color/black_40"
                    android:textSize="15sp"
                    android:textStyle="normal"
                    tools:text="20x10" />
            </LinearLayout>

            <View
                android:id="@+id/nominal_line"
                style="@style/Divider.Horizontal"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/nominal_line_guideline"
                app:layout_constraintTop_toBottomOf="@id/balance" />

            <View
                android:id="@+id/balance_click_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_0dp"
                app:layout_constraintBottom_toBottomOf="@id/nominal_line"
                app:layout_constraintTop_toTopOf="@id/nominals_background"
                tools:visibility="gone" />
            <!--1st nominal fields group-->
            <androidx.constraintlayout.widget.Group
                android:id="@+id/nominal_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="nominal_line, balance, currency_symbol" />

            <!--1st nominal fields ends-->
            <!--2nd nominal fields starts-->

            <TextView
                android:id="@+id/tv_modal_main_title"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:text="Harga Modal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/exprLayout" />

            <View
                android:id="@+id/cursor_modal"
                android:layout_width="2.0dip"
                android:layout_height="28.0dip"
                android:layout_gravity="center_vertical"
                android:background="@color/black_60"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/balance_modal"
                app:layout_constraintEnd_toEndOf="@id/balance_modal"
                app:layout_constraintTop_toTopOf="@id/balance_modal" />

            <TextView
                android:id="@+id/balance_modal"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:autoSizeMaxTextSize="34dp"
                android:autoSizeMinTextSize="24dp"
                android:autoSizeStepGranularity="1dp"
                android:fontFamily="@font/roboto"
                android:hint="0"
                android:maxLength="20"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/exprLayout" />

            <TextView
                android:id="@+id/currency_symbol_modal"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="@dimen/_16dp"
                android:text="Rp"
                android:textColor="@color/black"
                android:textSize="22sp"
                app:layout_constraintBottom_toBottomOf="@id/balance_modal"
                app:layout_constraintEnd_toStartOf="@id/balance_modal"
                app:layout_constraintTop_toBottomOf="@id/exprLayout" />

            <LinearLayout
                android:id="@+id/exprLayout_modal"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/_16dp"
                android:gravity="center"
                android:textColor="@color/black_40"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/modal_line"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/text_amount_calc_modal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="6sp"
                    android:textAlignment="center"
                    android:textColor="#666666"
                    android:textSize="15sp"
                    android:textStyle="normal"
                    tools:text="20x10" />
            </LinearLayout>

            <View
                android:id="@+id/modal_line"
                style="@style/Divider.Horizontal"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/nominal_line_guideline"
                app:layout_constraintTop_toBottomOf="@id/balance_modal" />

            <TextView
                android:id="@+id/txt_modal_indicator"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:text="Keuntungan"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/exprLayout_modal" />

            <TextView
                android:id="@+id/txt_profit_amount"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/exprLayout_modal"
                tools:text="Rp100.000" />

            <RelativeLayout
                android:id="@+id/text_info_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:background="@drawable/round_corner_info_message"
                android:orientation="horizontal"
                android:padding="@dimen/_12dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_modal_indicator">

                <ImageView
                    android:id="@+id/info_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_info_icon" />

                <TextView
                    android:id="@+id/txt_info_message"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginLeft="@dimen/_10dp"
                    android:layout_toRightOf="@+id/info_icon"
                    android:text="@string/modal_info_message_new"
                    android:textColor="@color/black_60" />

            </RelativeLayout>

            <androidx.constraintlayout.widget.Group
                android:id="@+id/modal_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:constraint_referenced_ids="txt_profit_amount, txt_modal_indicator, modal_line, exprLayout_modal,currency_symbol_modal,balance_modal,tv_modal_main_title" />

            <View
                android:id="@+id/modal_click_view"
                android:layout_width="@dimen/_0dp"
                android:layout_height="@dimen/_0dp"
                app:layout_constraintBottom_toBottomOf="@id/modal_line"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/currency_symbol_modal"
                app:layout_constraintTop_toTopOf="@id/currency_symbol_modal"
                tools:visibility="gone" />


            <!--2nd nominal fields ends-->
            <View
                android:id="@+id/view_status_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@+id/rg_status"
                app:layout_constraintTop_toTopOf="@+id/rg_status" />

            <!--status starts-->
            <TextView
                android:id="@+id/tv_status_label"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="Status"
                app:layout_constraintBottom_toBottomOf="@+id/rg_status"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/rg_status" />

            <RadioGroup
                android:id="@+id/rg_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/custom_switch_background"
                android:checkedButton="@id/rb_paid"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_info_layout">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_paid"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/custom_switch_color"
                    android:button="@null"
                    android:paddingStart="12dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="8dp"
                    android:text="Lunas"
                    android:textColor="@color/white" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_unpaid"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/custom_switch_color"
                    android:button="@null"
                    android:paddingStart="12dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="8dp"
                    android:text="Belum Lunas"
                    android:textColor="@color/white" />
            </RadioGroup>

            <androidx.constraintlayout.widget.Group
                android:id="@+id/gp_lunas"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="rg_status, tv_status_label, view_status_bg" />

            <include
                android:id="@+id/contact_search_container_non_edit"
                layout="@layout/contact_transaction_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_12dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/rg_status" />

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/notPaidOffSendCustomerSms"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:button="@drawable/ic_checkbox_not_checked"
                android:fontFamily="@font/roboto"
                android:paddingLeft="@dimen/_10dp"
                android:text="@string/send_sms_title"
                android:textColor="@color/black_60"
                android:textSize="14sp"
                android:textStyle="normal"
                android:theme="@style/CheckBoxStyle"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contact_search_container_non_edit" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/unpaid_contact_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible" />

            <View
                android:id="@+id/invisible_bg_anchor"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/notPaidOffSendCustomerSms" />
            <!--contact ends-->
            <!--TODO: hariom please check-->
            <!--<<<<<<< HEAD-->
            <!--            &lt;!&ndash;product starts&ndash;&gt;-->

            <!--            <View-->
            <!--                android:id="@+id/product_divider"-->
            <!--                style="@style/Divider.Horizontal"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="10dp"-->
            <!--                app:layout_constraintTop_toBottomOf="@id/invisible_bg_anchor" />-->

            <!--            <View-->
            <!--                android:id="@+id/view_barang_bg"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="0dp"-->
            <!--                app:layout_constraintBottom_toTopOf="@+id/other_fields_divider"-->
            <!--                app:layout_constraintTop_toBottomOf="@+id/product_divider" />-->

            <!--            <TextView-->
            <!--                android:id="@+id/product_et"-->
            <!--                style="@style/Body1"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginStart="@dimen/_16dp"-->
            <!--                android:text="@string/product_sold_label"-->
            <!--                app:layout_constraintBottom_toBottomOf="@id/btn_add_product"-->
            <!--                app:layout_constraintStart_toStartOf="parent"-->
            <!--                app:layout_constraintTop_toTopOf="@id/btn_add_product" />-->

            <!--            <ScrollView-->
            <!--                android:id="@+id/product_scrollview"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="@dimen/_16dp"-->
            <!--                android:scrollbars="none"-->
            <!--                app:layout_constraintHeight_max="256dp"-->
            <!--                app:layout_constraintTop_toBottomOf="@id/product_divider">-->

            <!--                <LinearLayout-->
            <!--                    android:id="@+id/product_container"-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginStart="@dimen/_16dp"-->
            <!--                    android:layout_marginEnd="@dimen/_16dp"-->
            <!--                    android:orientation="vertical" />-->
            <!--            </ScrollView>-->

            <!--            <TextView-->
            <!--                android:id="@+id/btn_add_product"-->
            <!--                style="@style/Body3"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="@dimen/_8dp"-->
            <!--                android:layout_marginEnd="@dimen/_16dp"-->
            <!--                android:background="@drawable/bg_custom_icon_button"-->
            <!--                android:drawablePadding="@dimen/_8dp"-->
            <!--                android:gravity="center_vertical"-->
            <!--                android:paddingStart="10dp"-->
            <!--                android:paddingTop="10dp"-->
            <!--                android:paddingEnd="10dp"-->
            <!--                android:paddingBottom="10dp"-->
            <!--                android:text="@string/add_product"-->
            <!--                android:textAllCaps="false"-->
            <!--                android:textColor="@color/colorPrimary"-->
            <!--                android:textSize="@dimen/text_12sp"-->
            <!--                app:drawableStartCompat="@mipmap/ic_plus_white_24dp"-->
            <!--                app:drawableTint="@color/colorPrimary"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintTop_toBottomOf="@id/product_scrollview" />-->

            <!--            <androidx.constraintlayout.widget.Group-->
            <!--                android:id="@+id/product_group"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:visibility="visible"-->
            <!--                app:constraint_referenced_ids="other_fields_divider, product_et, product_scrollview, btn_add_product" />-->
            <!--            &lt;!&ndash;product starts&ndash;&gt;-->

            <!--=======-->
            <!--&gt;>>>>>> develop-->
            <View
                android:id="@+id/other_fields_divider"
                style="@style/Divider.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                app:layout_constraintTop_toBottomOf="@id/invisible_bg_anchor" />

            <View
                android:id="@+id/nomimal_others"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_0dp"
                app:layout_constraintHorizontal_bias="0.0"
                android:background="@color/white"
                app:layout_constraintVertical_bias="0.0"
                app:layout_constraintTop_toBottomOf="@id/other_fields_divider"
                app:layout_constraintBottom_toBottomOf="parent"/>



            <TextView
                android:id="@+id/tv_optional_info"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="Informasi Opsional"
                app:drawableEndCompat="@drawable/ic_chevron_down"
                app:layout_constraintTop_toBottomOf="@id/other_fields_divider" />

            <include
                android:id="@+id/inc_camera_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                layout="@layout/layout_camera_view"
                app:layout_constraintTop_toBottomOf="@id/tv_optional_info"/>

            <EditText
                android:id="@+id/note_et"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:drawableStart="@drawable/ic_notes"
                android:hint="@string/label_note"
                android:inputType="text"
                app:layout_constraintTop_toBottomOf="@id/inc_camera_view" />

            <EditText
                android:id="@+id/category_et"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:clickable="true"
                android:drawableStart="@drawable/ic_category"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:hint="@string/select_category"
                android:inputType="none"
                app:layout_constraintTop_toBottomOf="@id/note_et" />


            <View
                android:id="@+id/vw_category_new"
                android:layout_width="@dimen/_2dp"
                android:layout_height="@dimen/_0dp"
                android:layout_marginStart="@dimen/_20dp"
                app:layout_constraintTop_toTopOf="@id/parent_category"
                app:layout_constraintBottom_toBottomOf="@id/parent_category"
                app:layout_constraintStart_toStartOf="@id/parent_category"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/parent_category"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/rg_trx_type"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_height="wrap_content">
                <EditText
                    android:id="@+id/category_et_new"
                    style="@style/EditTextBordered"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:clickable="true"
                    android:visibility="gone"
                    android:drawableStart="@drawable/ic_category"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:hint="@string/select_category"
                    android:inputType="none"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                   />
                <View
                    android:id="@+id/type_divider"
                    style="@style/Divider.Black5"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:visibility="gone"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginBottom="@dimen/_10dp"
                    app:layout_constraintTop_toBottomOf="@id/category_et_new" />


                <include
                    android:visibility="visible"
                    android:id="@+id/ch_roof_category"
                    layout="@layout/primary_category_item"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>



            <!--paid contact starts-->

            <include
                android:id="@+id/paid_contact_search_container_non_edit"
                layout="@layout/contact_transaction_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/category_et" />

            <!--paid contact ends-->

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/paidOffSendCustomerSms"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:button="@drawable/ic_checkbox_not_checked"
                android:fontFamily="@font/roboto"
                android:paddingLeft="@dimen/_10dp"
                android:text="@string/send_sms_title"
                android:textColor="@color/black_60"
                android:textSize="14sp"
                android:textStyle="normal"
                android:theme="@style/CheckBoxStyle"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/paid_contact_search_container_non_edit" />

            <TextView
                android:id="@+id/tv_date"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:drawablePadding="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/_8dp"
                android:paddingEnd="@dimen/_8dp"
                android:paddingBottom="@dimen/_8dp"
                android:textSize="@dimen/text_14sp"
                app:drawableStartCompat="@drawable/ic_calendar"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/paidOffSendCustomerSms"
                tools:ignore="RtlSymmetry"
                tools:text="11/11/2021" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/other_fields_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="note_et, tv_date, inc_camera_view"
                tools:visibility="visible" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </ScrollView>

    <View
        android:id="@+id/saveOnboarding"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@+id/button_divider" />

    <View
        android:id="@+id/button_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#EEEEEE"
        app:layout_constraintBottom_toTopOf="@id/btn_save" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_bulk_transaksi"
        android:layout_height="wrap_content"
        android:text="@string/btn_add_cash_transaction"
        style="@style/ButtonOutline.White"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:text="@string/save"
        app:layout_constraintStart_toEndOf="@id/btn_bulk_transaksi"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Custom Keyboard  -->
    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/keyboardView"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:layout_height="0dp" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/success_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_success"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/trx_success" />

        <TextView
            android:id="@+id/tv_trx_success"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/transaksi_berhasil_dicatat"
            android:textColor="@color/green_80"
            android:textSize="@dimen/text_24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <FrameLayout
        android:id="@+id/fragment_container_contact"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="gone" />


</androidx.constraintlayout.widget.ConstraintLayout>
