<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_saldo_wallet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_8dp"
        android:padding="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="@id/btn_saldo_topup"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btn_saldo_topup"
        app:srcCompat="@drawable/ic_saldo_icon_home" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:paddingVertical="@dimen/_2dp"
        android:text="@string/zero_amount"
        android:textColor="@color/black_80"
        app:layout_constraintBottom_toTopOf="@id/tv_subheading"
        app:layout_constraintStart_toEndOf="@+id/iv_saldo_wallet"
        app:layout_constraintTop_toTopOf="@id/btn_saldo_topup"
        app:layout_constraintVertical_chainStyle="packed" />

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_5dp"
        android:padding="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_amount"
        app:layout_constraintStart_toEndOf="@id/tv_amount"
        app:layout_constraintTop_toTopOf="@id/tv_amount">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_saldo_info"
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:layout_gravity="center"
            app:layout_constraintBottom_toBottomOf="@id/tv_amount"
            app:layout_constraintStart_toEndOf="@+id/tv_amount"
            app:layout_constraintTop_toTopOf="@id/tv_amount"
            app:srcCompat="@drawable/ic_info_blue" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_subheading"
        style="@style/Label2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/make_credit_and_sales"
        android:textColor="@color/blue_80"
        app:layout_constraintBottom_toBottomOf="@id/btn_saldo_topup"
        app:layout_constraintStart_toStartOf="@+id/tv_amount"
        app:layout_constraintTop_toBottomOf="@+id/tv_amount"
        tools:text="Cashback Rp56.930" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_saldo_topup"
        style="@style/ButtonOutline.Blue1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_20dp"
        android:text="@string/plus_isi_saldo"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_10dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@id/btn_saldo_topup" />

</androidx.constraintlayout.widget.ConstraintLayout>