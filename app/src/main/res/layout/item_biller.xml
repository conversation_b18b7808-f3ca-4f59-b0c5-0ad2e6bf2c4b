<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_8dp"
    app:cardElevation="@dimen/_8dp"
    app:cardCornerRadius="@dimen/_4dp"
    app:strokeWidth="@dimen/_1dp">

    <FrameLayout
        android:id="@+id/bg_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/biller_img"
            android:layout_width="100dp"
            android:layout_height="44dp" />
    </FrameLayout>
</com.google.android.material.card.MaterialCardView>
