<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/verifyTxt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:fontFamily="@font"
            android:text="@string/verifying_number"
            android:textAlignment="center"
            android:textColor="@color/colorPrimary"
            android:textSize="25.0sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleLarge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:indeterminate="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/verifyTxt" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/loadingPanel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="verifyTxt,progress_bar" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/inputLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/_16dp">

            <ImageView
                android:id="@+id/close_img"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="7dp"
                android:layout_marginTop="11dp"
                android:src="@mipmap/close_grey"
                android:tint="@color/black_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/banner_img"
                android:layout_width="wrap_content"
                android:layout_height="140dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginBottom="@dimen/_8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/close_img"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/title"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColor="@color/black_80"
                android:text="@string/ask_to_register_title"
                app:layout_constraintBottom_toTopOf="@id/txt_subtitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/banner_img" />

            <TextView
                android:id="@+id/txt_subtitle"
                style="@style/Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/ask_to_register_subtitle"
                android:textColor="@color/black_60"
                app:layout_constraintBottom_toTopOf="@id/mobileNumberInputLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />

            <com.hbb20.CountryCodePicker
                android:id="@+id/countryPicker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:ccpDialog_allowSearch="false"
                app:ccpDialog_backgroundColor="@color/white"
                app:ccpDialog_textColor="#444444"
                app:ccp_autoDetectCountry="false"
                app:ccp_autoDetectLanguage="true"
                app:ccp_customMasterCountries="@string/countries_prefferred_in_spinner"
                app:ccp_defaultLanguage="INDONESIA"
                app:ccp_defaultNameCode="ID"
                app:ccp_defaultPhoneCode="62"
                app:ccp_excludedCountries="US"
                app:ccp_showFullName="false"
                app:ccp_showNameCode="false"
                app:ccp_showPhoneCode="true"
                app:layout_constraintBottom_toBottomOf="@id/mobileNumberInputLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mobileNumberInputLayout" />

            <Spinner
                android:id="@+id/countrySpinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/countryPicker"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/countryPicker"
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/mobileNumberInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:textColorHint="@color/lightBlack"
                android:visibility="visible"
                app:passwordToggleDrawable="@null"
                app:boxCornerRadiusBottomEnd="4dp"
                app:boxCornerRadiusBottomStart="4dp"
                app:boxCornerRadiusTopEnd="4dp"
                app:boxCornerRadiusTopStart="4dp"
                app:boxStrokeColor="@color/light_grey"
                app:layout_constraintBottom_toTopOf="@id/warningText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/countryPicker"
                app:layout_constraintTop_toBottomOf="@id/txt_subtitle">

                <EditText
                    android:id="@+id/phoneET"
                    style="@style/Base.Widget.MaterialComponents.TextInputEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/login_input_mobile"
                    android:inputType="number"
                    android:maxLength="13"
                    android:textColor="@color/black"
                    android:textColorHint="@color/colorPrimary"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/warningText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_warning_login"
                android:drawableStart="@drawable/ic_warning"
                android:drawablePadding="@dimen/_8dp"
                android:paddingStart="@dimen/_8dp"
                android:paddingTop="@dimen/_8dp"
                android:paddingEnd="@dimen/_8dp"
                android:paddingBottom="@dimen/_8dp"
                android:textAlignment="textStart"
                android:textColor="@color/black_60"
                android:visibility="gone"
                app:drawableTint="#FF8383"
                app:layout_constraintBottom_toTopOf="@id/txt_send_code"
                app:layout_constraintTop_toBottomOf="@id/mobileNumberInputLayout"
                tools:text="Maaf, Anda terlalu banyak melakukan percobaan login. Silakan tunggu beberapa saat sebelum mencoba lagi."
                tools:visibility="visible" />

            <TextView
                android:id="@+id/txt_send_code"
                style="body'"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="20dp"
                android:text="@string/send_code_to"
                app:layout_constraintBottom_toTopOf="@id/btn_sms"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/warningText" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_sms"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:layout_weight="1"
                android:backgroundTint="@drawable/login_button_state"
                android:enabled="false"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:padding="12dp"
                android:text="@string/use_sms_otp"
                android:textColor="@drawable/login_text_state"
                android:textSize="14sp"
                app:cornerRadius="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btn_wa"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_send_code"
                app:strokeColor="@drawable/login_button_stroke_state"
                app:strokeWidth="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_wa"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:backgroundTint="@drawable/login_wa_button_state"
                android:enabled="false"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/use_whatsapp_otp"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:cornerRadius="4dp"
                app:icon="@mipmap/ic_whatsapp_white_24dp"
                app:iconGravity="textStart"
                app:iconTint="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/btn_sms"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/btn_sms"
                app:layout_constraintTop_toTopOf="@id/btn_sms" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
