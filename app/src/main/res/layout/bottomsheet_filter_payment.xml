<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_8dp">

        <TextView
            android:id="@+id/btn_reset"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/reset_label"
            android:textColor="@color/colorPrimary"
            app:layout_constraintEnd_toStartOf="@+id/btn_close"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_close"
            app:tint="@color/black_40" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toTopOf="@id/btn_confirm_filter"
            app:layout_constraintTop_toBottomOf="@id/btn_close">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_heading"
                    style="@style/Heading2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/filter_products" />

                <!-- need to change to view binding -->
                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_all"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/all_transaction_type" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_out"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/pay_label" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_in"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/bill_label" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_saldo_in"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/saldo_in" />

                <View
                    android:id="@+id/type_saldo_in_separator"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_saldo_out"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/saldo_out" />

                <View
                    android:id="@+id/type_saldo_out_separator"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_saldo_redemption"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/filter_history_saldo_bonus" />

                <View
                    android:id="@+id/type_saldo_redemption_separator"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_pulsa"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/pulsa" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_paket_data"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/packet_data" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_listrik"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/token_listrik" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_ewallet"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/topup_ewallet" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_voucher_game"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/voucher_game1" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_bpjs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/bpjs" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_pdam"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/title_pdam" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_multifinance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/title_multifinance" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_internet_dan_tv_cable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/title_internet_dan_tv_cable" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_vehicle_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/title_vehicle_tax" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <TextView
                    android:id="@+id/tv_filter_heading"
                    style="@style/Heading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/filter_status"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/rg_type" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_type_all_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/filter_all" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_success"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/payment_status_completed" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_waiting"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/waiting_label" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_failed"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/fail_label" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_expired"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/expired_label" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/cb_hold"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/in_process" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_5" />

            </LinearLayout>

        </ScrollView>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_confirm_filter"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/confirm_label"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
