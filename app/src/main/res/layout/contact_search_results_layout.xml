<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search_results_fragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_add_new_fav_contact"
            style="@style/SubHeading2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black_5"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_8dp"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Simpan “Hani” sebagai pelanggan favorit baru" />

        <include
            android:id="@+id/include_add_customer"
            layout="@layout/add_contact_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/tv_add_new_fav_contact" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_add_contact"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/yellow_10"
            android:paddingStart="@dimen/_16dp"
            android:paddingTop="@dimen/_8dp"
            android:paddingEnd="@dimen/_16dp"
            android:paddingBottom="@dimen/_8dp"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/include_add_customer">

            <TextView
                android:id="@+id/tv_add_contact_title"
                style="@style/SubHeading1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/add_contact_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_add_contact_desc"
                style="@style/Body3"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/add_contact_description"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_add_contact_title" />

            <ImageView
                android:id="@+id/iv_add_contact_image"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/ic_add_contact_image"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_add_contact_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_contact_picker"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_add_contact"
            tools:listitem="@layout/phonebook_contact_item" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:indeterminate="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>