<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_ppob_title"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/ppob_products_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_ppob_sub_title"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/ppob_products_sub_title"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="@+id/tv_ppob_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_ppob_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_ppob"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_8dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_ppob_sub_title"
        app:spanCount="5"
        tools:itemCount="10"
        tools:listitem="@layout/layout_ppob_item" />

    <View
        android:id="@+id/vw_coachmark"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="@+id/rv_ppob"
        app:layout_constraintTop_toTopOf="@+id/tv_ppob_title" />

</androidx.constraintlayout.widget.ConstraintLayout>