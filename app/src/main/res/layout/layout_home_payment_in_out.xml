<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/top_rounded_rect_129ffd">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_home_blue_top"
        android:nestedScrollingEnabled="false"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        tools:itemCount="1"
        tools:listitem="@layout/home_blue_top_item"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>