<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/_16dp"
            android:paddingBottom="@dimen/_16dp"
            android:background="@color/white"
            android:orientation="vertical"
            android:paddingLeft="24.0dip">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginTop="7.0dip"
                android:text="@string/customer_phone"
                android:textAppearance="@style/tut_heading"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/closeDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_alignParentRight="true"
                android:src="@mipmap/close_white" />
        </RelativeLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/new_divider" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/body_text"
                android:paddingBottom="@dimen/_16dp"
                android:text="@string/payment_reminder_phone"/>

            <EditText
                android:id="@+id/phoneET"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:background="#e8f5ff"
                android:hint="@string/hint_phone"
                android:textColorHint="@color/hint_color"
                android:textSize="15sp"
                android:inputType="number"
                android:focusedByDefault="true"
                android:textColor="#666666"
                android:padding="@dimen/_16dp" />
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:layout_gravity="right"
                android:layout_marginTop="24dp">

                <TextView
                    android:id="@+id/btn_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:text="@string/cancel"
                    android:textStyle="bold"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:textColor="#0091FF"
                    android:lineSpacingExtra="1sp"
                    android:translationY="-0.44sp"
                    android:background="@drawable/button_bg"
                    android:gravity="center"
                    />

                <TextView
                    android:id="@+id/btn_save"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:text="@string/save"
                    android:textStyle="bold"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:paddingLeft="16dp"
                    android:fontFamily="@font/roboto"
                    android:paddingRight="16dp"
                    android:textColor="#FFFFFF"
                    android:lineSpacingExtra="1sp"
                    android:translationY="-0.44sp"
                    android:background="@drawable/button_next_bg"
                    android:gravity="center"
                    />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>