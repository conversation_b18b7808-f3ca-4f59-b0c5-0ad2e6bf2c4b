<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:cardCornerRadius="4dp"
    app:cardElevation="0dp"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="24dp">

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:fontFamily="@font/roboto"
            android:padding="@dimen/_16dp"
            android:text="@string/add_customer_from_contacts"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/closeDialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_8dp"
            android:src="@mipmap/close_white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/txt_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_title"
            app:layout_goneMarginEnd="@dimen/_8dp"
            tools:visibility="visible" />


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_input_name"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:background="@color/white"
            android:hint="@string/customer_name_hint"
            app:passwordToggleDrawable="@null"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="1dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_title">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/customerNm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:imeOptions="actionNext"
                android:inputType="textCapWords"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/hint_color"
                android:textSize="16sp" />

            <requestFocus />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_input_phone"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:background="@color/white"
            android:hint="@string/customer_mobile_hint"
            android:maxLines="1"
            app:passwordToggleDrawable="@null"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="1dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_input_name">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/customerPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/hint_color"
                android:textSize="16sp" />


        </com.google.android.material.textfield.TextInputLayout>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Button.OutlinePrimary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="8dp"
            android:fontFamily="@font/roboto"
            android:text="@string/cancel"
            android:textAllCaps="false"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/btn_save"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_input_phone" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/Button"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:fontFamily="@font/roboto"
            android:text="@string/save"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/btn_cancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_cancel"
            app:layout_constraintTop_toTopOf="@id/btn_cancel" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>