<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">


    <TextView
        android:id="@+id/tv_total_transactions"
        style="@style/BukuTextStyle.Black40.18Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_12dp"
        android:gravity="center"
        tools:text="@string/income_label"
        android:textStyle="bold"
        android:textColor="@color/green_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/subtitle"
        style="@style/BukuTextStyle.Black40.18Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingBottom="@dimen/_12dp"
        android:text="@string/recorded_this_month"
        android:textColor="@color/black40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_total_transactions" />


    <com.github.mikephil.charting.charts.PieChart
        android:id="@+id/chart"
        android:layout_width="@dimen/_250dp"
        android:layout_height="@dimen/_250dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/subtitle" />


    <TextView
        android:id="@+id/tv_chart_description"
        style="@style/Body3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/_12dp"
        android:paddingBottom="@dimen/_12dp"
        android:text="@string/following_is_total_transaction_recorded_by_category"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/chart" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/transactionRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:clipToPadding="false"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_chart_description" />


    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/transactionRecyclerView"
        />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/seeAll"
        style="@style/SubHeading2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_90dp"
        android:background="@android:color/transparent"
        android:gravity="center"
        android:text="@string/lihat_semua"
        android:textAllCaps="false"
        android:textColor="@color/black60"
        android:textSize="@dimen/dimen_14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line" />


</androidx.constraintlayout.widget.ConstraintLayout>
