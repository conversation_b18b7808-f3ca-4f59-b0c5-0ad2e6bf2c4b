<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_category"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/Toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/TootleTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_0dp"
                android:paddingEnd="@dimen/_24dp"
                android:text="@string/select_category_title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_help_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />



            <ImageView
                android:id="@+id/tv_help_icon"
                style="@style/Label2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:paddingHorizontal="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_5dp"
                android:paddingVertical="@dimen/_10dp"
                app:drawableTopCompat="@drawable/ic_baseline_help_outline_white"
                android:src="@drawable/ic_baseline_help_outline_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toBottomOf="@id/tb_category"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_select_category"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_5dp"
                android:foregroundGravity="center"
                tools:listitem="@layout/item_select_category_variant2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#F2F2F2"
                android:layout_margin="@dimen/_10dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintStart_toStartOf="@id/rv_select_category"
                app:layout_constraintTop_toBottomOf="@id/rv_select_category" >

                <TextView
                    android:id="@+id/tv_select_category_info"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:padding="15dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:drawablePadding="@dimen/_5dp"
                    style="@style/Body3"
                    android:gravity="center"
                    android:text="@string/category_info_credit"
                    app:drawableStartCompat="@drawable/ic_info_icon" />



            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_category_header"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/category_header"
                android:layout_marginTop="@dimen/_20dp"
                style="@style/SubHeading1"
                android:visibility="gone"
                android:layout_marginHorizontal="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/cl_info"
                app:layout_constraintStart_toStartOf="@id/rv_select_category"
                app:layout_constraintEnd_toEndOf="@id/rv_select_category"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_category_details"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/tv_category_header"
                app:layout_constraintStart_toStartOf="@id/rv_select_category"
                app:layout_constraintEnd_toEndOf="@id/rv_select_category"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>