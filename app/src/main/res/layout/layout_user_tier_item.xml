<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="@dimen/_12dp">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/_22dp"
        android:layout_height="@dimen/_22dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@drawable/ic_share" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:maxLines="1"
        app:layout_constraintBottom_toTopOf="@id/tv_sub_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="HardcodedText" />

    <TextView
        android:id="@+id/tv_sub_title"
        style="@style/Label2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:ellipsize="none"
        android:singleLine="false"
        android:textColor="#004980"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="HardcodedText" />

</androidx.constraintlayout.widget.ConstraintLayout>