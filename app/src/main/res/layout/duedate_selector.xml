<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@android:color/transparent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/bottomsheet_rounded"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp"
        android:gravity="center_horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/collection_bottomsheet_title"
            android:textStyle="bold"
            android:textSize="18sp"
            android:textColor="@color/black_60"
            android:layout_marginBottom="@dimen/_16dp"/>

        <RelativeLayout
            android:id="@+id/changeDueDateBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/collection_bottomsheet_set"
                android:textSize="16sp"
                android:textColor="@color/black_60"
                android:layout_alignParentStart="true"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_chevron_right"
                android:tint="@color/black_60"
                android:layout_alignParentEnd="true"
                tools:ignore="ContentDescription" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/black_5"
            android:layout_marginBottom="@dimen/_16dp"/>

        <RelativeLayout
            android:id="@+id/deleteDueDateBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/delete"
                android:textSize="16sp"
                android:textColor="@color/black_60"
                android:layout_alignParentStart="true"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_chevron_right"
                android:tint="@color/black_60"
                android:layout_alignParentEnd="true"
                tools:ignore="ContentDescription" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/black_5"
            android:layout_marginBottom="@dimen/_16dp"/>

        <TextView
            android:id="@+id/closeBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/close_tutorial"
            android:textStyle="bold"
            android:textSize="18sp"
            android:textColor="@color/black_40"/>

    </LinearLayout>

</LinearLayout>