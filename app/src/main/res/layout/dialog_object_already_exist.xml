<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/_16dp">

    <ImageView
        android:id="@+id/warningImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_warning"
        android:tint="@color/red_error"
        android:layout_marginBottom="@dimen/_16dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/object_already_exist_msg"
        android:textColor="@color/black"
        android:textAlignment="center"
        android:textSize="16sp" />

</LinearLayout>
