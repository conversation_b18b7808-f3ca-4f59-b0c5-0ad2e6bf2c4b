<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/_20dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_category_name"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="Category Title"
        style="@style/SubHeading1"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_category_details"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="Category Detail"
        style="@style/Body1"
        android:layout_marginTop="@dimen/_10dp"
        android:textColor="@color/black_60"
        app:layout_constraintTop_toBottomOf="@id/tv_category_name"
        app:layout_constraintStart_toStartOf="@id/tv_category_name"
        app:layout_constraintEnd_toEndOf="@id/tv_category_name" />

</androidx.constraintlayout.widget.ConstraintLayout>