<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_save">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent" >

            <RadioGroup
                android:id="@+id/rg_trx_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checkedButton="@id/rb_selling"
                android:orientation="horizontal"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_selling"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:background="@drawable/trx_type_green_bg_selector_light"
                    android:buttonTint="@color/white"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_10dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:text="@string/sales"
                    android:textColor="@drawable/trx_type_text_color_selector"
                    android:textStyle="bold" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rb_expense"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_weight="1"
                    android:background="@drawable/trx_type_red_bg_selector_light"
                    android:buttonTint="@color/white"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_10dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:text="@string/expense_label"
                    android:textColor="@drawable/trx_type_text_color_selector"
                    android:textStyle="bold" />
            </RadioGroup>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_total_penjualan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rg_trx_type"
                app:srcCompat="@drawable/ic_total_penjualan" />

            <TextView
                android:id="@+id/txt_main_title"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/total_penjualan"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@id/iv_total_penjualan"
                app:layout_constraintTop_toTopOf="@id/iv_total_penjualan" />

            <View
                android:id="@+id/cursor"
                android:layout_width="2.0dip"
                android:layout_height="28.0dip"
                android:layout_gravity="center_vertical"
                android:background="@color/black_60"
                app:layout_constraintBottom_toBottomOf="@id/balance"
                app:layout_constraintEnd_toEndOf="@id/balance"
                app:layout_constraintTop_toTopOf="@id/balance" />

            <TextView
                android:id="@+id/balance"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:autoSizeMaxTextSize="34dp"
                android:autoSizeMinTextSize="22dp"
                android:autoSizeStepGranularity="1dp"
                android:fontFamily="@font/roboto"
                android:hint="0"
                android:maxLength="20"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rg_trx_type" />

            <TextView
                android:id="@+id/currency_symbol"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:maxLength="18"
                android:text="Rp"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/balance"
                app:layout_constraintEnd_toStartOf="@id/balance"
                app:layout_constraintTop_toTopOf="@id/balance" />

            <LinearLayout
                android:id="@+id/exprLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/_16dp"
                android:gravity="center"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/nominal_line"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/text_amount_calc"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="6sp"
                    android:textAlignment="center"
                    android:textColor="@color/black_40"
                    android:textSize="15sp"
                    android:textStyle="normal"
                    tools:text="20x10" />
            </LinearLayout>

            <View
                android:id="@+id/nominal_line"
                style="@style/Divider.Horizontal"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/balance" />

            <View
                android:id="@+id/vw_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_2dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_4dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_4dp"
                android:background="@color/black_10"
                app:layout_constraintTop_toBottomOf="@id/exprLayout" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_harga_pokok"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_divider"
                app:srcCompat="@drawable/ic_harga_model" />


            <TextView
                android:id="@+id/tv_modal_main_title"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/harga_pokok_modal"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@id/iv_harga_pokok"
                app:layout_constraintTop_toTopOf="@id/iv_harga_pokok" />

            <View
                android:id="@+id/cursor_modal"
                android:layout_width="2.0dip"
                android:layout_height="28.0dip"
                android:layout_gravity="center_vertical"
                android:background="@color/black_60"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/balance_modal"
                app:layout_constraintEnd_toEndOf="@id/balance_modal"
                app:layout_constraintTop_toTopOf="@id/balance_modal" />

            <TextView
                android:id="@+id/balance_modal"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:autoSizeMaxTextSize="34dp"
                android:autoSizeMinTextSize="24dp"
                android:autoSizeStepGranularity="1dp"
                android:fontFamily="@font/roboto"
                android:hint="0"
                android:maxLength="20"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/exprLayout" />

            <TextView
                android:id="@+id/currency_symbol_modal"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="@dimen/_16dp"
                android:text="Rp"
                android:textColor="@color/black"
                android:textSize="22sp"
                app:layout_constraintBottom_toBottomOf="@id/balance_modal"
                app:layout_constraintEnd_toStartOf="@id/balance_modal"
                app:layout_constraintTop_toBottomOf="@id/exprLayout" />

            <LinearLayout
                android:id="@+id/exprLayout_modal"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/_16dp"
                android:gravity="center"
                android:textColor="@color/black_40"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/modal_line"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/text_amount_calc_modal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="6sp"
                    android:textAlignment="center"
                    android:textColor="#666666"
                    android:textSize="15sp"
                    android:textStyle="normal"
                    tools:text="20x10" />
            </LinearLayout>

            <View
                android:id="@+id/modal_line"
                style="@style/Divider.Horizontal"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/balance_modal" />

            <TextView
                android:id="@+id/tv_bulk_transaksi_info"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:background="@color/yellow_10"
                android:text="@string/bulk_transaksi_i"
                android:padding="@dimen/_8dp"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:layout_marginBottom="@dimen/_8dp"
                android:drawablePadding="@dimen/_8dp"
                app:layout_constraintStart_toStartOf="@id/iv_total_penjualan"
                app:layout_constraintEnd_toEndOf="@id/balance"
                app:layout_constraintTop_toBottomOf="@+id/exprLayout_modal"
                app:drawableStartCompat="@drawable/ic_bulk_info" />

            <RelativeLayout
                android:id="@+id/rl_modal_indicator"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                android:paddingTop="@dimen/_8dp"
                android:paddingBottom="@dimen/_8dp"
                android:layout_marginTop="@dimen/_8dp"
                app:layout_constraintTop_toBottomOf="@id/tv_bulk_transaksi_info" >

                <TextView
                    android:id="@+id/txt_modal_indicator"
                    style="@style/Body2"
                    android:layout_width="72dp"
                    android:layout_height="30dp"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="@dimen/_16dp"
                    android:text="Keuntungan"
                    android:textSize="@dimen/text_16sp" />

                <TextView
                    android:id="@+id/txt_profit_amount"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_alignParentEnd="true"
                    android:textSize="@dimen/text_16sp"
                    tools:text="Rp100.000" />

            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_note"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                app:srcCompat="@drawable/ic_note_icon"
                app:layout_constraintTop_toBottomOf="@+id/rl_modal_indicator"
                app:layout_constraintStart_toStartOf="@id/iv_total_penjualan" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_note"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_8dp"
                app:layout_constraintStart_toEndOf="@+id/iv_note"
                app:layout_constraintEnd_toStartOf="@+id/tv_expand"
                app:layout_constraintBottom_toBottomOf="@+id/iv_note"
                app:layout_constraintTop_toTopOf="@+id/iv_note"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_expand"
                android:layout_width="@dimen/_0dp"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:text="@string/tambah_detai"
                android:drawableEnd="@drawable/ic_dropdown"
                app:layout_constraintEnd_toEndOf="@id/balance"
                app:layout_constraintTop_toTopOf="@id/iv_note"
                app:layout_constraintBottom_toBottomOf="@id/iv_note" />

            <View
                android:id="@+id/vw_bottom_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_8dp"
                app:layout_constraintTop_toBottomOf="@id/tv_expand" />


            <EditText
                android:id="@+id/category_et"
                style="@style/EditTextBordered"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:clickable="true"
                android:drawableStart="@drawable/ic_category"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:hint="@string/select_category"
                android:inputType="none"
                app:layout_constraintTop_toBottomOf="@id/vw_bottom_divider" />


            <include
                android:id="@+id/product_header_layout"
                layout="@layout/layout_product_list_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/category_et"
                tools:visibility="visible" />

            <ScrollView
                android:id="@+id/product_scrollview"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_0dp"
                android:layout_marginTop="@dimen/_8dp"
                android:scrollbars="vertical"
                app:layout_constraintHeight_max="180dp"
                app:layout_constraintTop_toBottomOf="@id/product_header_layout"
                tools:background="@color/black_40"
                tools:layout_height="180dp">

                <LinearLayout
                    android:id="@+id/product_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:orientation="vertical" />
            </ScrollView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_add_product"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_4dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_4dp"
                android:background="@drawable/frame_rounded_black_10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/product_scrollview">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_barang"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_0dp"
                    android:layout_marginStart="@dimen/_8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_barang_terjual" />


                <TextView
                    android:id="@+id/product_et"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_16dp"
                    android:text="@string/product_sold_label"
                    android:textColor="@color/black_80"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/btn_add_product"
                    app:layout_constraintStart_toEndOf="@+id/iv_barang"
                    app:layout_constraintTop_toTopOf="@id/btn_add_product" />

                <TextView
                    android:id="@+id/btn_add_product"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:background="@drawable/bg_custom_icon_button"
                    android:drawablePadding="@dimen/_8dp"
                    android:gravity="center_vertical"
                    android:paddingStart="10dp"
                    android:paddingTop="10dp"
                    android:paddingEnd="10dp"
                    android:paddingBottom="10dp"
                    android:text="@string/add_product_new"
                    android:textAllCaps="false"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/text_12sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/view_barang_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@+id/product_divider"
                app:layout_constraintTop_toBottomOf="@+id/rg_trx_type" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"

        android:text="@string/label_submit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.8"/>

</androidx.constraintlayout.widget.ConstraintLayout>