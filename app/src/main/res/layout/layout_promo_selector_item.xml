<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_12dp">

    <TextView
        android:id="@+id/tv_toggle_name"
        style="@style/Body4"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_24dp"
        app:layout_constraintEnd_toStartOf="@id/sw_toggle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Sembunyikan produk yang tidak memiliki harga jual" />

    <Switch
        android:id="@+id/sw_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:thumbTint="@color/switch_thumb_selector"
        android:trackTint="@color/switch_track_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_toggle_name"
        tools:ignore="UseSwitchCompatOrMaterialXml" />
</androidx.constraintlayout.widget.ConstraintLayout>