<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginStart="@dimen/_16dp"
        app:srcCompat="@drawable/ic_icon_search_new" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_search"
        style="@style/NoLineTextInputLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        app:boxBackgroundColor="@color/white"
        app:endIconDrawable="@drawable/ic_close"
        app:endIconMode="clear_text"
        app:endIconTint="@color/black_40"
        app:hintEnabled="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        app:passwordToggleDrawable="@null">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search"
            style="@style/Body2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/search_contacts"
            android:imeOptions="actionDone"
            android:maxLines="1"
            android:paddingStart="@dimen/_16dp"
            android:paddingTop="@dimen/_14dp"
            android:paddingEnd="@dimen/_16dp"
            android:paddingBottom="@dimen/_14dp"
            android:singleLine="true"
            android:textColor="@color/black_80"
            android:textColorHint="@color/black_40"
            app:drawableTint="@color/black_40"
            tools:text="asd" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_filter"
        style="@style/Button.Outline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/filter"
        android:textAllCaps="false"
        android:textColor="@color/black_40"
        app:icon="@drawable/ic_filter"
        app:iconTint="@color/black_40" />

</LinearLayout>