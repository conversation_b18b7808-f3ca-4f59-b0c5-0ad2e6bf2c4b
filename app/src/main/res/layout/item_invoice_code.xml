<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_invoice_code"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_8dp"
    android:background="@drawable/bg_solid_red5_corner_8dp"
    android:backgroundTint="@color/blue_5">

    <TextView
        android:id="@+id/tv_code_label"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:gravity="center"
        android:text="@string/code_booking"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_code_value"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:gravity="center"
        android:textColor="@color/blue_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_code_label"
        tools:text="07P677UA66SXV50H" />

    <TextView
        android:id="@+id/tv_code_copy"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:background="@drawable/bg_black_outline_8dp"
        android:gravity="center"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_4dp"
        android:text="@string/label_copy"
        android:textColor="@color/black_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_code_value" />

</androidx.constraintlayout.widget.ConstraintLayout>