<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cv_utang_receipt"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="top"
    android:animateLayoutChanges="true"
    app:cardBackgroundColor="@color/white"
    app:contentPaddingBottom="32dp">

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTransactionDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_header_gradient_bg"
            android:paddingStart="20dp"
            android:paddingTop="12dp"
            android:paddingEnd="20dp"
            android:paddingBottom="12dp"
            android:textAlignment="textStart"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Senin, 11 Nov 2011 21:30 WIB" />

        <View
            android:id="@+id/header_line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="@color/black_10"
            android:visibility="gone" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraint_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp">


            <TextView
                android:id="@+id/tvWarungName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/ic_check_circle_green"
                android:drawablePadding="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_80"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Warung Chimay" />

            <TextView
                android:id="@+id/tvWarungPhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_40"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvWarungName"
                tools:text="08129213122" />

            <View
                android:id="@+id/line1"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="12dp"
                android:background="@color/black_10"
                app:layout_constraintTop_toBottomOf="@id/tvWarungPhone" />

            <TextView
                android:id="@+id/tv_cst_name_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:text="@string/name_label"
                android:textColor="@color/black_40"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line1" />

            <TextView
                android:id="@+id/tv_cst_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:text="@string/name_label"
                android:textColor="@color/black_80"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tv_cst_phone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_cst_name_label"
                tools:text="Zulfakar Muhammad" />

            <TextView
                android:id="@+id/tv_cst_phone_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:text="@string/mobile_phone_label"
                android:textColor="@color/black_40"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line1" />

            <TextView
                android:id="@+id/tv_cst_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:text="@string/name_label"
                android:textColor="@color/black_80"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_cst_phone_label"
                tools:text="089671744371" />


            <View
                android:id="@+id/bgNominalBackground"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="12dp"
                android:background="@color/black_33"
                app:layout_constraintBottom_toBottomOf="@id/tvTransactionNominal"
                app:layout_constraintTop_toBottomOf="@id/tv_cst_name" />

            <TextView
                android:id="@+id/tv_transaction_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginTop="8dp"
                android:text="@string/giving_label"
                style="@style/Body2"
                android:textColor="@color/black_80"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/bgNominalBackground" />

            <TextView
                android:id="@+id/tvTransactionNominal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:paddingBottom="@dimen/_8dp"
                android:textColor="@color/black_80"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="@id/tv_transaction_type"
                app:layout_constraintTop_toBottomOf="@id/tv_transaction_type"
                tools:text="Rp.50.000" />


            <ImageView
                android:id="@+id/img_secure"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_8dp"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_secure_transaction_new"
                app:layout_constraintBottom_toBottomOf="@id/bgNominalBackground"
                app:layout_constraintDimensionRatio="W, 1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/bgNominalBackground" />

            <View
                android:id="@+id/note_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="12dp"
                android:background="@color/black_10"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bgNominalBackground" />

            <TextView
                android:id="@+id/tv_label_note"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/roboto"
                android:text="@string/label_note"
                android:textColor="@color/black_40"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/note_line" />

            <TextView
                android:id="@+id/tv_transaction_note"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_80"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_label_note"
                tools:text="Ongkos pekerja minggu pertama" />

            <!--transaction detail-->


            <!--required for the animation-->

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/transaction_detail_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:layout_constraintTop_toBottomOf="@id/tv_transaction_note">

                <View
                    android:id="@+id/note_line_bottom"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@color/black_10"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/transaction_total_label"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:text="@string/total_utang"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/tv_transaction_total"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/note_line_bottom" />

                <TextView
                    android:id="@+id/tv_transaction_total"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/note_line_bottom"
                    tools:text="Rp123.0000" />

                <TextView
                    android:id="@+id/paid_total_label"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:text="@string/total_sudah_dibayar"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/tv_paid_total"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transaction_total_label" />

                <TextView
                    android:id="@+id/tv_paid_total"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transaction_total_label"
                    tools:text="Rp123.0000" />

                <TextView
                    android:id="@+id/remaining_total_label"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:text="@string/kurang_bayar"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/tv_remaining_total"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/paid_total_label" />

                <TextView
                    android:id="@+id/tv_remaining_total"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/paid_total_label"
                    tools:text="Rp123.0000" />

                <View
                    android:id="@+id/detail_line_bottom"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@color/black_10"
                    app:layout_constraintTop_toBottomOf="@id/tv_remaining_total" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</androidx.cardview.widget.CardView>
