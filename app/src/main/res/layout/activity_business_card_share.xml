<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_background_share"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        android:src="@drawable/ic_business_card_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/btn_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:background="@drawable/background_circular_black5"
        android:src="@drawable/ic_cross"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/editBusinessCard"
        style="@style/ButtonOutline.WhiteAll"
        android:text="@string/edit_data"
        android:layout_marginHorizontal="@dimen/_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_share_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintBottom_toTopOf="@+id/iv_star"
        app:layout_constraintEnd_toEndOf="@+id/iv_background_share"
        app:layout_constraintStart_toStartOf="@+id/iv_background_share" />

    <ImageView
        android:id="@+id/iv_star"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/star"
        app:layout_constraintBottom_toBottomOf="@id/iv_background_share"
        app:layout_constraintEnd_toStartOf="@+id/tv_share_text"
        app:layout_constraintTop_toBottomOf="@+id/tv_share_name"
        app:layout_constraintTop_toTopOf="@id/iv_background_share" />

    <TextView
        android:id="@+id/tv_share_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/share_text_business_card"
        android:textColor="@color/white"
        android:textSize="@dimen/text_24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/iv_background_share"
        app:layout_constraintStart_toStartOf="@+id/iv_background_share"
        app:layout_constraintTop_toBottomOf="@+id/iv_star" />

    <ImageView
        android:id="@+id/iv_star2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/star2"
        app:layout_constraintStart_toEndOf="@+id/tv_share_text"
        app:layout_constraintTop_toBottomOf="@+id/tv_share_text" />

    <com.airbnb.lottie.LottieAnimationView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/ll_share"
        app:layout_constraintEnd_toEndOf="@id/card_container"
        app:layout_constraintStart_toStartOf="@id/card_container"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="false"
        app:lottie_rawRes="@raw/card_share" />


    <FrameLayout
        android:id="@+id/card_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_24dp"
        android:layout_marginEnd="@dimen/_24dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_star2">

        <include
            android:id="@+id/business_card_preview_share"
            layout="@layout/business_card_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/iv_star2"
            app:layout_constraintStart_toStartOf="@+id/iv_star"
            app:layout_constraintTop_toBottomOf="@+id/iv_star2"
            tools:visibility="visible" />

        <com.bukuwarung.activities.card.newcard.NewBusinessCardWidget
            android:id="@+id/new_business_card_preview_share"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/iv_star2"
            app:layout_constraintStart_toStartOf="@+id/iv_star"
            app:layout_constraintTop_toBottomOf="@+id/iv_star2"
            tools:visibility="visible" />

    </FrameLayout>


    <LinearLayout
        android:id="@+id/ll_share"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_50dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:weightSum="1"
        app:layout_constraintEnd_toEndOf="@+id/card_container"
        app:layout_constraintStart_toStartOf="@+id/card_container"
        app:layout_constraintTop_toBottomOf="@+id/tv_share_info">

        <include
            android:id="@+id/share_instagram"
            layout="@layout/share_option"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.33" />

        <include
            android:id="@+id/share_whatsapp"
            layout="@layout/share_option"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.33" />

        <include
            android:id="@+id/share_normal"
            layout="@layout/share_option"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.33" />

    </LinearLayout>


    <TextView
        android:id="@+id/tv_share_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_50dp"
        android:text="@string/share_options"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="@+id/card_container"
        app:layout_constraintStart_toStartOf="@+id/card_container"
        app:layout_constraintTop_toBottomOf="@+id/card_container" />


    <LinearLayout
        android:id="@+id/ll_download"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_20dp"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_download_new"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/download_text"
            android:textColor="@color/black_60"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>