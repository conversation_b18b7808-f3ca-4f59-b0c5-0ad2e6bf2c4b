<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:layout_height="wrap_content"
    android:paddingEnd="@dimen/_16dp"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="14dp">

    <androidx.appcompat.widget.AppCompatRadioButton
        android:id="@+id/radioButton"
        android:layout_width="32dp"
        android:layout_height="24dp"
        android:checked="false"
        android:fontFamily="@font/roboto"
        android:text="@string/bal_debit"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:textStyle="normal"
        android:theme="@style/DefaultRadioButtonTheme"
        android:layout_marginBottom="@dimen/_16dp"
        android:clickable="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:textSize="14sp"
        android:textAllCaps="false"
        android:textColor="@color/black"
        tools:text="Restoran / Kafe / Tempat Makan"
        app:layout_constraintLeft_toRightOf="@id/radioButton"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:background="@color/white_shade_bg"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/optionalBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_more_horiz"
        android:tint="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
