<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="@color/colorPrimary"
        android:paddingStart="@dimen/_0dp"
        android:paddingEnd="@dimen/_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:titleTextColor="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="24dp"
                android:layout_toRightOf="@+id/backBtn"
                android:drawableLeft="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="26dp"
                android:lineSpacingExtra="8sp"
                android:maxLines="1"
                android:text="@string/user_profile"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>

    </androidx.appcompat.widget.Toolbar>

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_60dp"
        android:layout_below="@+id/toolbar"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:srcCompat="@drawable/user_profile_background_shape" />

    <ImageView
        android:id="@+id/profilePic"
        android:layout_width="@dimen/_80dp"
        android:layout_height="@dimen/_80dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView"
        app:srcCompat="@drawable/ic_icon_shop" />

    <TextView
        android:id="@+id/txt_owner_name"
        style="@style/Heading1"
        android:layout_width="0dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="@+id/profilePic"
        app:layout_constraintTop_toBottomOf="@+id/profilePic"
        tools:text="Usaha 123" />

    <ImageView
        android:id="@+id/phone_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="@+id/txt_owner_name"
        app:layout_constraintTop_toBottomOf="@+id/txt_owner_name"
        app:srcCompat="@drawable/ic_utang_phone" />

    <TextView
        android:id="@+id/phone_number"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_marginStart="@dimen/_16dp"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintBottom_toBottomOf="@+id/phone_icon"
        app:layout_constraintStart_toEndOf="@+id/phone_icon"
        app:layout_constraintTop_toTopOf="@+id/phone_icon"
        tools:text="987654321" />

    <View
        android:id="@+id/divider_pin_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_8dp"
        android:background="@color/black_0"
        android:layout_marginTop="@dimen/_16dp"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/phone_icon"
        />

    <TextView
        android:id="@+id/tv_pin_title"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/account_settings_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider_pin_top" />

    <TextView
        android:id="@+id/see_profile_btn"
        style="@style/Body1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:drawableEnd="@drawable/ic_right_arrow"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp"
        android:text="@string/edit_profile"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_pin_title" />

    <View
        android:id="@+id/vw_divider_11"
        style="@style/Divider.Black5"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/see_profile_btn" />

    <TextView
        android:id="@+id/bank_account_btn"
        style="@style/Body1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:drawableEnd="@drawable/ic_right_arrow"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp"
        android:text="@string/fragment_bank_account_list_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider_11" />

    <View
        android:id="@+id/divider12"
        style="@style/Divider.Black5"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bank_account_btn" />


</androidx.constraintlayout.widget.ConstraintLayout>
