<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:orientation="horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/dot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=" ● "
                android:textColor="@color/black_20"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/selfRemainderCategoryImage"
                android:layout_width="0dp"
                android:layout_height="16dp"
                app:layout_constraintWidth_percent="0.05"
                android:layout_gravity="center_vertical"
                android:alpha="0.5"
                android:src="@drawable/pembayaran"
                app:tint="@color/black"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/selfRemainderCategoryText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Transaksi"
                android:textColor="@color/black_40"
                android:textSize="16sp"
                android:layout_marginStart="@dimen/_5dp"
                app:layout_constraintBottom_toBottomOf="@+id/selfRemainderCategoryImage"
                app:layout_constraintStart_toEndOf="@+id/selfRemainderCategoryImage"
                app:layout_constraintTop_toTopOf="@+id/selfRemainderCategoryImage" />

            <TextView
                android:id="@+id/selfRemainderTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:fontFamily="@font/roboto"
                android:text="17:00 "
                android:textColor="@color/black_80"
                android:textFontWeight="700"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="@+id/selfRemainderCategoryImage"
                app:layout_constraintTop_toBottomOf="@+id/selfRemainderCategoryText" />


            <TextView
                android:id="@+id/remainderNotes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:text="Buat catatan harian"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintStart_toStartOf="@+id/selfRemainderTime"
                app:layout_constraintTop_toBottomOf="@+id/selfRemainderTime" />


            <LinearLayout
                android:id="@+id/linearLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <Switch
                    android:id="@+id/selfRemainderSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleX="1.1"
                    android:scaleY="1.1"
                    android:layout_marginEnd="@dimen/_26dp"
                    android:checked="true"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/icon_menu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:contentDescription="@null"
                    android:padding="3dp"
                    android:src="@drawable/ic_main_menu_dot"
                    app:tint="@color/black_60" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/dataLayout"
        android:background="@color/section_end" />
</LinearLayout>