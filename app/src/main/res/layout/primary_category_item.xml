<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content">


    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="#EEEEEE"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/editSelectLayout"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/txt_main_title"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_10dp"
            android:text="Kategori transaksi"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_select_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Body2"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/select_category_title"
            android:textColor="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
    <com.google.android.material.chip.Chip
        android:id="@+id/ch_category_select"
        android:layout_width="wrap_content"
        android:visibility="visible"
        android:textColor="@color/black"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_5dp"
        android:focusable="false"
        android:inputType="none"
        android:padding="@dimen/_5dp"
        android:text="@string/kategori_lain"
        app:chipBackgroundColor="@color/black_5"
        app:chipIcon="@drawable/ic_category_blue"
        android:drawableTint="@color/blue_60"
        app:iconStartPadding="@dimen/_3dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/editSelectLayout"
        app:textEndPadding="@dimen/_10dp"
        app:textStartPadding="@dimen/_10dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/hsvChip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5dp"
        android:paddingStart="@dimen/_10dp"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ch_category_select"
        app:layout_constraintTop_toBottomOf="@+id/editSelectLayout">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_select_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foregroundGravity="center"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:listitem="@layout/new_cash_category_item" />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <View
        android:id="@+id/divider_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_5dp"
        android:layout_marginTop="@dimen/_5dp"
        android:background="#EEEEEE"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hsvChip" />

</androidx.constraintlayout.widget.ConstraintLayout>


