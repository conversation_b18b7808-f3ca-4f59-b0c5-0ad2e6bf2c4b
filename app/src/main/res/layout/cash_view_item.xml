<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:foreground="?android:attr/selectableItemBackground"
    android:orientation="vertical"
    >

    <LinearLayout
        android:id="@+id/dataLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center"
        >

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_gravity="center_vertical"

            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:id="@+id/not_paid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:paddingTop="2dp"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_8dp"
                android:paddingBottom="2dp"
                android:text="@string/not_paid"
                android:textColor="#E50707"
                android:textSize="10sp"
                android:visibility="visible"
                android:background="@drawable/rectangle_bottom_right"
                />

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="6sp"
                android:singleLine="true"
                android:paddingEnd="8dp"
                android:text=""
                android:ellipsize="end"
                android:textColor="#222222"
                android:textSize="14sp"
                android:textStyle="normal"
                tools:text="Siomay Bu Ani"
                android:layout_marginStart="16dp"/>

            <TextView
                android:id="@+id/category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="6sp"
                android:maxLines="1"
                android:paddingEnd="8dp"
                android:text=""
                android:textColor="#8D8D8D"
                android:textSize="12sp"
                android:textStyle="normal"
                tools:text="Penjualan"
                android:layout_marginStart="@dimen/_16dp"
                />
        </LinearLayout>

        <TextView
            android:id="@+id/incomeAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="end|center_vertical"
            android:lineSpacingExtra="6sp"
            android:maxLines="2"
            android:text="-"
            android:textColor="@color/in_green"
            android:textSize="16sp"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="14sp"
            android:paddingEnd="18dp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            tools:text="SGD 100,004" />
        <TextView
            android:id="@+id/expenseAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="end|center_vertical"
            android:lineSpacingExtra="6sp"
            android:maxLines="2"
            android:paddingEnd="18dp"
            android:text="-"
            android:textColor="@color/out_red"
            android:textSize="16sp"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="14sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            tools:text="SGD 100,004" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="@dimen/_16dp"
        android:background="@color/new_divider" />
</LinearLayout>