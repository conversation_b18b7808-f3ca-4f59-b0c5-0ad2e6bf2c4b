<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:id="@+id/bizz_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/business_card_canvas"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="@drawable/bcard1"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toTopOf="@id/hr_guideline"
                app:layout_constraintLeft_toLeftOf="@id/business_card_canvas"
                app:layout_constraintTop_toTopOf="@id/business_card_canvas">

                <TextView
                    android:id="@+id/name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto"
                    android:maxLines="1"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:text="test"
                    android:textColor="#ffffff"
                    android:textSize="21sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:id="@+id/captionLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/name"
                    android:layout_alignParentLeft="true"
                    android:layout_centerHorizontal="true"
                    android:paddingLeft="17dp">

                    <TextView
                        android:id="@+id/caption"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/roboto"
                        android:maxLines="1"
                        android:text="test"
                        android:textColor="#ffffff"
                        android:textSize="12sp" />
                </LinearLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginRight="@dimen/_8dp"
                android:src="@drawable/juragan"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/hr_guideline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_begin="84dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="11"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/business_card_canvas"
                app:layout_constraintLeft_toLeftOf="@id/business_card_canvas"
                app:layout_constraintTop_toBottomOf="@id/hr_guideline">

                <LinearLayout
                    android:id="@+id/nameLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/ownerIcon"
                        android:layout_width="@dimen/business_card_icon_size"
                        android:layout_height="@dimen/business_card_icon_size"
                        android:layout_marginLeft="@dimen/business_card_icon_margin"
                        android:layout_marginTop="@dimen/business_card_item_margin_top"
                        android:src="@drawable/ic_person"
                        android:tint="@color/white" />

                    <TextView
                        android:id="@+id/owner"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/business_card_item_margin"
                        android:layout_marginTop="@dimen/card_top_margin"
                        android:layout_toRightOf="@+id/ownerIcon"
                        android:fontFamily="@font/roboto"
                        android:textColor="#ffffff"
                        android:textSize="10sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/phoneLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/phoneIcon"
                        android:layout_width="@dimen/business_card_icon_size"
                        android:layout_height="@dimen/business_card_icon_size"
                        android:layout_below="@+id/ownerIcon"
                        android:layout_marginLeft="@dimen/business_card_icon_margin"
                        android:layout_marginTop="@dimen/card_top_margin"
                        android:fontFamily="@font/roboto"
                        android:src="@mipmap/ic_phone_white_18dp" />

                    <TextView
                        android:id="@+id/phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/owner"
                        android:layout_marginLeft="@dimen/business_card_item_margin"
                        android:layout_marginTop="@dimen/card_top_margin"
                        android:layout_toRightOf="@+id/phoneIcon"
                        android:fontFamily="@font/roboto"
                        android:textColor="#ffffff"
                        android:textSize="10sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/emailLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/emailIcon"
                        android:layout_width="@dimen/business_card_icon_size"
                        android:layout_height="@dimen/business_card_icon_size"
                        android:layout_below="@+id/phoneIcon"
                        android:layout_marginLeft="@dimen/business_card_icon_margin"
                        android:layout_marginTop="@dimen/card_top_margin"
                        android:src="@drawable/ic_email" />

                    <TextView
                        android:id="@+id/email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/phoneIcon"
                        android:layout_marginLeft="@dimen/business_card_item_margin"
                        android:layout_marginTop="@dimen/card_top_margin"
                        android:layout_toRightOf="@+id/emailIcon"
                        android:fontFamily="@font/roboto"
                        android:textColor="#ffffff"
                        android:textSize="10sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/addrLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/addressIcon"
                        android:layout_width="@dimen/business_card_icon_size"
                        android:layout_height="@dimen/business_card_icon_size"
                        android:layout_below="@+id/emailIcon"
                        android:layout_marginLeft="@dimen/business_card_icon_margin"
                        android:layout_marginTop="@dimen/card_top_margin"
                        android:src="@drawable/location_icon" />

                    <TextView
                        android:id="@+id/address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/emailIcon"
                        android:layout_marginLeft="@dimen/business_card_item_margin"
                        android:layout_marginTop="@dimen/card_top_margin"
                        android:layout_toRightOf="@+id/addressIcon"
                        android:fontFamily="@font/roboto"
                        android:textColor="#ffffff"
                        android:textSize="10sp" />
                </LinearLayout>
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>
