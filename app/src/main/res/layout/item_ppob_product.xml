<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginTop="@dimen/_4dp"
    android:layout_marginEnd="@dimen/_16dp"
    android:layout_marginBottom="@dimen/_4dp"
    app:cardCornerRadius="@dimen/_4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/_20dp"
        android:paddingTop="@dimen/_10dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_10dp">

        <TextView
            android:id="@+id/popular_txt"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/_4dp"
            android:text="@string/most_chosen"
            android:textColor="@color/blue_80"
            app:drawableStartCompat="@drawable/ic_star"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/name_txt"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/choose_btn"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/popular_txt" />

        <TextView
            android:id="@+id/price_txt"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/red_100"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/name_txt"
            tools:text="Rp 1.000.000" />

        <TextView
            android:id="@+id/tv_original_price"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_4dp"
            android:background="@drawable/strike_through"
            android:textColor="@color/black_40"
            app:layout_constraintStart_toEndOf="@id/price_txt"
            app:layout_constraintTop_toBottomOf="@id/name_txt"
            tools:text="Rp 1.800.000" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/choose_btn"
            style="@style/ButtonFill.Blue80"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:layout_marginTop="@dimen/_4dp"
            android:paddingVertical="@dimen/_4dp"
            android:text="@string/choose"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/disturbance_txt"
            style="@style/Body3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/disturbance"
            android:textColor="@color/white"
            android:textSize="8sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/choose_btn"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/overlay_disabled"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/bright_foreground_disabled_material_dark"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_promo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        app:srcCompat="@drawable/ic_promo" />
</com.google.android.material.card.MaterialCardView>
