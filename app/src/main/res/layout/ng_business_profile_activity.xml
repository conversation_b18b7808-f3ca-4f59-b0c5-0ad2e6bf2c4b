<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black_5"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
    android:background="@color/black_5"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:layout_width="match_parent"
        android:layout_height="340dp"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/profile_bg"/>

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_centerVertical="true"
                android:layout_marginTop="@dimen/_20dp"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_24dp"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_toEndOf="@+id/back_btn"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/edit_business_profile"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@id/back_btn"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UnusedAttribute" />

            <TextView
                android:id="@+id/deleteBtn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_24dp"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_toEndOf="@+id/back_btn"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/delete"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UnusedAttribute" />


            <include
                layout="@layout/layout_business_profile_percentage"
                android:id="@+id/profile_summary_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/title"
                android:layout_marginTop="@dimen/_30dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <include
        android:id="@+id/layout_basic_info"
        layout="@layout/layout_business_profile_first_step"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <include
        android:id="@+id/layout_operation_info"
        layout="@layout/layout_business_information"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_basic_info" />

    <include
        android:id="@+id/layout_additional_info"
        layout="@layout/layout_business_profile_additional_info"
        android:layout_width="@dimen/_0dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_operation_info" />

</androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/fragment_container_test"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>