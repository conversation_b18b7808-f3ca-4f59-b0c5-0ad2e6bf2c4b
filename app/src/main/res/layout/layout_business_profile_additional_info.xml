<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/selected_tab"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/layout_heading"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        layout="@layout/layout_business_profile_section_heading"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:id="@+id/form_area"
        app:layout_constraintTop_toBottomOf="@id/layout_heading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

    <include
        android:id="@+id/layout_production_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_buyer_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/layout_production_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_item_sold_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/layout_buyer_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_establishment"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/layout_item_sold_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_outlet_count"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/layout_establishment"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <include
        android:id="@+id/layout_emp_count"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        layout="@layout/layout_business_profile_section_item"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_outlet_count"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_incentive_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:textColor="@color/black_60"
        android:text="@string/complete_additional_info"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:paddingBottom="@dimen/dimen_200dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/layout_heading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>