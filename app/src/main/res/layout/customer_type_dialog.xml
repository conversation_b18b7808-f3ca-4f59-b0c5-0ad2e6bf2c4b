<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottomsheet_rounded"
    android:orientation="vertical"
    android:padding="@dimen/_16dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/dialog_title"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:text="@string/pilih_pelanggan_kamu" />

        <ImageView
            android:id="@+id/close_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center_vertical"
            android:padding="@dimen/_8dp"
            android:src="@drawable/close" />

    </FrameLayout>

    <TextView
        style="@style/Label1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bisa_pilih_lebih_dari_satu"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_10sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@color/dot_default"
        app:layout_constraintTop_toBottomOf="@id/cb_selling_online" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cb_reseller"
        android:layout_width="match_parent"
        android:button="@drawable/rounded_checkbox_selector"
        android:paddingStart="6dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/reseller" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        android:background="@color/dot_default"
        app:layout_constraintTop_toBottomOf="@id/cb_selling_online" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cb_dropshipper"
        android:layout_width="match_parent"
        android:button="@drawable/rounded_checkbox_selector"
        android:paddingStart="6dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/dropshipper" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        android:background="@color/dot_default"
        app:layout_constraintTop_toBottomOf="@id/cb_selling_online" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cb_personal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:paddingStart="6dp"
        android:button="@drawable/rounded_checkbox_selector"
        android:text="@string/personal_use" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        android:background="@color/dot_default"
        app:layout_constraintTop_toBottomOf="@id/cb_selling_online" />


</LinearLayout>