<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_marginTop="@dimen/_20dp"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back"
                android:layout_gravity="top"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_marginStart="@dimen/_24dp"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_toEndOf="@+id/back_btn"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:layout_gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/business_operational_information"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@id/back_btn"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UnusedAttribute" />

    </androidx.appcompat.widget.Toolbar>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/_0dp"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/_56dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/white_background_top_radius_16">

      <TextView
          android:id="@+id/tv_alamat_usaha"
          android:layout_width="@dimen/_0dp"
          android:layout_height="wrap_content"
          style="@style/SubHeading1"
          android:text="@string/business_address_hint"
          android:textColor="@color/black_80"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintTop_toTopOf="parent"
          android:layout_margin="@dimen/_16dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_business_category_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:paddingStart="@dimen/_16dp"
            android:paddingEnd="@dimen/_16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_alamat_usaha"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_business_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:text="@string/choose_business_address"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_14sp"
                android:editable="false"
                android:focusableInTouchMode="false"
                android:focusable="false"
                android:drawableTint="@color/black_80"
                android:drawableEnd="@drawable/ic_chevron_right" />

        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/tv_booking_hours"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            style="@style/SubHeading1"
            android:text="@string/business_hour_title"
            android:textColor="@color/black_80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/til_business_category_layout"
            android:layout_margin="@dimen/_16dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_booking_hours"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:paddingStart="@dimen/_16dp"
            android:paddingEnd="@dimen/_16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_booking_hours"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_booking_hours"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:text="@string/choose_opening_hours"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_14sp"
                android:editable="false"
                android:focusableInTouchMode="false"
                android:focusable="false"
                android:drawableTint="@color/black_80"
                android:drawableEnd="@drawable/ic_chevron_right" />

        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/round_corner_blue_rectangle"
            android:text="Informasi alamat dan jam buka akan ditampilkan pada nota usaha kamu"
            app:layout_constraintTop_toBottomOf="@+id/til_booking_hours"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_24dp"
            android:textColor="@color/black_60"
            android:paddingVertical="@dimen/_8dp"
            android:paddingHorizontal="@dimen/_12dp"
            android:drawableLeft="@drawable/ic_info_primary"
            android:drawablePadding="@dimen/_12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="@string/save"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>