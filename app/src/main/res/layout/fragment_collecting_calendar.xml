<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center">

    <LinearLayout
        android:id="@+id/rvContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/fragmentRV"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"/>

    </LinearLayout>

    <include
        android:id="@+id/noCustomer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/collecting_no_customer"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <include
        android:id="@+id/noCalendar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/collecting_no_calendar"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ProgressBar
        android:id="@+id/loadingBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:backgroundTint="@color/colorPrimary"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/bottomContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="@dimen/_16dp"
        android:orientation="vertical"
        android:padding="@dimen/_16dp"
        android:visibility="gone"
        app:cardElevation="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:elevation="@dimen/_16dp"
            android:orientation="vertical"
            android:padding="@dimen/_16dp">

            <TextView
                android:id="@+id/customerCountText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/default_placeholder"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/amountText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="2"
                android:text="@string/default_placeholder"
                android:textColor="#8D8D8D"
                android:textSize="14sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/createNewCollectingBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:backgroundTint="#046BB9"
                android:gravity="center"
                android:padding="12dp"
                android:text="@string/collecting_calendar_create"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                app:cornerRadius="4dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>