<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_disbursal_bank"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:background="@drawable/bg_solid_red5_corner_8dp_stroke_red60"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_12dp"
    android:paddingEnd="@dimen/_10dp"
    android:paddingBottom="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/out_red"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Silakan Ubah Rekening Penerima" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toStartOf="@id/tv_set_bank"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="Biar pembayaran tagihan diteruskan ke rekening kamu" />

    <TextView
        android:id="@+id/tv_set_bank"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_solid_red60_corner_4dp"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_8dp"
        android:text="@string/fragment_add_bank_account_title"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/tv_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_message" />

    <include
        android:id="@+id/include_bank_layout"
        layout="@layout/layout_bank_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_6dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>