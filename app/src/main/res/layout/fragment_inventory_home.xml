<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/stock_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/menuIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingEnd="32dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@mipmap/ic_menu_white_24dp" />

            <TextView
                android:id="@+id/screenTitle"
                style="@style/Heading2"
                android:text="@string/stock_home_page_heading"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/menuIcon"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_refresh_data"
                android:layout_width="25dp"
                android:layout_height="@dimen/_25dp"
                app:srcCompat="@drawable/ic_refresh_data"
                android:layout_marginRight="@dimen/_8dp"
                app:layout_constraintEnd_toStartOf="@id/tv_pos_icon"
                app:layout_constraintTop_toTopOf="@id/tv_pos_icon"
                app:layout_constraintBottom_toBottomOf="@id/tv_pos_icon" />

            <TextView
                android:id="@+id/tv_pos_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:text="@string/cashier_mode"
                android:visibility="visible"
                android:layout_marginEnd="@dimen/_12dp"
                app:drawableTopCompat="@drawable/ic_cashier"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>


    <com.bukuwarung.baseui.DefaultViewPager
        android:id="@+id/inventory_home_viewpager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stock_toolbar">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/inventory_home_tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabSelectedTextColor="@color/blue_80"
            app:tabTextAppearance="@style/SubHeading1"
            app:tabTextColor="#8D8D8D"
            app:tabPaddingStart="0dp"
            app:tabPaddingEnd="0dp"
            app:tabGravity="fill"
            app:tabBackground="@color/white"
            app:tabIndicatorHeight="2dp"
            android:elevation="4dp"
            android:background="@color/white"
            app:tabIndicatorColor="@color/blue_80"
            android:layout_gravity="top" />

    </com.bukuwarung.baseui.DefaultViewPager>


</androidx.constraintlayout.widget.ConstraintLayout>