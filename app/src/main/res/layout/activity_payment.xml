<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:background="@color/grey">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                android:maxLines="1"
                android:paddingStart="@dimen/_0dp"
                android:paddingEnd="@dimen/_16dp"
                android:text="@string/fragment_history_title"
                android:textColor="@color/white"
                android:textSize="@dimen/text_16sp"
                android:textStyle="bold"
                app:autoSizeMaxTextSize="@dimen/text_16sp"
                app:autoSizeMinTextSize="@dimen/text_10sp"
                app:autoSizeStepGranularity="1sp"
                app:autoSizeTextType="uniform" />

            <ImageView
                android:id="@+id/iv_help"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_baseline_help_outline"
                app:tint="@color/white" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/parent_layout_tabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_45dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dimen_28dp"
                android:layout_height="@dimen/dimen_28dp"
                android:layout_marginStart="@dimen/_16dp"
                app:srcCompat="@drawable/ic_icon_search_new" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_search"
                style="@style/NoLineTextInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:boxBackgroundColor="@color/white"
                app:endIconDrawable="@drawable/ic_close"
                app:endIconMode="clear_text"
                app:endIconTint="@color/black_40"
                app:hintEnabled="false"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line"
                app:passwordToggleDrawable="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    style="@style/Body2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="@string/search_by_name_hint_text"
                    android:imeOptions="actionDone"
                    android:maxLines="1"
                    android:paddingStart="@dimen/_16dp"
                    android:paddingTop="@dimen/_14dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="@dimen/_14dp"
                    android:singleLine="true"
                    android:textColor="@color/black_80"
                    android:textColorHint="@color/black_20"
                    app:drawableTint="@color/black_40"
                    tools:text="asd" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButtonToggleGroup
                android:id="@+id/btn_filter_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_filter"
                    style="@style/Button.Text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:paddingTop="@dimen/_0dp"
                    android:paddingBottom="@dimen/_0dp"
                    android:text="@string/filter"
                    android:textAllCaps="false"
                    android:textColor="@color/filter_button_text_color_state"
                    app:icon="@drawable/ic_filter"
                    app:iconTint="@color/filter_button_text_color_state" />
            </com.google.android.material.button.MaterialButtonToggleGroup>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@color/black_5" />


        <HorizontalScrollView
            android:id="@+id/chipScrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/cg_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:selectionRequired="true"
                app:singleSelection="true">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_filter_range"
                    style="@style/CashFilterChipStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:checkable="true"
                    android:text="@string/set_range"
                    app:checkedIconEnabled="false"
                    app:closeIcon="@drawable/ic_cevron_down_white"
                    app:closeIconTint="@color/cash_chip_text_color_state"
                    app:closeIconVisible="true" />

            </com.google.android.material.chip.ChipGroup>
        </HorizontalScrollView>
  </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_history"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/parent_layout_tabs"
        tools:listitem="@layout/item_list_payment_history" />
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="@dimen/_45dp"
        android:layout_height="@dimen/_45dp"
        android:layout_marginTop="@dimen/_90dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <View
        android:id="@+id/vw_shadow_bottom_tab_layout"
        style="@style/ShadowBottom"
        android:layout_height="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/parent_layout_tabs" />

    <TextView
        android:id="@+id/tv_label_empty_state"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:drawablePadding="@dimen/_8dp"
        android:gravity="center"
        android:text="@string/fragment_payment_history_pager_empty_state"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:drawableTint="@color/black_40"
        app:drawableTopCompat="@drawable/ic_history_large"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>