<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="1dp"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline08"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.08" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.75" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline19h"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.19" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline88h"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.88" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline50h"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.50" />

    <ImageView
        android:id="@+id/card_background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:background="@drawable/business_card_border"
        android:scaleType="fitXY"
        android:src="@drawable/business_card_background_one"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <TextView
        android:id="@+id/tv_slogan"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        app:layout_constraintEnd_toEndOf="@id/guideline75"
        app:layout_constraintStart_toStartOf="@id/guideline08"
        app:layout_constraintTop_toBottomOf="@id/guideline50h"
        tools:text="Maju terus pantang mundur" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/card_shop_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="24dp"
        android:maxLines="1"
        app:autoSizeMaxTextSize="16sp"
        app:autoSizeMinTextSize="10sp"
        app:autoSizeStepGranularity="1sp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toStartOf="@id/guideline75"
        app:layout_constraintStart_toStartOf="@id/guideline08"
        app:layout_constraintTop_toTopOf="@id/guideline19h"
        tools:text="Denny’s Coffee &amp; Snack" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/card_shop_owner"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:maxLines="1"
        app:autoSizeMaxTextSize="12sp"
        app:autoSizeMinTextSize="10sp"
        app:autoSizeStepGranularity="1sp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toEndOf="@id/card_shop_title"
        app:layout_constraintStart_toStartOf="@id/card_shop_title"
        app:layout_constraintTop_toBottomOf="@id/card_shop_title"
        tools:text="Denny Kurniawan" />

    <ImageView
        android:id="@+id/card_shop_link_icon_bg"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/card_shop_address"
        app:layout_constraintStart_toStartOf="@id/card_shop_title"
        app:layout_constraintTop_toTopOf="@id/card_shop_address" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/card_shop_link_icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        app:layout_constraintBottom_toBottomOf="@id/card_shop_link_icon_bg"
        app:layout_constraintEnd_toEndOf="@id/card_shop_link_icon_bg"
        app:layout_constraintStart_toStartOf="@id/card_shop_link_icon_bg"
        app:layout_constraintTop_toTopOf="@id/card_shop_link_icon_bg"
        app:srcCompat="@drawable/location_icon" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/card_shop_address"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="4dp"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textSize="@dimen/text_10sp"
        app:autoSizeMaxTextSize="12sp"
        app:autoSizeMinTextSize="10sp"
        app:autoSizeStepGranularity="1sp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="@id/guideline88h"
        app:layout_constraintEnd_toStartOf="@id/guideline75"
        app:layout_constraintStart_toEndOf="@id/card_shop_link_icon_bg"
        tools:text="tokoko.id/dennycoffeeandsnack" />

    <ImageView
        android:id="@+id/card_shop_phone_icon_bg"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/card_shop_phone"
        app:layout_constraintStart_toStartOf="@id/card_shop_title"
        app:layout_constraintTop_toTopOf="@id/card_shop_phone" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/card_shop_phone_icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        app:layout_constraintBottom_toBottomOf="@id/card_shop_phone_icon_bg"
        app:layout_constraintEnd_toEndOf="@id/card_shop_phone_icon_bg"
        app:layout_constraintStart_toEndOf="@id/card_shop_phone_icon_bg"
        app:layout_constraintStart_toStartOf="@id/card_shop_phone_icon_bg"
        app:srcCompat="@drawable/ic_baseline_call_24" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/card_shop_phone"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="4dp"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textSize="@dimen/text_10sp"
        app:autoSizeMaxTextSize="12sp"
        app:autoSizeMinTextSize="10sp"
        app:autoSizeStepGranularity="1sp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toTopOf="@id/card_shop_address"
        app:layout_constraintEnd_toStartOf="@id/guideline75"
        app:layout_constraintStart_toEndOf="@id/card_shop_phone_icon_bg"
        tools:text="+62 878-7779-0968" />

</androidx.constraintlayout.widget.ConstraintLayout>
