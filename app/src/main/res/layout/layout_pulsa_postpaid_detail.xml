<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_phone_number_label"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/mobile_phone_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_phone_number"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/blue_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_phone_number_label"
        tool:text="86618066539" />

    <TextView
        android:id="@+id/tv_customer_name_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/input_customer_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_phone_number_label" />

    <TextView
        android:id="@+id/tv_operator"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/operator"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_customer_name_message" />

    <TextView
        android:id="@+id/tv_operator_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_operator"
        tool:text="XL Prioritas" />


    <TextView
        android:id="@+id/tv_customer_name"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_customer_name_message"
        tool:text="Dea Clarissa S..." />

    <TextView
        android:id="@+id/tv_favourite"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/favourite_contact"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_operator" />

    <TextView
        android:id="@+id/tv_favourite_value"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        app:drawableEndCompat="@drawable/ic_favourite_fill"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_favourite"
        app:layout_constraintTop_toTopOf="@+id/tv_favourite"
        tool:text="Dea Clarissa" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tool:visibility="visible"
        app:constraint_referenced_ids="tv_favourite, tv_favourite_value" />

    <View
        android:id="@+id/divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:layout_marginTop="@dimen/_20dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_favourite" />

    <TextView
        android:id="@+id/tv_total_tagihan"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18dp"
        android:text="@string/total_transaction"
        android:textColor="@color/black_80"
        android:textStyle="bold"
        android:layout_marginBottom="@dimen/_18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <TextView
        android:id="@+id/tv_total_tagihan_value"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18dp"
        tool:text="Rp300.00"
        android:textColor="@color/red_80"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

</androidx.constraintlayout.widget.ConstraintLayout>
