<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".feature.login.createPassword.screen.CreateNewPasswordActivity">

    <com.bukuwarung.ui_component.component.appbar.SimpleAppBar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:backButton="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:titleTextAppearance="@style/Heading2"
        app:title="@string/change_password" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/successLayout"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                >
                <TextView
                    android:id="@+id/tv_old_password_label"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_40dp"
                    android:text="@string/old_password"
                    android:textSize="@dimen/dimen_14sp"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/old_password_layout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5dp"
                    app:boxStrokeWidth="@dimen/_1dp"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:endIconMode="password_toggle"
                    app:layout_constraintStart_toStartOf="@+id/tv_old_password_label"
                    app:layout_constraintTop_toBottomOf="@+id/tv_old_password_label"
                    app:passwordToggleDrawable="@null">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tv_old_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/enter_password_to_change"
                        android:inputType="textPassword"
                        android:layout_marginHorizontal="@dimen/_16dp"
                        android:maxLength="16"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/hint_color"
                        android:textSize="@dimen/text_16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/tv_error"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    android:text="@string/password_does_not_match"
                    android:textColor="@color/red80"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/old_password_layout" />


                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_8dp"
                    android:background="@color/black5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_error"
                    android:layout_marginTop="@dimen/_12dp"
                    android:id="@+id/divider"
                    />


                <TextView
                    android:id="@+id/tv_password_label"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20dp"
                    android:text="@string/new_password"
                    android:textSize="@dimen/dimen_14sp"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/divider" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/password_layout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5dp"
                    app:boxStrokeWidth="@dimen/_1dp"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:endIconMode="password_toggle"
                    app:layout_constraintStart_toStartOf="@+id/tv_password_label"
                    app:layout_constraintTop_toBottomOf="@+id/tv_password_label"
                    app:passwordToggleDrawable="@null">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tv_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/enter_new_password"
                        android:inputType="textPassword"
                        android:digits="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,_!~*@#%&amp;+"
                        android:layout_marginHorizontal="@dimen/_16dp"
                        android:maxLength="16"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/hint_color"
                        android:textSize="@dimen/text_16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/tv_password_error"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    android:text="@string/password_does_not_match"
                    android:textColor="@color/red80"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/password_layout" />


                <TextView
                    android:id="@+id/name_label"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_40dp"
                    android:text="@string/corfirm_new_password_title"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/password_layout" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/user_name_layout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5dp"
                    app:boxStrokeWidth="@dimen/_1dp"
                    app:endIconMode="password_toggle"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:layout_constraintStart_toStartOf="@+id/name_label"
                    app:layout_constraintTop_toBottomOf="@+id/name_label"
                    app:passwordToggleDrawable="@null">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tv_confirm_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_16dp"
                        android:hint="@string/confirm_password_to_change"
                        android:inputType="textPassword"
                        android:digits="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,_!~*@#%&amp;+"
                        android:maxLength="16"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/hint_color"
                        android:textSize="@dimen/text_16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/tv_password_match"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    android:text="@string/both_password_matches"
                    android:textColor="@color/green_60"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/user_name_layout" />

                <TextView
                    android:id="@+id/name_labelq"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_24dp"
                    android:text="@string/password_must_contain_following"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_password_match" />

                <com.bukuwarung.ui_component.component.alert.BukuAlert
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    app:type="info"
                    android:visibility="gone"
                    app:alertText="@string/create_password_info"
                    android:layout_marginBottom="@dimen/_30dp"
                    app:layout_constraintBottom_toTopOf="@+id/btn_enter"
                    app:layout_constraintEnd_toEndOf="@+id/btn_enter"
                    app:layout_constraintStart_toStartOf="@+id/btn_enter" />

                <com.bukuwarung.ui_component.component.button.BukuButton
                    android:id="@+id/btn_enter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_16dp"
                    android:padding="@dimen/_14dp"
                    android:textAllCaps="false"
                    android:textSize="@dimen/text_16sp"
                    app:buttonText="@string/save"
                    app:buttonType="disableGrey"
                    app:layout_constraintVertical_bias="1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/password_criteria_5" />


                <TextView
                    android:id="@+id/password_criteria_1"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:gravity="center"
                    android:drawableStart="@drawable/check_circle_grey"
                    android:drawablePadding="@dimen/dimen_6dp"
                    android:text="@string/password_minimum_8_character"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="@+id/name_labelq"
                    app:layout_constraintTop_toBottomOf="@+id/name_labelq" />

                <TextView
                    android:id="@+id/password_criteria_2"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:gravity="center"
                    android:drawableStart="@drawable/check_circle_grey"
                    android:drawablePadding="@dimen/dimen_6dp"
                    android:text="@string/lowercase"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="@+id/name_labelq"
                    app:layout_constraintTop_toBottomOf="@+id/password_criteria_1" />

                <TextView
                    android:id="@+id/password_criteria_3"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:gravity="center"
                    android:drawableStart="@drawable/check_circle_grey"
                    android:drawablePadding="@dimen/dimen_6dp"
                    android:text="@string/uppercase"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="@+id/name_labelq"
                    app:layout_constraintTop_toBottomOf="@+id/password_criteria_2" />

                <TextView
                    android:id="@+id/password_criteria_4"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:gravity="center"
                    android:drawableStart="@drawable/check_circle_grey"
                    android:drawablePadding="@dimen/dimen_6dp"
                    android:text="@string/number"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="@+id/name_labelq"
                    app:layout_constraintTop_toBottomOf="@+id/password_criteria_3" />

                <TextView
                    android:id="@+id/password_criteria_5"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:gravity="center"
                    android:drawableStart="@drawable/check_circle_grey"
                    android:drawablePadding="@dimen/dimen_6dp"
                    android:text="@string/special_characters_criteria"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/dimen_14sp"
                    app:layout_constraintStart_toStartOf="@+id/name_labelq"
                    app:layout_constraintTop_toBottomOf="@+id/password_criteria_4" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ProgressBar
                android:id="@+id/progressbar"
                android:visibility="visible"
                tools:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/serverErrorLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/errorImage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="70dp"
                    android:layout_marginTop="@dimen/_40dp"
                    android:src="@mipmap/brick_diconnected_icon"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/errorMessage"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_30dp"
                    android:gravity="center"
                    android:text="@string/error_state_message"
                    app:layout_constraintEnd_toEndOf="@+id/errorImage"
                    app:layout_constraintStart_toStartOf="@+id/errorImage"
                    app:layout_constraintTop_toBottomOf="@+id/errorImage" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnRetry"
                    style="@style/ButtonOutline.Blue"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_40dp"
                    android:layout_marginBottom="@dimen/_50dp"
                    android:enabled="true"
                    android:paddingTop="@dimen/_10dp"
                    android:paddingBottom="@dimen/_10dp"
                    android:text="@string/retry"
                    android:textAllCaps="false"
                    android:textSize="@dimen/text_16sp"
                    android:textStyle="bold"
                    app:cornerRadius="@dimen/_2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@+id/errorMessage"
                    app:layout_constraintStart_toStartOf="@+id/errorMessage"
                    app:layout_constraintTop_toBottomOf="@+id/errorMessage" />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>



</androidx.constraintlayout.widget.ConstraintLayout>
