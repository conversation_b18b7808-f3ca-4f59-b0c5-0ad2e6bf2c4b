<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground">

    <View
        android:id="@+id/vw_section_divider"
        style="@style/Divider.Horizontal"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_4dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_heading"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:drawablePadding="@dimen/_10dp"
        android:text="@string/favourite_title"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_star_grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_section_divider"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_bank"
        android:layout_width="56dp"
        android:layout_height="44dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:contentDescription="@null"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_heading"
        app:srcCompat="@drawable/ic_bank" />

    <TextView
        android:id="@+id/tv_bank_title"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_12dp"
        android:layout_marginTop="@dimen/_13dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        app:layout_constraintEnd_toStartOf="@id/iv_menu"
        app:layout_constraintStart_toEndOf="@id/iv_bank"
        app:layout_constraintTop_toBottomOf="@id/tv_heading"
        tools:text="Dea Clarissa Safitri" />

    <TextView
        android:id="@+id/tv_account_number"
        style="@style/Body2"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_12dp"
        android:layout_marginTop="@dimen/dimen_2dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_bank"
        app:layout_constraintTop_toBottomOf="@id/tv_bank_title"
        app:layout_constraintVertical_bias="0"
        tools:text="3883134xxxx" />

    <ImageView
        android:id="@+id/iv_menu"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="@null"
        android:padding="3dp"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="@id/tv_account_number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_bank_title"
        app:srcCompat="@drawable/ic_more_vertical" />

    <View
        android:id="@+id/divider"
        style="@style/Divider.Horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/v_disabled"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:alpha="0.5"
        android:background="@color/black_5"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.ui_component.component.alert.BukuAlert
        android:id="@+id/ba_info"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:visibility="gone"
        app:alertText="Demi keamanan pembayaran, rekening ini kami kunci. Silakan pilih rekening yang lain. Cari tahu lebih lanjut."
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_account_number"
        app:type="error_without_drawable"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
