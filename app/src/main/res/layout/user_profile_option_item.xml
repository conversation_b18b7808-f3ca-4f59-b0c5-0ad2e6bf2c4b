<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_4dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:layout_constraintDimensionRatio="1:1">

    <TextView
        android:id="@+id/tv_user_profile_option_item"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp"
        android:text="@string/edit_profile"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_completion_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/_8dp"
        android:visibility="gone"
        app:srcCompat="@drawable/ic_verified_user_16dp"
        app:layout_constraintStart_toEndOf="@+id/tv_user_profile_option_item"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <ImageView
        android:id="@+id/iv_right_arrow"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        android:paddingLeft="@dimen/_4dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:srcCompat="@drawable/ic_right_arrow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <View
        android:id="@+id/vw_divider_11"
        style="@style/Divider.Black5"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_user_profile_option_item" />



</androidx.constraintlayout.widget.ConstraintLayout>