<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/go_setup_your_printer_header"
        android:textAlignment="viewStart"
        android:textColor="@color/black_80"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/go_setup_your_printer_message"
        android:textAlignment="viewStart"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv1" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnClose"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:fontFamily="@font/roboto"
        android:text="@string/label_close"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintEnd_toStartOf="@id/btnGo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv2"
        app:strokeColor="@color/colorPrimary" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnGo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginStart="4dp"
        android:fontFamily="@font/roboto"
        android:text="@string/setup_printer_command"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:backgroundTint="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnClose"
        app:layout_constraintTop_toBottomOf="@id/tv2" />

</androidx.constraintlayout.widget.ConstraintLayout>