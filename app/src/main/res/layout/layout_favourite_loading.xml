<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            layout="@layout/layout_favourite_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            layout="@layout/layout_favourite_shimmer"
            android:layout_marginTop="@dimen/_25dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            layout="@layout/layout_favourite_shimmer"
            android:layout_marginTop="@dimen/_25dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            layout="@layout/layout_favourite_shimmer"
            android:layout_marginTop="@dimen/_25dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            layout="@layout/layout_favourite_shimmer"
            android:layout_marginTop="@dimen/_25dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </LinearLayout>

</com.facebook.shimmer.ShimmerFrameLayout>