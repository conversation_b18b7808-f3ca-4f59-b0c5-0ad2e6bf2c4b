<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    android:layout_marginTop="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
       >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_heading"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/tv_heading"
                android:layout_margin="@dimen/_16dp"
                android:text="Pengelolaan Stok"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <com.bukuwarung.widget.ViewTextLabel
                android:id="@+id/new_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                app:labelColor="@color/out_red"
                app:labelText="@string/new_label"
                app:labelTextColor="@color/white"
                android:layout_marginStart="@dimen/_15dp"
                app:layout_constraintBottom_toBottomOf="@id/tv_heading"
                app:layout_constraintStart_toEndOf="@id/tv_heading"
                app:layout_constraintTop_toTopOf="@id/tv_heading" />

            <ImageView
                android:id="@+id/iv_dropdown"
                android:layout_width="@dimen/_14dp"
                android:layout_height="@dimen/_14dp"
                android:src="@drawable/ic_chevron_down"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginEnd="@dimen/_24dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_15dp"
            android:id="@+id/cl_card"
            app:layout_constraintTop_toBottomOf="@+id/cl_heading"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            >

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:id="@+id/bestSellingLayout"
                app:cardCornerRadius="8dp"
                >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_solid_white_corner_8dp_stroke_black10"
                    >

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/bestSellingProductLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_15dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_best_selling_product_title"
                            style="@style/Heading2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:text="@string/best_selling_product"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_best_selling_product"
                            style="@style/Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5dp"
                            android:textColor="@color/black20"
                            app:layout_constraintStart_toStartOf="@+id/tv_best_selling_product_title"
                            app:layout_constraintTop_toBottomOf="@+id/tv_best_selling_product_title"
                            tools:text="Indomie Goreng" />


                        <ImageView
                            android:id="@+id/img_product_select"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/ic_right_arrow"
                            android:backgroundTint="@color/black20"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/line"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_1dp"
                        android:layout_marginTop="@dimen/_5dp"
                        android:background="@color/black_10"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/bestSellingProductLayout" />


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/bestSellingCategoryLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_15dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/line">

                        <TextView
                            android:id="@+id/tv_best_selling_category_title"
                            style="@style/Heading2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:text="@string/best_selling_category"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_best_selling_category"
                            style="@style/Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5dp"
                            android:textColor="@color/black20"
                            app:layout_constraintStart_toStartOf="@+id/tv_best_selling_category_title"
                            app:layout_constraintTop_toBottomOf="@+id/tv_best_selling_category_title"
                            tools:text="Mie Instan" />


                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.cardview.widget.CardView>


            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:id="@+id/total_txn_layout"
                app:layout_constraintTop_toBottomOf="@+id/bestSellingLayout"
                app:cardCornerRadius="8dp"
                >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_solid_white_corner_8dp_stroke_black10"
                        android:id="@+id/layout_price_breakup"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        >

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/totalTxnWithProduct"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/_15dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/tv_total_transaction_with_product_title"
                                style="@style/Heading2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:maxLines="2"
                                android:text="@string/total_transaction_with_product"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_total_txn_with_product"
                                style="@style/Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/_5dp"
                                android:textColor="@color/black20"
                                app:layout_constraintStart_toStartOf="@+id/tv_total_transaction_with_product_title"
                                app:layout_constraintTop_toBottomOf="@+id/tv_total_transaction_with_product_title"
                                tools:text="10 Transaksi" />


                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <View
                            android:id="@+id/line2"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_1dp"
                            android:layout_marginTop="@dimen/_5dp"
                            android:background="@color/black_10"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/totalTxnWithProduct" />


                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/layoutAmounts"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/_15dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/line2">

                            <TextView
                                android:id="@+id/tv_product_sales_details_title"
                                style="@style/Heading2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:maxLines="2"
                                android:text="@string/product_sales_details"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_total_Keuntungan_desc"
                                style="@style/Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/_5dp"
                                android:text="(Total Penjualan - Total Modal = Total Keuntungan)"
                                android:textColor="@color/black20"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="@+id/tv_product_sales_details_title"
                                app:layout_constraintTop_toBottomOf="@+id/tv_product_sales_details_title" />


                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/amount_breakup_layout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:layout_constraintTop_toBottomOf="@+id/tv_total_Keuntungan_desc">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/layout_total_penjualan"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingVertical="@dimen/dimen_14dp"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/tv_total_penjualan_desc"
                                        style="@style/Body2"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/total_penjualan"
                                        android:textColor="@color/black60"
                                        android:textSize="@dimen/dimen_14sp"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent" />

                                    <TextView
                                        android:id="@+id/tv_total_penjualan"
                                        style="@style/Body2"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/black60"
                                        android:textSize="@dimen/dimen_14sp"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        tools:text="Rp250.000" />

                                </androidx.constraintlayout.widget.ConstraintLayout>

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/layout_total_modal"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingVertical="@dimen/dimen_14dp"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/layout_total_penjualan">

                                    <TextView
                                        android:id="@+id/tv_total_modal_desc"
                                        style="@style/Body2"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/total_modal"
                                        android:textColor="@color/black60"
                                        android:textSize="@dimen/dimen_14sp"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent" />

                                    <TextView
                                        android:id="@+id/tv_total_modal"
                                        style="@style/Body2"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/black60"
                                        android:textSize="@dimen/dimen_14sp"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        tools:text="Rp250.000" />

                                </androidx.constraintlayout.widget.ConstraintLayout>


                            </androidx.constraintlayout.widget.ConstraintLayout>


                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingVertical="@dimen/dimen_14dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            android:background="@color/neutral_50"
                            android:paddingHorizontal="@dimen/_15dp"
                            app:layout_constraintTop_toBottomOf="@+id/layoutAmounts"
                            android:id="@+id/totalLayout"
                            >

                            <TextView
                                android:id="@+id/tv_total_keuntungan_desc"
                                style="@style/SubHeading1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_profits"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/dimen_14sp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_total_keuntungan"
                                style="@style/SubHeading2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/dimen_14sp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="Rp250.000" />

                            <TextView
                                android:id="@+id/tv_note"
                                style="@style/Body3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black40"
                                android:textSize="@dimen/dimen_14sp"
                                android:layout_marginTop="@dimen/_12dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/tv_total_keuntungan_desc"
                                android:text="@string/amount_breakdown_note"
                                />

                        </androidx.constraintlayout.widget.ConstraintLayout>





                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:layout_marginTop="@dimen/_20dp"
                        android:background="@drawable/bg_solid_white_corner_8dp_stroke_black10"
                        app:layout_constraintTop_toBottomOf="@+id/layout_price_breakup">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/lowStockProductLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/_15dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/tv_low_stock_product_title"
                                style="@style/Heading2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:maxLines="2"
                                android:text="@string/low_stock_product_title"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_low_stock_product"
                                style="@style/Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/_5dp"
                                android:textColor="@color/black20"
                                app:layout_constraintStart_toStartOf="@+id/tv_low_stock_product_title"
                                app:layout_constraintTop_toBottomOf="@+id/tv_low_stock_product_title"
                                tools:text="3 produk" />


                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/ic_right_arrow"
                                android:backgroundTint="@color/black20"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <View
                            android:id="@+id/line3"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_1dp"
                            android:layout_marginTop="@dimen/_5dp"
                            android:background="@color/black_10"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/lowStockProductLayout" />


                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/availableStockProductLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/_15dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/line3">

                            <TextView
                                android:id="@+id/tv_available_stock_product_title"
                                style="@style/Heading2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:maxLines="2"
                                android:text="@string/available_stock_product_title"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_available_stock_product"
                                style="@style/Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/_5dp"
                                android:textColor="@color/black20"
                                app:layout_constraintStart_toStartOf="@+id/tv_available_stock_product_title"
                                app:layout_constraintTop_toBottomOf="@+id/tv_available_stock_product_title"
                                tools:text="100 produk" />

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/ic_right_arrow"
                                android:backgroundTint="@color/black20"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />


                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.cardview.widget.CardView>


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>

