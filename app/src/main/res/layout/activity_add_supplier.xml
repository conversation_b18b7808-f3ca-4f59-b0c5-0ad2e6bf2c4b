<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".activities.supplier.AddSupplierActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:paddingStart="@dimen/_0dp"
        android:paddingEnd="@dimen/_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:titleTextColor="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@color/colorPrimary"
                android:fontFamily="@font/roboto"
                app:srcCompat="@drawable/ic_back" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_toEndOf="@+id/iv_back"
                android:background="@color/colorPrimary"
                android:ellipsize="end"
                android:fontFamily="@font/roboto_bold"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/_3sp"
                android:maxLines="1"
                android:text="@string/add_supplier"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                tools:ignore="UnusedAttribute" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="top"
        android:lineSpacingExtra="@dimen/_4sp"
        android:text="@string/mobile_phone_label"
        android:textColor="@color/black_80"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <com.hbb20.CountryCodePicker
        android:id="@+id/countryPicker"
        style="@style/EditTextBordered"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:padding="@dimen/_4dp"
        app:ccpDialog_allowSearch="false"
        app:ccpDialog_backgroundColor="@color/white"
        app:ccpDialog_textColor="@color/black_80"
        app:ccp_autoDetectCountry="false"
        app:ccp_autoDetectLanguage="true"
        app:ccp_customMasterCountries="@string/countries_prefferred_in_spinner"
        app:ccp_defaultLanguage="INDONESIA"
        app:ccp_defaultNameCode="ID"
        app:ccp_defaultPhoneCode="62"
        app:ccp_excludedCountries="US"
        app:ccp_showArrow="false"
        app:ccp_showFlag="false"
        app:ccp_showFullName="false"
        app:ccp_showNameCode="false"
        app:ccp_showPhoneCode="true"
        app:ccp_textSize="@dimen/dimen_14sp"
        app:layout_constraintBottom_toBottomOf="@+id/et_phone_number"
        app:layout_constraintEnd_toStartOf="@id/et_phone_number"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toTopOf="@+id/et_phone_number" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_phone_number"
        style="@style/EditTextBordered"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:gravity="start"
        android:hint="81310629241"
        android:imeOptions="actionDone"
        android:inputType="phone"
        android:lineSpacingExtra="@dimen/_4sp"
        android:maxLength="18"
        android:textColor="@color/black80"
        android:textColorHint="@color/black10"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="normal"
        app:layout_constraintEnd_toStartOf="@id/btn_check_number"
        app:layout_constraintStart_toEndOf="@id/countryPicker"
        app:layout_constraintTop_toBottomOf="@id/tv_phone_number" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/ib_import_contact"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_12dp"
        android:background="@null"
        app:layout_constraintBottom_toBottomOf="@id/et_phone_number"
        app:layout_constraintEnd_toEndOf="@id/et_phone_number"
        app:layout_constraintTop_toTopOf="@id/et_phone_number" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_check_number"
        style="@style/EditTextBordered"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_0dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@drawable/btn_enabled_blue_bg"
        android:enabled="false"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center"
        android:text="@string/check_number"
        android:textColor="@color/btn_color_bg_blue_state"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintBottom_toBottomOf="@id/et_phone_number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/et_phone_number" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_confirm_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/_2sp"
        android:text="@string/check_registered_number_hint"
        android:textColor="@color/black_60"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toBottomOf="@id/et_phone_number" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_store_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="top"
        android:lineSpacingExtra="@dimen/_4sp"
        android:text="@string/business_card_shop_name"
        android:textColor="@color/black_80"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toBottomOf="@id/tv_confirm_number" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_store_name"
        style="@style/EditTextBordered"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="start"
        android:hint="@string/enter_shop_name"
        android:imeOptions="actionDone"
        android:inputType="text"
        android:lineSpacingExtra="@dimen/_4sp"
        android:textColor="@color/black80"
        android:textColorHint="@color/black10"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toBottomOf="@id/tv_store_name" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_shop_link"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="top"
        android:lineSpacingExtra="@dimen/_4sp"
        android:text="@string/shop_link"
        android:textColor="@color/black_80"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toBottomOf="@id/et_store_name" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_shop_link"
        style="@style/EditTextBordered"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:gravity="start"
        android:imeOptions="actionDone"
        android:lineSpacingExtra="@dimen/_4sp"
        android:textColor="@color/black80"
        android:textSize="@dimen/dimen_14sp"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toBottomOf="@id/tv_shop_link"
        app:prefixText="@string/http_bold_underline"
        app:prefixTextColor="@color/black80">

    </androidx.appcompat.widget.AppCompatEditText>

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/ib_link_verified"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_12dp"
        android:background="@null"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/et_shop_link"
        app:layout_constraintEnd_toEndOf="@id/et_shop_link"
        app:layout_constraintTop_toTopOf="@id/et_shop_link"
        android:src="@drawable/ic_tick_green" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_shop_link_second"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="top"
        android:visibility="gone"
        android:lineSpacingExtra="@dimen/_4sp"
        android:text="@string/shop_link"
        android:textColor="@color/black_80"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toBottomOf="@id/et_shop_link" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_shop_link_second"
        style="@style/EditTextBordered"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:gravity="start"
        android:visibility="gone"
        android:inputType="none"
        android:enabled="false"
        android:imeOptions="actionDone"
        android:lineSpacingExtra="@dimen/_4sp"
        android:textColor="@color/black80"
        android:textSize="@dimen/dimen_14sp"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_phone_number"
        app:layout_constraintTop_toBottomOf="@id/tv_shop_link_second"
        app:prefixText="@string/http_bold_underline"
        app:prefixTextColor="@color/black80">

    </androidx.appcompat.widget.AppCompatEditText>


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_save"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_48dp"
        android:layout_margin="@dimen/_16dp"
        android:background="@drawable/button_enabled_bg"
        android:enabled="false"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/simpan"
        android:textAllCaps="false"
        android:textColor="@color/btn_text_color_state"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.bukuwarung.ui_component.component.alert.BukuAlert
        android:id="@+id/tv_succes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dimen_16dp"
        android:visibility="gone"
        app:alertText="@string/supplier_registered"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_save"
        app:type="success" />

    <com.bukuwarung.ui_component.component.alert.BukuAlert
        android:id="@+id/tv_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dimen_16dp"
        android:visibility="gone"
        app:alertText="@string/supplier_not_registered"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_save"
        app:type="info" />

    <ProgressBar
        android:id="@+id/pb_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>