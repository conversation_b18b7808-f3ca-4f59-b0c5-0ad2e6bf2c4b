<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/rv_bg"
    android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/colorPrimary"
                app:theme="@style/ToolbarTheme">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/toolBarTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@+id/buttonLayout"
                        android:fontFamily="sans-serif-medium"
                        android:maxLines="1"
                        android:paddingRight="16dp"
                        android:text="@string/enter_category_name"
                        android:textColor="#ffffff"
                        android:textSize="16sp"
                        android:textStyle="normal"
                        app:autoSizeMaxTextSize="16sp"
                        app:autoSizeMinTextSize="10sp"
                        app:autoSizeStepGranularity="1sp"
                        app:autoSizeTextType="uniform" />

                    <LinearLayout
                        android:id="@+id/buttonLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/save"
                            android:layout_width="wrap_content"
                            android:layout_height="40dp"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="@dimen/_8dp"
                            android:fontFamily="@font/roboto"
                            android:gravity="center"
                            android:maxWidth="180dp"
                            android:maxLines="1"
                            android:text="@string/save"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14sp"
                            app:autoSizeMaxTextSize="14sp"
                            app:autoSizeMinTextSize="12sp"
                            app:autoSizeStepGranularity="1sp"
                            app:autoSizeTextType="uniform"
                            app:backgroundTint="@color/white"
                            app:cornerRadius="16dp" />
                    </LinearLayout>
                </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/toolbar"
                android:layout_marginBottom="-6dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:background="@color/white"
                android:orientation="vertical"
                android:paddingTop="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#fffbfa"
                    android:orientation="vertical"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="8dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/add_customer_text_margin_top">

                        <ImageView
                            android:id="@+id/nameIcon"
                            android:layout_width="@dimen/add_customer_icon_size"
                            android:layout_height="@dimen/add_customer_icon_size"
                            android:layout_marginTop="@dimen/add_customer_icon_margin_top"
                            android:tint="@color/black"
                            android:src="@drawable/ic_category_tag" />

                        <TextView
                            android:id="@+id/customerTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:layout_marginBottom="@dimen/add_customer_text_margin"
                            android:layout_toRightOf="@+id/nameIcon"
                            android:lineSpacingExtra="3.8sp"
                            android:text="@string/category_name"
                            android:textColor="@color/black"
                            android:textSize="12.2sp" />

                        <EditText
                            android:id="@+id/customerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/customerTitle"
                            android:layout_marginLeft="12dp"
                            android:layout_toRightOf="@+id/nameIcon"
                            android:hint="@string/enter_category_name"
                            android:textColor="#442b2d"
                            android:textSize="16.3sp" />
                    </RelativeLayout>

                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:alpha="0.12"
                android:background="@color/black"
                android:visibility="invisible" />

            <LinearLayout
                android:id="@+id/deleteContainer"
                android:layout_width="fill_parent"
                android:layout_height="50.0dip"
                android:layout_marginTop="16.0dip"
                android:layout_marginBottom="5.0dip"
                android:background="@color/white"
                android:elevation="2.0dip"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/_16dp"
                android:paddingRight="@dimen/_16dp">

                <ImageView
                    android:layout_width="24.0dip"
                    android:layout_height="24.0dip"
                    android:layout_gravity="center_vertical"
                    android:tint="@color/red_error"
                    app:srcCompat="@drawable/delete_red" />

                <TextView
                    android:id="@+id/delete_cst"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="30.0dip"
                    android:layout_marginLeft="30.0dip"
                    android:layout_marginEnd="30.0dip"
                    android:layout_marginRight="30.0dip"
                    android:ellipsize="end"
                    android:text="@string/delete_category_text"
                    android:textColor="@color/red_error"
                    android:textSize="16.0sp" />
            </LinearLayout>
            </LinearLayout>
            </ScrollView>
        </LinearLayout>
</RelativeLayout>
