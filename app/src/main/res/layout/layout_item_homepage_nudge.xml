<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_button_outline_primary">

    <ImageView
        android:id="@+id/iv_position"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_number_one_blue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginVertical="@dimen/_16dp"/>

    <ImageView
        android:id="@+id/iv_account_premium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_badgepremium"
        app:layout_constraintStart_toEndOf="@id/iv_position"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/_16dp"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/iv_account_premium"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/_8dp"
        style="@style/SubHeading1"
        android:layout_marginBottom="@dimen/_16dp"
        android:layout_marginStart="@dimen/_10dp"
        android:text="Verifikasi Akun Premium"/>

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/iv_account_premium"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_8dp"
        style="@style/Body3"
        android:textColor="@color/black_40"
        android:layout_marginStart="@dimen/_10dp"
        android:text="Belum verifikasi"/>

    <ImageView
        android:id="@+id/iv_tick"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/ic_gray_tick"
        android:layout_marginEnd="@dimen/_14dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>