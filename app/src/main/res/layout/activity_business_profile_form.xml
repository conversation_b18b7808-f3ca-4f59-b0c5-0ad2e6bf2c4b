<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_alignParentTop="true"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        app:theme="@style/ToolbarTheme"
        app:titleTextColor="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:fontFamily="@font/roboto"
                android:gravity="center_vertical"
                android:text="@string/edit_profile"
                android:textColor="#ffffff"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/delete_business_btn"
                android:layout_alignParentEnd="true"
                android:layout_alignParentTop="true"
                android:layout_alignParentBottom="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginRight="@dimen/_16dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:text="@string/delete"
                android:gravity="center"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="0dp"
                android:paddingBottom="0dp"
                app:iconSize="16sp"
                app:iconPadding="4dp"
                app:icon="@drawable/delete_red"
                app:iconTint="#046BB9"
                app:iconGravity="textStart"
                app:cornerRadius="4dp"
                android:textStyle="bold"
                android:textSize="14sp"
                app:backgroundTint="@color/white"
                android:textColor="#046BB9" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/profileLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:layout_below="@+id/toolbar"
        android:paddingRight="16dp"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/ownerNameLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/nameIcon"
                android:layout_width="@dimen/form_icon_size"
                android:layout_height="@dimen/form_icon_size"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:tint="@color/colorPrimary"
                android:src="@mipmap/person_black" />

            <LinearLayout
                android:layout_toRightOf="@+id/nameIcon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/ownerInputLayout"
                    android:layout_width="match_parent"
                    android:layout_toRightOf="@+id/nameIcon"
                    android:layout_height="wrap_content"
                    android:hint="@string/business_owner_name_hint"
                    android:textColorHint="@color/black"
                    app:passwordToggleDrawable="@null"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="1dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textColorHint="@color/hint_color"
                        android:id="@+id/ownerName"
                        android:textSize="16sp"
                        />

                    <requestFocus/>

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:id="@+id/ownerNameError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="10dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_warning"
                        android:tint="@color/red_error"
                        android:layout_marginEnd="8dp"/>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/owner_name_error"
                        android:textColor="@color/red_error"/>

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/bizNameLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <ImageView
                android:id="@+id/businessIcon"
                android:layout_width="@dimen/form_icon_size"
                android:layout_height="@dimen/form_icon_size"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:tint="@color/colorPrimary"
                android:src="@drawable/ic_business_big" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_toRightOf="@+id/businessIcon">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/business_name_hint"
                    android:textColorHint="@color/black"
                    app:passwordToggleDrawable="@null"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="1dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:id="@+id/bizName"
                        android:textSize="16sp"
                        />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:id="@+id/businessError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="10dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_warning"
                        android:tint="@color/red_error"
                        android:layout_marginEnd="8dp"/>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/business_name_error"
                        android:textColor="@color/red_error"/>

                </LinearLayout>

            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/bizLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp">

            <ImageView
                android:id="@+id/bizIcon"
                android:layout_width="@dimen/form_icon_size"
                android:layout_height="@dimen/form_icon_size"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:src="@drawable/ic_list_category"
                android:tint="@color/colorPrimary" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="top"
                android:layout_toRightOf="@+id/bizIcon">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/select_business_hint"
                    android:textColorHint="@color/black"
                    app:passwordToggleDrawable="@null"
                    app:boxStrokeColor="@color/black"
                    app:boxStrokeWidth="1dp"
                    app:endIconCheckable="true"
                    android:id="@+id/custom_end_icon"
                    app:endIconDrawable="@drawable/ic_chevron_down_blue"
                    app:endIconMode="custom"
                    app:endIconTint="@color/colorPrimaryDark">

                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:id="@+id/businessTypeET"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:textColorHint="@color/hint_color"
                        android:inputType="text"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:focusable="false"
                        android:layout_height="52dp" />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:id="@+id/businessTypeError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="10dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_warning"
                        android:tint="@color/red_error"
                        android:layout_marginEnd="8dp"/>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/business_type_error"
                        android:textColor="@color/red_error"/>

                </LinearLayout>

            </LinearLayout>
        </RelativeLayout>

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/save"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/save"
        android:textAllCaps="false"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/_16dp"
        android:paddingTop="10dp"
        android:gravity="center"
        android:paddingBottom="10dp"
        android:paddingLeft="25dp"
        android:paddingRight="25dp"
        app:cornerRadius="2dp"
        android:textStyle="bold"
        android:textSize="16sp"
        android:enabled="false"
        style="@style/DefaultMaterialButtonStyle"
        android:textColor="@color/cta_button_text" />

</RelativeLayout>
