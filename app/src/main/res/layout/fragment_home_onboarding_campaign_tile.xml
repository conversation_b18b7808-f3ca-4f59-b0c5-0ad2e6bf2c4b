<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cv_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_16dp"
    android:elevation="@dimen/_2dp"
    android:visibility="gone"
    app:cardCornerRadius="@dimen/_10dp"
    tools:visibility="visible">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/_8dp">

            <TextView
                android:id="@+id/tv_timer"
                style="@style/Body3"
                android:background="@drawable/bg_solid_red5_corner_8dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/_8dp"
                android:paddingHorizontal="@dimen/_8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Berakhir dalam 6 Hari : 07 Jam" />

        <TextView
            android:id="@+id/tv_onboarding_campaign_title"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toStartOf="@id/iv_campaign_image"
            app:layout_constraintHorizontal_bias="0"
            android:layout_marginTop="@dimen/_4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_timer"
            tools:text="Onboarding Campaign Title is yet to start.pls subscribe to our channel." />

        <TextView
            android:id="@+id/tv_onboarding_campaign_body"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/_8dp"
            android:textColor="@color/colorPrimary"
            android:visibility="gone"
            app:drawableEndCompat="@drawable/ic_right_arrow"
            app:drawableTint="@color/colorPrimary"
            app:layout_constraintEnd_toStartOf="@id/iv_campaign_image"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_onboarding_campaign_title"
            tools:text="Yuk, ikuti Misi dan menangkan hadiahnya!" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_join_campaign"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/btn_color_state"
            android:textColor="@color/black_80"
            app:layout_constraintStart_toStartOf="@id/tv_onboarding_campaign_title"
            app:layout_constraintTop_toBottomOf="@id/tv_onboarding_campaign_body"
            tools:text="Ikutan Misi Sekarang" />

        <TextView
            android:id="@+id/tv_terms_and_conditons"
            style="@style/Body3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:text="@string/terms_and_conditions_applies"
            app:layout_constraintBottom_toBottomOf="@id/bt_join_campaign"
            app:layout_constraintEnd_toStartOf="@id/iv_campaign_image"
            app:layout_constraintStart_toEndOf="@id/bt_join_campaign"
            app:layout_constraintTop_toTopOf="@id/bt_join_campaign" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_campaign_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:alpha="0.5"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_onboarding_tile" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>