<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/select_bank"
        app:drawableEndCompat="@drawable/ic_cross_circle_bg" />

    <com.bukuwarung.ui_component.component.inputview.BukuSearchView
        android:id="@+id/bsv_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_16dp"
        app:editTextHint="@string/search_banks"
        app:editTextLeftDrawable="@drawable/ic_search_grey" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_banks"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <com.bukuwarung.ui_component.component.empty_view.BukuEmptyView
        android:id="@+id/bev_empty_state"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_60dp"
        android:visibility="gone"
        app:empty_view_icon="@drawable/ic_reminder_empty"
        app:empty_view_title="@string/banks_not_found"
        app:empty_view_type="CUSTOM" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_60dp"
        android:visibility="gone" />

</LinearLayout>