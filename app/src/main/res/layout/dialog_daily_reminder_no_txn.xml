<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="@dimen/_16dp"
    android:layout_margin="@dimen/_16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="title"
        android:fontFamily="@font/roboto_bold"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        tools:text="Description"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/ok_btn"
        android:backgroundTint="@color/new_yellow"
        app:backgroundTint="@color/new_yellow"
        android:textColor="@color/black_80"
        android:background="@drawable/rectangle_self_reminder"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/record_transaksi"
        android:fontFamily="@font/roboto_bold"
        android:textStyle="bold"
        android:textAllCaps="false"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message"
        />


</androidx.constraintlayout.widget.ConstraintLayout>