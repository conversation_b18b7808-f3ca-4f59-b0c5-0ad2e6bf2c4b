<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_0">

    <View
        android:id="@+id/vw_download"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toTopOf="@id/cl_root"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/sv_home_dashboard" />

    <View
        android:layout_width="match_parent"
        android:layout_height="500dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorPrimary"
                app:layout_constraintTop_toTopOf="parent"
                app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_centerVertical="true"
                android:layout_marginTop="@dimen/_10dp"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_24dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_toEndOf="@+id/back_btn"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_24dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/performa_bisnis"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@id/back_btn"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UnusedAttribute" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipTransactionGroupByDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginTop="@dimen/_5dp"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/may_2021"
                android:textColor="@color/white"
                android:textAppearance="@style/chipText"
                android:layout_marginEnd="@dimen/_5dp"
                app:chipMinTouchTargetSize="0dp"
                app:checkedIconEnabled="false"
                app:chipStrokeColor="@color/white"
                app:chipStrokeWidth="@dimen/_1dp"
                app:chipBackgroundColor="@color/blue_60"
                app:closeIcon="@drawable/ic_cevron_down_white"
                app:closeIconTint="@color/white"
                app:closeIconEnabled="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:closeIconVisible="true" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

<ScrollView
    android:id="@+id/sv_home_dashboard"
    android:layout_width="match_parent"
    android:fillViewport="true"
    app:layout_constraintTop_toBottomOf="@id/toolbar"
    android:layout_height="0dp"
    android:alwaysDrawnWithCache="true"
    app:layout_constraintBottom_toBottomOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:paddingBottom="@dimen/_100dp"
        android:layout_height="wrap_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="350dp"
            android:background="@color/colorPrimary"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/total_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/keuntungan_txt"
            android:textColor="@color/white"
            style="@style/Body1"
            android:layout_marginTop="@dimen/_40dp"
            android:textSize="@dimen/text_28sp"
            android:text="@string/total"/>
        <TextView
            android:id="@+id/keuntungan_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:textColor="@color/white"
            style="@style/Body1"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_40dp"
            android:textSize="@dimen/text_28sp"
            android:text="@string/keuntungan_one"
            android:visibility="gone"
            />
        <TextView
            android:id="@+id/kamu_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/keuntungan_txt"
            android:textColor="@color/white"
            style="@style/Body1"
            android:layout_marginTop="@dimen/_40dp"
            android:textSize="@dimen/text_28sp"
            android:text="@string/kamu"
            />
        <TextView
            android:id="@+id/total_amt_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/keuntungan_txt"
            android:textColor="@color/white"
            style="@style/Body1"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="@dimen/_10dp"
            android:textSize="@dimen/text_28sp"
            android:text="@string/rp5_000_000"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/subHeading_txt"
            android:paddingTop="@dimen/_2dp"
            android:paddingBottom="@dimen/_2dp"
            android:paddingStart="@dimen/_8dp"
            android:paddingEnd="@dimen/_8dp"
            android:layout_marginTop="@dimen/_12dp"
            style="@style/Body3"
            android:background="@drawable/round_corner_light_blue26_rectangle"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:text="@string/lebih_banyak_rp500_000_5_dari_bulan_lalu"
            app:layout_constraintTop_toBottomOf="@+id/total_amt_txt"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <LinearLayout
            android:id="@+id/ll_business_dashboard_head"
            android:layout_width="match_parent"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/subHeading_txt"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_height="wrap_content">
        </LinearLayout>


        <ImageView
            android:id="@+id/imageView"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            android:src="@drawable/clap"
            android:layout_marginTop="@dimen/_40dp"
            app:layout_constraintTop_toBottomOf="@+id/ll_business_dashboard_head"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/tv_bottom_date"
            android:padding="@dimen/_10dp"
            android:gravity="center"
            android:textColor="@color/grey_91"
            android:fontFamily="@font/roboto"
            android:textSize="@dimen/text_12sp"
            android:text="@string/mantap_kamu_sudah_melihat_rekap_bisnis_kamu_untuk_bulan_meiember_2021"
            app:layout_constraintTop_toBottomOf="@+id/imageView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/btn_download"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/download_pdf"
        style="@style/ButtonOutline"
        android:layout_marginTop="@dimen/_30dp"
        app:cornerRadius="@dimen/_20dp"
        android:drawablePadding="-20sp"
        android:layout_marginBottom="@dimen/_20dp"
        android:textColor="@color/black_80"
        android:layout_marginEnd="@dimen/_20dp"
        android:backgroundTint="@color/new_yellow"
        app:drawableLeftCompat="@drawable/download"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/fragment_container_business_dashboard"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>