<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_20dp">

    <TextView
        android:id="@+id/title"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/custom_unit_dialog_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/et_unit_name"
        style="@style/EditTextBordered"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:hint="@string/custom_unit_name_hint"
        android:maxLength="8"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <TextView
        android:id="@+id/max_length"
        style="@style/Body3"
        android:layout_marginTop="3dp"
        android:textColor="@color/black_60"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/custom_unit_name_length"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_unit_name" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnCancel"
        style="@style/Button.OutlinePrimary"
        android:layout_width="@dimen/_0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="4dp"
        android:text="@string/cancel"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="@+id/btnSave"
        app:layout_constraintEnd_toStartOf="@id/btnSave"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btnSave"
        app:layout_constraintVertical_bias="1.0" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnSave"
        style="@style/ButtonFill.Blue"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/save"
        android:textAllCaps="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnCancel"
        app:layout_constraintTop_toBottomOf="@id/max_length" />
</androidx.constraintlayout.widget.ConstraintLayout>