<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_20dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingBottom="@dimen/_38dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_cross"
        app:tint="@color/black_80" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_illustration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_switch_qris_book"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_close" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_40dp"
        android:layout_marginTop="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_40dp"
        android:gravity="center_horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_illustration"
        tools:text="Fitur QRIS hanya tersedia di\nbuku usaha Papera Indah" />

    <TextView
        android:id="@+id/tv_description"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_40dp"
        android:layout_marginTop="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_40dp"
        android:gravity="center_horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="Untuk melanjutkan, silakan pindah ke buku usaha [QRIS Bookname] dan transaksi akan tercatat di sana." />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_switch_book"
        style="@style/Button.OutlinePrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_14dp"
        android:text="@string/switch_business_book"
        android:textAllCaps="false"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_description" />

</androidx.constraintlayout.widget.ConstraintLayout>
