<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_customer_id_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/customer_id_message"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_customer_id"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/blue_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_customer_id_message"
        tool:text="86618066539" />

    <TextView
        android:id="@+id/tv_product_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/nominal_token"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_customer_id" />

    <TextView
        android:id="@+id/tv_product_name"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_product_message"
        tool:text="100.000" />

    <TextView
        android:id="@+id/tv_customer_name_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/input_customer_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_message" />

    <TextView
        android:id="@+id/tv_period"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/period"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_customer_name_message" />

    <TextView
        android:id="@+id/tv_period_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_period"
        tool:text="Agustus 2021" />

    <TextView
        android:id="@+id/tv_total_lembar_tagihan"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/total_billing"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_period" />

    <TextView
        android:id="@+id/tv_total_lembar_tagihan_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_total_lembar_tagihan"
        tool:text="1 Lembar" />

    <TextView
        android:id="@+id/tv_customer_name"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_customer_name_message"
        tool:text="Dea Clarissa S..." />

    <TextView
        android:id="@+id/tv_tarif_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/tarif"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_total_lembar_tagihan" />

    <TextView
        android:id="@+id/tv_tarif"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_tarif_message"
        tool:text="R1/2200VA" />

    <TextView
        android:id="@+id/tv_mobile_number_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/mobile_phone_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_tarif_message" />

    <TextView
        android:id="@+id/tv_mobile_number"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_mobile_number_message"
        tool:text="081275598545" />

    <TextView
        android:id="@+id/tv_favourite"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/favourite_contact"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mobile_number_message" />

    <TextView
        android:id="@+id/tv_favourite_value"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        app:drawableEndCompat="@drawable/ic_favourite_fill"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_favourite"
        app:layout_constraintTop_toTopOf="@+id/tv_favourite"
        tool:text="Dea Clarissa" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tool:visibility="visible"
        app:constraint_referenced_ids="tv_favourite, tv_favourite_value" />

    <View
        android:id="@+id/divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:layout_marginTop="@dimen/_20dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mobile_number_message" />

    <TextView
        android:id="@+id/tv_total_tagihan"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18dp"
        android:text="@string/total_bill"
        android:textColor="@color/black_80"
        android:textStyle="bold"
        android:layout_marginBottom="@dimen/_18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <TextView
        android:id="@+id/tv_total_tagihan_value"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18dp"
        tool:text="Rp300.00"
        android:textColor="@color/red_80"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

</androidx.constraintlayout.widget.ConstraintLayout>
