<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:id="@+id/dateCardView"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:paddingLeft="2dp"
        android:paddingRight="2dp"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:orientation="horizontal"
        android:background="@drawable/date_range_background"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:id="@+id/startDate"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="start"
            android:background="@color/white">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/calender_blue"
                android:paddingEnd="6dp"
                android:layout_gravity="center_vertical"/>

            <LinearLayout
                android:id="@+id/tvStartContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/headerStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/start_date"
                    android:textSize="12sp"
                    android:fontFamily="@font/roboto"
                    android:textColor="#8D8D8D" />

                <TextView
                    android:id="@+id/tvStartDate"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_vertical"
                    android:text="@string/start_date"
                    android:textColor="#4a4a4a"
                    android:textSize="14sp"
                    android:textStyle="normal"/>

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:paddingLeft="8dp"
            android:paddingRight="@dimen/_8dp"
            android:text="-"
            android:fontFamily="@font/roboto"
            android:background="@color/white"
            android:textColor="@color/heading_text"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/endDate"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@color/white">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/calender_blue"
                android:paddingEnd="6dp"
                android:layout_gravity="center_vertical"/>

            <LinearLayout
                android:id="@+id/tvEndContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/headerEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/end_date"
                    android:textSize="12sp"
                    android:fontFamily="@font/roboto"
                    android:textColor="#8D8D8D" />

                <TextView
                    android:id="@+id/tvEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/roboto"
                    android:text="@string/end_date"
                    android:textColor="#4a4a4a"
                    android:textSize="14sp"
                    android:textStyle="normal" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>