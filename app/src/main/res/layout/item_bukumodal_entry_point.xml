<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_grey_rect_4dp">
    <!--    android:background="@drawable/rounded_grey_rect_4dp"-->


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="@dimen/_16dp"
            android:layout_height="@dimen/_16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_item_title"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_4dp"
            android:layout_marginEnd="@dimen/_2dp"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toStartOf="@+id/iv_isi_saldo"
            app:layout_constraintStart_toEndOf="@id/iv_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginEnd="@dimen/_2dp"
            app:layout_constraintEnd_toStartOf="@+id/iv_isi_saldo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_icon">

            <TextView
                android:id="@+id/tv_black_font"
                style="@style/SubHeading2"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="2dp"
                android:textColor="@color/black_80" />

            <TextView
                android:id="@+id/tv_blue_font"
                style="@style/SubHeading2"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/blue60" />

            <TextView
                android:id="@+id/tv_failure_text"
                style="@style/SubHeading2"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/load_failed"
                android:textColor="@color/red40"
                android:visibility="gone" />

        </FrameLayout>

        <ImageView
            android:id="@+id/iv_isi_saldo"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_saldo_topup_plus" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/sl_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <include
            android:id="@+id/shimmer_layout"
            layout="@layout/layout_bnpl_item_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.facebook.shimmer.ShimmerFrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>