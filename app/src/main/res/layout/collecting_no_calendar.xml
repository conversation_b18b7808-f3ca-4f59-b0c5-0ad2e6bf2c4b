<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="top|center_horizontal"
        app:layout_constraintVertical_bias="0.15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/calendarIcon"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_calendar"
            android:tint="#69BDFD"/>

        <TextView
            android:id="@+id/subtitleNoCalendar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Atur tanggal untuk menagih tepat waktu secara otomatis"
            android:textSize="16sp"
            android:textAlignment="center"
            android:textColor="@color/black_66"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>