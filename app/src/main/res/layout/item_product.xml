<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_product_name"
        style="@style/Body1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:layout_weight="1"
        android:layout_marginStart="@dimen/_10dp"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintBottom_toTopOf="@id/tv_stock"
        app:layout_constraintEnd_toStartOf="@id/numberStepper"
        app:layout_constraintStart_toEndOf="@id/checkbox"
        app:layout_constraintTop_toTopOf="@id/checkbox"
        app:layout_constraintVertical_chainStyle="spread"
        tools:text="Nama produk yang sangat panjang" />


    <TextView
        android:id="@+id/tv_stock"
        style="@style/Body3"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/manage_product_stock"
        android:textColor="@color/green_100"
        app:layout_constraintBottom_toBottomOf="@id/checkbox"
        app:layout_constraintStart_toEndOf="@id/checkbox"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="Stok:20"
        tools:textColor="@color/black_80" />

    <TextView
        android:id="@+id/tv_stock_unit"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:text="@string/manage_product_stock"
        android:textColor="@color/black_40"
        app:layout_constraintLeft_toRightOf="@+id/tv_stock"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="PCS"
        tools:textColor="@color/black_40" />
  <!--  <TextView
        android:id="@+id/tv_stock_running_out"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:drawablePadding="2dp"
        android:gravity="center_vertical"
        android:text="@string/stock_low_label"
        android:textColor="@color/warning_orange"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_info_small"
        app:drawableTint="@color/warning_orange"
        app:layout_constraintBottom_toBottomOf="@id/tv_stock"
        app:layout_constraintStart_toEndOf="@id/tv_stock"
        app:layout_constraintTop_toTopOf="@id/tv_stock"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_stock_empty"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:drawablePadding="2dp"
        android:gravity="center_vertical"
        android:text="@string/stock_empty_label"
        android:textColor="@color/out_red"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_info_small"
        app:drawableTint="@color/out_red"
        app:layout_constraintBottom_toBottomOf="@id/tv_stock"
        app:layout_constraintStart_toEndOf="@id/tv_stock"
        app:layout_constraintTop_toTopOf="@id/tv_stock"
        tools:visibility="visible" />-->


    <com.bukuwarung.activities.expense.NumberStepper
        android:id="@+id/numberStepper"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_14dp"
        app:layout_constraintBottom_toBottomOf="@id/checkbox"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/checkbox" />

    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:background="@color/black_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/checkbox" />
</androidx.constraintlayout.widget.ConstraintLayout>