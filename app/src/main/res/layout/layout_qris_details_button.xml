<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_qris_access"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_rectangle_yellow5_8dp"
    android:paddingStart="@dimen/_0dp"
    android:paddingEnd="@dimen/_10dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_qr_bg"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:background="@drawable/bg_rounded_rectangle_yellow60_8dp"
        android:paddingHorizontal="@dimen/_4dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_qris_qr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_qris_qr" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_qris"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_qris_qr"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_qris_logo" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_qris_arrow"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_arrow_black_circle_yellow_bg" />

    <TextView
        android:id="@+id/tv_qris_status"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:lineSpacingExtra="@dimen/_2dp"
        android:text="@string/accept_payments_using_qris"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_qris_arrow"
        app:layout_constraintStart_toEndOf="@id/cl_qr_bg"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>