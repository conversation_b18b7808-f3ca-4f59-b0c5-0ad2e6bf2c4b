<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_basic_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/basic_info"
        style="@style/Heading3"
        android:textColor="@color/black_000000"
        android:layout_marginStart="@dimen/_16dp" />

    <ImageView
        android:id="@+id/iv_completion_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/_4dp"
        app:srcCompat="@drawable/ic_verified_user_16dp"
        app:layout_constraintStart_toEndOf="@+id/tv_basic_info"
        app:layout_constraintTop_toTopOf="@id/tv_basic_info"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <TextView
        android:id="@+id/tv_change"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/change"
        style="@style/SubHeading1"
        android:textColor="@color/blue_60"
        android:drawablePadding="@dimen/_4dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_2dp" />

</androidx.constraintlayout.widget.ConstraintLayout>