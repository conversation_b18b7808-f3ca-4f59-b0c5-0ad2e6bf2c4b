<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_12dp"
    android:layout_marginHorizontal="@dimen/_16dp"
    android:background="@drawable/bg_rounded_rectangle_white_8dp">

    <TextView
        android:id="@+id/tv_name"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Region Name" />

    <TextView
        android:id="@+id/tv_admin_fee"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_4dp"
        android:textColor="@color/red_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        tools:text="Biaya Admin +Rp1.500" />


    <TextView
        android:id="@+id/tv_customer_cost"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_0dp"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_15dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/btn_check"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_admin_fee"
        tools:text="Total biaya tambahan +Rp3.500" />

    <TextView
        android:id="@+id/tv_profit"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/untung"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_profit_amount"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_profit"
        tools:text="Rp3.000" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_check"
        style="@style/Button.OutlinePrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_14dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingTop="@dimen/_4dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_4dp"
        android:text="@string/ubah_harga"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_profit_amount"
        app:rippleColor="@color/black_40" />
</androidx.constraintlayout.widget.ConstraintLayout>