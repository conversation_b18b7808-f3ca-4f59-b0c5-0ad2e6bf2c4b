<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_stateful_text"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_stateful_label"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:text="@string/click_details"
        android:textColor="@color/colorPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_stateful_icon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="@dimen/_6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_stateful_label"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_chevron_down_padded" />

    <ProgressBar
        android:id="@+id/pb_stateful_loading"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="@dimen/_6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_stateful_label"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>