<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".activities.pos.PosCartFragment">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="28dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:src="@drawable/ic_arrow_back" />

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="35dp"
                android:layout_marginRight="35dp"
                android:layout_toEndOf="@id/closeBtn"
                android:alpha="1"
                android:fontFamily="@font/roboto"
                android:text="@string/order_details"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/pos_cart_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        android:background="#EEF8FF">
        <include
            android:id="@+id/product_header_layout"
            layout="@layout/layout_pos_product_list_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            tools:visibility="visible" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/nextLinearLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pos_cart_header">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/stock_unit_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                tools:listitem="@layout/layout_pos_product_cart_item" />

            <LinearLayout
                android:id="@+id/subTotalLinearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/stock_unit_recycler_view">

                <View
                    android:id="@+id/gray_layer"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_8dp"
                    android:background="@color/black_5" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:paddingStart="@dimen/_16dp"
                        android:paddingTop="@dimen/_12dp"
                        android:paddingBottom="@dimen/_12dp"
                        android:text="@string/subtotal"
                        android:textColor="@color/black_80"
                        android:textSize="@dimen/text_16sp" />

                    <TextView
                        android:id="@+id/tv_subtotal_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:paddingTop="@dimen/_12dp"
                        android:paddingEnd="@dimen/_16dp"
                        android:paddingBottom="@dimen/_12dp"
                        android:textColor="@color/black_80"
                        android:textSize="@dimen/text_18sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:text="Rp50.000" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_empty_cart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/_50dp"
                    android:layout_marginBottom="@dimen/_40dp"
                    android:text="@string/delete_order"
                    android:textColor="@color/red_100"
                    android:textSize="@dimen/text_14sp" />
            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <LinearLayout
        android:id="@+id/nextLinearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <View
            android:id="@+id/button_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            app:layout_constraintTop_toTopOf="@id/nextLinearLayout" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_product_summary_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_12dp"
                android:paddingBottom="@dimen/_12dp"
                android:text="@string/total_orders"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_product_price_summary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:paddingTop="@dimen/_12dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_12dp"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="Rp125.000" />
        </RelativeLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:enabled="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="@string/receive_payment"
            android:textSize="@dimen/text_16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>