<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffeaeaea"
    android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/colorPrimary"
                app:theme="@style/ToolbarTheme">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/toolBarLabel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@+id/buttonLayout"
                        android:fontFamily="@font/roboto"
                        android:maxLines="1"
                        android:paddingRight="16dp"
                        android:text="@string/customerProfileTitle"
                        android:textColor="#ffffff"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:autoSizeMaxTextSize="16sp"
                        app:autoSizeMinTextSize="10sp"
                        app:autoSizeStepGranularity="1sp"
                        app:autoSizeTextType="uniform" />

                    <LinearLayout
                        android:id="@+id/buttonLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/edit"
                            android:layout_width="wrap_content"
                            android:layout_height="40dp"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="@dimen/_8dp"
                            android:fontFamily="@font/roboto"
                            android:gravity="center"
                            android:maxWidth="160dp"
                            android:maxLines="1"
                            android:text="@string/edit"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14sp"
                            app:autoSizeMaxTextSize="14sp"
                            app:autoSizeMinTextSize="12sp"
                            app:autoSizeStepGranularity="1sp"
                            app:autoSizeTextType="uniform"
                            app:backgroundTint="@color/white"
                            app:cornerRadius="16dp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/save"
                            android:layout_width="wrap_content"
                            android:layout_height="40dp"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="@dimen/_8dp"
                            android:fontFamily="@font/roboto"
                            android:gravity="center"
                            android:maxWidth="160dp"
                            android:maxLines="1"
                            android:text="@string/save"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14sp"
                            app:autoSizeMaxTextSize="14sp"
                            app:autoSizeMinTextSize="12sp"
                            app:autoSizeStepGranularity="1sp"
                            app:autoSizeTextType="uniform"
                            app:backgroundTint="@color/white"
                            app:cornerRadius="16dp" />
                    </LinearLayout>
                </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/toolbar"
                android:layout_marginBottom="-6dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:background="@color/white"
                android:orientation="vertical"
                android:paddingTop="8dp">

                <FrameLayout
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="24dp"
                    android:layout_marginBottom="36dp"
                    android:gravity="center_horizontal">

                    <LinearLayout
                        android:id="@+id/pic"
                        android:layout_width="96dp"
                        android:layout_height="96dp"
                        android:layout_below="@+id/phoneboook"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="16dp"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/profilePic"
                            android:layout_width="96dp"
                            android:layout_height="96dp" />

                        <TextView
                            android:id="@+id/nameInitial"
                            android:layout_width="96dp"
                            android:layout_height="96dp"
                            android:background="@drawable/oval_0"
                            android:fontFamily="@font/roboto"
                            android:gravity="center"
                            android:text="A"
                            android:textColor="@color/white"
                            android:textSize="32sp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/cameraIcon"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="bottom|right|center_vertical|center_horizontal|center"
                        android:background="@drawable/background_circular_blue"
                        android:scaleType="center"
                        android:src="@mipmap/edit_white"
                        android:visibility="gone" />
                </FrameLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#fffbfa"
                    android:orientation="vertical"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="8dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/add_customer_text_margin_top">

                        <ImageView
                            android:id="@+id/nameIcon"
                            android:layout_width="@dimen/add_customer_icon_size"
                            android:layout_height="@dimen/add_customer_icon_size"
                            android:layout_marginTop="@dimen/add_customer_icon_margin_top"
                            android:src="@mipmap/customer_name_grey" />

                        <TextView
                            android:id="@+id/customerLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:layout_marginBottom="@dimen/add_customer_text_margin"
                            android:layout_toRightOf="@+id/nameIcon"
                            android:lineSpacingExtra="3.8sp"
                            android:text="@string/input_customer_name"
                            android:textColor="@color/black"
                            android:textSize="12.2sp" />

                        <EditText
                            android:id="@+id/customerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/customerLabel"
                            android:layout_marginLeft="12dp"
                            android:layout_toRightOf="@+id/nameIcon"
                            android:hint="@string/input_customer_name_hint"
                            android:textColor="#442b2d"
                            android:textSize="16.3sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/add_customer_text_margin_top">

                        <ImageView
                            android:id="@+id/mobileIcon"
                            android:layout_width="@dimen/add_customer_icon_size"
                            android:layout_height="@dimen/add_customer_icon_size"
                            android:layout_marginTop="@dimen/add_customer_icon_margin_top"
                            android:layout_marginRight="16dp"
                            android:src="@mipmap/customer_phone_grey" />

                        <TextView
                            android:id="@+id/mobileLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/add_customer_text_margin"
                            android:layout_toRightOf="@+id/mobileIcon"
                            android:lineSpacingExtra="3.8sp"
                            android:text="@string/new_customer_number"
                            android:textColor="@color/black"
                            android:textSize="12.2sp" />

                        <com.hbb20.CountryCodePicker
                            android:id="@+id/countryPicker"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBottom="@+id/mobile"
                            android:layout_marginLeft="-8dp"
                            android:layout_marginRight="-8dp"
                            android:layout_marginBottom="4dp"
                            android:layout_toRightOf="@+id/mobileIcon"
                            app:ccp_autoDetectLanguage="true"
                            app:ccp_countryPreference="@string/countries_prefferred_in_spinner"
                            app:ccp_defaultLanguage="ENGLISH"
                            app:ccp_excludedCountries="@string/countries_in_eu"
                            app:ccp_showFlag="false"
                            app:ccp_showFullName="false"
                            app:ccp_showNameCode="false"
                            app:ccp_showPhoneCode="true"
                            app:ccp_textSize="16sp" />

                        <Spinner
                            android:id="@+id/countrySpinner"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentBottom="true"
                            android:layout_marginLeft="-8dp"
                            android:layout_marginRight="-8dp"
                            android:layout_toRightOf="@+id/mobileIcon"
                            android:visibility="gone" />

                        <EditText
                            android:id="@+id/mobile"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/mobileLabel"
                            android:layout_toRightOf="@+id/countryPicker"
                            android:hint="@string/new_customer_number"
                            android:inputType="number"
                            android:lineSpacingExtra="4sp"
                            android:textColor="#442b2d"
                            android:textSize="16sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/add_customer_text_margin_top">

                        <ImageView
                            android:id="@+id/addressIcon"
                            android:layout_width="@dimen/add_customer_icon_size"
                            android:layout_height="@dimen/add_customer_icon_size"
                            android:layout_marginTop="@dimen/add_customer_icon_margin_top"
                            android:src="@drawable/location_icon"
                            android:tint="@color/grey" />

                        <TextView
                            android:id="@+id/addressLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:layout_marginBottom="@dimen/add_customer_text_margin"
                            android:layout_toRightOf="@+id/addressIcon"
                            android:lineSpacingExtra="3.8sp"
                            android:text="@string/new_customer_address"
                            android:textColor="@color/black"
                            android:textSize="12.2sp" />

                        <EditText
                            android:id="@+id/address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/addressLabel"
                            android:layout_marginLeft="12dp"
                            android:layout_toRightOf="@+id/addressIcon"
                            android:hint="@string/new_customer_address_hint"
                            android:textColor="#442b2d"
                            android:textSize="16.3sp" />
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:alpha="0.12"
                android:background="@color/black"
                android:visibility="invisible" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16.0dip"
                android:visibility="gone"
                android:background="@color/white"
                android:elevation="2.0dip"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16.0dip"
                    android:layout_marginTop="8.0dip"
                    android:lineSpacingMultiplier="1.36"
                    android:text="@string/account_settings_title"
                    android:textAllCaps="true"
                    android:textColor="@color/black_44"
                    android:textSize="14.0sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="50.0dip"
                    android:layout_marginTop="8.0dip"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/_16dp"
                    android:paddingRight="@dimen/_16dp">

                    <ImageView
                        android:layout_width="24.0dip"
                        android:layout_height="50.0dip"
                        android:layout_centerVertical="true"
                        android:paddingTop="13.0dip"
                        android:paddingBottom="13.0dip"
                        android:tint="@color/grey"
                        app:srcCompat="@drawable/ic_sms" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="16.0dip"
                        android:layout_weight="1.0"
                        android:text="@string/transaction_sms"
                        android:textColor="@color/black_44"
                        android:textSize="16.0dip" />

                    <Switch
                        android:id="@+id/switch_sms"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:checked="true" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="50.0dip"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/_16dp"
                    android:paddingRight="@dimen/_16dp">

                    <ImageView
                        android:layout_width="24.0dip"
                        android:layout_height="50.0dip"
                        android:layout_centerVertical="true"
                        android:paddingTop="13.0dip"
                        android:paddingBottom="13.0dip"
                        android:tint="@color/grey"
                        app:srcCompat="@drawable/ic_select_language" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="16.0dip"
                        android:layout_weight="1.0"
                        android:text="@string/transaction_sms_language"
                        android:textColor="@color/black_44"
                        android:textSize="16.0dip" />

                    <TextView
                        android:id="@+id/sms_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textColor="@color/colorPrimary"
                        android:text="@string/indonesian" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/deleteContainer"
                android:layout_width="fill_parent"
                android:layout_height="50.0dip"
                android:layout_marginTop="16.0dip"
                android:layout_marginBottom="5.0dip"
                android:background="@color/white"
                android:elevation="2.0dip"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/_16dp"
                android:paddingRight="@dimen/_16dp">

                <ImageView
                    android:layout_width="24.0dip"
                    android:layout_height="24.0dip"
                    android:layout_gravity="center_vertical"
                    android:tint="@color/red_error"
                    app:srcCompat="@drawable/delete_red" />

                <TextView
                    android:id="@+id/delete_cst"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="30.0dip"
                    android:layout_marginLeft="30.0dip"
                    android:layout_marginEnd="30.0dip"
                    android:layout_marginRight="30.0dip"
                    android:ellipsize="end"
                    android:text="@string/delete_cst_text"
                    android:textColor="@color/red_error"
                    android:textSize="16.0sp" />
            </LinearLayout>
            </LinearLayout>
            </ScrollView>
        </LinearLayout>
</RelativeLayout>
