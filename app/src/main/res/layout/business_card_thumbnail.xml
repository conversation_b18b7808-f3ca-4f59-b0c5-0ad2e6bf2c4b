<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable name="imageSrc" type="android.graphics.drawable.Drawable"/>
        <variable
            name="callback"
            type="com.bukuwarung.activities.card.BusinessCardActivity"/>
    </data>
<androidx.cardview.widget.CardView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardCornerRadius="8dp"
    android:layout_marginLeft="8dp"
    android:elevation="10dp">

    <ImageView
        android:id="@+id/button1"
        android:layout_width="66dp"
        android:layout_height="36dp"
        android:onClick="@{(view) -> callback.onClickThumbnail(view)}"
        android:src="@drawable/bcard1" />
</androidx.cardview.widget.CardView>
</layout>


