<LinearLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#eff4ff"
    android:paddingLeft="16dp"
    android:paddingTop="8dp"
    android:layout_marginBottom="2dp"
    android:paddingBottom="8dp"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="7"
        android:orientation="vertical"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="10sp"
            android:text="Rincian"
            android:textColor="@color/heading_text"
            android:textStyle="bold"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/transCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="10sp"
            android:text=""
            tools:text="5 Transaksi"
            android:textColor="@color/greyTextColor"
            android:paddingTop="5dp"
            android:textSize="12sp"
            app:autoSizeMaxTextSize="15sp"
            app:autoSizeMinTextSize="14sp" />

    </LinearLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="7"
        android:orientation="vertical"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/in"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:lineSpacingExtra="10sp"
            android:paddingRight="8dp"
            android:text="@string/creditWithSign"
            android:textSize="14sp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="14sp"
            app:autoSizeMinTextSize="12sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            android:textColor="@color/in_green" />

        <TextView
            android:id="@+id/creditCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="10sp"
            android:text=""
            tools:text="9000"
            android:textAlignment="center"
            android:textColor="@color/greyTextColor"
            android:paddingTop="5dp"
            android:textSize="14sp"
            app:autoSizeMaxTextSize="14sp"
            app:autoSizeMinTextSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="7"
        android:orientation="vertical"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/out"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="7"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:textSize="14sp"
            app:autoSizeMaxTextSize="15sp"
            android:textStyle="bold"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            android:lineSpacingExtra="10sp"
            android:paddingRight="8dp"
            android:text="@string/debitWithSign"
            android:textColor="@color/out_red" />

        <TextView
            android:id="@+id/debitCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="10sp"
            android:text=""
            tools:text="10.000"
            android:textAlignment="center"
            android:textColor="@color/greyTextColor"
            android:paddingTop="5dp"
            android:textSize="14sp"
            app:autoSizeMaxTextSize="14sp"
            app:autoSizeMinTextSize="12sp" />

    </LinearLayout>

</LinearLayout>