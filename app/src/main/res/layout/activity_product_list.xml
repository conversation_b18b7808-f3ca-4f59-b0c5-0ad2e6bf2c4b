<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="false">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:title="@string/item_list"
        app:titleTextColor="@color/white" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_product_name"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:boxCornerRadiusBottomEnd="@dimen/_6dp"
        app:boxCornerRadiusBottomStart="@dimen/_6dp"
        app:boxCornerRadiusTopEnd="@dimen/_6dp"
        app:boxCornerRadiusTopStart="@dimen/_6dp"
        app:boxStrokeColor="@color/bg_edittext_stroke_color"
        app:boxStrokeWidth="@dimen/_1dp"
        app:hintEnabled="false"
        app:layout_constraintEnd_toStartOf="@id/barrier_search"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb"
        app:passwordToggleDrawable="@null">

        <EditText
            android:id="@+id/et_search"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/search_items"
            android:maxLength="40"
            android:maxLines="1"
            android:paddingLeft="@dimen/_16dp"
            android:textColor="@color/black_80"
            android:textColorHint="@color/black_20"
            app:layout_constraintTop_toBottomOf="@id/tb" />
    </com.google.android.material.textfield.TextInputLayout>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_search"
        android:layout_width="@dimen/_1dp"
        android:layout_height="wrap_content"
        app:barrierDirection="left"
        app:constraint_referenced_ids="btn_add_product_search"
        app:layout_constraintBottom_toTopOf="@id/line"
        app:layout_constraintStart_toStartOf="@id/btn_add_product_search"
        app:layout_constraintTop_toBottomOf="@id/tb" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_add_product_search"
        style="@style/ButtonFill.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/add_product"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb"
        tools:visibility="visible" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@id/til_product_name" />

    <TextView
        android:id="@+id/search_product_name_text"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginRight="@dimen/_16dp"
        app:layout_constraintEnd_toStartOf="@+id/btn_add_product"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_add_product"
        style="@style/ButtonFill.Blue"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        android:text="@string/add_product"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />


    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="search_product_name_text,btn_add_product" />

    <View
        android:id="@+id/btn_add_product_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_10"
        app:layout_constraintTop_toBottomOf="@id/barrier" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/btn_add_product_grp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="search_product_name_text, btn_add_product, btn_add_product_divider"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_product_info"
        style="@style/EditTextBordered"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@color/yellow_10"
        android:padding="@dimen/_8dp"
        android:text="@string/decimal_qty_info"
        app:drawableEndCompat="@drawable/close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_add_product_divider" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_product_info" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_product"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintBottom_toTopOf="@id/fl_button"
        app:layout_constraintTop_toBottomOf="@id/tv_product_info"
        tools:listitem="@layout/item_product" />

    <include
        android:id="@+id/layout_no_product"
        layout="@layout/product_list_blank_screen"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/fl_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <FrameLayout
        android:id="@+id/fl_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_gravity="top"
            android:background="@color/black_5" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:text="@string/save"
            android:textAllCaps="true" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>