<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/_8dp"
    android:background="@color/white"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/indexText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:background="@drawable/background_circular_yellow"
        android:gravity="center"
        android:paddingStart="7dp"
        android:paddingTop="3dp"
        android:paddingEnd="7dp"
        android:paddingBottom="3dp"
        android:text="@string/default_placeholder"
        android:textColor="@color/black_66"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/contentText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/default_placeholder"
        android:textColor="@color/black"
        android:textSize="14sp" />

</LinearLayout>