<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_pending_trx_info"
    android:background="@drawable/bg_rounded_rectangle_pale_yellow_8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_info_yellow"
        android:layout_marginStart="@dimen/_10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        style="@style/Body3"
        android:id="@+id/tv_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_8dp"
        android:layout_marginHorizontal="@dimen/_10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_info"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Proses ini membutuhkan waktu sekitar x - xx menit"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/pending_info_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_info,tv_info"/>

    <TextView
        style="@style/Body3"
        android:id="@+id/tv_help_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/black_60"
        tools:text="Transaksi telah berlangsung lebih dari xx menit. Silakan hubungi bantuan."
        android:layout_marginVertical="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_14dp"
        app:layout_constraintEnd_toStartOf="@+id/btn_help"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_help"
        style="@style/DefaultMaterialButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:text="@string/to_learn"
        android:textAllCaps="false"
        android:textColor="@color/cta_text_button"
        android:textSize="16sp"
        android:textStyle="bold"
        android:paddingHorizontal="@dimen/_14dp"
        android:paddingVertical="@dimen/_10dp"
        android:letterSpacing="0.05"
        app:backgroundTint="@color/buku_CTA"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/help_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_help_info,btn_help"/>

</androidx.constraintlayout.widget.ConstraintLayout>