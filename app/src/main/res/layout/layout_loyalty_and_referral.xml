<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorGreyLight"
    xmlns:app="http://schemas.android.com/apk/res-auto">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:weightSum="1">

            <androidx.cardview.widget.CardView
                android:layout_width="@dimen/_0dp"
                android:id="@+id/cv_shimmer_loyalty_referral"
                android:layout_height="wrap_content"
                android:elevation="@dimen/_4dp"
                android:layout_margin="@dimen/_10dp"
                app:cardCornerRadius="@dimen/_10dp"
                android:layout_weight="1" >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" >

                    <include
                        android:id="@+id/layout_loyalty_shimmer"
                        layout="@layout/layout_user_tier_shimmer"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintWidth_percent="0.48"/>

                    <include
                        android:id="@+id/layout_referral_shimmer"
                        layout="@layout/layout_user_tier_shimmer"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toEndOf="@id/layout_loyalty_shimmer"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintWidth_percent="0.48"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_loyalty_referral"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:elevation="@dimen/_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/_10dp"
        app:cardCornerRadius="@dimen/_10dp"
        app:layout_constraintWidth_percent="0.95">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/layout_loyalty"
                layout="@layout/layout_loyalty_referral_item"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.5"
                android:stateListAnimator="@null" />

            <View
                android:id="@+id/divider"
                android:layout_width="@dimen/_1dp"
                android:layout_height="@dimen/_0dp"
                android:background="@color/black_10"
                app:layout_constraintStart_toStartOf="@+id/layout_referral"
                app:layout_constraintTop_toTopOf="@+id/layout_referral"
                app:layout_constraintBottom_toBottomOf="@+id/layout_referral"
                />

            <include
                android:id="@+id/layout_referral"
                layout="@layout/layout_loyalty_referral_item"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="@id/layout_loyalty"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintWidth_percent="0.5"
                android:stateListAnimator="@null" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>