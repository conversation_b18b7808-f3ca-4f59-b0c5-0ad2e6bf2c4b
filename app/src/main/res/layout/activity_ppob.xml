<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/include_semua"
        layout="@layout/layout_ppob_icon_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        app:srcCompat="@drawable/ic_category_filter" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_ppob"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:orientation="horizontal"
        android:background="@color/blue_60"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/include_semua"
        app:layout_constraintStart_toEndOf="@id/include_semua"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        tools:listitem="@layout/layout_ppob_icon_item" />

    <com.bukuwarung.baseui.DefaultViewPager
        android:id="@+id/vp_ppob"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv_ppob" />
</androidx.constraintlayout.widget.ConstraintLayout>