<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/contact_form"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_8dp"
        app:cardElevation="@dimen/_0dp"
        app:strokeWidth="@dimen/_1dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_12dp">

            <ImageView
                android:id="@+id/contact_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_user"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/black40" />

            <TextView
                android:id="@+id/customer_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:text="@string/label_customer"
                android:textColor="@color/black60"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/contact_icon"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/customer_name_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:textAlignment="textEnd"
                android:textColor="@color/black80"
                android:textSize="@dimen/text_14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/contact_arrow_icon"
                app:layout_constraintStart_toEndOf="@id/customer_label"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/contact_arrow_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_chevron_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/black40" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/error_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6dp"
        android:textColor="@color/red80"
        android:textSize="@dimen/text_12sp" />

</LinearLayout>
