<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner">

    <TextView
        android:id="@+id/lunaskan_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginTop="24dp"
        android:text="@string/select_unit"
        android:textColor="@color/black_80"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/closeDialog"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        android:layout_marginTop="29dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:src="@drawable/close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/stock_unit_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="20dp"
        android:paddingTop="32dp"
        android:paddingRight="20dp"
        android:paddingBottom="100dp"
        app:layout_constraintTop_toBottomOf="@id/lunaskan_label" />

    <include
        android:id="@+id/add_unit_layout"
        layout="@layout/add_stock_unit_layout"
        android:layout_width="match_parent"
        android:layout_height="68dp"
        app:layout_constraintBottom_toBottomOf="parent"

        />


</androidx.constraintlayout.widget.ConstraintLayout>