<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_address"
        style="@style/ToolbarTheme"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:titleTextAppearance="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:title="@string/business_address_hint" />

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_top_rounded_corner" />

</LinearLayout>