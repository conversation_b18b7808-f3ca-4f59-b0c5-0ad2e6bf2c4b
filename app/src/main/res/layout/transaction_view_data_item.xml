<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include layout="@layout/transaction_view_header_item" />

    <LinearLayout
        android:id="@+id/main_container"
        android:layout_width="match_parent"
        android:layout_height="75dp"
        android:layout_below="@+id/listHeader">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_integrated_label"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_transaction_integrated"
                android:paddingStart="@dimen/_12dp"
                android:paddingTop="2dp"
                android:paddingEnd="@dimen/_12dp"
                android:paddingBottom="2dp"
                android:text="@string/connected_to_transaction"
                android:textColor="@color/red_100"
                android:textSize="9sp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />


            <TextView
                android:id="@+id/date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_weight="7"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="10sp"
                android:maxLines="1"
                android:paddingRight="8dp"
                android:text="23232.0"
                android:textColor="@color/body_text"
                android:textSize="15sp"
                app:autoSizeMaxTextSize="15sp"
                app:autoSizeMinTextSize="12sp"
                app:autoSizeStepGranularity="1sp"
                app:autoSizeTextType="uniform"
                app:layout_constraintBottom_toTopOf="@id/note"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_integrated_label"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/note"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/date"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end"
                android:paddingEnd="@dimen/_4dp"
                android:text="Balance till 3rd December"
                android:textColor="#9dacb4"
                android:textSize="@dimen/trx_info_text_size"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/date" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/creditAmount"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:background="@color/credit_column_bg"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:lineSpacingExtra="8sp"
            android:maxLines="1"
            android:paddingRight="8dp"
            android:text="-"
            android:textColor="@color/in_green"
            android:textSize="@dimen/amount_text_size"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="10sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform" />

        <TextView
            android:id="@+id/debitAmount"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:lineSpacingExtra="10sp"
            android:maxLines="1"
            android:paddingLeft="4dp"
            android:paddingRight="8dp"
            android:text="2000.9"
            android:textColor="@color/out_red"
            android:textSize="@dimen/amount_text_size"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="10sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform" />
    </LinearLayout>

    <TextView
        android:id="@+id/section"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/main_container"
        android:background="@color/section_end" />
</RelativeLayout>
