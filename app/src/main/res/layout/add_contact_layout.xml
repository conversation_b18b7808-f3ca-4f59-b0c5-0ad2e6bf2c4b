<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingBottom="@dimen/_16dp"
    >

    <ImageView
        android:id="@+id/icon_contact_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:padding="8dp"
        android:background="@drawable/ic_add_contact"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/black_80" />


    <TextView
        android:id="@+id/new_phone_number_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginStart="@dimen/_20dp"
        android:ellipsize="end"
        android:fontFamily="@font/roboto"
        android:text="@string/add_contact"
        android:textColor="@color/black_60"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/icon_contact_add"
        app:layout_constraintStart_toEndOf="@id/icon_contact_add"
        app:layout_constraintTop_toTopOf="@id/icon_contact_add" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/add_contact_customer_name"
        android:layout_marginStart="5dp"
        android:ellipsize="end"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="7.7sp"
        android:maxLines="1"
        android:textColor="@color/black_60"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@id/new_phone_number_tv"
        app:layout_constraintTop_toTopOf="@id/new_phone_number_tv"
        android:layout_marginEnd="@dimen/_16dp"
        />



</androidx.constraintlayout.widget.ConstraintLayout>