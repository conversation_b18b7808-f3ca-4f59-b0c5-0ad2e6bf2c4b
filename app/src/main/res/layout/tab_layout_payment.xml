<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    android:fitsSystemWindows="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:contentInsetStartWithNavigation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/menuIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@mipmap/ic_menu_white_24dp" />

            <TextView
                android:id="@+id/screenTitle"
                style="@style/Heading2"
                android:singleLine="true"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginStart="@dimen/_5dp"
                android:textColor="@color/white"
                android:gravity="start"
                android:layout_marginEnd="@dimen/_5dp"
                android:layout_width="@dimen/_0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/menuIcon"
                app:layout_constraintEnd_toStartOf="@+id/reshow_tutorial_icon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Usaha " />

            <com.bukuwarung.widget.SafeGuaranteeAnimView
                android:id="@+id/reshow_tutorial_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/loading_state_lottie"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/loading_state_txt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintVertical_chainStyle="packed"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/loading_payment" />

    <TextView
        android:id="@+id/loading_state_txt"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_50dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_50dp"
        android:gravity="center"
        android:text="@string/payment_tab_loading_state_message"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loading_state_lottie" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/loading_state_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="loading_state_lottie,loading_state_txt" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/coordinatorLayout"
        android:layout_width="0dp"
        android:layout_height="@dimen/_0dp"
        android:fitsSystemWindows="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        tools:visibility="visible">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@id/app_bar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:expanded="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/collapsable_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <View
                        android:id="@+id/summary_gradient_bg"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_0dp"
                        android:background="@drawable/payment_tab_gradient_bg"
                        app:layout_constraintBottom_toTopOf="@id/bnpl_saldo_layout"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/iv_saldo_wallet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        app:layout_constraintBottom_toBottomOf="@id/btn_topup"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/btn_topup"
                        app:srcCompat="@drawable/ic_saldo" />

                    <TextView
                        android:id="@+id/tv_saldo_balance"
                        style="@style/Heading3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:textColor="@color/white"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:layout_constraintBottom_toTopOf="@id/tv_cashback_balance"
                        app:layout_constraintStart_toEndOf="@id/iv_saldo_wallet"
                        app:layout_constraintTop_toTopOf="@id/btn_topup"
                        tools:text="Rp1.000.000" />

                    <TextView
                        android:id="@+id/tv_cashback_balance"
                        style="@style/Label2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/make_credit_and_sales"
                        android:textColor="@color/white"
                        app:layout_constraintBottom_toBottomOf="@id/btn_topup"
                        app:layout_constraintStart_toStartOf="@id/tv_saldo_balance"
                        app:layout_constraintTop_toBottomOf="@id/tv_saldo_balance"
                        tools:text="Cashback Rp56.930" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/iv_saldo_info"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_8dp"
                        app:layout_constraintBottom_toBottomOf="@id/tv_saldo_balance"
                        app:layout_constraintStart_toEndOf="@id/tv_saldo_balance"
                        app:layout_constraintTop_toTopOf="@id/tv_saldo_balance"
                        app:srcCompat="@drawable/ic_saldo_info" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_topup"
                        style="@style/ButtonOutline.Blue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:paddingStart="@dimen/_8dp"
                        android:paddingTop="@dimen/_4dp"
                        android:paddingEnd="@dimen/_6dp"
                        android:paddingBottom="@dimen/_4dp"
                        android:text="@string/topup_saldo"
                        android:textColor="@color/white"
                        app:icon="@mipmap/ic_plus_white_24dp"
                        app:iconPadding="@dimen/_2dp"
                        app:iconTint="@color/white"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:strokeColor="@color/white" />

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/saldo_group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:constraint_referenced_ids="btn_topup,iv_saldo_wallet,iv_saldo_info,tv_saldo_balance, tv_cashback_balance" />

                    <View
                        android:id="@+id/layout_saldo_coachmark"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_0dp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@+id/btn_topup"
                        app:layout_constraintTop_toTopOf="@+id/btn_topup" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/summaryView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_12dp"
                        android:layout_marginRight="@dimen/_16dp"
                        android:background="@drawable/white_drawable_round_8dp"
                        android:paddingTop="@dimen/_12dp"
                        android:paddingBottom="@dimen/_12dp"
                        android:elevation="@dimen/dimen_8dp"
                        app:layout_constraintTop_toBottomOf="@id/btn_topup">

                        <View
                            android:id="@+id/summary_in_view"
                            android:layout_width="@dimen/_0dp"
                            android:layout_height="@dimen/_0dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/v_line"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <View
                            android:id="@+id/summary_out_view"
                            android:layout_width="@dimen/_0dp"
                            android:layout_height="@dimen/_0dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/v_line"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/income_txt"
                            style="@style/Body3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/money_in"
                            app:layout_constraintEnd_toStartOf="@+id/v_line"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/expense_txt"
                            style="@style/Body3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/money_out"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/v_line"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/income_total_txt"
                            style="@style/Heading3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:textColor="@color/in_green"
                            app:layout_constraintEnd_toEndOf="@+id/income_txt"
                            app:layout_constraintStart_toStartOf="@+id/income_txt"
                            app:layout_constraintTop_toBottomOf="@id/income_txt"
                            tools:text="Rp100.000" />

                        <TextView
                            android:id="@+id/expense_total_txt"
                            style="@style/Heading3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:textColor="@color/out_red"
                            app:layout_constraintEnd_toEndOf="@+id/expense_txt"
                            app:layout_constraintStart_toStartOf="@+id/expense_txt"
                            app:layout_constraintTop_toBottomOf="@id/income_txt"
                            tools:text="Rp100.000" />

                        <View
                            android:id="@+id/v_line"
                            android:layout_width="@dimen/_1dp"
                            android:layout_height="0dp"
                            android:background="@color/black_5"
                            app:layout_constraintBottom_toBottomOf="@id/income_total_txt"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.bukuwarung.payments.widget.QrisDetailsButton
                        android:id="@+id/btn_qris_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_12dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/summaryView" />

                    <View
                        android:id="@+id/layout_ppob_coachmark"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_0dp"
                        app:layout_constraintBottom_toBottomOf="@+id/layout_ppob"
                        app:layout_constraintTop_toTopOf="@+id/layout_ppob" />

                    <FrameLayout
                        android:id="@+id/bnpl_saldo_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_12dp"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@+id/btn_qris_detail" />

                    <com.bukuwarung.payments.widget.SaldoLimitsView
                        android:id="@+id/saldo_limit_view"
                        android:layout_width="0dp"
                        android:layout_marginTop="10dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/bnpl_saldo_layout" />

                    <View
                        android:id="@+id/vw_separator"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_1dp"
                        android:background="@color/black_5"
                        app:layout_constraintTop_toBottomOf="@id/saldo_limit_view"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_ppob"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="@dimen/_8dp"
                        app:layout_constraintTop_toBottomOf="@+id/vw_separator">

                        <TextView
                            android:id="@+id/ppob_title"
                            style="@style/Heading3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_10dp"
                            android:text="@string/ppob_title_in_tab"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/iv_revisit_coachmark"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_10dp"
                            android:layout_marginEnd="@dimen/_16dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_info_icon" />

                        <FrameLayout
                            android:id="@+id/ppob_frame_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toBottomOf="@+id/ppob_title" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/vp_payment_banner"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layout_ppob" />

                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/tb_payment_banner"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="@dimen/_6dp"
                        android:layout_marginTop="@dimen/_10dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:background="@color/transparent"
                        android:foregroundGravity="bottom"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/vp_payment_banner"
                        app:layout_constraintTop_toBottomOf="@+id/vp_payment_banner"
                        app:tabBackground="@drawable/selector_banner"
                        app:tabGravity="center"
                        app:tabIndicatorHeight="@dimen/_0dp"
                        app:tabPaddingEnd="@dimen/_8dp"
                        app:tabPaddingStart="@dimen/_8dp"
                        app:tabSelectedTextColor="@android:color/transparent"
                        app:tabTextColor="@android:color/transparent" />

                    <com.bukuwarung.payments.widget.PaymentInfoView
                        android:id="@+id/paymentInfoMessage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10dp"
                        app:layout_constraintTop_toBottomOf="@id/tb_payment_banner"
                        tools:layout_editor_absoluteX="0dp" />

                    <View
                        android:id="@+id/count_bg"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_0dp"
                        android:background="@color/f8f8f8"
                        app:layout_constraintBottom_toBottomOf="@id/see_all_txt"
                        app:layout_constraintTop_toBottomOf="@id/paymentInfoMessage" />

                    <TextView
                        android:id="@+id/transaction_count_txt"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:paddingVertical="@dimen/_16dp"
                        android:text="@string/last_payments"
                        android:textColor="@color/black_60"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/paymentInfoMessage" />

                    <TextView
                        android:id="@+id/see_all_txt"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:paddingVertical="@dimen/_16dp"
                        android:text="@string/check_all_payments"
                        android:textColor="@color/blue_60"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/paymentInfoMessage" />

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/trx_count_group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="see_all_txt,count_bg,transaction_count_txt" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:backgroundTint="@color/colorPrimary"
            android:visibility="gone"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/payment_rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/error_state_group"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_30dp"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/error_state_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toTopOf="@id/error_state_txt"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:srcCompat="@drawable/ic_no_inet" />

                <TextView
                    android:id="@+id/error_state_txt"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_50dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_50dp"
                    android:gravity="center"
                    android:text="@string/payment_tab_error_state_message"
                    app:layout_constraintBottom_toTopOf="@id/btn_payment_down"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/error_state_img" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_payment_down"
                    style="@style/ButtonOutline.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:paddingTop="@dimen/_12dp"
                    android:text="@string/reload"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/error_state_txt"
                    app:strokeWidth="@dimen/_1dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/empty_state_info"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/_35dp">

                <com.facebook.shimmer.ShimmerFrameLayout
                    android:id="@+id/empty_state_info_effect"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:elevation="@dimen/_1dp"
                        app:cardCornerRadius="@dimen/_8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tv_empty_state_effect1"
                                style="@style/Body3"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/_12dp"
                                android:layout_marginStart="@dimen/_12dp"
                                android:layout_marginTop="@dimen/_16dp"
                                android:layout_marginEnd="@dimen/_12dp"
                                android:background="@color/grey"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="Bayar pake BukuWarung bisa hemat 200 kali setara dengan beli HP baru. Wadaw!" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tv_empty_state_effect2"
                                android:layout_width="100dp"
                                android:layout_height="12dp"
                                android:layout_marginStart="@dimen/_12dp"
                                android:layout_marginTop="@dimen/_8dp"
                                android:background="@color/grey"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/tv_empty_state_effect1" />

                            <ImageView
                                android:id="@+id/iv_empty_state_effect"
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:layout_marginStart="@dimen/_12dp"
                                android:layout_marginTop="@dimen/_12dp"
                                android:layout_marginBottom="@dimen/_16dp"
                                android:background="@color/grey"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/tv_empty_state_effect2" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tv_buku_tips_effect"
                                style="@style/SubHeading2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_12dp"
                                android:layout_marginEnd="@dimen/_12dp"
                                android:background="@color/grey"
                                android:textColor="@color/blue_80"
                                app:layout_constraintBottom_toTopOf="@+id/tv_from_buku_effect"
                                app:layout_constraintEnd_toStartOf="@+id/iv_arrow_effect"
                                app:layout_constraintStart_toEndOf="@+id/iv_empty_state_effect"
                                app:layout_constraintTop_toTopOf="@+id/iv_empty_state_effect"
                                tools:text="@string/payment_0_tips" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tv_from_buku_effect"
                                style="@style/Label1"
                                android:layout_width="80dp"
                                android:layout_height="12dp"
                                android:layout_marginStart="@dimen/_12dp"
                                android:background="@color/grey"
                                app:layout_constraintBottom_toBottomOf="@+id/iv_empty_state_effect"
                                app:layout_constraintStart_toEndOf="@+id/iv_empty_state_effect"
                                app:layout_constraintTop_toBottomOf="@+id/tv_buku_tips_effect" />

                            <ImageView
                                android:id="@+id/iv_arrow_effect"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="@dimen/_12dp"
                                android:background="@color/grey"
                                app:layout_constraintBottom_toBottomOf="@+id/iv_empty_state_effect"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/iv_empty_state_effect" />
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </androidx.cardview.widget.CardView>
                </com.facebook.shimmer.ShimmerFrameLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/empty_state_info_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20dp"
                    android:elevation="@dimen/_1dp"
                    android:visibility="gone"
                    app:cardCornerRadius="@dimen/_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tv_empty_state"
                            style="@style/Body3"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_12dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_12dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="Bayar pake BukuWarung bisa hemat 200 kali setara dengan beli HP baru. Wadaw!" />

                        <ImageView
                            android:id="@+id/iv_empty_state"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_12dp"
                            android:layout_marginTop="@dimen/_12dp"
                            android:layout_marginBottom="@dimen/_16dp"
                            android:src="@drawable/ic_payment_0_hi"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_empty_state" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tv_buku_tips"
                            style="@style/SubHeading2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:text="@string/payment_0_tips"
                            android:textColor="@color/blue_80"
                            app:layout_constraintBottom_toTopOf="@+id/tv_from_buku"
                            app:layout_constraintEnd_toStartOf="@+id/iv_arrow"
                            app:layout_constraintStart_toEndOf="@+id/iv_empty_state"
                            app:layout_constraintTop_toTopOf="@+id/iv_empty_state" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tv_from_buku"
                            style="@style/Label1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/from_bukuwarung"
                            app:layout_constraintBottom_toBottomOf="@+id/iv_empty_state"
                            app:layout_constraintStart_toEndOf="@+id/iv_empty_state"
                            app:layout_constraintTop_toBottomOf="@+id/tv_buku_tips" />

                        <ImageView
                            android:id="@+id/iv_arrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_12dp"
                            android:src="@drawable/ic_arrow_right_blue_circle_bg"
                            app:layout_constraintBottom_toBottomOf="@+id/iv_empty_state"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/iv_empty_state" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_empty_state_static"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/iv_empty_state_static"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bulk_empty" />

                    <TextView
                        android:id="@+id/iv_empty_state_title"
                        style="@style/Heading3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:gravity="center"
                        android:text="@string/no_trx_in_last_30_days"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_empty_state_static" />

                    <TextView
                        android:id="@+id/iv_empty_state_message"
                        style="@style/Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:gravity="center"
                        android:text="@string/no_recent_trx_hint"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_empty_state_title" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/add_payment_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_24dp"
        android:backgroundTint="@color/buku_CTA"
        android:elevation="8dp"
        android:fontFamily="@font/roboto"
        android:text="@string/create_payment"
        android:textStyle="bold"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@mipmap/ic_plus_white_24dp"
        app:iconGravity="start"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/snackbar_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_success_message"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/blue_5"
        android:drawablePadding="@dimen/_4dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_14dp"
        android:textColor="@color/green_60"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/ic_close"
        app:drawableTint="@color/green_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        tools:text="Yey! Pengajuan akses kode QRIS berhasil dikirim. Pengajuanmu akan diproses hingga 7 hari kerja."
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
