<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black_5"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingTop="@dimen/_16dp"
    android:paddingBottom="@dimen/_66dp">

    <View
        android:id="@+id/vw_line"
        android:layout_width="48dp"
        android:layout_height="4dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_line"
        tools:text="Kirim ke 087775598545" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/send_sms_without_saving_contact"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_whatsapp"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_solid_white_corner_12dp"
        android:drawablePadding="@dimen/_8dp"
        android:padding="@dimen/_16dp"
        android:text="@string/whatsapp"
        android:textColor="@color/green"
        app:drawableEndCompat="@drawable/ic_chevron_right_black_20"
        app:drawableStartCompat="@drawable/ic_whatsapp_green"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle" />

    <TextView
        android:id="@+id/tv_sms"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_solid_white_corner_12dp"
        android:drawablePadding="@dimen/_8dp"
        android:padding="@dimen/_16dp"
        android:text="@string/message"
        android:textColor="@color/yellow"
        app:drawableEndCompat="@drawable/ic_chevron_right_black_20"
        app:drawableStartCompat="@drawable/ic_sms_yellow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_whatsapp" />
</androidx.constraintlayout.widget.ConstraintLayout>