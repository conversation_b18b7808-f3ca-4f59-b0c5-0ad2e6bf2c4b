<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <ScrollView
        android:id="@+id/content_scroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="top"
        android:layout_weight="1"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/coordinatorLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/_16dp">

            <ImageView
                android:id="@+id/img_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/login_header"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="@dimen/_0dp"
                android:layout_height="0dp"
                android:background="@drawable/login_header_bg"
                app:layout_constraintBottom_toBottomOf="@id/img_header"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/img_header" />

            <ImageView
                android:id="@+id/logo"
                android:layout_width="72dp"
                android:layout_height="72dp"
                android:background="@drawable/bg_circle"
                android:padding="@dimen/_12dp"
                app:srcCompat="@drawable/app_logo"
                app:layout_constraintBottom_toBottomOf="@id/img_header"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/img_header"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/tv_header"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:text="@string/accounting_app_for_bosses"
                android:textSize="20sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/logo" />

            <include
                android:id="@+id/user_count_layout"
                layout="@layout/user_count_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_header" />


            <TextView
                android:id="@+id/tv_sub_header"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:text="@string/bosses_have_used_bw"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/user_count_layout" />

            <com.hbb20.CountryCodePicker
                android:id="@+id/countryPicker"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_4dp"
                android:background="@drawable/edittext_bg_flat"
                android:paddingStart="@dimen/_4dp"
                android:paddingEnd="@dimen/_4dp"
                app:ccpDialog_allowSearch="false"
                app:ccpDialog_backgroundColor="@color/white"
                app:ccpDialog_textColor="@color/black_60"
                app:ccp_autoDetectCountry="false"
                app:ccp_autoDetectLanguage="true"
                app:ccp_customMasterCountries="@string/countries_prefferred_in_spinner"
                app:ccp_defaultLanguage="INDONESIA"
                app:ccp_defaultNameCode="ID"
                app:ccp_defaultPhoneCode="62"
                app:ccp_excludedCountries="US"
                app:ccp_flagBorderColor="@color/dark_gray"
                app:ccp_showArrow="false"
                app:ccp_showFullName="false"
                app:ccp_showNameCode="false"
                app:ccp_showPhoneCode="true"
                app:layout_constraintBottom_toBottomOf="@+id/mobileNumberInputLayout"
                app:layout_constraintEnd_toStartOf="@id/mobileNumberInputLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/mobileNumberInputLayout" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/mobileNumberInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4dp"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColorHint="@color/lightBlack"
                android:visibility="visible"
                app:boxCornerRadiusBottomEnd="4dp"
                app:boxCornerRadiusBottomStart="4dp"
                app:boxCornerRadiusTopEnd="4dp"
                app:boxCornerRadiusTopStart="4dp"
                app:boxStrokeColor="@color/black_10"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:hintTextAppearance="@style/Heading1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/countryPicker"
                app:layout_constraintTop_toBottomOf="@id/tv_sub_header"
                app:layout_goneMarginTop="@dimen/_16dp"
                app:passwordToggleDrawable="@null">

                <EditText
                    android:id="@+id/phoneET"
                    style="@style/Base.Widget.MaterialComponents.TextInputEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/login_input_mobile_new"
                    android:importantForAutofill="no"
                    android:inputType="number"
                    android:maxLength="14"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black_20"
                    android:textSize="@dimen/text_24sp"
                    android:textStyle="bold" />

                <requestFocus />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/tv_secure"
                style="@style/SubHeading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:drawablePadding="@dimen/_8dp"
                android:gravity="center_vertical"
                android:text="@string/your_data_is_secure"
                android:textColor="@color/black_60"
                app:drawableStartCompat="@drawable/data_aman"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mobileNumberInputLayout" />

            <TextView
                android:id="@+id/warningText"
                style="@style/Body3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/bg_warning_login"
                android:drawablePadding="@dimen/_16dp"
                android:gravity="center_vertical"
                android:paddingVertical="@dimen/_16dp"
                android:paddingStart="@dimen/_8dp"
                android:paddingEnd="@dimen/_8dp"
                android:text="@string/phone_number_length_warning"
                android:visibility="gone"
                app:drawableStartCompat="@drawable/ic_warning_red"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mobileNumberInputLayout"
                tools:visibility="gone"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <CheckBox
        android:id="@+id/tnc_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:paddingHorizontal="@dimen/_6dp"
        android:paddingTop="@dimen/_6dp"
        android:paddingVertical="@dimen/_4dp"
        android:gravity="top"
        android:buttonTint="@color/otp_field_stroke_color"
        style="@style/Body3"
        android:visibility="gone"
        tools:visibility="visible"
        android:textColor="@color/black_80"
        android:text="@string/terms_and_conditions_without_cb"
        />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_next"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:enabled="false"
        android:text="@string/next" />
</LinearLayout>