<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/title"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="textStart"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Title" />

    <TextView
        android:id="@+id/body"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textAlignment="textStart"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:text="long textlong textlong textlong textlong textlong textlong text" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnLeft"
        style="@style/Button.OutlinePrimary.Bold"
        android:layout_width="@dimen/_0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="4dp"
        android:text="@string/cancel"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="@+id/btnRight"
        app:layout_constraintEnd_toStartOf="@id/btnRight"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btnRight" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnRight"
        style="@style/ButtonFill.Blue.Bold"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/save"
        android:textAllCaps="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnLeft"
        app:layout_constraintTop_toBottomOf="@id/body" />

</androidx.constraintlayout.widget.ConstraintLayout>