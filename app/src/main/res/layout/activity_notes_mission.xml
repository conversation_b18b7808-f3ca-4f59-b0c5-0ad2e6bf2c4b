<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.print.NotesMissionActivity">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="@color/design_default_color_primary"
        android:paddingStart="@dimen/_0dp"
        android:paddingEnd="@dimen/_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:titleTextColor="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/design_default_color_primary"
            >

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                android:src="@drawable/ic_back"
                android:background="@color/design_default_color_primary"
                />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/_24dp"
                android:layout_toRightOf="@+id/back_btn"
                android:drawableLeft="@drawable/dot_highlighter"
                android:background="@color/design_default_color_primary"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="@string/mission_details"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>

    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:fillViewport="true"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cv_brick_disconnection_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            >

            <ImageView
                android:id="@+id/iv_banner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_notes_mission" />


            <TextView
                android:id="@+id/tv_notes_mission_desc"
                style="@style/Body2"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_width="@dimen/_0dp"
                android:gravity="start"
                android:text="@string/notes_mission_description"
                android:textColor="@color/black_80"
                android:layout_marginTop="@dimen/_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_banner" />

            <View
                android:id="@+id/divider_top"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_8dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@color/black_0"
                android:visibility="visible"
                app:layout_constraintTop_toBottomOf="@+id/tv_notes_mission_desc" />


            <TextView
                android:id="@+id/mission_title"
                style="@style/Heading3"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_20dp"
                android:text="@string/your_mission"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider_top" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/activationStep1Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/mission_title">

                <ImageView
                    android:id="@+id/icon_status_ok"
                    android:layout_width="@dimen/_36dp"
                    android:layout_height="@dimen/_36dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:adjustViewBounds="true"
                    android:contentDescription="@null"
                    android:src="@drawable/ic_checkbox_fill"
                    android:visibility="invisible"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/icon_status_pending1"
                    android:layout_width="@dimen/_36dp"
                    android:layout_height="@dimen/_36dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:adjustViewBounds="true"
                    android:background="@drawable/circle_gray_category"
                    android:backgroundTint="@color/blue_10"
                    android:contentDescription="@null"
                    android:gravity="center"
                    android:text="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/view_pending_status_timeline1"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:background="@drawable/bg_timeline_dashed_line"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@id/icon_status_pending1"
                    app:layout_constraintStart_toStartOf="@id/icon_status_pending1"
                    app:layout_constraintTop_toBottomOf="@id/icon_status_pending1" />


                <TextView
                    android:id="@+id/txt_label_status_ok"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/connect_bank_account_and_ewallet"
                    android:textColor="@color/black_40"
                    android:textSize="@dimen/text_12sp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/icon_status_ok"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/first_step_layout"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/icon_status_pending1"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/txt_label_status_pending1"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/txt_status_pending1"
                        style="@style/Heading3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:text="@string/complete_your_profile"
                        android:textColor="@color/black_80"
                        android:textSize="@dimen/text_14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="@id/txt_label_status_pending1"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_goneMarginBottom="@dimen/_8dp"
                        />

                    <TextView
                        android:id="@+id/txt_additional_info1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/fill_profile_mission_message"
                        android:textColor="@color/black_60"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@id/txt_label_status_pending1"
                        app:layout_constraintTop_toBottomOf="@id/txt_status_pending1" />


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/mb_complete_profile"
                        style="@style/ButtonOutline.Blue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/complete_profile"
                        android:textAllCaps="false"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/txt_additional_info1" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_20dp"
                        app:layout_constraintTop_toBottomOf="@id/mb_complete_profile" />

                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/activationStep2Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/activationStep1Layout">

                <ImageView
                    android:id="@+id/icon_status_ok_2"
                    android:layout_width="@dimen/_36dp"
                    android:layout_height="@dimen/_36dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:adjustViewBounds="true"
                    android:contentDescription="@null"
                    android:src="@drawable/ic_checkbox_fill"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/icon_status_pending2"
                    android:layout_width="@dimen/_36dp"
                    android:layout_height="@dimen/_36dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:adjustViewBounds="true"
                    android:background="@drawable/circle_hollow_blue"
                    android:backgroundTint="@color/blue_10"
                    android:contentDescription="@null"
                    android:gravity="center"
                    android:text="2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/view_pending_status_timeline2"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:background="@drawable/bg_timeline_dashed_line"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@id/icon_status_pending2"
                    app:layout_constraintStart_toStartOf="@id/icon_status_pending2"
                    app:layout_constraintTop_toBottomOf="@id/icon_status_pending2" />

                <TextView
                    android:id="@+id/txt_label_status_pending2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:fontFamily="@font/roboto"
                    app:layout_constraintBottom_toBottomOf="@id/icon_status_pending2"
                    app:layout_constraintStart_toEndOf="@id/icon_status_pending2"
                    app:layout_constraintTop_toTopOf="@id/icon_status_pending2" />

                <TextView
                    android:id="@+id/txt_status_pending2"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/fill_business_profile_mission_title"
                    android:textColor="@color/black_80"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/txt_label_status_pending2"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/txt_additional_info2"
                    style="@style/Body3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/fill_business_profile_mission_message"
                    android:textColor="@color/black_80"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/txt_label_status_pending2"
                    app:layout_constraintTop_toBottomOf="@id/txt_status_pending2" />




                <com.google.android.material.button.MaterialButton
                    android:id="@+id/activate_now"
                    style="@style/ButtonOutline.Blue"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12dp"
                    android:text="@string/activate_now"
                    android:textAllCaps="false"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/txt_additional_info2"
                    app:layout_constraintTop_toBottomOf="@+id/txt_additional_info2" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_30dp"
                    app:layout_constraintTop_toBottomOf="@id/activate_now" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rewardStepLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/activationStep2Layout">


                <ImageView
                    android:id="@+id/icon_reward"
                    android:layout_width="@dimen/_36dp"
                    android:layout_height="@dimen/_36dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:adjustViewBounds="true"
                    android:background="@drawable/circle_gray_category"
                    android:contentDescription="@null"
                    android:gravity="center"
                    android:padding="@dimen/_2dp"
                    android:src="@drawable/ic_reward_inactive"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/reward_title"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_14dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/mission_reward_title"
                    android:textColor="@color/black_80"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/icon_reward"
                    app:layout_constraintTop_toTopOf="@id/icon_reward"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginBottom="@dimen/_50dp"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>



</androidx.constraintlayout.widget.ConstraintLayout>