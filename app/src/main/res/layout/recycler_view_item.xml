<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

  <ImageView
      android:id="@+id/imageView2"
      android:layout_width="50dp"
      android:layout_height="50dp"
      tools:src="@tools:sample/avatars"
      app:layout_constraintStart_toStartOf="parent"
      android:layout_marginStart="8dp"
      app:layout_constraintTop_toTopOf="parent"
      android:layout_marginTop="8dp" />

  <TextView
      android:id="@+id/textView"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      tools:text="@tools:sample/full_names"
      android:textSize="20sp"
      android:textColor="@android:color/black"
      app:layout_constraintTop_toTopOf="@+id/imageView2"
      app:layout_constraintStart_toEndOf="@+id/imageView2"
      android:layout_marginStart="8dp"
      android:layout_marginBottom="8dp"
      app:layout_constraintBottom_toTopOf="@+id/textView2" />

  <TextView
      android:id="@+id/textView2"
      android:layout_width="285dp"
      android:layout_height="20dp"
      tools:text="@tools:sample/lorem[4:10]"
      app:layout_constraintBottom_toBottomOf="@+id/imageView2"
      app:layout_constraintStart_toEndOf="@+id/imageView2"
      android:layout_marginStart="8dp"
      app:layout_constraintEnd_toEndOf="parent"
      android:layout_marginEnd="8dp"
      app:layout_constraintHorizontal_bias="0.050" />

  <TextView
      android:id="@+id/textView3"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      tools:text="@tools:sample/date/hhmm"
      app:layout_constraintTop_toTopOf="@+id/imageView2"
      app:layout_constraintEnd_toEndOf="parent"
      android:layout_marginEnd="8dp" />
</androidx.constraintlayout.widget.ConstraintLayout>