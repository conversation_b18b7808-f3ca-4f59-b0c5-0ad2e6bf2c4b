<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    tools:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/_8dp">

    <AutoCompleteTextView
        android:id="@+id/productNameET"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/product_edittext_bg"
        android:hint="Nama produk"
        android:textColorHint="#8D8D8D"
        android:layout_weight="1"/>

    <EditText
        android:id="@+id/productCountET"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="2"
        android:layout_marginStart="12dp"
        tools:text="0"
        android:hint="Jumlah"
        android:inputType="number"
        android:background="@drawable/product_edittext_bg"
        android:textAlignment="center"
        android:textSize="16sp"
        android:textColor="#222222"/>

</LinearLayout>