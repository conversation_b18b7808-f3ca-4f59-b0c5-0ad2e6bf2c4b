<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        tools:showIn="@layout/activity_cash_transaction_entry">

        <!--Width ratio is 3:1:2-->

        <TextView
            android:id="@+id/tv_product_lable"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:text="@string/barang"
            android:textAlignment="viewStart"
            android:textColor="@color/black_60" />

        <TextView
            android:id="@+id/tv_qty_lable"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/qty_ina"
            android:textAlignment="viewEnd"
            android:textColor="@color/black_60" />

        <!--could be selling or buying price-->
        <TextView
            android:id="@+id/tv_price"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:text="@string/selling_price"
            android:textAlignment="viewEnd"
            android:textColor="@color/black_60" />

    </LinearLayout>
</layout>