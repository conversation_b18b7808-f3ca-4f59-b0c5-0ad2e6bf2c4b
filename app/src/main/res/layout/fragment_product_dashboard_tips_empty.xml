<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cv_home"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginTop="@dimen/_10dp"
    android:layout_marginEnd="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="6dp">


    <TextView
        android:id="@+id/tv_business_tips"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/tips_juragan_buku_warung"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"

        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="6dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_business_tips">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_business_heading"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/fitur_bikin_untung_yg_harus_pakai_di_buku_warung"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/ppop_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
               android:visibility="gone"
                android:background="@color/black_5"
                app:layout_constraintTop_toBottomOf="@id/tv_business_heading" />


            <include
                android:visibility="gone"
                android:id="@+id/ppop_tip"
                layout="@layout/business_dashboard_empty_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5dp"
                app:layout_constraintTop_toBottomOf="@+id/utang_divider" />

            <View
                android:visibility="gone"

                android:id="@+id/payment_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"

                android:background="@color/black_5"
                app:layout_constraintTop_toBottomOf="@id/tv_business_heading" />


            <include
                android:visibility="gone"

                android:id="@+id/payment_tip"
                layout="@layout/business_dashboard_empty_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5dp"
                app:layout_constraintTop_toBottomOf="@+id/utang_divider" />


            <View
                android:id="@+id/transaction_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                android:visibility="gone"

                android:background="@color/black_5"
                app:layout_constraintTop_toBottomOf="@id/tv_business_heading" />


            <include
                android:visibility="gone"

                android:id="@+id/transaction_tip"
                layout="@layout/business_dashboard_empty_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5dp"
                app:layout_constraintTop_toBottomOf="@+id/utang_divider" />

            <View
                android:id="@+id/utang_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                android:visibility="gone"

                android:background="@color/black_5"
                app:layout_constraintTop_toBottomOf="@id/tv_business_heading" />


            <include
                android:visibility="gone"

                android:id="@+id/utang_tip"
                layout="@layout/business_dashboard_empty_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5dp"
                app:layout_constraintTop_toBottomOf="@+id/utang_divider" />

        </LinearLayout>


    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>

