<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvNoFilterResult"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="98dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/empty_trx_with_filter"
        android:textSize="14sp" />
</FrameLayout>