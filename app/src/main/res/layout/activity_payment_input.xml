<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_5">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="@dimen/dimen_0dp"
        app:layout_constraintBottom_toTopOf="@+id/ll_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.bukuwarung.payments.widget.PaymentDetailView
                android:id="@+id/pdv_view"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/ll_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@drawable/bg_rounded_rectangle_white_8dp"
                android:orientation="vertical"
                android:paddingBottom="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/pdv_view">

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:text="@string/enter_amount_out_message"
                    android:textColor="@color/black_40" />

                <com.bukuwarung.baseui.CurrencyEditText
                    android:id="@+id/et_amount"
                    style="@style/Heading1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@color/white"
                    android:gravity="end"
                    android:hint="@string/zero_amount"
                    android:imeOptions="actionDone"
                    android:inputType="number"
                    android:paddingTop="@dimen/_6dp"
                    android:paddingBottom="@dimen/_6dp"
                    android:singleLine="true"
                    android:text="@string/currency"
                    app:drawableLeftCompat="@drawable/ic_arrow_blue_bg_top_right" />

                <View
                    android:id="@+id/vw_divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_4dp"
                    android:background="@color/black_10" />

                <TextView
                    android:id="@+id/tv_amount_error"
                    style="@style/Label1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:text="@string/error_minimum_payment_limit"
                    android:textColor="@color/red_80"
                    android:visibility="gone" />
            </LinearLayout>

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/ba_warning"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ll_amount"
                app:type="warning" />

            <include
                android:id="@+id/include_payment_categories"
                layout="@layout/payment_categories_layout"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ba_warning" />

            <TextView
                android:id="@+id/tv_category_error_message"
                style="@style/Label1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/payment_category_error_message"
                android:textColor="@color/red_80"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@id/include_payment_categories"
                app:layout_constraintTop_toBottomOf="@id/include_payment_categories" />

            <TextView
                android:id="@+id/tv_info"
                style="@style/Body3"
                android:layout_width="@dimen/dimen_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_corner_8dp_stroke_black10"
                android:drawablePadding="@dimen/_10dp"
                android:padding="@dimen/_8dp"
                android:text="@string/payment_out_info"
                android:textColor="@color/black_60"
                app:drawableStartCompat="@drawable/ic_alert_info"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_category_error_message" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/ll_button"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_continue"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_18dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_18dp"
            android:text="@string/next"
            android:textAllCaps="false"
            app:cornerRadius="@dimen/_4dp"
            app:rippleColor="@color/black_40" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>