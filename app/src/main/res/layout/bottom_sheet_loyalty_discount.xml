<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingTop="@dimen/_10dp"
    android:paddingBottom="@dimen/_20dp">

    <View
        android:id="@+id/vw_close_bar"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:background="@drawable/background_filled_black10_border_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_close_bar" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_understand"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/understand"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="0dp"
        android:visibility="gone"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@+id/vw_close_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
