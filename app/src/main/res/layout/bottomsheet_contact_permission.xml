<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/_16dp">

        <ImageView
            android:id="@+id/iv_contact"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_contact_permission" />

        <TextView
            android:id="@+id/title"
            style="@style/Heading2"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/contact_permission_title_new"
            android:textColor="@color/black_60"
            app:layout_constraintBottom_toBottomOf="@id/iv_contact"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_contact"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_1"
            style="@style/Body2"
            android:layout_width="@dimen/dimen_28dp"
            android:layout_height="@dimen/dimen_28dp"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@drawable/bg_circle_blue"
            android:gravity="center"
            android:text="@string/numeric_one"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_contact" />

        <TextView
            android:id="@+id/txt_desc_1"
            style="@style/Body1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_2dp"
            android:text="@string/contact_permission_point1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/txt_1"
            app:layout_constraintTop_toTopOf="@id/txt_1" />

        <TextView
            android:id="@+id/txt_2"
            style="@style/Body2"
            android:layout_width="@dimen/dimen_28dp"
            android:layout_height="@dimen/dimen_28dp"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@drawable/bg_circle_blue"
            android:gravity="center"
            android:text="@string/numeric_two"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_desc_1" />

        <TextView
            android:id="@+id/txt_desc_2"
            style="@style/Body1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_2dp"
            android:text="@string/contact_permission_point2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/txt_2"
            app:layout_constraintTop_toTopOf="@id/txt_2" />

        <TextView
            android:id="@+id/txt_3"
            style="@style/Body2"
            android:layout_width="@dimen/dimen_28dp"
            android:layout_height="@dimen/dimen_28dp"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@drawable/bg_circle_blue"
            android:gravity="center"
            android:text="@string/numeric_three"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_desc_2" />

        <TextView
            android:id="@+id/txt_desc_3"
            style="@style/Body1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_2dp"
            android:text="@string/contact_permission_point3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/txt_3"
            app:layout_constraintTop_toTopOf="@id/txt_3" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_reject"
            style="@style/ButtonOutline"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:text="@string/reject"
            android:textAllCaps="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_allow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_desc_3" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_allow"
            style="@style/ButtonFill"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:text="@string/allow"
            android:textAllCaps="true"
            app:layout_constraintBottom_toBottomOf="@id/btn_reject"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_reject"
            app:layout_constraintTop_toTopOf="@id/btn_reject" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
