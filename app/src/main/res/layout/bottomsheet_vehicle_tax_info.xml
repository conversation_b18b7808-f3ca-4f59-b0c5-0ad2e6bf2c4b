<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingTop="@dimen/_10dp"
    android:paddingBottom="@dimen/_16dp">

    <View
        android:id="@+id/vw_line"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:layout_marginTop="@dimen/_10dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_heading"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_line"
        tools:text="Panduan Pembayaran E-Samsat JATIM" />

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/tv_heading" />

    <TextView
        android:id="@+id/tv_body"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_image" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_close"
        style="@style/DefaultMaterialButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_36dp"
        android:fontFamily="@font/roboto_bold"
        android:letterSpacing="0.05"
        android:paddingStart="@dimen/_6dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_6dp"
        android:paddingBottom="@dimen/_8dp"
        android:text="@string/okay_got_it"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/tv_body" />
</androidx.constraintlayout.widget.ConstraintLayout>