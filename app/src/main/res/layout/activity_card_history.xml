<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/toolbar"
        layout="@layout/widget_toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tlHistory"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:theme="@style/TabLayout_Theme"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:tabSelectedTextColor="@color/blue_60"
        app:tabTextAppearance="@style/FilterTabLayoutTextAppearance"
        app:tabTextColor="@color/black_40" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFiltersContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black_5"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp"
        app:layout_constraintTop_toBottomOf="@id/tlHistory">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClearFilter"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/hsvFilters"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/vector_filter_history"
            tools:visibility="visible" />

        <HorizontalScrollView
            android:id="@+id/hsvFilters"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ivClearFilter"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvFilterDate"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/filter_button_selector"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:padding="10dp"
                    android:text="@string/select_date"
                    app:drawableEndCompat="@drawable/vector_down_grey" />

                <TextView
                    android:id="@+id/tvSort"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/filter_button_selector"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:padding="10dp"
                    android:text="@string/sort"
                    app:drawableEndCompat="@drawable/vector_down_grey" />

            </LinearLayout>
        </HorizontalScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clFiltersContainer" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvHistory"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingBottom="60dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clFiltersContainer"
        tools:listitem="@layout/item_list_card_history" />

    <include
        android:id="@+id/layoutEmptyView"
        layout="@layout/layout_empty_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clFiltersContainer" />

    <ProgressBar
        android:id="@+id/pbLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>