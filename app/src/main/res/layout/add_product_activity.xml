<?xml version="1.0" encoding="utf-8"?>

<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/add_product_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/back"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/screenTitle"
                    style="@style/Heading2"
                    android:text="@string/add_product_title"
                    android:textColor="@color/white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/back"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginLeft="28dp" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingTop="@dimen/_12dp"
            android:paddingBottom="@dimen/_12dp"
            android:visibility="gone"
            android:background="@drawable/bottomsheet_rounded">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:gravity="center"
                android:textSize="@dimen/text_18sp"
                android:layout_marginStart="@dimen/_12dp"
                android:textColor="@color/black_80"
                android:layout_alignParentStart="true"
                android:text="@string/add_product_title"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/_12dp"
                android:paddingBottom="@dimen/_10dp"
                android:paddingTop="@dimen/_5dp"
                android:paddingStart="@dimen/_10dp"
                android:src="@drawable/close"/>

        </RelativeLayout>
        <FrameLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
