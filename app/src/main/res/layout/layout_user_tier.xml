<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_home"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/background_user_tier">

    <include
        android:id="@+id/layout_membership_status"
        layout="@layout/layout_user_tier_item"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/vw_divider"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/layout_membership_status"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="0dp"
        android:background="#CCE9FF" />

    <include
        android:id="@+id/layout_points"
        layout="@layout/layout_user_tier_item"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/vw_divider"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

</androidx.constraintlayout.widget.ConstraintLayout>