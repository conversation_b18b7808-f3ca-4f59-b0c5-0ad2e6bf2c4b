<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/parent_container"
    android:padding="@dimen/_0dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rl_category"
        android:layout_width="@dimen/_36dp"
        android:layout_height="@dimen/_36dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/circle_gray_category"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_category"
            android:layout_width="@dimen/_20dp"
            android:layout_height="@dimen/_20dp"
            android:layout_centerInParent="true"
            app:srcCompat="@drawable/ic_camera" />

        <ImageView
            android:id="@+id/iv_category_selected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:visibility="gone"
            app:srcCompat="@drawable/category_selected_icon" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tv_category"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:gravity="start"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintBottom_toBottomOf="@+id/rl_category"
        app:layout_constraintEnd_toStartOf="@+id/selectCategoryRadioButton"
        app:layout_constraintStart_toEndOf="@id/rl_category"
        app:layout_constraintTop_toTopOf="@+id/rl_category"
        tools:text="@string/category_name" />

    <androidx.appcompat.widget.AppCompatRadioButton
        android:id="@+id/selectCategoryRadioButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_category"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_category" />

    <View
        android:id="@+id/vw_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@+id/rl_category"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>