<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardCornerRadius="@dimen/_8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/_20dp">

        <TextView
            android:id="@+id/input_number_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/captcha_confirmation"
            android:textColor="@color/black80"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/close_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/close_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black80" />

        <androidx.cardview.widget.CardView
            android:id="@+id/captcha_image_frame"
            android:layout_width="0dp"
            android:layout_height="@dimen/_48dp"
            android:layout_marginTop="@dimen/_16dp"
            app:cardCornerRadius="@dimen/_8dp"
            app:cardElevation="@dimen/_0dp"
            app:layout_constraintEnd_toStartOf="@id/restart_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/input_number_text">

            <ImageView
                android:id="@+id/captcha_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY" />

        </androidx.cardview.widget.CardView>

        <ProgressBar
            android:id="@+id/loading_bar"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            app:layout_constraintBottom_toBottomOf="@id/captcha_image_frame"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/captcha_image_frame" />

        <ImageView
            android:id="@+id/restart_icon"
            android:layout_width="32dp"
            android:layout_height="@dimen/_32dp"
            android:layout_marginStart="@dimen/_16dp"
            android:src="@drawable/ic_refresh"
            app:layout_constraintBottom_toBottomOf="@id/captcha_image_frame"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/captcha_image_frame"
            app:layout_constraintTop_toTopOf="@id/captcha_image_frame" />

        <TextView
            android:id="@+id/input_number_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/captcha_input"
            android:textColor="@color/black60"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/captcha_image_frame" />

        <com.bukuwarung.widget.SelfClearFocusEditText
            android:id="@+id/digit_one"
            style="@style/Base.Widget.MaterialComponents.TextInputEditText"
            android:layout_width="42dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@drawable/numeric_captcha_unfocused"
            android:inputType="number"
            android:maxLength="2"
            android:textAlignment="center"
            android:textSize="@dimen/text_24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/digit_two"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/input_number_description" />

        <com.bukuwarung.widget.SelfClearFocusEditText
            android:id="@+id/digit_two"
            style="@style/Base.Widget.MaterialComponents.TextInputEditText"
            android:layout_width="42dp"
            android:layout_height="wrap_content"
            android:background="@drawable/numeric_captcha_unfocused"
            android:inputType="number"
            android:maxLength="2"
            android:textAlignment="center"
            android:textSize="@dimen/text_24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/digit_three"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@id/digit_one"
            app:layout_constraintTop_toTopOf="@id/digit_one" />

        <com.bukuwarung.widget.SelfClearFocusEditText
            android:id="@+id/digit_three"
            style="@style/Base.Widget.MaterialComponents.TextInputEditText"
            android:layout_width="42dp"
            android:layout_height="wrap_content"
            android:background="@drawable/numeric_captcha_unfocused"
            android:inputType="number"
            android:maxLength="2"
            android:textAlignment="center"
            android:textSize="@dimen/text_24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/digit_four"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@id/digit_two"
            app:layout_constraintTop_toTopOf="@id/digit_one" />

        <com.bukuwarung.widget.SelfClearFocusEditText
            android:id="@+id/digit_four"
            style="@style/Base.Widget.MaterialComponents.TextInputEditText"
            android:layout_width="42dp"
            android:layout_height="wrap_content"
            android:background="@drawable/numeric_captcha_unfocused"
            android:inputType="number"
            android:maxLength="2"
            android:textAlignment="center"
            android:textSize="@dimen/text_24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/digit_five"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@id/digit_three"
            app:layout_constraintTop_toTopOf="@id/digit_one" />

        <com.bukuwarung.widget.SelfClearFocusEditText
            android:id="@+id/digit_five"
            style="@style/Base.Widget.MaterialComponents.TextInputEditText"
            android:layout_width="42dp"
            android:layout_height="wrap_content"
            android:background="@drawable/numeric_captcha_unfocused"
            android:inputType="number"
            android:maxLength="2"
            android:textAlignment="center"
            android:textColor="@color/blue_80"
            android:textSize="@dimen/text_24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@id/digit_four"
            app:layout_constraintTop_toTopOf="@id/digit_one" />

        <TextView
            android:id="@+id/error_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:textColor="@color/red80"
            android:textSize="@dimen/text_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/digit_one" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/next_button"
            style="@style/ButtonFill"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:enabled="false"
            android:text="@string/next"
            app:layout_constraintEnd_toStartOf="@id/verifying_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/error_text" />

        <ProgressBar
            android:id="@+id/verifying_bar"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:layout_marginStart="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="@id/next_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/next_button"
            app:layout_constraintTop_toTopOf="@id/next_button" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
