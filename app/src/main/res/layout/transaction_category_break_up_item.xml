<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_4dp"
    android:paddingVertical="@dimen/_2dp"
    >

    <FrameLayout
        android:id="@+id/bg"
        style="@style/Heading3"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:background="@drawable/bg_rounded_blue80"
        android:paddingHorizontal="@dimen/_4dp"
        android:paddingVertical="@dimen/_2dp"
        android:textAllCaps="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_category_name"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintEnd_toStartOf="@+id/tv_number_of_transactions"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toTopOf="@+id/bg"
        app:layout_constraintVertical_chainStyle="spread"
        tools:text="Penjualan" />

    <TextView
        android:id="@+id/tv_total_transaction_currency_txt"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_4dp"
        android:ellipsize="end"
        android:text="@string/currency"
        android:textColor="@color/green_100"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_category_name" />

    <TextView
        android:id="@+id/tv_total_transaction_value"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLength="20"
        android:maxLines="2"
        android:textColor="@color/black40"
        app:layout_constraintBottom_toBottomOf="@+id/tv_number_of_transactions"
        app:layout_constraintStart_toStartOf="@+id/tv_category_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_category_name"
        tools:text="0.000" />


    <TextView
        android:id="@+id/tv_subtext"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:textColor="@color/out_red"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/bg"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toBottomOf="@id/tv_category_name"
        tools:text="" />


    <TextView
        android:id="@+id/tv_percentage"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:maxLength="14"
        android:textColor="@color/black40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg"
        tools:text="25%" />

    <TextView
        android:id="@+id/tv_number_of_transactions"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:maxLength="14"
        android:textColor="@color/black40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_percentage"
        tools:text="120 Transaksi" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_4dp"
        android:background="@color/black_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_total_transaction_value" />

</androidx.constraintlayout.widget.ConstraintLayout>
