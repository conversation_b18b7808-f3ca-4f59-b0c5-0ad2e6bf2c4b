<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/entriesLayout"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:layout_marginTop="8dp"
    android:foreground="?attr/selectableItemBackground"
    android:background="?attr/selectableItemBackground">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <ImageView
            android:id="@+id/nav_icon"
            android:layout_width="26dp"
            android:layout_height="28dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="8dp"
            android:background="?attr/selectableItemBackground"
            android:tint="@color/heading_text"
            app:srcCompat="@drawable/ic_business_big" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="8dp"
            android:layout_toStartOf="@+id/iv_daily_update_icon"
            android:layout_toEndOf="@+id/nav_icon"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:maxLines="1"
            android:textColor="#4a4a4a"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="test" />

        <ImageView
            android:id="@+id/forward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_margin="16dp"
            android:layout_toStartOf="@+id/iv_daily_update_icon"
            android:background="@drawable/oval_0"
            android:backgroundTint="@color/subheader"
            android:scaleType="center"
            android:tint="@color/subheader" />

        <ImageView
            android:id="@+id/iv_daily_update_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/_16dp"
            app:srcCompat="@drawable/ic_daily_update_icon"
            />
    </RelativeLayout>

</FrameLayout>
