<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_product_header"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!--Width ratio is 3:1:2-->

    <TextView
        android:id="@+id/tv_product_name"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:textAlignment="viewStart"
        app:layout_constraintEnd_toStartOf="@id/tv_product_count"
        app:layout_constraintHorizontal_weight="3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingBottom="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingStart="@dimen/_16dp"
        tools:text="Beras" />

    <TextView
        android:id="@+id/tv_product_count"
        style="@style/Body2"
        android:layout_width="0dp"
        android:textAlignment="viewEnd"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/tv_product_price"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/tv_product_name"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingTop="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp"
        tools:text="2,5" />

    <!--could be selling or buying price-->
    <TextView
        android:id="@+id/tv_product_price"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewEnd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="2"
        app:layout_constraintStart_toEndOf="@id/tv_product_count"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingTop="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_16dp"
        tools:text="125.000" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/black_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>