<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/root_layout"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/alice_blue">
    <ImageView
        android:id="@+id/moneyBoxImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="2dp"
        android:layout_marginStart="@dimen/_16dp"
        app:srcCompat="@drawable/ic_save_money"/>
    <TextView
        android:id="@+id/moneySavedMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/moneyBoxImage"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:text="@string/payment_message_info"
        android:textColor="@color/blue_60"
        style="@style/Body3"/>
</androidx.constraintlayout.widget.ConstraintLayout>