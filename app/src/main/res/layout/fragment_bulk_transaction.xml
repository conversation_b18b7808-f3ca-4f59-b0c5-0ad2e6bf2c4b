<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_parent"
    android:background="@color/black_5"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/ly_aggregate"
        layout="@layout/bulk_transaction_aggregate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginBottom="@dimen/_10dp" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_2dp"
        android:background="@color/black_10"
        app:layout_constraintTop_toBottomOf="@id/ly_aggregate" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_bulk_transaksi"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/btn_tambah_transaksi"
        app:layout_constraintTop_toBottomOf="@+id/ly_aggregate" />

    <include
        android:id="@+id/ly_empty_state"
        layout="@layout/empty_state_bulk_transaksi"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_tambah_transaksi"
        app:layout_constraintTop_toBottomOf="@+id/ly_aggregate"
        android:visibility="gone"
        />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_tambah_transaksi"
        style="@style/ButtonOutline.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/btn_add_cash_transaction"
        app:icon="@drawable/ic_plus_blue"
        app:layout_constraintBottom_toTopOf="@id/btn_keyboard_barrier"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/btn_keyboard_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="custom_keyboard,btn_simpan_container" />


    <FrameLayout
        android:id="@+id/btn_simpan_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save_bulk_transaksi"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="@string/simpan_semua" />
    </FrameLayout>

    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/custom_keyboard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>