<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:paddingStart="@dimen/_18dp"
    android:paddingEnd="@dimen/_18dp"
    android:paddingBottom="@dimen/_18dp"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_bg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            app:srcCompat="@drawable/category_unselected_background"
            app:layout_constraintDimensionRatio="H, 1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/category_selected_background" />

        <ImageView
            android:id="@+id/iv_category"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_0dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_bg"
            app:layout_constraintDimensionRatio="H, 1:1"
            app:layout_constraintEnd_toEndOf="@id/guide_image_vertical_end"
            app:layout_constraintStart_toStartOf="@id/guide_image_vertical_start"
            app:layout_constraintTop_toTopOf="@id/iv_bg"
            tools:src="@drawable/app_logo" />

        <ImageView
            android:id="@+id/iv_selected"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_0dp"
            app:srcCompat="@drawable/category_selected_icon"
            android:visibility="gone"
            app:layout_constraintDimensionRatio="H, 1:1"
            app:layout_constraintEnd_toEndOf="@id/iv_bg"
            app:layout_constraintStart_toEndOf="@id/guide_image_vertical_end"
            app:layout_constraintTop_toBottomOf="@id/iv_category"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_image_vertical_start"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.25" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_image_vertical_end"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.75" />

        <TextView
            android:id="@+id/tv_category_name"
            style="@style/Body3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:textAlignment="center"
            app:layout_constraintTop_toBottomOf="@id/iv_bg"
            tools:text="BukuWarung" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>