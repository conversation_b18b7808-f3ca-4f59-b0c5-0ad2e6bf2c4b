<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingBottom="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btn_close"
        android:layout_width="@dimen/_12dp"
        android:layout_height="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/close" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_save_account"
        android:layout_width="@dimen/_160dp"
        android:layout_height="@dimen/_160dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_close"
        app:srcCompat="@drawable/qris_deletion_image" />

    <TextView
        android:id="@+id/tv_info_header"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:text="@string/qris_book_deletion_error_title"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_save_account" />

    <TextView
        android:id="@+id/tv_info_body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_40dp"
        android:layout_marginTop="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_40dp"
        android:text="@string/qris_book_deletion_error_message"
        android:textAlignment="center"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_info_header" />

    <TextView
        android:id="@+id/btn_return"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_50dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_50dp"
        android:layout_marginBottom="@dimen/_32dp"
        android:background="@drawable/round_corner_blue_border_rectangle"
        android:paddingStart="@dimen/_32dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_32dp"
        android:paddingBottom="@dimen/_8dp"
        android:text="@string/back"
        android:textAlignment="center"
        android:textColor="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_info_body" />

</androidx.constraintlayout.widget.ConstraintLayout>