<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_empty_view"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:src="@drawable/bulk_empty"
        android:layout_marginTop="@dimen/dimen_24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_middle_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_middle_text"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Belum ada catatan transaksi, nih"
        android:fontFamily="@font/roboto_bold"
        android:textSize="@dimen/text_16sp"
        android:layout_marginTop="@dimen/dimen_24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent=".8"
        />
    
    <TextView
        android:id="@+id/tv_additional_text"
        android:text="@string/empty_bulk_description"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_middle_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.8" />

</androidx.constraintlayout.widget.ConstraintLayout>