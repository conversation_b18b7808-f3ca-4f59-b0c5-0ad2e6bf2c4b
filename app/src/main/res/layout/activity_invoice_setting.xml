<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activities.invoice.InvoiceSettingActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="@color/colorPrimary"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/invoice_setting"
                android:textColor="@color/white" />

            <ProgressBar
                android:id="@+id/location_loading"
                android:layout_width="?attr/actionBarSize"
                android:layout_height="?attr/actionBarSize"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:indeterminate="true"
                android:indeterminateTint="@color/white"
                android:visibility="gone"
                android:padding="@dimen/_16dp"
                tools:visibility="visible" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/fl_button"
        android:scrollbars="none"
        android:layout_below="@id/tb">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:id="@+id/main_container"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                >

                <com.bukuwarung.activities.print.CashTransactionOldReceipt
                    android:id="@+id/cash_receipt"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.bukuwarung.activities.print.CashTransactionReceipt
                    android:visibility="gone"
                    android:id="@+id/cash_receipt_new"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <View
                    style="@style/Divider.Black5"
                    android:background="@color/black_20" />

                <TextView
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/content_preference_label" />

                <FrameLayout
                    android:id="@+id/logo_frame"
                    android:layout_width="72dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:background="@drawable/frame_rounded_black_10_solid">

                    <TextView
                        android:id="@+id/tv_upload_logo"
                        style="@style/Label1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:drawablePadding="@dimen/_8dp"
                        android:text="@string/upload_logo"
                        android:textColor="@color/black_40"
                        android:textSize="10sp"
                        android:visibility="visible"
                        app:drawableTint="@color/black_40"
                        app:drawableTopCompat="@drawable/ic_akar_icons_image"
                        tools:ignore="SmallSp" />

                    <ImageView
                        android:id="@+id/img_logo"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/_8dp"
                        android:scaleType="centerCrop"
                        android:visibility="gone"
                        tools:src="@drawable/app_logo" />

                </FrameLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_address"
                    style="@style/OutlineTextInputStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:hintEnabled="false"
                    app:endIconDrawable="@drawable/ic_right"
                    app:endIconTint="@color/black_60"
                    app:startIconDrawable="@drawable/location_icon"
                    app:startIconTint="@color/black_60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/ic_chevron_right_black_60"
                        android:ellipsize="end"
                        android:focusable="false"
                        android:hint="@string/fill_address"
                        android:imeOptions="actionDone"
                        android:inputType="textCapWords"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_phone"
                    style="@style/OutlineTextInputStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_16dp"
                    app:passwordToggleDrawable="@null"
                    app:hintEnabled="false"
                    app:startIconDrawable="@drawable/ic_call_blue"
                    app:startIconTint="@color/black_60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/fill_phone_number"
                        android:inputType="phone"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@+id/main_container"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:paddingHorizontal="@dimen/_14dp"
                android:id="@+id/nota_setting_layout"
                >



                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:background="@drawable/rounded_grey_rect_4dp"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:id="@+id/bukuwarung_watermark_setting_layout"
                    >

                    <TextView
                        android:id="@+id/tv_logo_removal_title"
                        style="@style/Heading3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_16dp"
                        android:text="@string/note_logo_settings"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_logo_removal_message"
                        style="@style/Body3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingBottom="@dimen/_16dp"
                        android:text="@string/your_note_logo_is_active"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_logo_removal_title" />


                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/watermark_switch_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:background="@drawable/rounded_grey_rect_4dp"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:id="@+id/bukuwarung_watermark_setting_overlay"
                    android:alpha="0.6"
                    tools:visibility="gone"
                    >

                    <TextView
                        android:id="@+id/tv_logo_removal_title2"
                        style="@style/Heading3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_16dp"
                        android:visibility="invisible"
                        tools:text="@string/note_logo_settings"
                        android:text="@string/logo_bukuwarung"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_logo_removal_message2"
                        style="@style/Body3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingBottom="@dimen/_16dp"
                        android:visibility="invisible"
                        android:text="@string/logo_bukuwarung_deactivate"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_logo_removal_title2" />


                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/watermark_switch_btn2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/bukuwarung_watermark_setting_layout"
                    android:background="@drawable/bg_rounded_rectangle_blue_5"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:paddingVertical="@dimen/_12dp"
                    android:id="@+id/watermark_settings_info_layout"
                    >

                    <TextView
                        android:id="@+id/tv_info"
                        style="@style/Body3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/want_to_use_this_feature_watermark"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        />


                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.constraintlayout.widget.ConstraintLayout>



    </ScrollView>

    <ProgressBar
        android:id="@+id/pb_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        tools:visibility="visible"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/fl_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white">

        <View
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_gravity="top"
            android:background="@color/black_5" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:text="@string/save"
            android:textAllCaps="false" />

    </FrameLayout>

</RelativeLayout>