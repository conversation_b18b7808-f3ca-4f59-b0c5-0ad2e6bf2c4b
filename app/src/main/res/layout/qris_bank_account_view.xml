<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bank_account_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/blue_5"
    android:paddingTop="@dimen/_15dp"
    app:layout_constraintTop_toBottomOf="@id/add_bank_layout">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_safe_bank_account_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_18dp"
        android:src="@drawable/ic_safe"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_label_account_txt"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:text="@string/qris_main_beneficiary_account"
        app:layout_constraintBottom_toBottomOf="@id/iv_safe_bank_account_img"
        app:layout_constraintStart_toEndOf="@id/iv_safe_bank_account_img"
        app:layout_constraintTop_toTopOf="@id/iv_safe_bank_account_img" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bank_details"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_solid_white_corner_8dp_stroke_black10"
        android:padding="@dimen/_12dp"
        app:layout_constraintEnd_toEndOf="@id/tv_button_change_account"
        app:layout_constraintStart_toStartOf="@id/iv_safe_bank_account_img"
        app:layout_constraintTop_toBottomOf="@id/iv_safe_bank_account_img">

        <FrameLayout
            android:id="@+id/fl_bank_image"
            android:layout_width="56dp"
            android:layout_height="44dp"
            android:background="@drawable/bg_stroke_black15_corner_4dp"
            android:padding="@dimen/_2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_image_bank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/ic_bank" />

        </FrameLayout>

        <TextView
            android:id="@+id/tv_txt_bank_title"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintBottom_toTopOf="@+id/tv_txt_account_number"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/fl_bank_image"
            app:layout_constraintTop_toTopOf="@id/fl_bank_image"
            tools:text="Bank Name" />

        <TextView
            android:id="@+id/tv_txt_account_number"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:layout_marginTop="@dimen/_2dp"
            android:textColor="@color/black_40"
            app:layout_constraintBottom_toBottomOf="@id/fl_bank_image"
            app:layout_constraintStart_toEndOf="@id/fl_bank_image"
            app:layout_constraintTop_toBottomOf="@id/tv_txt_bank_title"
            tools:text="Bank Account" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_button_change_account"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:text="@string/label_change"
        android:textColor="@color/blue_60"
        app:layout_constraintBottom_toBottomOf="@id/iv_safe_bank_account_img"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_safe_bank_account_img" />

    <TextView
        android:id="@+id/tv_blocked_bank_account"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/bg_red_outline"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_4dp"
        android:text="@string/blocked_info_text"
        android:textColor="@color/red_80"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/iv_safe_bank_account_img"
        app:layout_constraintTop_toBottomOf="@id/cl_bank_details"
        app:layout_goneMarginBottom="@dimen/_12dp" />

    <TextView
        android:id="@+id/tv_name_matching_warning"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@drawable/bg_solid_red5_corner_8dp_stroke_red40"
        android:drawablePadding="@dimen/_10dp"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/_2dp"
        android:paddingHorizontal="@dimen/_12dp"
        android:paddingVertical="@dimen/_8dp"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/ic_close"
        app:drawableTint="@color/red_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_blocked_bank_account"
        tools:text="Ubah rekening penerima ke rekening BNI - Dea Clarissa gagal karena [notes from panacea]. Selengkapnya"
        tools:visibility="visible" />

    <View
        android:id="@+id/vw_click_listener"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/cl_bank_details"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/blue_5"
        app:layout_constraintBottom_toBottomOf="@id/tv_security_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_security_message" />

    <TextView
        android:id="@+id/tv_security_message"
        style="@style/Label2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:drawablePadding="@dimen/_10dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/_2dp"
        android:paddingStart="@dimen/_18dp"
        android:paddingTop="@dimen/_12dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_12dp"
        android:text="@string/qris_code_info"
        android:textColor="@color/blue_80"
        app:drawableStartCompat="@drawable/ic_lock_shield"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name_matching_warning" />

</androidx.constraintlayout.widget.ConstraintLayout>