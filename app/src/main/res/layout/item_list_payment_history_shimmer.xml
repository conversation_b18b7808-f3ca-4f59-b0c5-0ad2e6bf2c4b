<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:paddingTop="@dimen/_16dp"
    tools:background="@color/white">

    <View
        android:id="@+id/vw_payment_type"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@color/shimmer_placeholder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/tv_name"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="@dimen/_20dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@color/shimmer_placeholder"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintEnd_toStartOf="@id/tv_amount"
        app:layout_constraintStart_toEndOf="@id/vw_payment_type"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Name" />

    <View
        android:id="@+id/tv_date"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="@dimen/_20dp"
        android:layout_marginTop="@dimen/_5dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@color/shimmer_placeholder"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_tag"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        tools:text="9 Dec 2020, 14:50" />

    <TextView
        android:id="@+id/tv_tag"
        style="@style/Label2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_2dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/bg_rounded_rectangle_blue_5_4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/_4dp"
        android:textColor="@color/blue_40"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/br_status"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        tools:text="Lebih Cepat \u26A1"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_status"
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        app:barrierDirection="start"
        app:constraint_referenced_ids="cl_fail_layout, tv_status"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/tv_amount"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_20dp"
        android:layout_height="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@color/shimmer_placeholder"
        app:layout_constraintEnd_toStartOf="@id/right_arrow"
        app:layout_constraintStart_toEndOf="@id/tv_name"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Rp100000" />

    <View
        android:id="@+id/tv_status"
        style="@style/Body3"
        android:layout_width="@dimen/_20dp"
        android:layout_height="@dimen/_20dp"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@color/shimmer_placeholder"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/right_arrow"
        app:layout_constraintTop_toBottomOf="@id/tv_amount"
        tools:text="Berhasil" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_fail_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@color/red_5"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/right_arrow"
        app:layout_constraintTop_toBottomOf="@id/tv_amount">

        <TextView
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:text="@string/purchase_fail"
            android:textColor="@color/red_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:id="@+id/right_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_chevron_right"
        app:tint="@color/black_40" />

    <View
        style="@style/Divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>