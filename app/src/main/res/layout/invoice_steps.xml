<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clInvoiceStep"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp_black5"
    android:paddingHorizontal="@dimen/_12dp"
    android:paddingVertical="@dimen/_8dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivStepImage"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/bg_circle"
        android:backgroundTint="@color/colorPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStepNumber"
        style="@style/BukuTextStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/ivStepImage"
        app:layout_constraintEnd_toEndOf="@id/ivStepImage"
        app:layout_constraintStart_toStartOf="@id/ivStepImage"
        app:layout_constraintTop_toTopOf="@id/ivStepImage"
        tools:text="A" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStepTitle"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:textColor="#171717"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivDone"
        app:layout_constraintStart_toEndOf="@id/ivStepImage"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Ankur Saxena" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_arrow_circle_right" />

</androidx.constraintlayout.widget.ConstraintLayout>

