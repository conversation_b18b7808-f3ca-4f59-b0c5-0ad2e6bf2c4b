<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- toolBar(this is shown for cash transaction invoice) -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolBar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white"
        android:visibility="gone"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/closebtnCross"
                android:layout_width="28dp"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:layout_marginStart="22dp"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:src="@mipmap/close_grey"
                app:tint="@color/black_80" />

            <TextView
                android:id="@+id/toolBarTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="35dp"
                android:layout_marginRight="35dp"
                android:layout_toRightOf="@id/closebtnCross"
                android:alpha="1"
                android:fontFamily="@font/roboto"
                android:text="@string/invoice"
                android:textColor="@color/black_80"
                android:textSize="18sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Appbar(this is shown for cash transaction detail) -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        app:layout_constraintTop_toBottomOf="@+id/toolBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:theme="@style/ToolbarTheme">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/closeBtn"
                    android:layout_width="28dp"
                    android:layout_height="match_parent"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center"
                    android:layout_marginLeft="35dp"
                    android:layout_marginRight="35dp"
                    android:layout_toRightOf="@id/closeBtn"
                    android:alpha="1"
                    android:fontFamily="@font/roboto"
                    android:text="@string/transaction_detail"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:paddingStart="@dimen/dimen_24dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/closeBtn"
                    android:textStyle="bold" />

                <androidx.appcompat.widget.AppCompatButton
                    android:background="@android:color/transparent"
                    android:id="@+id/editBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_toStartOf="@id/deleteBtn"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/edit"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:tint="@color/white"
                    tools:ignore="SmallSp"
                    app:layout_constraintEnd_toStartOf="@+id/deleteBtn"
                    app:layout_goneMarginEnd="@dimen/_16dp"
                    app:drawableTopCompat="@drawable/ic_edit" />

                <TextView
                    android:id="@+id/deleteBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="visible"
                    tools:ignore="SmallSp"
                    app:layout_goneMarginEnd="@dimen/_16dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:drawableTopCompat="@drawable/delete_red" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:id="@+id/notFoundContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="Mohon Maaf, Transaksi Tidak Ditemukan"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold" />

    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/loadingContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <ProgressBar
            android:id="@+id/loadingBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:progressTint="@color/colorPrimary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/loadingBar"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/_16dp"
            android:text="Mohon Tunggu..."
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold" />

    </RelativeLayout>
    <TextView
        android:id="@+id/tvOffline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/app_bar"
        android:background="@color/yellow5"
        android:elevation="@dimen/_12dp"
        android:drawablePadding="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="12dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="12dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:text="@string/recordings_are_saved_in_offline_mode"
        android:textColor="@color/black_80"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_offline_msg" />

    <ScrollView
        android:id="@+id/mainContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/layout_bottom_container"
        app:layout_constraintTop_toBottomOf="@+id/tvOffline">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#F1F1F1"
            android:id="@+id/content_root"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal"
                android:padding="@dimen/_16dp"
                android:visibility="gone"
                android:weightSum="1"
                tools:visibility="visible">

                <TextView
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.6"
                    android:text="@string/unpaid_transaction_type"
                    android:textColor="@color/black_80"
                    android:textSize="@dimen/text_14sp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_success"
                    style="@style/ButtonOutline.Blue"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.4"
                    android:text="@string/button_success_text"
                    android:textSize="@dimen/text_14sp" />

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/payment_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible">

                <View
                    android:id="@+id/unpaid_bg"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="@color/light_blue_background"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/unpaid_red_txt" />

                <TextView
                    android:id="@+id/unpaid_red_txt"
                    style="@style/Heading3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/red_5"
                    android:paddingStart="@dimen/_16dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="@dimen/_8dp"
                    android:text="@string/unpaid_transaction_type"
                    android:textColor="@color/red_80"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/ask_payment_title_txt"
                    style="@style/Heading3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/ask_digital_payment"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/unpaid_red_txt" />

                <TextView
                    android:id="@+id/ask_payment_subtitle_txt"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/ask_digital_payment_message"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ask_payment_title_txt" />

                <LinearLayout
                    android:layout_width="@dimen/_0dp"
                    app:layout_constraintTop_toBottomOf="@id/ask_payment_subtitle_txt"
                    android:orientation="horizontal"
                    android:id="@+id/unpaid_buttons_action_container"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:paddingTop="@dimen/_12dp"
                    android:paddingStart="@dimen/_16dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:layout_height="wrap_content">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_mark_paid_payment"
                        style="@style/ButtonOutline.Blue"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/white"
                        android:text="@string/button_success_text" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_create_payment"
                        style="@style/ButtonFill.Blue80"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:text="@string/create_payment_link" />

                </LinearLayout>

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/unpaid_payment_query_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:constraint_referenced_ids="unpaid_buttons_action_container,ask_payment_subtitle_txt" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/share_payment_btn"
                    style="@style/ButtonFill.Blue80"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/share_payment_link"
                    app:layout_constraintBottom_toBottomOf="@id/link_et"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/link_et" />

                <EditText
                    android:id="@+id/link_et"
                    style="@style/EditTextBordered"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_10dp"
                    android:ellipsize="end"
                    android:enabled="false"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textSize="@dimen/text_12sp"
                    app:layout_constraintEnd_toStartOf="@id/share_payment_btn"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/unpaid_buttons_action_container" />

                <TextView
                    android:id="@+id/expired_title_txt"
                    style="@style/Body3"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:textColor="@color/red_80"
                    android:textSize="@dimen/text_10sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/link_et"
                    tools:text="@string/active_link_until" />

                <View
                    android:id="@+id/divider_expired"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@color/divider_light_blue"
                    app:layout_constraintTop_toBottomOf="@id/expired_title_txt" />

                <TextView
                    android:id="@+id/payment_id_title_txt"
                    style="@style/Body3"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/transaction_number"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider_expired" />

                <TextView
                    android:id="@+id/payment_id_txt"
                    style="@style/SubHeading2"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_10dp"
                    android:gravity="end"
                    app:layout_constraintEnd_toStartOf="@id/copy_img2"
                    app:layout_constraintStart_toEndOf="@id/payment_account_title_txt"
                    app:layout_constraintTop_toBottomOf="@id/divider_expired"
                    tools:text="ASDASDS1" />

                <ImageView
                    android:id="@+id/copy_img2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:src="@drawable/ic_copy"
                    app:layout_constraintBottom_toBottomOf="@id/payment_id_txt"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/payment_id_txt" />

                <TextView
                    android:id="@+id/payment_account_title_txt"
                    style="@style/Body3"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/label_your_account"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/payment_id_title_txt" />

                <TextView
                    android:id="@+id/payment_account_txt"
                    style="@style/SubHeading2"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/payment_account_title_txt"
                    app:layout_constraintTop_toBottomOf="@id/payment_id_title_txt"
                    tools:text="BNI - 1231" />

                <TextView
                    android:id="@+id/payment_bank_title_txt"
                    style="@style/Body3"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/bank_account_name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/payment_account_txt" />

                <TextView
                    android:id="@+id/bank_account_name_txt"
                    style="@style/SubHeading2"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/payment_bank_title_txt"
                    app:layout_constraintTop_toBottomOf="@id/payment_account_txt"
                    tools:text="User Account name" />


                <TextView
                    android:id="@+id/admin_fee_title_txt"
                    style="@style/Body3"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/transaction_fees"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/bank_account_name_txt" />

                <TextView
                    android:id="@+id/admin_fee_txt"
                    style="@style/SubHeading2"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/admin_fee_title_txt"
                    app:layout_constraintTop_toBottomOf="@id/bank_account_name_txt"
                    tools:text="Rp1.000" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/payment_detail_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="copy_img2,has_paid_txt,mark_paid_payment_btn2,payment_id_title_txt,payment_id_txt,divider_expired,expired_title_txt,payment_bank_title_txt,bank_account_name_txt,payment_account_title_txt,payment_account_txt,link_et,share_payment_btn,admin_fee_title_txt,admin_fee_txt"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/barrier_payment"
                    android:layout_width="wrap_content"
                    android:layout_height="1dp"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="admin_fee_title_txt, unpaid_buttons_action_container" />

                <View
                    android:id="@+id/blue_divider"
                    style="@style/Divider.Horizontal"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@color/divider_light_blue"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/barrier_payment" />

                <TextView
                    android:id="@+id/safe_txt"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_10dp"
                    android:drawablePadding="@dimen/_10dp"
                    android:text="@string/guaranteed_safe"
                    android:textColor="@color/blue_60"
                    app:drawableStartCompat="@drawable/ic_tick_safe_blue"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/blue_divider" />

                <View
                    android:id="@+id/blue_divider2"
                    style="@style/Divider.Horizontal"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@color/divider_light_blue"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/safe_txt" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/payment_others_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="blue_divider2, blue_divider,safe_txt" />

                <com.bukuwarung.payments.widget.PaymentInfoView
                    android:id="@+id/payment_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/barrier_payment" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/payment_info_barrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="blue_divider2,payment_info" />

                <TextView
                    android:id="@+id/has_paid_txt"
                    style="@style/Heading3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:text="@string/has_paid_manually"
                    app:layout_constraintBottom_toBottomOf="@id/mark_paid_payment_btn2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/mark_paid_payment_btn2" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/mark_paid_payment_btn2"
                    style="@style/ButtonOutline.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_4dp"
                    android:backgroundTint="@color/white"
                    android:paddingStart="38dp"
                    android:paddingTop="@dimen/_2dp"
                    android:paddingEnd="38dp"
                    android:paddingBottom="@dimen/_2dp"
                    android:text="@string/button_success_text"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/payment_info_barrier" />

                <ProgressBar
                    android:id="@+id/loading_state_progress_bar"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginTop="@dimen/_20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ask_payment_title_txt" />

                <TextView
                    android:id="@+id/loading_state_txt"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_18dp"
                    android:text="@string/payment_tab_loading_state_message"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/loading_state_progress_bar" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/loading_state_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="loading_state_txt,loading_state_progress_bar" />

                <TextView
                    android:id="@+id/error_state_title_txt"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginEnd="50dp"
                    android:gravity="center"
                    android:text="@string/no_connection_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ask_payment_title_txt" />

                <TextView
                    android:id="@+id/error_state_txt"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="50dp"
                    android:gravity="center"
                    android:text="@string/no_connection_subtitle"
                    app:layout_constraintBottom_toTopOf="@id/reload_btn"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/error_state_title_txt" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/reload_btn"
                    style="@style/ButtonOutline.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/reload"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/error_state_txt"
                    app:layout_constraintVertical_chainStyle="packed" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/error_state_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="error_state_title_txt,error_state_txt,reload_btn" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/top_divider"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="@color/new_divider"
                android:visibility="gone" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/transaction_summary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingTop="@dimen/_16dp">

                <TextView
                    android:id="@+id/tv_income_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/sales"
                    android:textColor="@color/black_80"
                    android:textSize="16sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_base_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/harga_modal"
                    android:textColor="@color/black_80"
                    android:textSize="16sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_income_label" />

                <!--Dinamic valued views-->
                <TextView
                    android:id="@+id/tv_income_nominal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:fontFamily="@font/roboto"
                    android:textColor="@color/green_80"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Rp20.000" />

                <TextView
                    android:id="@+id/tv_base_price_nominal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="20dp"
                    android:fontFamily="@font/roboto"
                    android:textColor="@color/out_red"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_income_nominal"
                    tools:text="Rp20.000" />

                <LinearLayout
                    android:id="@+id/bg_margin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/profit_bg"
                    android:padding="12dp"
                    app:layout_constraintTop_toBottomOf="@id/tv_base_price">

                    <TextView
                        android:id="@+id/tv_is_profit"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/cash_in_profit_text"
                        android:textColor="@color/green_80"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_margin_nominal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/green_80"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="Rp1.000.000" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@color/black_10"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/bg_margin" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:visibility="gone"
                android:id="@+id/layout_payment_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white">
                <TextView
                    android:id="@+id/trx_no_title_txt"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/trx_no"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/trx_no_txt"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_12dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/copy_img"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/trx_no" />

                <ImageView
                    android:id="@+id/copy_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_20dp"
                    android:src="@drawable/ic_copy"
                    app:layout_constraintBottom_toBottomOf="@id/trx_no_txt"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/trx_no_txt" />

                <View
                    android:id="@+id/account_detail_divider1"
                    style="@style/Divider.Horizontal"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@color/black_10"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/trx_no_txt" />

                <TextView
                    android:id="@+id/bank_title_txt"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/payment_method"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/account_detail_divider1" />

                <TextView
                    android:id="@+id/bank_txt"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_12dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/account_detail_divider1"
                    tools:text="@string/label_bank" />

                <View
                    android:id="@+id/account_detail_divider2"
                    style="@style/Divider.Horizontal"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@color/black_10"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/bank_title_txt" />

                <TextView
                    android:id="@+id/account_title_txt"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/produk"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/account_detail_divider2" />

                <TextView
                    android:id="@+id/product_name_txt"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_12dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/account_detail_divider2"
                    tools:text="Pulsa Telkomsel 100.000" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@color/black_10"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/product_name_txt" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_redirect_to_payments"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:background="@color/white" >
                <TextView
                    android:id="@+id/tv_from_payments"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/processed_from_payment_tab"
                    app:drawableEndCompat="@drawable/ic_info_black20"
                    app:drawableStartCompat="@drawable/ic_transfer"
                    android:drawablePadding="@dimen/_12dp"
                    android:layout_marginVertical="@dimen/_16dp"
                    android:layout_marginStart="@dimen/_16dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:srcCompat="@drawable/ic_right_arrow"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@color/black_10"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_from_payments" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.bukuwarung.activities.print.CashTransactionReceipt
                android:id="@+id/cash_receipt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.bukuwarung.activities.print.CashTransactionOldReceipt
                android:id="@+id/cash_receipt_old"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_upload_photo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_10dp"
                android:text="@string/foto_transaksi"
                style="@style/Body2"/>

            <ImageView
                android:id="@+id/img_transaksi"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_150dp"
                android:scaleType="center"
                android:layout_margin="@dimen/_10dp" />

            <TextView
                android:id="@+id/btn_invoice_preference"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:background="@color/white"
                android:padding="@dimen/_16dp"
                android:text="@string/change_invoice_preference"
                android:textColor="@color/black_80"
                app:drawableEndCompat="@drawable/ic_right_arrow"
                tools:ignore="RtlSymmetry" />
        </LinearLayout>

    </ScrollView>

    <!-- Buttons -->
    <include
        android:id="@+id/layout_bottom_container"
        android:layout_width="@dimen/_0dp"
        android:layout_height="130dp"
        layout="@layout/bottom_share_invoice_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
