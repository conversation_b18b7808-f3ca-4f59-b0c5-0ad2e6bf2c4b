<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/label_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:drawableLeftCompat="@drawable/ic_yellow_fav"
        android:text="Barang Favorit"
        android:textColor="@color/black_60"
        android:visibility="gone"
        android:drawablePadding="@dimen/_4dp"
        android:paddingVertical="@dimen/_2dp"
        android:paddingHorizontal="@dimen/_4dp"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/bg_rounded_rectangle_fff5d9"/>

    <FrameLayout
        android:id="@+id/bg"
        style="@style/Heading3"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:background="@drawable/initial_product_name_bg"
        android:textAllCaps="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/label_favourite" />

    <ImageView
        android:id="@+id/badge"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_16dp"
        android:src="@drawable/bg_collection_red"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/bg"
        app:layout_constraintEnd_toEndOf="@id/bg"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toTopOf="@id/bg"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_initial"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="@id/bg"
        app:layout_constraintEnd_toEndOf="@id/bg"
        app:layout_constraintStart_toStartOf="@id/bg"
        app:layout_constraintTop_toTopOf="@id/bg"
        tools:text="M" />


    <TextView
        android:id="@+id/tv_product_name"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintEnd_toStartOf="@id/btn_manage_stock"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toTopOf="@+id/bg"
        app:layout_constraintVertical_chainStyle="spread"
        tools:text="Long product name that can’t fit in 1 line that will exceed the whole text area" />

    <TextView
        android:id="@+id/price_added_label_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:ellipsize="end"
        android:maxLength="20"
        android:maxLines="2"
        android:text="@string/selling_price"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_10sp"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_name"
        tools:text="Harga Jual"

        />

    <TextView
        android:id="@+id/selling_price_currency_txt"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_5dp"
        android:ellipsize="end"
        android:text="@string/currency"
        android:textColor="@color/green_100"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toBottomOf="@+id/price_added_label_text"

        />

    <TextView
        android:id="@+id/selling_price_txt"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:layout_marginTop="@dimen/_5dp"
        android:maxLength="20"
        android:maxLines="2"
        android:textColor="@color/green_100"
        app:layout_constraintStart_toEndOf="@id/selling_price_currency_txt"
        app:layout_constraintTop_toBottomOf="@+id/price_added_label_text"
        tools:text="0.000" />

    <TextView
        android:id="@+id/tv_measurement"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_5dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toEndOf="@id/selling_price_txt"
        app:layout_constraintTop_toBottomOf="@+id/price_added_label_text"
        tools:text="/Pcs" />

    <TextView
        android:id="@+id/tv_subtext"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:textColor="@color/out_red"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/bg"
        app:layout_constraintStart_toEndOf="@id/bg"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_manage_stock"
        style="@style/ButtonOutline.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/manage_stock_label"
        android:textSize="@dimen/text_12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/label_favourite" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_manage_price"
        style="@style/ButtonOutline.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/manage_price_label"
        android:textSize="@dimen/text_12sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_stock"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:maxLength="14"
        android:textColor="@color/out_red"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/selling_price_txt"
        tools:text="Stok: -20" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_10"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_stock" />

</androidx.constraintlayout.widget.ConstraintLayout>
