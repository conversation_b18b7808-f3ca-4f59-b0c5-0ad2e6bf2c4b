<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner"
    android:padding="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/dialog_img"
        android:layout_width="@dimen/_160dp"
        android:layout_height="@dimen/_160dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_kyc_verification" />

    <TextView
        android:id="@+id/title_txt"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:gravity="center"
        android:text="@string/verification_needed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_img" />

    <TextView
        android:id="@+id/subtitle_txt"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:gravity="center"
        android:text="@string/verification_needed_subtitle"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_txt" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/continue_btn"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cancel_btn"
        app:layout_constraintTop_toBottomOf="@id/subtitle_txt" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/cancel_btn"
        style="@style/ButtonOutline"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:text="@string/cancel"
        app:layout_constraintEnd_toStartOf="@id/continue_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/subtitle_txt" />
</androidx.constraintlayout.widget.ConstraintLayout>
