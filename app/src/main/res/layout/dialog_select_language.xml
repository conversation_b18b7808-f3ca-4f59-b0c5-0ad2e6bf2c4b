<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="300dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingBottom="16dp">

    <RelativeLayout
        android:layout_width="fill_parent"
        android:id="@+id/title"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:padding="12.0dip">

        <ImageView
            android:layout_width="40.0dip"
            android:layout_height="40.0dip"
            android:id="@+id/categoryIcon"
            android:layout_centerHorizontal="true"
            android:tint="@color/white_shade_bg"
            app:srcCompat="@drawable/ic_select_lang" />

        <TextView
            android:id="@+id/languageSelectDialogTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="7.0dip"
            android:text="@string/dialog_choose_language"
            android:layout_below="@+id/categoryIcon"
            android:textColor="@color/white_shade_bg"
            android:textSize="16.0sp"
            android:textStyle="bold" />
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/closeDialog"
            android:layout_alignParentRight="true"
            android:tint="@color/white"
            android:src="@drawable/ic_close"/>
    </RelativeLayout>

    <ListView
        android:id="@+id/cstLanguage"
        android:layout_marginTop="16dp"
        tools:listitem="@layout/langauge_list_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title" />
</RelativeLayout>
