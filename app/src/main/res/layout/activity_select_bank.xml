<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:title="@string/fragment_add_bank_account_title" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/input_search"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/input_search" />

    <ImageView
        android:id="@+id/icon_search"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="@null"
        android:src="@drawable/ic_search"
        app:layout_constraintBottom_toBottomOf="@id/input_search"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/input_search"
        app:tint="@color/colorPrimary" />


    <TextView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="center_vertical"
        android:hint="@string/search_banks"
        android:textColorHint="@color/black_40"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/input_search"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/icon_search"
        app:layout_constraintTop_toTopOf="@id/input_search" />

    <EditText
        android:id="@+id/input_search"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:background="@color/white"
        android:fontFamily="@font/roboto"
        android:gravity="center_vertical"
        android:hint="@string/search_banks"
        android:inputType="textCapSentences"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textColorHint="@color/black_40"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/icon_search"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <ImageView
        android:id="@+id/icon_cancel"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="@null"
        android:src="@drawable/ic_close"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/input_search"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/input_search"
        app:tint="@color/colorPrimary" />

    <View
        style="@style/ShadowBottom"
        android:layout_height="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/input_search" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_banks"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/input_search" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginTop="45dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>
