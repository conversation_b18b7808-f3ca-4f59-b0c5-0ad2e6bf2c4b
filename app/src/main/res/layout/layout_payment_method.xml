<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="@dimen/_16dp"
    android:background="@drawable/bg_rounded_rectangle_white_8dp">

    <TextView
        android:id="@+id/tv_payment_method"
        style="@style/Body2"
        android:text="@string/label_payment_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_detail_transaction"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/black_80"
        tools:text="Transfer Bank BNI"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_payment_method"/>

    <TextView
        android:id="@+id/tv_transaction_fee"
        style="@style/Body2"
        android:text="@string/transaction_fees"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_payment_method"/>

    <TextView
        android:id="@+id/tv_transaction_fee_value"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/blue_60"
        tools:text="GRATIS"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_transaction_fee"/>

    <TextView
        android:id="@+id/tv_expired_in"
        style="@style/Body2"
        android:text="@string/expired_in"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_transaction_fee"/>

    <TextView
        android:id="@+id/tv_expired_time"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/black_80"
        tools:text="00:02:00:00"
        android:drawablePadding="@dimen/_5dp"
        app:drawableStartCompat="@drawable/ic_clock_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_expired_in"/>

    <TextView
        android:id="@+id/tv_time"
        style="@style/Label1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/black_20"
        android:text="@string/time"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_expired_time"/>

    <include
        android:id="@+id/include_payment_block"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        layout="@layout/layout_payment_method_block"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_time"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_title_instruction"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/payment_guide"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_payment_block"/>

    <include
        android:id="@+id/include_one_instruction"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        layout="@layout/layout_payment_instruction"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title_instruction"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <include
        android:id="@+id/include_two_instruction"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        layout="@layout/layout_payment_instruction"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@+id/include_one_instruction"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <include
        android:id="@+id/include_three_instruction"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        layout="@layout/layout_payment_instruction"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@+id/include_two_instruction"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>