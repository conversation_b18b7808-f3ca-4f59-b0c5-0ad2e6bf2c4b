<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/rv_bg"
        android:orientation="vertical"
        tools:context=".activities.settings.SettingsActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ActionBarAppTheme">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="fill_parent"
                android:layout_height="?actionBarSize"
                app:contentInsetLeft="0.0dip"
                app:contentInsetStart="0.0dip"
                app:contentInsetStartWithNavigation="0.0dip">

                <ImageView
                    android:id="@+id/backBtn"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="center"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/screen_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:fontFamily="@font/roboto"
                    android:text="@string/account_settings_title"
                    android:textColor="@color/white"
                    android:textSize="18.0dip"
                    android:textStyle="bold" />
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:id="@+id/ll_restore_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black_60"
            android:paddingVertical="@dimen/_8dp"
            android:visibility="gone"
            tools:visibility="visible">

            <ProgressBar
                android:id="@+id/pb_restore_progress"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginStart="@dimen/_16dp"
                android:indeterminate="true"
                android:indeterminateTint="@color/white"
                android:indeterminateTintMode="src_atop" />

            <TextView
                android:id="@+id/tv_restore_progress"
                style="@style/Body3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/records_are_updated"
                android:textColor="@color/white" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/rv_bg"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12.0dip"
                android:layout_marginTop="12.0dip"
                android:lineSpacingMultiplier="1.36"
                android:text="@string/backup_settings_title"
                android:textAllCaps="true"
                android:textColor="@color/heading_text"
                android:textSize="14.0sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8.0dip"
                android:background="@color/white"
                android:elevation="2.0dip"
                android:orientation="vertical"
                android:padding="8dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16.0dip"
                        android:layout_marginTop="8.0dip"
                        android:text="@string/last_backup"
                        android:textColor="@color/body_text" />

                    <TextView
                        android:id="@+id/lastBackup"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10.0dip"
                        android:layout_marginTop="8.0dip"
                        android:text="5 minutes ago"
                        android:textColor="@color/body_text"
                        android:textSize="12dp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="8dp"
                    android:paddingRight="@dimen/_16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8.0dip"
                        android:lineSpacingExtra="2dp"
                        android:paddingTop="8dp"
                        android:paddingRight="4dp"
                        android:text="@string/backup_msg"
                        android:textColor="@color/body_text"
                        android:textSize="13.0dip" />

                    <TextView
                        android:id="@+id/btn_sync"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="8.0dip"
                        android:paddingTop="10dp"
                        android:paddingRight="20.0dip"
                        android:paddingBottom="10.0dip"
                        android:layout_marginTop="@dimen/_8dp"
                        android:background="@drawable/btn_enabled_blue_bg"
                        android:text="@string/update_data_now"
                        android:textAllCaps="true"
                        android:textColor="@color/btn_color_bg_blue_state"
                        android:textSize="12dp" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8.0dip"
                android:layout_marginTop="12.0dip"
                android:lineSpacingMultiplier="1.36"
                android:text="@string/general_settings_title"
                android:textAllCaps="true"
                android:textColor="@color/heading_text"
                android:textSize="14.0sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8.0dip"
                android:background="@color/white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="40.0dip"
                    android:layout_marginTop="6.0dip"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingRight="@dimen/_16dp">

                    <ImageView
                        android:layout_width="24.0dip"
                        android:layout_height="50.0dip"
                        android:layout_centerVertical="true"
                        android:paddingTop="13.0dip"
                        android:paddingBottom="13.0dip"
                        app:tint="@color/grey"
                        app:srcCompat="@drawable/ic_clock" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="12dp"
                        android:layout_weight="1.0"
                        android:text="@string/time_format"
                        android:textColor="@color/body_text"
                        android:textSize="14.0dip" />

                    <Switch
                        android:id="@+id/time_format"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:checked="true" />
                </LinearLayout>

                <TextView
                    android:id="@+id/timeSample"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="52.0dp"
                    android:layout_marginTop="-10dp"
                    android:layout_marginBottom="10dp"
                    android:text="14:00"
                    android:textColor="@color/greyTextColor" />

                <TextView
                    android:id="@+id/divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/section_end" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/daily_update_layout"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8.0dip"
                android:background="@color/white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6.0dip"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/_16dp"
                    android:paddingRight="@dimen/_16dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:srcCompat="@drawable/ic_settings_daily_recap_icon" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="12dp"
                        android:layout_weight="1.0"
                        android:text="@string/daily_business_update_reminder"
                        android:textColor="@color/body_text"
                        android:textSize="@dimen/text_14sp" />

                    <Switch
                        android:id="@+id/daily_business_recap_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:checked="true" />
                </LinearLayout>

                <TextView
                    android:id="@+id/daily_update_subtitle_holder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="52.0dp"
                    android:layout_marginTop="-10dp"
                    android:layout_marginBottom="10dp"
                    android:text=""
                    android:textColor="@color/greyTextColor" />

                <TextView
                    android:id="@+id/divider_daily_update"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/section_end" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8.0dip"
                android:layout_marginTop="12.0dip"
                android:lineSpacingMultiplier="1.36"
                android:text="@string/security_setting"
                android:textAllCaps="true"
                android:textColor="@color/black_44"
                android:textSize="14.0sp"
                android:visibility="visible" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8.0dip"
                android:background="@color/white"
                android:orientation="vertical"
                android:id="@+id/app_language_layout"
                android:visibility="gone">

                <LinearLayout
                    android:id="@+id/selectLang"
                    android:layout_width="fill_parent"
                    android:layout_height="40.0dip"
                    android:layout_marginTop="6.0dip"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingRight="@dimen/_16dp">

                    <ImageView
                        android:layout_width="24.0dip"
                        android:layout_height="50.0dip"
                        android:layout_centerVertical="true"
                        android:paddingTop="15.0dip"
                        android:paddingBottom="15.0dip"
                        app:tint="@color/grey"
                        app:srcCompat="@drawable/ic_select_language" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="12dp"
                        android:layout_weight="1.0"
                        android:text="@string/appLanguage"
                        android:textColor="@color/body_text"
                        android:textSize="14.0dip" />

                    <TextView
                        android:id="@+id/app_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="@string/indonesian"
                        android:textColor="@color/colorPrimary" />
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="52.0dp"
                    android:layout_marginTop="-10dp"
                    android:layout_marginRight="52.0dp"
                    android:layout_marginBottom="10dp"
                    android:text="@string/select_app_language"
                    android:textColor="@color/greyTextColor" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/section_end" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8.0dip"
                android:visibility="gone"
                android:background="@color/white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="40.0dip"
                    android:layout_marginTop="6.0dip"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="@dimen/_16dp">

                    <ImageView
                        android:layout_width="24.0dip"
                        android:layout_height="50.0dip"
                        android:paddingTop="13.0dip"
                        android:paddingBottom="13.0dip"
                        app:srcCompat="@drawable/ic_transaction_active"
                        app:tint="@color/grey" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="12dp"
                        android:layout_weight="1.0"
                        android:text="@string/use_cash_feature"
                        android:textColor="@color/body_text"
                        android:textSize="14.0dip" />

                    <Switch
                        android:id="@+id/cash_feature"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:checked="true" />
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="52.0dp"
                    android:layout_marginTop="-10dp"
                    android:layout_marginRight="40dp"
                    android:layout_marginBottom="10dp"
                    android:paddingRight="@dimen/_16dp"
                    android:text="@string/enabled_cash_explain"
                    android:textColor="@color/greyTextColor" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/section_end" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:id="@+id/payment_switch_layout"
                android:layout_height="wrap_content"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/payment_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:src="@drawable/ic_payment_tab"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/payment_title_txt"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:text="@string/use_payment_feature"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/payment_feature"
                    app:layout_constraintStart_toEndOf="@id/payment_img"
                    app:layout_constraintTop_toTopOf="@id/payment_img" />

                <TextView
                    android:id="@+id/payment_desc_txt"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_marginBottom="@dimen/_16dp"
                    android:text="@string/enabled_payment_explain"
                    android:textColor="@color/black_60"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/payment_feature"
                    app:layout_constraintStart_toEndOf="@id/payment_img"
                    app:layout_constraintTop_toBottomOf="@id/payment_title_txt" />

                <Switch
                    android:id="@+id/payment_feature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/payment_img" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/section_end"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:id="@+id/stock_switch_layout"
                android:visibility="gone"
                android:layout_height="wrap_content"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/stock_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:src="@drawable/ic_stock_tab"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/stock_title_txt"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:text="@string/use_stock_feature"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/stock_feature"
                    app:layout_constraintStart_toEndOf="@id/stock_img"
                    app:layout_constraintTop_toTopOf="@id/stock_img" />

                <TextView
                    android:id="@+id/stock_desc_txt"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_marginBottom="@dimen/_16dp"
                    android:text="@string/enabled_stock_explain"
                    android:textColor="#8D8D8D"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/stock_feature"
                    app:layout_constraintStart_toEndOf="@id/stock_img"
                    app:layout_constraintTop_toBottomOf="@id/stock_title_txt" />

                <Switch
                    android:id="@+id/stock_feature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/stock_img" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/section_end"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_pin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingVertical="@dimen/_12dp">

                <ImageView
                    android:id="@+id/iv_pin_lock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_locked"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/grey" />

                <TextView
                    android:id="@+id/tv_pin_info"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_pin_lock"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Ubah Pin Transaksi" />

                <TextView
                    android:id="@+id/tv_pin_change"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Ubah" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/password_change_container"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6.0dip"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingRight="@dimen/_16dp">

                    <ImageView
                        android:layout_width="24.0dip"
                        android:layout_height="50.0dip"
                        android:layout_centerVertical="true"
                        android:paddingTop="15.0dip"
                        android:paddingBottom="15.0dip"
                        app:srcCompat="@drawable/ic_password_change"
                        app:tint="@color/grey" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="12dp"
                        android:layout_weight="6.0"
                        android:text="@string/change_password"
                        android:textColor="@color/black_44"
                        android:textSize="14.0dip" />

                    <TextView
                        android:layout_width="0.0dip"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1.0"
                        android:text="@string/change"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/section_end" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/logoutContainer"
                style="@style/PadButtonStyle"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8.0dip"
                android:layout_marginBottom="5.0dip"
                android:background="@color/white"
                android:gravity="start"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/_16dp"
                android:paddingTop="@dimen/profile_action_item_padding_vertical"
                android:paddingRight="@dimen/_16dp"
                android:paddingBottom="@dimen/profile_action_item_padding_vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="24.0dip"
                    android:layout_height="24.0dip"
                    android:layout_gravity="center_vertical"
                    app:tint="#E74D32"
                    app:srcCompat="@drawable/ic_power" />

                <TextView
                    android:id="@+id/login_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginRight="30.0dip"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto"
                    android:text="@string/sign_out"
                    android:textColor="#E74D32"
                    android:textSize="16.0sp" />
            </LinearLayout>


        </LinearLayout>

    </LinearLayout>

</ScrollView>