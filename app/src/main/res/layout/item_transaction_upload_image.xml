<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/_8dp"
    app:cardElevation="@dimen/_0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/_4dp"
        android:paddingStart="@dimen/_4dp"
        android:paddingEnd="@dimen/_16dp">

        <androidx.cardview.widget.CardView
            android:layout_width="@dimen/_56dp"
            android:layout_height="@dimen/_56dp"
            app:cardBackgroundColor="@color/black5"
            app:cardCornerRadius="@dimen/_6dp"
            app:cardElevation="@dimen/_0dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_camera_new"
                app:tint="@color/black_20" />

            <ImageView
                android:id="@+id/image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:scaleType="centerCrop" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/upload_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:textColor="@color/blue60"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/remove_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:gravity="center"
            android:orientation="horizontal">

            <View
                android:layout_width="@dimen/_1dp"
                android:layout_height="@dimen/_32dp"
                android:background="@color/black10" />

            <ImageView
                android:id="@+id/remove_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:src="@drawable/ic_x_circle"
                app:tint="@color/black_20" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
