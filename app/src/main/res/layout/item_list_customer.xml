<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="customer"
            type="com.bukuwarung.database.entity.CustomerEntity" />

        <variable
            name="onCustomerClick"
            type="com.bukuwarung.payments.adapters.CustomerListAdapter.OnCustomerClickListener" />

        <import type="com.bukuwarung.utils.Utility"/>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:onClick="@{()-> onCustomerClick.onClick(customer)}"
        android:padding="@dimen/_16dp">

        <TextView
            android:id="@+id/txt_initials"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/oval_0"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:maxLength="1"
            android:text="@{customer.initial}"
            android:textColor="@color/white"
            android:textSize="22sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:ovalColor="@{customer.initial}"
            tools:text="A" />

        <TextView
            android:id="@+id/txt_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:maxLines="1"
            android:text="@{customer.name}"
            android:textColor="@color/heading_text"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@id/txt_balance"
            app:layout_constraintStart_toEndOf="@id/txt_initials"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Name" />

        <TextView
            android:id="@+id/txt_tempo"
            goneUnless="@{customer.customerId != null &amp;&amp; !customer.customerId.empty &amp;&amp; !customer.formattedDueDate.empty}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:maxLines="1"
            android:text='@{String.format("%s: %s", @string/label_tempo, customer.formattedDueDate)}'
            android:textColor="@color/body_text"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/txt_balance"
            app:layout_constraintStart_toStartOf="@id/txt_name"
            app:layout_constraintTop_toBottomOf="@id/txt_name"
            tools:text="Address" />

        <TextView
            android:id="@+id/txt_number"
            goneUnless="@{customer.customerId == null || customer.customerId.empty || customer.formattedDueDate.empty}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:maxLines="1"
            android:text='@{Utility.beautifyPhoneNumber(customer.phone)}'
            android:textColor="@color/body_text"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/txt_balance"
            app:layout_constraintStart_toStartOf="@id/txt_name"
            app:layout_constraintTop_toBottomOf="@id/txt_name"
            tools:text="Address" />


        <TextView
            android:id="@+id/txt_balance"
            goneUnless="@{customer.customerId != null &amp;&amp; !customer.customerId.empty}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:gravity="end"
            android:textColor="@{customer.balance >= 0.0 ? @color/in_green : @color/out_red}"
            android:textSize="16sp"
            android:textStyle="bold"
            app:balanceWithCurrency="@{customer.balance}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Rp100000" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>