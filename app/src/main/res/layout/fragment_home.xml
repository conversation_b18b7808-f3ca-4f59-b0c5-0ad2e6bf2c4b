<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_home"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">
  <androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_homepage"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:contentInsetStartWithNavigation="0dp"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
      >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/Heading2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:text="Store Name"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/notification_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="?android:attr/selectableItemBackground"
                android:src="@drawable/ic_alert"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/notify_highlighter_exp"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:background="@drawable/oval_pink"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/notification_icon"
                app:layout_constraintTop_toTopOf="@id/notification_icon"
                tools:text="2"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

      <androidx.constraintlayout.widget.ConstraintLayout
          android:id="@+id/cl_pending_edc_details"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:background="@color/yellow_5"
          android:paddingHorizontal="@dimen/_16dp"
          android:paddingVertical="@dimen/_12dp"
          android:visibility="gone"
          app:layout_constraintTop_toBottomOf="@+id/tb_homepage">

          <TextView
              android:id="@+id/edc_details_title"
              style="@style/SubHeading1"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:textColor="@color/yellow_80"
              app:layout_constraintTop_toTopOf="parent"
              tools:text="Silakan verifikasi akun sebelum 20 Juli 2024" />

          <TextView
              android:id="@+id/edc_details_desc"
              style="@style/Body3.black60"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_marginTop="@dimen/_2dp"
              app:layout_constraintTop_toBottomOf="@+id/edc_details_title"
              tools:text="Supaya mesin EDC segera dikirim dan dapat digunakan untuk bertransaksi. Selengkapnya" />
      </androidx.constraintlayout.widget.ConstraintLayout>

      <ScrollView
        android:id="@+id/sv_homepage"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintTop_toBottomOf="@id/cl_pending_edc_details"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:id="@+id/frame_homepage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

    </ScrollView>

    <include
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/layout_scroll_to_top"
        layout="@layout/home_scroll_to_top"
        android:layout_marginBottom="@dimen/_22dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_floating_home_year_in_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
            android:id="@+id/ib_yier_float_entry"
            android:layout_width="@dimen/_80dp"
            android:layout_height="@dimen/_80dp"
            android:src="@drawable/undang_float"
            android:scaleType="fitXY"
            android:background="@null"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:id="@+id/vw_home_float_entry"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_24dp"
            app:layout_constraintTop_toTopOf="@id/ib_yier_float_entry"
            app:layout_constraintEnd_toEndOf="@id/ib_yier_float_entry"
            app:layout_constraintStart_toStartOf="@id/ib_yier_float_entry" />

        <TextView
            android:id="@+id/tv_home_float_entry"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_24dp"
            android:backgroundTint="@color/transparent"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_cross_undang"
            app:layout_constraintTop_toTopOf="@id/ib_yier_float_entry"
            app:layout_constraintEnd_toEndOf="@id/ib_yier_float_entry"
            app:layout_constraintStart_toStartOf="@id/ib_yier_float_entry" />

    </androidx.constraintlayout.widget.ConstraintLayout>


  </androidx.constraintlayout.widget.ConstraintLayout>

    <com.bukuwarung.utils.MovableFloatingActionButton
        android:id="@+id/cl_laporan"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end">

        <ImageView
            android:id="@+id/iv_movable_gif"
            android:layout_width="@dimen/_60dp"
            android:layout_height="@dimen/_60dp"
            android:paddingTop="@dimen/_2dp"
            app:backgroundTint="@color/white"
            android:scaleType="fitXY"
            android:backgroundTint="@color/white"
            android:layout_gravity="bottom|end"
            android:background="@color/transparent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_margin="@dimen/_20dp"
            app:srcCompat="@drawable/business_dashboard_floating">

        </ImageView>

        <TextView
            android:id="@+id/tv_close"
            android:paddingTop="@dimen/_4dp"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/transparent"
            app:drawableEndCompat="@drawable/ic_cross_undang"
            app:layout_constraintTop_toTopOf="@id/iv_movable_gif"
            app:layout_constraintBottom_toTopOf="@id/iv_movable_gif"
            app:layout_constraintEnd_toEndOf="@id/iv_movable_gif"
            app:layout_constraintStart_toStartOf="@id/iv_movable_gif" />

    </com.bukuwarung.utils.MovableFloatingActionButton>


</androidx.coordinatorlayout.widget.CoordinatorLayout>