<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/product_detail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/et_product_name"
        style="@style/EditTextBordered"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:hint="@string/edit_product_name"
        app:layout_constraintTop_toBottomOf="@id/tv" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnCancel"
        style="@style/Button.OutlinePrimary"
        android:layout_width="@dimen/_0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="4dp"
        android:text="@string/cancel"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="@+id/btnSave"
        app:layout_constraintEnd_toStartOf="@id/btnSave"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btnSave" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnSave"
        style="@style/ButtonFill.Blue"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/save"
        android:textAllCaps="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnCancel"
        app:layout_constraintTop_toBottomOf="@id/et_product_name" />
</androidx.constraintlayout.widget.ConstraintLayout>