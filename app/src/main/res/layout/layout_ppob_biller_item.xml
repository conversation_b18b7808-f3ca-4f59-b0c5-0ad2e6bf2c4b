<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_biller"
        android:layout_width="48dp"
        android:layout_height="40dp"
        android:layout_marginStart="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_multifinance_default" />

    <TextView
        android:id="@+id/tv_name"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toEndOf="@+id/iv_biller"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Kota Bandung" />

    <TextView
        android:id="@+id/tv_error"
        style="@style/Label1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/multifinance_disturbance"
        android:textColor="@color/red_80"
        app:layout_constraintStart_toEndOf="@+id/iv_biller"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <ImageView
        android:id="@+id/iv_selected"
        android:layout_width="16dp"
        android:layout_height="11dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_error"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_name"
        app:srcCompat="@drawable/ic_selection" />

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_error" />

</androidx.constraintlayout.widget.ConstraintLayout>
