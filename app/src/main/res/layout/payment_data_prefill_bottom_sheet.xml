<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingTop="@dimen/_10dp"
    android:paddingBottom="@dimen/_18dp">

    <View
        android:id="@+id/vw_close_bar"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:background="@drawable/background_filled_black10_border_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_30dp"
        android:layout_marginTop="@dimen/_12dp"
        android:gravity="center"
        android:text="@string/continue_this_payment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_close_bar" />

    <TextView
        android:id="@+id/tv_destination_bank_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/destination_account"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <com.bukuwarung.payments.widget.PaymentBankAccountView
        android:id="@+id/bank_account_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_8dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_destination_bank_title" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bank_account_view" />

    <TextView
        android:id="@+id/tv_total_payment_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/label_total_payment"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    <TextView
        android:id="@+id/tv_total_payment_value"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/label_total_payment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_payment_title"
        tools:textColor="Rp1.000.000" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_amount_details"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_total_payment_title, tv_total_payment_value" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_make_new"
        style="@style/ButtonOutline.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:text="@string/make_new"
        app:layout_constraintEnd_toStartOf="@id/btn_continue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_payment_value" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_continue"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:text="@string/go_on"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_make_new"
        app:layout_constraintTop_toTopOf="@id/btn_make_new" />

</androidx.constraintlayout.widget.ConstraintLayout>
