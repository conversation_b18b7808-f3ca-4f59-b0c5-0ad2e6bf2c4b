<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_bottomsheet_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_6dp"
    android:paddingHorizontal="@dimen/_16dp">

    <ImageView
        android:id="@+id/img_row_icon"
        android:layout_width="52dp"
        android:layout_height="52dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_index"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/oval_blue10"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@{customer.initial}"
        android:textColor="@color/blue_80"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="1" />

    <TextView
        android:id="@+id/tv_left_main_text"
        android:layout_width="@dimen/_150dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:lineSpacingExtra="6sp"
        android:paddingVertical="@dimen/_4dp"
        android:textColor="@color/black_80"
        android:textSize="14sp"
        android:layout_marginStart="@dimen/_16dp"

        android:ellipsize="none"
        android:singleLine="false"
        app:layout_constraintStart_toEndOf="@+id/img_row_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_left_subtext"
        tools:text="text long long long lon123 456 789 123 456 789 1234 123 123 123 123" />
    <TextView
        android:id="@+id/tv_left_subtext"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:fontFamily="sans-serif"
        android:textColor="@color/black_80"
        android:textSize="12sp"
        android:textStyle="normal"
        android:text="0 Transaksi"
        app:layout_constraintTop_toBottomOf="@+id/tv_left_main_text"
        app:layout_constraintStart_toEndOf="@+id/img_row_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="0 Transaksi" />

    <TextView
        android:id="@+id/tv_right_subtext"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/black_60"
        android:textSize="12sp"
        android:text=""
        app:layout_constraintStart_toEndOf="@+id/tv_left_main_text"
        app:layout_constraintBottom_toBottomOf="@+id/tv_left_subtext"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Rp8.000.000000" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_1dp"
        android:layout_marginStart="@dimen/_16dp"
        android:background="@color/hrDivider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/img_row_icon"/>
</androidx.constraintlayout.widget.ConstraintLayout>