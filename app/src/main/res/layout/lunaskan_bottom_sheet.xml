<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_top_rounded_corner"
    android:padding="@dimen/_16dp">
    <TextView
        android:id="@+id/lunaskan_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="8dp"
        android:text="@string/lunaskan"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black_80"/>

    <ImageView
        android:id="@+id/closeDialog"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        android:src="@drawable/close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="5dp"
        android:layout_marginTop="13dp"/>

    <View
        android:id="@+id/border_nominal"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_button_outline"
        app:layout_constraintTop_toBottomOf="@id/lunaskan_label" />

    <TextView
        android:id="@+id/txt_main_title"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="@id/border_nominal"
        app:layout_constraintTop_toTopOf="@id/border_nominal"
        android:text="@string/receiving_label"
        android:textColor="@color/black_60"/>

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cb_sendSms"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_20dp"
        android:paddingStart="@dimen/_14dp"
        android:paddingEnd="@dimen/_0dp"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_14sp"
        android:text="@string/send_sms_proof"
        android:button="@drawable/ic_checkbox_not_checked"
        app:layout_constraintTop_toBottomOf="@id/border_nominal"
        app:layout_constraintStart_toStartOf="@id/border_nominal"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_18dp"/>


    <TextView
        android:visibility="gone"
        android:id="@+id/currency_symbol"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="3dp"
        android:fontFamily="@font/roboto"
        android:lineHeight="40sp"
        android:paddingRight="2dp"
        android:text="Rp"
        android:textColor="@color/black"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/balance"
        app:layout_constraintEnd_toStartOf="@id/balance"
        app:layout_constraintTop_toTopOf="@id/balance" />

    <TextView
        android:id="@+id/balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:autoSizeMaxTextSize="34dp"
        android:autoSizeMinTextSize="24dp"
        android:autoSizeStepGranularity="1dp"
        android:fontFamily="@font/roboto"
        android:hint="0"
        android:lineHeight="40sp"
        android:textColor="@color/black"
        android:textColorHint="@color/black"
        android:textSize="24.0sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/border_nominal"
        app:layout_constraintTop_toTopOf="@id/border_nominal" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/confirmationBtn"
        style="@style/DefaultMaterialButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/cb_sendSms"
        android:layout_marginTop="@dimen/_20dp"
        android:fontFamily="@font/roboto"
        android:text="@string/confirmation"
        android:textAllCaps="false"
        android:textColor="@color/cta_text_button"
        android:textSize="16sp"
        android:textStyle="bold"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:letterSpacing="0.05"
        app:backgroundTint="@color/buku_CTA"
        />

</androidx.constraintlayout.widget.ConstraintLayout>