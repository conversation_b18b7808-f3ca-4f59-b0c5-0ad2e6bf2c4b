<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_5"
    android:fitsSystemWindows="true">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="@dimen/dimen_0dp"
        app:layout_constraintBottom_toTopOf="@id/include_payment_method"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:id="@+id/vw_gradient"
                android:layout_width="@dimen/dimen_0dp"
                android:layout_height="@dimen/_100dp"
                android:background="@drawable/payment_tab_gradient_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.bukuwarung.payments.widget.PaymentDetailView
                android:id="@+id/pdv_view"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/ba_warning"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/pdv_view"
                app:type="warning" />

            <com.bukuwarung.ui_component.component.alert.BukuAlert
                android:id="@+id/ba_health_warning"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ba_warning"
                app:type="warning" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_layout_input_note"
                style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_16dp"
                app:boxBackgroundMode="none"
                app:hintEnabled="false"
                android:background="@drawable/bg_solid_white_corner_4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ba_health_warning"
                app:startIconDrawable="@drawable/ic_edit_square"
                app:startIconTint="@color/black_40">

                <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                    android:id="@+id/et_input_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:completionThreshold="1"
                    android:fontFamily="@font/roboto"
                    android:hint="@string/add_notes_optional"
                    android:imeOptions="actionDone"
                    android:inputType="textAutoComplete|textAutoCorrect"
                    android:lines="2"
                    android:minHeight="?attr/actionBarSize"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_0dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:paddingBottom="@dimen/_0dp"
                    android:singleLine="true"
                    android:textColor="@color/black_80"
                    android:textColorHint="@color/hint_color"
                    android:textSize="14sp" />

            </com.google.android.material.textfield.TextInputLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <include
        android:id="@+id/include_payment_method"
        layout="@layout/layout_order_form_payment_method"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

    <include
        android:id="@+id/include_payment_loading"
        layout="@layout/payment_loading_screen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>