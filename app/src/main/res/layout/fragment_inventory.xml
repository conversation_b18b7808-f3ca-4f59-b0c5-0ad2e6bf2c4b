<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground">

    <com.bukuwarung.activities.productcategory.view.ProductCategoryFilterView
        android:id="@+id/filter_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_associate_category"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/blue_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/filter_view"
        tools:visibility="visible">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_alert_circle"
            app:tint="@color/black_20" />

        <TextView
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_weight="1"
            android:text="@string/associate_this_category_label" />

        <TextView
            android:id="@+id/btn_associate_category"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:text="@string/associate_category"
            android:textColor="@color/colorPrimary" />

    </LinearLayout>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/filter_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="filter_view, ll_associate_category" />


    <View
        android:id="@+id/top_line"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/black_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/filter_barrier" />

    <include
        android:id="@+id/notFoundContainer"
        layout="@layout/stock_menipis_blank_screen"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_line" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/transactionRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_line"
        tools:listitem="@layout/inventory_product_item"
        tools:visibility="gone" />

    <include
        android:id="@+id/empty_screen_layout"
        layout="@layout/stock_empty_screen_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/top_line"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/no_record_found_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/no_stock_product_found"
        android:textColor="@color/black_60"
        android:textSize="15sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/filter_barrier" />

    <ImageView
        android:id="@+id/tutorarrow"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginRight="20dp"
        android:src="@drawable/ic_pointer"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/add_product_btn"
        app:layout_constraintEnd_toStartOf="@+id/add_product_btn"
        app:layout_constraintTop_toTopOf="@+id/add_product_btn"
        app:tint="#DC770E" />

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/add_product_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/add_new_product"
        android:textStyle="bold"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@mipmap/ic_plus_white_24dp"
        app:iconGravity="start"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
