<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dataLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_below="@+id/headerLayout"
    android:background="#e2e2e2"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="6sp"
            android:maxLines="1"
            android:layout_marginEnd="8dp"
            android:text="-"
            android:textColor="@color/black_60"
            android:textSize="15sp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="12sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/expenseAmount"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Test 123" />

        <TextView
            android:id="@+id/expenseAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="end"
            android:lineSpacingExtra="6sp"
            android:maxLines="1"
            android:text="-"
            android:textColor="@color/out_red"
            android:textSize="16sp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="16sp"
            app:autoSizeMinTextSize="14sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/name"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include layout="@layout/category_transaction_header_item"
        android:id="@+id/transaction_header"/>
</LinearLayout>
