<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:theme="@style/Theme.MaterialComponents.Light"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardCornerRadius="@dimen/_22dp"
    app:cardElevation="@dimen/_4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/blue_60">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/ll_filter"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:paddingTop="@dimen/_8dp"
            android:paddingBottom="@dimen/_8dp"
            app:drawableStartCompat="@drawable/ic_arrow_up_white"
            android:drawablePadding="@dimen/_8dp"
            android:text="Kembali ke atas"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>