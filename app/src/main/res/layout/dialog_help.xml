<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginEnd="@dimen/_10dp"
    android:layout_marginStart="@dimen/_10dp"
    android:padding="@dimen/_20dp">

    <TextView
        android:id="@+id/tv_help_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/hubungi_cs_bukuwarung"
        style="@style/Heading2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_help_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_help_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_help_title" />


   <androidx.cardview.widget.CardView
       android:id="@+id/cv_help_chat"
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       app:cardCornerRadius="@dimen/_10dp"
       android:layout_marginStart="@dimen/_1dp"
       android:layout_marginEnd="@dimen/_1dp"
       android:layout_marginTop="@dimen/_30dp"
       app:cardUseCompatPadding="true"
       app:layout_constraintTop_toBottomOf="@id/tv_help_title" >

       <androidx.constraintlayout.widget.ConstraintLayout
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:paddingStart="@dimen/_20dp"
           android:paddingTop="@dimen/_20dp"
           android:paddingBottom="@dimen/_20dp"
           android:paddingEnd="@dimen/_10dp">

           <androidx.appcompat.widget.AppCompatImageView
               android:id="@+id/iv_help_wa"
               android:layout_width="@dimen/_50dp"
               android:layout_height="@dimen/_50dp"
               android:src="@drawable/chat_wa"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toTopOf="parent" />

           <TextView
               android:id="@+id/tv_live_chat_heading"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:text="@string/live_chat"
               android:layout_marginStart="@dimen/_20dp"
               style="@style/Label1"
               app:layout_constraintTop_toTopOf="@id/iv_help_wa"
               app:layout_constraintStart_toEndOf="@id/iv_help_wa" />

           <TextView
               android:id="@+id/tv_live_chat_description"
               android:layout_width="@dimen/_0dp"
               android:layout_height="wrap_content"
               android:text="@string/live_chat_text"
               style="@style/Body1Sans"
               android:layout_marginEnd="@dimen/_10dp"
               app:layout_constraintEnd_toStartOf="@id/iv_forward_chat"
               app:layout_constraintTop_toBottomOf="@id/tv_live_chat_heading"
               app:layout_constraintStart_toStartOf="@id/tv_live_chat_heading" />

           <androidx.appcompat.widget.AppCompatImageView
               android:id="@+id/iv_forward_chat"
               android:layout_width="@dimen/_20dp"
               android:layout_height="@dimen/_20dp"
               app:srcCompat="@drawable/forward_blue"
               app:layout_constraintTop_toTopOf="parent"
               app:layout_constraintBottom_toBottomOf="parent"
               app:layout_constraintEnd_toEndOf="parent" />

       </androidx.constraintlayout.widget.ConstraintLayout>

   </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_help_call"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/_10dp"
        android:layout_marginStart="@dimen/_1dp"
        android:layout_marginEnd="@dimen/_1dp"
        android:layout_marginTop="@dimen/_20dp"
        app:cardUseCompatPadding="true"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@id/cv_help_chat" >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_20dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_help_chat"
                android:layout_width="@dimen/_50dp"
                android:layout_height="@dimen/_50dp"
                android:src="@drawable/live_chat"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_phone_chat_heading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/telepon"
                android:layout_marginStart="@dimen/_20dp"
                style="@style/Label1"
                app:layout_constraintTop_toTopOf="@id/iv_help_chat"
                app:layout_constraintStart_toEndOf="@id/iv_help_chat" />

            <TextView
                android:id="@+id/tv_phone_chat_description"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:text="@string/telepon_text"
                android:layout_marginEnd="@dimen/_10dp"
                style="@style/Body1Sans"
                app:layout_constraintEnd_toStartOf="@id/iv_forward_phone"
                app:layout_constraintTop_toBottomOf="@id/tv_phone_chat_heading"
                app:layout_constraintStart_toStartOf="@id/tv_phone_chat_heading" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_forward_phone"
                android:layout_width="@dimen/_20dp"
                android:layout_height="@dimen/_20dp"
                app:srcCompat="@drawable/forward_blue"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>