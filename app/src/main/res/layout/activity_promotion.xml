<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/coordinatorLayout"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:background="@color/white"
        android:fitsSystemWindows="true"
        app:layout_constraintBottom_toTopOf="@id/linearlayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fitsSystemWindows="true"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <ImageView
                    android:id="@+id/iv_banner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="?attr/actionBarSize"
                    android:scaleType="fitXY"
                    app:srcCompat="@drawable/promotion_banner" />


                <include
                    android:id="@+id/include_tool_bar"
                    layout="@layout/layout_activity_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top"
                    app:layout_collapseMode="pin" />
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <ImageView
                android:id="@+id/iv_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="@dimen/_13dp"
                android:background="@drawable/bg_solid_grey_corner_16_stroke_1"
                android:paddingHorizontal="@dimen/_14dp"
                android:paddingVertical="@dimen/_9dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_category_filter" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_filter"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginTop="@dimen/_13dp"
                android:layout_marginEnd="@dimen/_10dp"
                android:paddingBottom="@dimen/_10dp"
                android:visibility="gone"
                android:background="@color/white"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_filter"
                app:layout_constraintTop_toTopOf="parent" />

            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.bukuwarung.ui_component.component.error_view.BukuErrorView
                android:id="@+id/buku_error_view"
                android:layout_width="@dimen/dimen_0dp"
                android:layout_height="@dimen/dimen_0dp"
                android:layout_marginTop="@dimen/dimen_10dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rv_filter"
                tools:visibility="visible" />

            <FrameLayout
                android:id="@+id/fl_fragment_container"
                android:layout_width="@dimen/_0dp"
                android:layout_height="@dimen/_0dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_filter"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/linearlayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal"
        android:padding="@dimen/_8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_download"
            style="@style/ButtonOutline"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_weight="1"
            android:text="@string/download"
            app:icon="@drawable/ic_download_new"
            app:iconTint="@color/yellow60" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_share"
            style="@style/ButtonFill"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_weight="1"
            android:text="@string/share"
            app:icon="@drawable/ic_share" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
