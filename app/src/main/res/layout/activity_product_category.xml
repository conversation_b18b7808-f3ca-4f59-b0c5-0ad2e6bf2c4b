<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".activities.productcategory.view.ProductCategoryActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:theme="@style/Toolbar"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="<PERSON><PERSON><PERSON>"
        app:titleTextAppearance="@style/Heading2"
        app:titleTextColor="@color/white" />

    <LinearLayout
        android:id="@+id/ll_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/tb">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_search"
            style="@style/OutlineTextInputStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_6dp"
            android:layout_weight="1"
            android:focusedByDefault="false"
            app:endIconDrawable="@drawable/ic_mtrl_chip_close_circle"
            app:endIconMode="clear_text"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:startIconDrawable="@mipmap/search_black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusedByDefault="false"
                android:hint="@string/search_category"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_add_category"
            style="@style/ButtonFill.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_6dp"
            android:layout_weight="1"
            android:text="@string/add_category" />

    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_8dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@id/ll_action" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_product_categories"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toTopOf="@id/divider2"
        app:layout_constraintTop_toBottomOf="@id/divider" />

    <TextView
        android:id="@+id/no_record_found_txt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_32dp"
        android:layout_marginTop="@dimen/_24dp"
        android:gravity="center"
        android:text="@string/no_category_found"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/divider"
        tools:visibility="visible" />


    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="@color/black_5"
        app:layout_constraintBottom_toTopOf="@id/btn_container" />

    <FrameLayout
        android:id="@+id/btn_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_select_category"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/choose_category" />

    </FrameLayout>

    <LinearLayout
        android:id="@+id/ll_blank"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingStart="@dimen/_24dp"
        android:paddingEnd="@dimen/_24dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb">

        <TextView
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/add_category_guide_title"
            android:textAlignment="center"
            android:textColor="@color/colorPrimary" />

        <TextView
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/add_category_guide_reason"
            android:textAlignment="center" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_category_blank" />

        <TextView
            android:id="@+id/tv_blank_category_message"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/add_new_category_guide"
            android:textAlignment="center" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_add_category_from_blank"
            style="@style/ButtonFill.Blue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/add_category"
            app:icon="@drawable/ic_plus_blue" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>