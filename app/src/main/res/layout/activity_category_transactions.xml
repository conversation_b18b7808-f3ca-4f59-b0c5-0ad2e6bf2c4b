<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:id="@+id/categoryProfileLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/back_btn"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginRight="12dp"
                    android:src="@drawable/ic_back"
                    app:tint="@color/white" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@+id/back_btn">

                    <TextView
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/roboto"
                        android:maxLines="1"
                        android:text="Category"
                        android:textColor="#ffffff"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </RelativeLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_edit_category"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="26dp"
                    android:layout_height="29dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="8dp"
                    android:fontFamily="@font/roboto"
                    android:maxLines="1"
                    android:visibility="gone"
                    app:autoSizeMaxTextSize="14sp"
                    app:autoSizeMinTextSize="12sp"
                    app:autoSizeStepGranularity="1sp"
                    app:autoSizeTextType="uniform"
                    app:backgroundTint="@color/fui_transparent"
                    app:icon="@drawable/ic_edit"
                    app:iconTint="@color/white" />
            </RelativeLayout>
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/ablCategoryTransaction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <RelativeLayout
                    android:id="@+id/category_reminder_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/toolbar"
                    android:background="#F1F1F1">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:elevation="2dp"
                        android:layout_margin="@dimen/_16dp"
                        android:layout_height="wrap_content">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:paddingLeft="@dimen/_16dp"
                            android:layout_marginRight="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginBottom="@dimen/_16dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/balanceStatus"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentLeft="true"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/roboto"
                                android:text="@string/cash_out_summary_text"
                                android:textColor="@color/black_60"
                                android:textSize="16sp"
                                app:autoSizeMaxTextSize="16sp"
                                app:autoSizeMinTextSize="10sp"
                                app:autoSizeStepGranularity="2sp"
                                app:autoSizeTextType="uniform" />
                            <TextView
                                android:id="@+id/categoryBalance"
                                android:layout_alignParentRight="true"
                                android:fontFamily="@font/roboto"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_marginRight="4dp"
                                android:maxLines="1"
                                android:text="0"
                                android:textColor="@color/out_red"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                app:autoSizeTextType="uniform" />
                        </RelativeLayout>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:id="@+id/profit_layout"
                            android:orientation="vertical">
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:paddingLeft="@dimen/_16dp"
                                android:paddingRight="@dimen/_16dp"
                                android:layout_marginTop="-4dp"
                                android:layout_marginBottom="@dimen/_16dp"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/roboto"
                                    android:id="@+id/buying_label"
                                    android:layout_centerVertical="true"
                                    android:layout_alignParentLeft="true"
                                    android:textColor="@color/black_60"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeStepGranularity="2sp"
                                    app:autoSizeTextType="uniform"
                                    android:text="@string/cash_in_buying_price"
                                    />
                                <TextView
                                    android:id="@+id/buying_amount_value"
                                    android:layout_alignParentRight="true"
                                    android:fontFamily="@font/roboto"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="16dp"
                                    android:layout_marginRight="4dp"
                                    android:maxLines="1"
                                    android:text="0"
                                    android:textColor="@color/out_red"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    app:autoSizeTextType="uniform" />
                            </RelativeLayout>
                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#eeeeee" />
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:paddingLeft="@dimen/_16dp"
                                android:paddingRight="@dimen/_16dp"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/roboto"
                                    android:id="@+id/profit_label"
                                    android:layout_centerVertical="true"
                                    android:layout_alignParentLeft="true"
                                    android:textColor="@color/black_60"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeStepGranularity="2sp"
                                    app:autoSizeTextType="uniform"
                                    android:text="@string/cash_in_profit_text"
                                    />
                                <TextView
                                    android:id="@+id/profit_val"
                                    android:layout_alignParentRight="true"
                                    android:fontFamily="@font/roboto"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="16dp"
                                    android:layout_marginTop="16dp"
                                    android:layout_marginRight="4dp"
                                    android:layout_marginBottom="16dp"
                                    android:maxLines="1"
                                    android:text="0"
                                    android:textColor="@color/heading_text"
                                    android:textSize="15sp"
                                    android:textStyle="bold"
                                    app:autoSizeMaxTextSize="24sp"
                                    app:autoSizeMinTextSize="12sp"
                                    app:autoSizeStepGranularity="2sp"
                                    app:autoSizeTextType="uniform" />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>

                </RelativeLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eff4ff"
                android:paddingStart="16dp"
                android:paddingTop="8dp"
                android:paddingEnd="16dp"
                android:paddingBottom="8dp">

                <CheckBox
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/selectorAll"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto"
                    android:text="@string/date"
                    android:textColor="@color/black_60"
                    />

                <TextView
                    android:id="@+id/tvColMoneyIn"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto"
                    android:textAlignment="center"
                    android:textColor="@color/black_60"
                    tools:text="@string/income_label" />

                <TextView
                    android:id="@+id/tvRowStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/black_60"
                    tools:text="Pengeluaran" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/transactionRV"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:elevation="3dp" />

        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/transactionButtonLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:elevation="@dimen/stdElevation"
        android:orientation="vertical">

        <TextView
            android:id="@+id/section"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_above="@+id/bottom"
            android:background="@color/section_end" />

        <LinearLayout
            android:id="@+id/bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:visibility="gone"
            >

            <com.google.android.material.button.MaterialButton
                android:id="@+id/debitBtn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginRight="@dimen/_16dp"
                android:layout_weight="1"
                android:backgroundTint="@color/buku_CTA"
                android:elevation="3dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:padding="12dp"
                android:text="@string/add_cash_out_header"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textStyle="bold"
                app:cornerRadius="4dp"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/creditBtn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginRight="@dimen/_16dp"
                android:layout_weight="1"
                android:backgroundTint="@color/buku_CTA"
                android:elevation="3dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:padding="12dp"
                android:text="@string/add_cash_in_header"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textStyle="bold"
                app:cornerRadius="4dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/bottomLayoutNew"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/deleteTransaction"
                style="@style/ButtonOutline"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginRight="@dimen/_16dp"
                android:layout_weight="1"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_14sp"
                android:text="@string/delete_transaction"
                android:textAllCaps="false"
                />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/changeCategory"
                android:layout_width="0dp"
                style="@style/ButtonOutline"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_marginRight="@dimen/_16dp"
                android:layout_weight="1"
                android:backgroundTint="@color/web_orange"
                android:text="@string/change_category"
                android:textAllCaps="false"
                android:textSize="@dimen/text_14sp"
                android:textColor="@color/black_80"
                />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
