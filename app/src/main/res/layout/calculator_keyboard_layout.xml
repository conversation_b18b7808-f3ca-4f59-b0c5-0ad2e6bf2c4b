<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardElevation="12dp">

    <RelativeLayout
        android:id="@+id/calculator_input_view"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:background="@color/colorGreyLight"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/keylayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentLeft="true"
            android:layout_alignParentBottom="true"
            android:baselineAligned="false"
            android:orientation="horizontal"
            android:weightSum="4.0">

            <LinearLayout
                android:layout_width="0.0dip"
                android:layout_height="wrap_content"
                android:layout_weight="1.0"
                android:orientation="vertical">

                <Button
                    android:id="@+id/clear_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="C" />

                <Button
                    android:id="@+id/one_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="1" />

                <Button
                    android:id="@+id/four_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="4" />

                <Button
                    android:id="@+id/seven_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="7" />

                <Button
                    android:id="@+id/zero_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="0" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0.0dip"
                android:layout_height="wrap_content"
                android:layout_weight="1.0"
                android:orientation="vertical">

                <Button
                    android:id="@+id/divider_button"
                    style="@style/PadButtonStyle.Numeric.Operator"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"

                    android:text="÷" />

                <Button
                    android:id="@+id/two_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"

                    android:text="2" />


                <Button
                    android:id="@+id/five_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"

                    android:text="5" />

                <Button
                    android:id="@+id/eight_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="8" />

                <Button
                    android:id="@+id/two_zero_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="000" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0.0dip"
                android:layout_height="wrap_content"
                android:layout_weight="1.0"
                android:orientation="vertical">

                <Button
                    android:id="@+id/multiplication_button"
                    style="@style/PadButtonStyle.Numeric.Operator"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="x" />

                <Button
                    android:id="@+id/three_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="3" />


                <Button
                    android:id="@+id/six_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="6" />

                <Button
                    android:id="@+id/nine_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="9" />

                <Button
                    android:id="@+id/point_button"
                    style="@style/PadButtonStyle.Numeric"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="." />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0.0dip"
                android:layout_height="fill_parent"
                android:layout_weight="1.0"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/delete_button"
                    style="@style/PadButtonStyle.Numeric.Operator"
                    android:layout_width="fill_parent"
                    android:layout_weight="0.0"
                    android:padding="12dp"
                    android:src="@drawable/ic_backspace" />

                <Button
                    android:id="@+id/subtraction_button"
                    style="@style/PadButtonStyle.Numeric.Operator"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.0"
                    android:text="-" />

                <Button
                    android:id="@+id/sum_button"
                    style="@style/PadButtonStyle.Numeric.Operator"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.0"
                    android:text="+" />

                <Button
                    android:id="@+id/equal_button"
                    style="@style/PadButtonStyle.Numeric.Green"
                    android:layout_width="fill_parent"
                    android:layout_height="fill_parent"
                    android:text="="
                    android:visibility="gone" />

                <Button
                    android:id="@+id/submit_button"
                    style="@style/PadButtonStyle.Numeric.Operator"
                    android:layout_width="fill_parent"
                    android:layout_height="fill_parent"
                    android:background="@color/buku_CTA"
                    android:padding="4dp"
                    android:src="@drawable/ic_check_mark"
                    android:text="@string/save"
                    android:textColor="@color/cta_button_text"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    app:autoSizeMaxTextSize="16sp"
                    app:autoSizeMinTextSize="12sp"
                    app:autoSizeStepGranularity="1sp" />
            </LinearLayout>
        </LinearLayout>

    </RelativeLayout>
</androidx.cardview.widget.CardView>
