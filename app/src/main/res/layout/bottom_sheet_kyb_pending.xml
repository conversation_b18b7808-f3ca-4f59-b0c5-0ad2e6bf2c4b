<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp">

    <View
        android:id="@+id/vw_divider"
        style="@style/Divider"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_shop_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider"
        app:srcCompat="@drawable/ic_shop_name" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@id/iv_shop_name" />

    <TextView
        android:id="@+id/tv_body"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_close"
        style="@style/ButtonOutline.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_40dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:text="@string/cancel_btn"
        app:layout_constraintEnd_toStartOf="@id/btn_take_photo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_body" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_take_photo"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_40dp"
        android:text="@string/shop_verification"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_close"
        app:layout_constraintTop_toBottomOf="@id/tv_body" />

</androidx.constraintlayout.widget.ConstraintLayout>