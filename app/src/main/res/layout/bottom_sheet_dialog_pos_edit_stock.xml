<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_corner_background_bottom_sheet"
    android:orientation="vertical"
    android:padding="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/label_favourite"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:background="@drawable/button_round_cornerd_edit_stroke_pop_up"
        android:drawableStart="@drawable/ic_fav_grey"
        android:drawablePadding="@dimen/_4dp"
        android:paddingHorizontal="@dimen/_12dp"
        android:gravity="center"
        android:paddingVertical="@dimen/_8dp"
        android:text="@string/favourite_label"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/productName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/black"
        android:textSize="@dimen/text_18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/label_favourite"
        app:layout_constraintStart_toEndOf="@id/label_favourite"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Product name" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:src="@drawable/ic_cross"
        android:padding="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="@+id/label_favourite"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_product_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/selling_price"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/productName" />

    <TextView
        android:id="@+id/tv_product_price_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/_16dp"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/productName"
        tools:text="Rp50.000" />

    <TextView
        android:id="@+id/tv_stock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:drawablePadding="3dp"
        android:fontFamily="@font/roboto"
        android:text="@string/stock_of_goods"
        android:textColor="@color/black_80"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_product_price" />

    <TextView
        android:id="@+id/tv_stock_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/_20dp"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_product_price"
        tools:text="50 Karung" />

    <include
        android:id="@+id/numberStepperLayout"
        layout="@layout/pos_number_stepper"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/_36dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_stock" />

    <TextView
        android:id="@+id/tv_empty_cart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_40dp"
        android:text="@string/delete_items"
        android:textColor="@color/red_100"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/numberStepperLayout" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_confirm"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/_40dp"
        android:text="@string/save"
        android:textAllCaps="true"
        app:layout_constraintTop_toBottomOf="@id/tv_empty_cart" />

    <TextView
        android:id="@+id/tv_expr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_currency"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/ll_result"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/cursor"
        android:layout_width="2.0dip"
        android:layout_height="28.0dip"
        android:layout_gravity="center_vertical"
        android:background="@color/black_60"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/keyboardView"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>