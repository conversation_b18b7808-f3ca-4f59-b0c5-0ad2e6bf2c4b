<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/_12dp">

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/black5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/name_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black80"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toStartOf="@id/quantity_text"
        app:layout_constraintHorizontal_weight="1.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />

    <TextView
        android:id="@+id/quantity_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:textAlignment="textEnd"
        android:textColor="@color/black40"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintStart_toEndOf="@id/name_text"
        app:layout_constraintTop_toBottomOf="@id/divider" />

</androidx.constraintlayout.widget.ConstraintLayout>
