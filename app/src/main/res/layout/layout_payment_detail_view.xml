<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_rectangle_white_8dp"
    android:paddingBottom="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_destination_bank_account"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/destination_account"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_add_fav"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/add_to_fav"
        android:textColor="@color/black_20"
        app:drawableEndCompat="@drawable/ic_favourite_grey"
        app:layout_constraintBottom_toBottomOf="@+id/tv_destination_bank_account"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_destination_bank_account" />

    <com.bukuwarung.payments.widget.PaymentBankAccountView
        android:id="@+id/bank_account_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_add_fav" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bank_account_view" />

    <TextView
        android:id="@+id/tv_total_amount"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/label_total_payment"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    <TextView
        android:id="@+id/tv_total_amount_value"
        style="@style/Heading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_amount"
        tools:text="Rp1.002.000" />

    <TextView
        android:id="@+id/tv_total_amount_received_by_customer"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/total_received_by_customer"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_amount_value"
        tools:text="Total Diterima Pelanggan" />

    <TextView
        android:id="@+id/tv_total_amount_received_by_customer_value"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_amount_value"
        tools:text="Rp1.000.000" />

    <TextView
        android:id="@+id/tv_admin_fee_title"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/platform_fee"
        android:textColor="@color/black_40"
        app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_total_amount_received_by_customer" />

    <TextView
        android:id="@+id/tv_admin_fee"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:background="@drawable/strike_through"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/tv_discounted_fee"
        app:layout_constraintTop_toBottomOf="@+id/tv_total_amount_received_by_customer_value"
        tools:text="Rp2.500"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_discounted_fee"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/blue60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_total_amount_received_by_customer_value"
        tools:text="Rp2.000"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_fee"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_admin_fee, tv_discounted_fee" />

    <TextView
        android:id="@+id/tv_loyalty_tier"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/br_fee" />

    <TextView
        android:id="@+id/tv_loyalty_discount"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/blue60"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_loyalty_tier" />

    <TextView
        android:id="@+id/tv_subscription_discount"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/discount_bukuwarung_plus"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/ic_info_grey_icon_lt_grey_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_loyalty_tier" />

    <TextView
        android:id="@+id/tv_subscription_discount_value"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/blue60"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_subscription_discount" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gp_fee"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="br_fee, tv_admin_fee, tv_admin_fee_title, tv_total_amount_received_by_customer_value, tv_total_amount_received_by_customer, tv_total_amount_value, tv_total_amount, vw_divider" />
</androidx.constraintlayout.widget.ConstraintLayout>