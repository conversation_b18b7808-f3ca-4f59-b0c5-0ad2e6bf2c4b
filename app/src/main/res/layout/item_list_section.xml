<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="sectionTitle"
            type="String" />
    </data>

    <TextView
        android:id="@+id/txt_section_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/section_color"
        android:fontFamily="@font/roboto"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="8dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="8dp"
        android:text="@{sectionTitle}"
        android:textColor="@color/black_60"
        android:textSize="15sp"
        android:textStyle="bold" />

</layout>