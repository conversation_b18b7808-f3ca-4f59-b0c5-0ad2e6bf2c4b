<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="@dimen/_8dp"
    android:id="@+id/empty_info_root"
    android:background="@drawable/bg_rounded_rectangle_white_8dp"
    >

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_info"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_48dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:src="@drawable/business_empty_utang"
        android:background="@drawable/oval_blue10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="3"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_header"
        android:layout_marginTop="@dimen/_5dp"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_12dp"
        android:text="@string/pencatatan_keuangan_bisnis"
        android:textColor="@color/black_80"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_15dp"
        app:layout_constraintEnd_toStartOf="@+id/ic_info"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_subtext"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:layout_marginTop="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="@dimen/_2sp"
        style="@style/Body2"
        android:text="@string/penting_buat_juragan_catat_utang"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@+id/ic_info"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_header" />

    <TextView
        android:id="@+id/tv_body"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_5dp"
        android:text="@string/pelajari_fitur"
        android:textSize="@dimen/text_14sp"
        android:textColor="@color/blue_60"
        app:layout_constraintStart_toEndOf="@+id/btn_try_it_now"
        app:layout_constraintTop_toBottomOf="@id/tv_subtext"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_try_it_now"
        style="@style/ButtonFill.Blue"
        android:layout_width="@dimen/_0dp"
        android:layout_marginTop="@dimen/_5dp"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_2dp"
        android:paddingBottom="@dimen/_2dp"
        android:layout_marginHorizontal="@dimen/_12dp"
        android:text="@string/coba_sekarang"
        android:textAllCaps="false"
        android:textAppearance="@style/SubHeading1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subtext"
        app:layout_constraintBottom_toBottomOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>

