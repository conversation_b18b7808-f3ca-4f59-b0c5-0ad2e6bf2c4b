<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_bank_account"
        android:layout_width="56dp"
        android:layout_height="44dp"
        android:layout_marginVertical="@dimen/_12dp"
        android:layout_marginStart="@dimen/_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_empty_bank" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:text="@string/new_payment_out_bank_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_subtitle"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintStart_toEndOf="@id/iv_bank_account"
        app:layout_constraintTop_toTopOf="@+id/iv_bank_account" />

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/new_payment_out_bank_subtitle"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="@+id/iv_bank_account"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintStart_toEndOf="@id/iv_bank_account"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="left"
        app:constraint_referenced_ids="iv_add_bank, tv_change_bank_account" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_add_bank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_plus_yellow" />

    <TextView
        android:id="@+id/tv_change_bank_account"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/label_change"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>