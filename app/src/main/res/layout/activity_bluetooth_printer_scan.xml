<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".activities.print.setup.BluetoothPrinterScanActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:paddingStart="@dimen/_0dp"
        android:paddingEnd="@dimen/_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme"
        app:titleTextColor="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@color/colorPrimary"
                android:fontFamily="@font/roboto"
                app:srcCompat="@drawable/ic_back" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_24dp"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_24dp"
                android:layout_toEndOf="@+id/iv_back"
                android:background="@color/colorPrimary"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="top"
                android:lineHeight="@dimen/_26dp"
                android:lineSpacingExtra="@dimen/text_8sp"
                android:maxLines="1"
                android:text="Bluetooth"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:drawableLeftCompat="@drawable/dot_highlighter"
                tools:ignore="UnusedAttribute" />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_refresh"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@color/colorPrimary"
                android:fontFamily="@font/roboto"
                app:srcCompat="@drawable/ic_refresh_svg" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_bluetooth_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/black_34"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        tools:text="@string/devices_available" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_printer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_bluetooth_title"
        tools:listitem="@layout/printer_item" />

    <include
        android:id="@+id/no_printer_view"
        layout="@layout/layout_no_printer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <include
        android:id="@+id/scanning_printer_view"
        layout="@layout/layout_scanning_printer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/printer_snackbar_guideline"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:layout_marginBottom="@dimen/_16dp" />

</androidx.constraintlayout.widget.ConstraintLayout>