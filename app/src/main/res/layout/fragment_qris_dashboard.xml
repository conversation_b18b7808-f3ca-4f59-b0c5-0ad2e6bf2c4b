<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black5">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/vw_top_background"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_300dp"
            android:background="@drawable/bg_blue_white_gradient"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dimen_16dp"
            android:background="@drawable/bg_rounded_rectangle_8dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_transaksi_qris"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:padding="@dimen/_8dp"
                android:text="@string/payment_with_qris"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_date_filter"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="@drawable/bg_corner_16dp_stroke_black10"
                android:drawablePadding="@dimen/_8dp"
                android:padding="@dimen/_8dp"
                android:text="@string/filter_today"
                app:drawableEndCompat="@drawable/ic_chevron_down"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_date_range"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_32dp"
                android:drawablePadding="@dimen/_4dp"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_transaksi_qris"
                tools:text="30 Juni 2022 - 6 Juli 2022" />

            <ImageView
                android:id="@+id/iv_previous"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="@id/tv_date_range"
                app:layout_constraintBottom_toBottomOf="@id/tv_date_range"
                app:layout_constraintStart_toStartOf="parent"
                app:srcCompat="@drawable/ic_chevron_left_black"
                android:layout_marginStart="@dimen/_16dp"
                android:padding="@dimen/_4dp"/>

            <ImageView
                android:id="@+id/iv_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="@id/tv_date_range"
                app:layout_constraintBottom_toBottomOf="@id/tv_date_range"
                app:layout_constraintEnd_toEndOf="parent"
                app:srcCompat="@drawable/ic_chevron_right_black"
                android:layout_marginEnd="@dimen/_16dp"
                android:padding="@dimen/_4dp"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_amount_details"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:background="@drawable/bg_black_outline"
                android:padding="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_date_range">

                <View
                    android:id="@+id/vw_divider_vertical"
                    android:layout_width="@dimen/_1dp"
                    android:layout_height="@dimen/_0dp"
                    android:background="@color/black_0"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_already_received_label"
                    style="@style/Body3"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/already_entered_account"
                    app:layout_constraintEnd_toStartOf="@id/vw_divider_vertical"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_already_received"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/green_60"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_already_received_label"
                    tools:text="Rp200.000" />

                <TextView
                    android:id="@+id/tv_yet_to_be_received_label"
                    style="@style/Body3"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/yet_to_enter_account"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/vw_divider_vertical"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_yet_to_be_received"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:textColor="@color/black60"
                    app:layout_constraintStart_toStartOf="@id/vw_divider_vertical"
                    app:layout_constraintTop_toBottomOf="@id/tv_yet_to_be_received_label"
                    tools:text="Rp50.000" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_total_payment_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/label_total_payment"
                android:textColor="@color/black40"
                android:textSize="@dimen/text_18sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_amount_details" />

            <TextView
                android:id="@+id/tv_total_payment_value"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:textColor="@color/green_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_total_payment_label"
                tools:text="Rp250.000" />

            <com.github.mikephil.charting.charts.PieChart
                android:id="@+id/pc_qris_transactions"
                android:layout_width="@dimen/_160dp"
                android:layout_height="@dimen/_160dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_total_payment_value" />

            <ImageView
                android:id="@+id/iv_already_entered_account_colour"
                android:layout_width="@dimen/_16dp"
                android:layout_height="@dimen/_16dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@color/green_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/pc_qris_transactions" />

            <TextView
                android:id="@+id/tv_already_entered_label"
                style="@style/SubHeading1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/already_entered_account"
                app:layout_constraintBottom_toBottomOf="@id/iv_already_entered_account_colour"
                app:layout_constraintEnd_toStartOf="@id/tv_already_entered_percentage"
                app:layout_constraintStart_toEndOf="@id/iv_already_entered_account_colour"
                app:layout_constraintTop_toTopOf="@id/iv_already_entered_account_colour" />

            <TextView
                android:id="@+id/tv_already_entered_percentage"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/black40"
                app:layout_constraintBottom_toBottomOf="@id/iv_already_entered_account_colour"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_already_entered_account_colour"
                tools:text="80%" />

            <TextView
                android:id="@+id/tv_already_entered_amount"
                style="@style/Body3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:textColor="@color/black40"
                app:layout_constraintStart_toStartOf="@id/tv_already_entered_label"
                app:layout_constraintTop_toBottomOf="@id/tv_already_entered_label"
                tools:text="Rp1.600.000" />

            <TextView
                android:id="@+id/tv_already_entered_transactions"
                style="@style/Body3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/black40"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_already_entered_percentage"
                tools:text="4 transaksi" />

            <View
                android:id="@+id/vw_divider_horizontal"
                style="@style/Divider.Horizontal"
                android:layout_width="@dimen/_0dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tv_already_entered_amount"
                app:layout_constraintTop_toBottomOf="@id/tv_already_entered_amount" />

            <ImageView
                android:id="@+id/iv_not_entered_account_colour"
                android:layout_width="@dimen/_16dp"
                android:layout_height="@dimen/_16dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@color/black20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vw_divider_horizontal" />

            <TextView
                android:id="@+id/tv_not_entered_label"
                style="@style/SubHeading1"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:text="@string/yet_to_enter_account"
                app:layout_constraintBottom_toBottomOf="@id/iv_not_entered_account_colour"
                app:layout_constraintEnd_toStartOf="@id/tv_already_entered_percentage"
                app:layout_constraintStart_toEndOf="@id/iv_not_entered_account_colour"
                app:layout_constraintTop_toTopOf="@id/iv_not_entered_account_colour" />

            <TextView
                android:id="@+id/tv_not_entered_percentage"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/black40"
                app:layout_constraintBottom_toBottomOf="@id/iv_not_entered_account_colour"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_not_entered_account_colour"
                tools:text="20%" />

            <TextView
                android:id="@+id/tv_not_entered_amount"
                style="@style/Body3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:textColor="@color/black40"
                app:layout_constraintStart_toStartOf="@id/tv_not_entered_label"
                app:layout_constraintTop_toBottomOf="@id/tv_not_entered_label"
                tools:text="Rp400.000" />

            <TextView
                android:id="@+id/tv_not_entered_transactions"
                style="@style/Body3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/black40"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_not_entered_percentage"
                tools:text="1 transaksi" />

            <View
                android:id="@+id/vw_divider_horizontal_2"
                style="@style/Divider.Horizontal"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_not_entered_amount" />

            <TextView
                android:id="@+id/tv_view_payment_history"
                style="@style/SubHeading1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/_16dp"
                android:text="@string/view_payment_history"
                android:textAlignment="center"
                android:textColor="@color/blue60"
                app:layout_constraintTop_toBottomOf="@id/vw_divider_horizontal_2" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>


    </androidx.constraintlayout.widget.ConstraintLayout>


</ScrollView>