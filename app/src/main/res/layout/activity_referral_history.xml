<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_alignParentTop="true"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:elevation="8dp"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="25dp"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_height="25dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/title"
                android:layout_marginLeft="24dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:text="@string/referral_history_title"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textColor="#ffffff"
                android:textStyle="normal" />
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/historyRv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:background="@color/white"
        android:visibility="gone" />

    <include
        android:id="@+id/empty_view"
        layout="@layout/default_empty_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:visibility="gone" />

    <ProgressBar
        android:id="@+id/rvLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:progressTint="@color/colorPrimary"
        android:padding="@dimen/_16dp"
        android:layout_gravity="center"
        android:layout_centerInParent="true"
        android:visibility="visible"/>

</RelativeLayout>