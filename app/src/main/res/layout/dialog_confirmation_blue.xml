<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp"
    >

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:textSize="@dimen/text_18sp"
        tools:text="@string/disable_integration"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:textSize="@dimen/text_14sp"
        tools:text="@string/disable_integration_info"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/no_btn"
        style="@style/ButtonOutline.Blue"
        android:textAppearance="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:paddingVertical="@dimen/_11dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_8dp"
        tools:text="@string/cancel"
        app:cornerRadius="@dimen/_4dp"
        android:textSize="@dimen/text_14sp"
        android:textAllCaps="false"
        app:layout_constraintEnd_toStartOf="@+id/yes_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message"
        />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/yes_btn"
        style="@style/ButtonFill.Blue"
        android:textAppearance="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        tools:text="@string/disable"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="@+id/no_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/no_btn"
        app:layout_constraintTop_toTopOf="@id/no_btn"
        />


</androidx.constraintlayout.widget.ConstraintLayout>