<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_56dp"
    android:layout_marginTop="@dimen/_8dp"
    android:layout_marginBottom="@dimen/_8dp"
    android:background="@drawable/background_corner_8dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_bluetooth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_bluetooth_vector" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_18dp"
        android:layout_marginTop="@dimen/_12dp"
        android:fontFamily="@font/roboto"
        android:gravity="top"
        android:textColor="@color/black_34"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toEndOf="@id/iv_bluetooth"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Eppos Printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:textColor="@color/grey_91"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tv_printer_name"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_name"
        tools:text="86:67:23:00:4A:C2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_connect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:padding="@dimen/_14dp"
        android:text="@string/connect_printer"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@id/iv_bluetooth"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_bluetooth" />

    <ProgressBar
        android:id="@+id/progress_bar_pair_printer"
        android:layout_width="@dimen/_20dp"
        android:layout_height="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:indeterminate="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_bluetooth"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_bluetooth"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
