<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="100dp"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_8dp"
    android:layout_marginTop="@dimen/_12dp"
    android:layout_marginBottom="@dimen/_8dp">

    <View
        android:id="@+id/vw_coming_soon_bg"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:background="@drawable/bg_circle"
        android:backgroundTint="@color/coming_soon"
        app:layout_constraintBottom_toBottomOf="@id/cl_tile_image"
        app:layout_constraintEnd_toEndOf="@id/cl_tile_image"
        app:layout_constraintStart_toStartOf="@id/cl_tile_image"
        app:layout_constraintTop_toTopOf="@id/cl_tile_image" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_tile_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:padding="@dimen/_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_quota_count_limit"
        app:layout_constraintTop_toTopOf="@id/tv_quota_count_limit">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_tile_image"
            android:layout_width="36dp"
            android:layout_height="36dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_payment_in_new_colored" />

        <TextView
            android:id="@+id/tv_coming_soon"
            style="@style/Caption"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:elevation="@dimen/_1dp"
            android:gravity="center"
            android:text="@string/coming_soon"
            android:textColor="@color/white"
            android:textSize="8sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_tile_image"
            app:layout_constraintEnd_toEndOf="@id/iv_tile_image"
            app:layout_constraintStart_toStartOf="@id/iv_tile_image"
            app:layout_constraintTop_toTopOf="@id/iv_tile_image"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_quota_count_limit"
        style="@style/Caption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_solid_green_80_corner_14_stroke_white"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/_4dp"
        android:paddingVertical="@dimen/_2dp"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/cl_tile_image"
        app:layout_constraintStart_toEndOf="@id/cl_tile_image"
        app:layout_constraintTop_toTopOf="@id/cl_tile_image"
        tools:text="Kuota 10x"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_tile_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="vw_coming_soon_bg, cl_tile_image" />

    <TextView
        android:id="@+id/tv_tile_name"
        style="@style/SubHeading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:gravity="center"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/br_tile_icon"
        tools:text="Bayar"
        tools:textColor="@color/black" />

    <View
        android:id="@+id/vw_badge"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/bg_circle"
        android:backgroundTint="@color/red"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/cl_tile_image"
        app:layout_constraintStart_toEndOf="@id/cl_tile_image"
        app:layout_constraintTop_toTopOf="@id/cl_tile_image"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>