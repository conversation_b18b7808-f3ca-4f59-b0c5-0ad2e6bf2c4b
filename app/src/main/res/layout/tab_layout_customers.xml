<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    android:fitsSystemWindows="true">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentScrim="?attr/colorPrimary"
            app:layout_scrollFlags="scroll"
            app:titleEnabled="false"
            app:toolbarId="@+id/toolbar">
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:layout_alignParentTop="true"
                android:background="?attr/colorPrimary"
                app:layout_collapseMode="pin"
                app:contentInsetStartWithNavigation="0dp"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <FrameLayout
                    android:id="@+id/toolbarLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <RelativeLayout
                        android:id="@+id/toolbarTextLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center_vertical">

                        <TextView
                            android:id="@+id/title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_marginStart="@dimen/_2dp"
                            android:layout_marginTop="@dimen/_4dp"
                            android:fontFamily="@font/roboto"
                            android:gravity="start"
                            android:lineSpacingExtra="8sp"
                            android:layout_centerVertical="true"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:paddingEnd="@dimen/_8dp"
                            android:layout_toStartOf="@+id/container"
                            android:textColor="@color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="121 ewwe  s erwfsf" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/btn_image_refresh"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_marginEnd="@dimen/_8dp"
                            android:orientation="horizontal"
                            android:layout_centerVertical="true"
                            app:srcCompat="@drawable/ic_refresh_data"
                            android:layout_toStartOf="@+id/container"/>

                        <LinearLayout
                            android:id="@+id/container"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="12dp"
                            android:gravity="end"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:id="@+id/collecting_calendar_icon"
                                android:layout_width="120dp"
                                android:layout_height="wrap_content"
                                android:background="@drawable/rectangle_white_radius16"
                                android:foreground="?android:attr/selectableItemBackground"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:paddingStart="4dp"
                                android:paddingTop="4dp"
                                android:paddingEnd="4dp"
                                android:paddingBottom="4dp"
                                android:visibility="gone"
                                >

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:ellipsize="end"
                                    android:text="@string/collection_calendar"
                                    android:textAlignment="center"
                                    android:textColor="@color/colorPrimary" />

                                <TextView
                                    android:id="@+id/collecting_calendar_count"
                                    android:layout_width="22dp"
                                    android:layout_height="22dp"
                                    android:background="@drawable/bg_collection_red"
                                    android:maxLength="2"
                                    android:text="@string/default_placeholder"
                                    android:gravity="center"
                                    android:textAlignment="center"
                                    android:textColor="@color/white"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <TextView
                                android:id="@+id/tv_help_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                android:text="@string/help"
                                android:visibility="visible"
                                app:drawableTopCompat="@drawable/ic_help_new" />

                            <FrameLayout
                                android:id="@+id/notification_container"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:visibility="gone">

                                <ImageView
                                    android:id="@+id/notification_icon"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_alert"
                                    app:tint="@color/white"
                                    android:visibility="gone" />

                                <TextView
                                    android:id="@+id/notify_highlighter"
                                    android:layout_width="14dp"
                                    android:layout_height="14dp"
                                    android:layout_gravity="top|end"
                                    android:background="@drawable/oval_pink"
                                    android:text="2"
                                    android:textColor="@color/white"
                                    android:textSize="10dp"
                                    android:textStyle="bold"
                                    android:visibility="gone" />

                            </FrameLayout>

                        </LinearLayout>
                    </RelativeLayout>
                </FrameLayout>
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <TextView
        android:id="@+id/tvOffline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/appBarLayout"
        android:background="@color/yellow5"
        android:elevation="@dimen/_12dp"
        android:drawablePadding="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="12dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="12dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:text="@string/recordings_are_saved_in_offline_mode"
        android:textColor="@color/black_80"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_offline_msg" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tvOffline"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@id/app_bar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:expanded="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                app:contentScrim="@color/white"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/summaryView"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:layout_marginBottom="12dp"
                    android:background="@color/white"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="6dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/filter_credit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:fontFamily="@font/roboto"
                            android:text="@string/filter_credit"
                            android:textSize="@dimen/text_12sp"
                            app:layout_constraintEnd_toStartOf="@+id/v_line"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/filter_debit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:fontFamily="@font/roboto"
                            android:text="@string/filter_debit"
                            android:textSize="@dimen/text_12sp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/v_line"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/creditTotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="@font/roboto"
                            android:paddingBottom="12dp"
                            android:textColor="@color/in_green"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toEndOf="@+id/filter_credit"
                            app:layout_constraintStart_toStartOf="@+id/filter_credit"
                            app:layout_constraintTop_toBottomOf="@id/filter_credit"
                            tools:text="Rp100.000" />

                        <TextView
                            android:id="@+id/debitTotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="@font/roboto"
                            android:textColor="@color/out_red"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toEndOf="@+id/filter_debit"
                            app:layout_constraintStart_toStartOf="@+id/filter_debit"
                            app:layout_constraintTop_toBottomOf="@id/filter_credit"
                            tools:text="Rp100.000" />

                        <View
                            android:id="@+id/v_line"
                            android:layout_width="1dp"
                            android:layout_height="@dimen/_0dp"
                            android:background="@color/black_5"
                            app:layout_constraintBottom_toBottomOf="@id/creditTotal"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <View
                            android:id="@+id/h_line"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black_5"
                            app:layout_constraintTop_toBottomOf="@id/creditTotal" />

                        <TextView
                            android:id="@+id/summary_btn"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="?attr/selectableItemBackground"
                            android:drawableStart="@drawable/new_report_icon"
                            android:drawableEnd="@drawable/ic_chevron_right_black_60"
                            android:drawablePadding="@dimen/_8dp"
                            android:fontFamily="@font/roboto"
                            android:gravity="center_vertical"
                            android:paddingStart="@dimen/_16dp"
                            android:paddingTop="12dp"
                            android:paddingEnd="@dimen/_16dp"
                            android:paddingBottom="12dp"
                            android:text="@string/credit_debit_report_label"
                            android:textColor="@color/black_80"
                            android:textSize="14sp"
                            app:layout_constraintTop_toBottomOf="@id/h_line" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <RelativeLayout
                android:id="@+id/searchLayout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_gravity="bottom"
                android:background="@color/colorBackground"
                android:fitsSystemWindows="true"
                android:gravity="center_vertical"
                android:visibility="gone"
                app:layout_collapseMode="pin">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp">

                    <LinearLayout
                        android:layout_width="0px"
                        android:layout_height="0px"
                        android:clickable="true"
                        android:focusable="true"
                        android:focusableInTouchMode="true" />

                    <EditText
                        android:id="@+id/searchQueryBox"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_toLeftOf="@+id/clear"
                        android:background="@color/white"
                        android:hint="@string/search_hint_text"
                        android:maxLines="1"
                        android:paddingLeft="16dp"
                        android:textColor="@color/main_text_color"
                        android:textColorHint="@color/hint_color"
                        android:textSize="16dp"
                        android:visibility="visible" />

                    <ImageView
                        android:id="@+id/clear"
                        android:layout_width="40dp"
                        android:layout_height="44dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="16dp"
                        android:background="@color/white"
                        android:padding="7dp"
                        android:src="@mipmap/close_grey"
                        android:visibility="visible" />
                </RelativeLayout>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/searchFilterLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingTop="4dp"
                android:paddingBottom="4dp">

                <TextView
                    android:id="@+id/customersCountTv"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_icon_search_new"
                    android:drawablePadding="@dimen/_8dp"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/_16dp"
                    tools:text="0 Pelanggan" />

                <TextView
                    android:id="@+id/sortMenu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:drawableTop="@drawable/ic_icon_sort_new"
                    android:text="@string/sort"
                    android:textAlignment="center"
                    android:textColor="@color/black_80"
                    android:textSize="10sp"
                    android:tint="@color/colorPrimary"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/downloadCstPdf"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:drawableTop="@drawable/ic_download_new"
                    android:text="@string/download"
                    android:textAlignment="center"
                    android:textColor="@color/black_80"
                    android:textSize="10sp"
                    android:visibility="visible"
                    tools:ignore="SmallSp" />


            </LinearLayout>

            <TextView
                android:id="@+id/downloadPdfDivider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/vrDivider" />

            <include layout="@layout/utang_filter_layout" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/vrDivider" />

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/ptrCustomer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:backgroundTint="@color/colorPrimary">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/customerRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/arrowArea"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="76dp"
        android:layout_toLeftOf="@id/addCustomerBtn"
        android:visibility="gone"
        >

        <ImageView
            android:id="@+id/tutorarrow"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginRight="20dp"
            android:src="@drawable/ic_pointer"
            app:tint="#DC770E" />
    </LinearLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/addCustomerBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:text="@string/btn_add_credit"
        android:textStyle="bold"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@mipmap/ic_plus_white_24dp"
        app:iconGravity="start"
        android:visibility="gone"
        android:layout_marginBottom="70dp"

        />

    <LinearLayout
        android:id="@+id/arrowAreaForNoTabMerge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="30dp"
        android:layout_toLeftOf="@id/addCustomerNewFormBtn"
        android:visibility="gone"
        >

        <ImageView
            android:id="@+id/tutorarrowForNoTabMerge"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginRight="20dp"
            android:src="@drawable/ic_pointer"
            app:tint="#DC770E" />
    </LinearLayout>
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/addCustomerNewFormBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:text="@string/btn_add_credit"
        android:textStyle="bold"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@mipmap/ic_plus_white_24dp"
        app:iconGravity="start"
        android:visibility="gone"
        android:layout_marginBottom="24dp"

        />


    <LinearLayout
        android:id="@+id/type_rg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:orientation="horizontal"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="70dp"
        android:visibility="gone"
        >


            <TextView
                android:id="@+id/text_expense"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:fontFamily="@font/roboto"
                android:text="@string/debit_label"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:layout_weight="1"
                android:background="@drawable/type_exp_selected_bg"
                 android:gravity="center"
                />

            <TextView
                android:id="@+id/text_income"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginStart="8dp"
                android:text="@string/credit_label"
                android:textStyle="bold"
                android:fontFamily="@font/roboto"
                android:textColor="@color/white"
                android:layout_weight="1"
                android:gravity="center"
                android:background="@drawable/type_inc_selected_bg"
                />
    </LinearLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/layoutWhatsAppPreview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="invisible"
        app:cardCornerRadius="8dp"
        app:cardElevation="@dimen/_8dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/shop_detail_header"
                android:layout_marginStart="12dp"
                android:layout_marginTop="@dimen/_16dp">

                <ImageView
                    android:id="@+id/shop_icon"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_alignParentStart="true"
                    android:background="@drawable/circle_with_border"
                    android:padding="8dp"
                    android:src="@drawable/logo_white_ticker" />

                <TextView
                    android:id="@+id/shopName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="3dp"
                    android:layout_toEndOf="@+id/shop_icon"
                    android:fontFamily="@font/roboto"
                    android:maxLines="1"
                    android:text="StoresName"
                    android:textColor="@color/heading_text"
                    android:textSize="16.0sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/shopPhone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/shopName"
                    android:layout_marginStart="12dp"
                    android:layout_toEndOf="@+id/shop_icon"
                    android:fontFamily="@font/roboto"
                    android:text="0812-3456-789"
                    android:textColor="#666666"
                    android:textSize="14.0sp" />
            </RelativeLayout>


            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16.0dip"
                android:layout_marginTop="12dp"
                android:layout_marginRight="16.0dip"
                android:layout_marginBottom="12.0dip"
                android:background="@drawable/round_stroke_light_background"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingLeft="8.0dip"
                android:paddingTop="19.0dip"
                android:paddingRight="8.0dip"
                android:paddingBottom="16.0dip">

                <TextView
                    android:id="@+id/reminderCardHeader"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="9.799988sp"
                    android:text="@string/payment_reminder"
                    android:textAllCaps="true"
                    android:textColor="#222222"
                    android:textSize="15.0sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/dueAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16.0dip"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:maxLines="1"
                    android:paddingLeft="8.0dip"
                    android:paddingRight="8.0dip"
                    tools:text="Rp 92300"
                    android:textColor="@color/out_red"
                    android:textSize="34.0sp"
                    android:textStyle="bold"
                    app:autoSizeMaxTextSize="32.0sp"
                    app:autoSizeMinTextSize="14.0sp"
                    app:autoSizeStepGranularity="2.0sp"
                    app:autoSizeTextType="uniform" />

                <TextView
                    android:id="@+id/reminderExplain"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/round_corner_blue_rectangle"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="5sp"
                    android:paddingLeft="19.0dip"
                    android:paddingTop="7.0dip"
                    android:paddingRight="19.0dip"
                    android:paddingBottom="7.0dip"
                    android:text="You owe SGD 2300 as on 5:00PM, 19th Nov, 2018"
                    android:textColor="@color/colorPrimary"
                    android:textSize="11sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_verified" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="4sp"
                    android:text="@string/verifiedbyapp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>


</RelativeLayout>
