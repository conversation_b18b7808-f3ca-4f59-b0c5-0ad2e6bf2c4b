<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/guide"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Tanggal" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.4" />

    <TextView
        android:id="@+id/tv_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:gravity="end"
        android:maxLines="2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guide"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="22 Jan 2024 16:59" />

</androidx.constraintlayout.widget.ConstraintLayout>