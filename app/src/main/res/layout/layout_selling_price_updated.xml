<?xml version="1.0" encoding="utf-8"?>
<TextView
    android:id="@+id/tv_selling_price_updated"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/SubHeading2"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:text="@string/selling_price_updated_message"
    android:textColor="@color/green_80"
    android:background="@color/green_5"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingTop="@dimen/_12dp"
    android:paddingBottom="@dimen/_12dp"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintBottom_toBottomOf="parent" />