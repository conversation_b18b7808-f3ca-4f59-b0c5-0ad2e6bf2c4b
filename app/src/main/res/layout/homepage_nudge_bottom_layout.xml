<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingBottom="@dimen/_16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_top_rounded_corner">

    <include
        android:id="@+id/layout_kyc"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        layout="@layout/layout_item_homepage_nudge"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginHorizontal="@dimen/_10dp"
        android:layout_marginTop="@dimen/_12dp"/>

    <include
        android:id="@+id/layout_kyb"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        layout="@layout/layout_item_homepage_nudge"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_kyc"
        android:layout_marginHorizontal="@dimen/_10dp"
        android:layout_marginTop="@dimen/_12dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_later"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_66dp"
        style="@style/ButtonOutline.White"
        android:text="Nanti Saja"
        android:textStyle="bold"
        android:textSize="16sp"
        android:textColor="@color/black_000000"
        android:layout_marginEnd="@dimen/_4dp"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintHorizontal_bias="0.4"
        app:layout_constraintTop_toBottomOf="@id/layout_kyb"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_verify"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_verify"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_66dp"
        style="@style/ButtonFill.Yellow"
        android:layout_marginStart="@dimen/_4dp"
        android:text="Verifikasi Sekarang"
        android:padding="@dimen/_0dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_10dp"
        app:layout_constraintHorizontal_bias="0.4"
        app:layout_constraintStart_toEndOf="@id/btn_later"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>