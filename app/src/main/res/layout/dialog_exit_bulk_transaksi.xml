<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_16dp"
    android:padding="@dimen/_16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Hapus Transaksi?"
        android:fontFamily="@font/roboto_bold"
        android:textSize="@dimen/text_18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />

    <TextView
        android:id="@+id/tv_body"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="Transaksi yang kamu hapus akan hilang dari daftar transaksi baru."
        app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_yes"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="Batal"
        style="@style/ButtonOutline.Blue"
        android:textSize="@dimen/text_12sp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_body"
        app:layout_constraintWidth_percent="0.49"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_no"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:text="Hapus"
        android:layout_marginStart="@dimen/_16dp"
        style="@style/ButtonFill.Blue"
        android:textSize="@dimen/text_12sp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toEndOf="@id/btn_yes"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_body"
        app:layout_constraintWidth_percent="0.49"/>

</androidx.constraintlayout.widget.ConstraintLayout>