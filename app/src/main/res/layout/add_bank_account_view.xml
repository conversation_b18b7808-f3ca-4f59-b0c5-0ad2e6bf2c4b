<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/add_bank_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintTop_toBottomOf="@id/til_layout_input_note">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_edittext_default"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_add_bank_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_add_card" />

    <TextView
        android:id="@+id/tv_add_bank_title_txt"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_10dp"
        android:text="@string/add_bank_account"
        app:layout_constraintEnd_toStartOf="@id/iv_add_bank_plus_img"
        app:layout_constraintStart_toEndOf="@id/iv_add_bank_img"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_add_bank_subtitle_txt"
        style="@style/Label1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_10dp"
        android:lineSpacingExtra="@dimen/_4dp"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_add_bank_plus_img"
        app:layout_constraintStart_toEndOf="@id/iv_add_bank_img"
        app:layout_constraintTop_toBottomOf="@id/tv_add_bank_title_txt"
        tools:text="Biar pembayaran transaksi bisa langsung masuk ke rekening kamu." />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_add_bank_plus_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_plus_blue" />

</androidx.constraintlayout.widget.ConstraintLayout>