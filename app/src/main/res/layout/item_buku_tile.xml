<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="76dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="@dimen/_6dp"
    android:paddingTop="@dimen/_5dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:layout_constraintDimensionRatio="1:1">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_tile_image"
        android:layout_width="@dimen/_60dp"
        android:layout_height="@dimen/_60dp"
        android:padding="@dimen/_10dp"
        app:cardElevation="@dimen/_0dp"
        app:cardCornerRadius="@dimen/_18dp"
        app:layout_constraintStart_toStartOf="@id/tv_tile_name"
        app:layout_constraintEnd_toEndOf="@id/tv_tile_name"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_tile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center">

            <TextView android:id="@+id/tv_promo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_promo"
                android:fontFamily="@font/roboto"
                android:paddingHorizontal="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_5dp"
                android:text="@string/promo"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_8sp"
                android:textStyle="bold"
                android:visibility="visible"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/iv_tile_image"
                app:layout_constraintEnd_toEndOf="@id/iv_tile_image"/>

            <TextView android:id="@+id/tv_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_free_text"
                android:fontFamily="@font/roboto"
                android:paddingHorizontal="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_5dp"
                android:text="@string/text_new"
                android:textColor="@color/white"
                android:textSize="@dimen/text_8sp"
                android:textStyle="bold"
                android:visibility="visible"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/iv_tile_image"
                app:layout_constraintEnd_toEndOf="@id/iv_tile_image"/>

            <TextView
                android:id="@+id/tv_coming_soon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Segera Hadir"
                android:elevation="@dimen/_1dp"
                android:gravity="center"
                android:textColor="@color/white"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_10dp"
                android:textSize="@dimen/text_10sp"
                app:layout_constraintTop_toTopOf="@id/iv_tile_image"
                app:layout_constraintBottom_toBottomOf="@id/iv_tile_image" />

            <ImageView
                android:id="@+id/iv_tile_image"
                android:layout_width="@dimen/_50dp"
                android:layout_height="@dimen/_50dp"
                android:scaleType="center"
                app:layout_constraintTop_toBottomOf="@+id/tv_new"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.cardview.widget.CardView>


    <TextView
        android:id="@+id/tv_tile_name"
        android:layout_width="76dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginTop="@dimen/_8dp"
        android:text="Kartu Nama gjhg jgj"
        android:paddingBottom="@dimen/_10dp"
        style="@style/Body3"
        android:textColor="@color/black_000000"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cv_tile_image" />


</androidx.constraintlayout.widget.ConstraintLayout>