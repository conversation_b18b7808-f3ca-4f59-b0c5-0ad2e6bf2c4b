<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/rv_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_below="@+id/category_layout"
        android:background="@color/colorPrimary"
        android:id="@+id/tab_indicator"
        android:layout_height="3dp">
        <TextView
            android:id="@+id/date_indicator"
            android:layout_width="0dp"
            android:layout_height="3dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_weight="7"
            android:layout_marginRight="45dp"
            android:layout_marginLeft="45dp"
            android:background="@drawable/rectangle_0"
            android:textAlignment="center"
            android:lineSpacingExtra="1sp"
            android:translationY="-0.44sp"
            android:gravity="top"
            />

        <TextView
            android:id="@+id/category_indicator"
            android:layout_width="0dp"
            android:background="@drawable/rectangle_0"
            android:layout_weight="7"
            android:textAlignment="center"
            android:layout_height="3dp"
            android:layout_marginRight="45dp"
            android:layout_marginLeft="45dp"
            android:visibility="invisible"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:lineSpacingExtra="1sp"
            android:translationY="-0.44sp"
            android:gravity="top"
            />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/notificationRv"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/view_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/toolbar"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        android:orientation="vertical">

        <TextView
            android:id="@+id/view_empty_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/no_notification"
            android:textColor="@color/body_text" />

    </LinearLayout>

</RelativeLayout>