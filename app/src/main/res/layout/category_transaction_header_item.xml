<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/listHeader"
    android:orientation="vertical"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:focusableInTouchMode="false"
        android:paddingLeft="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <TextView
            android:id="@+id/dateHeader"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="10sp"
            android:text="@string/notes_header"
            android:textColor="@color/black_60"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/amountHeader"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:gravity="right"
            android:lineSpacingExtra="10sp"
            android:paddingRight="18dp"
            android:text="@string/sales"
            android:textColor="@color/black_60"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/buyingPriceHeader"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:gravity="right"
            android:lineSpacingExtra="10sp"
            android:paddingRight="18dp"
            android:text="@string/expense_label"
            android:textColor="@color/black_60"
            android:textSize="14sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/section"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/headerLayout"
        android:background="@color/section_end" />
</LinearLayout>
