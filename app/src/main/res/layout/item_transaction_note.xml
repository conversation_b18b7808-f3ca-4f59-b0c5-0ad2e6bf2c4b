<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/_8dp"
    app:cardElevation="@dimen/_0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_12dp">

        <EditText
            android:id="@+id/note_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:drawableStart="@drawable/ic_edit_square"
            android:drawablePadding="@dimen/_12dp"
            android:hint="@string/add_notes_optional"
            android:inputType="text"
            android:textColor="@color/black60"
            android:textColorHint="@color/black40"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
