<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/listHeader"
    android:orientation="horizontal"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#eff4ff"
        android:focusableInTouchMode="false"
        android:paddingLeft="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <TextView
            android:id="@+id/dateHeader"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="10sp"
            android:text="@string/date"
            android:textColor="#5C5C5C"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:paddingRight="8dp"
            android:gravity="center">

            <TextView
                android:id="@+id/creditHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:lineSpacingExtra="10sp"
                android:text="@string/creditWithSign"
                android:textColor="#5C5C5C"
                android:textSize="14sp"/>

            <ImageView
                android:id="@+id/creditTooltip"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:src="@drawable/ic_info" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center"
            android:paddingRight="8dp">

            <TextView
                android:id="@+id/debitHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:lineSpacingExtra="10sp"
                android:text="@string/debitWithSign"
                android:textColor="#5C5C5C"
                android:textSize="14sp"/>

            <ImageView
                android:id="@+id/debitTooltip"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:src="@drawable/ic_info" />

        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/section"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/headerLayout"
        android:background="@color/section_end" />
</LinearLayout>
