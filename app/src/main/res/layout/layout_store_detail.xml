<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/_12dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:scaleType="center"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_store_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/iv_logo"
        style="@style/SubHeading1"
        tools:text="@string/stored"
        android:layout_marginTop="@dimen/_4dp"/>

    <TextView
        android:id="@+id/tv_store_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/black_40"
        android:gravity="center"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@id/tv_store_name"
        style="@style/Body2"
        tools:text="@string/stored"
        android:layout_marginTop="@dimen/_4dp"/>

    <TextView
        android:id="@+id/tv_store_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/black_40"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/tv_store_address"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_8dp"
        style="@style/Body2"
        tools:text="@string/stored"
        android:layout_marginTop="@dimen/_4dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>