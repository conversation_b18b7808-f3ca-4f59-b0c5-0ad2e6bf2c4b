<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_select_category_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="Informasi Kategori"
        app:titleTextColor="@color/white" />

    <TextView
        android:id="@+id/tv_header_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_10dp"
        android:text="Keuntungan Mencatat Keuangan <PERSON> ✨ "
        style="@style/Heading3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb_select_category_info" />

    <ImageView
        android:id="@+id/iv_informasi_first"
        android:layout_width="@dimen/_50dp"
        android:layout_height="@dimen/_50dp"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@id/tv_header_info"
        app:layout_constraintStart_toStartOf="@id/tv_header_info"
        app:srcCompat="@drawable/category_info_1" />

    <TextView
        android:id="@+id/tv_informasi_first"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:layout_marginStart="@dimen/_10dp"
        android:text="@string/category_info_1"
        app:layout_constraintStart_toEndOf="@id/iv_informasi_first"
        app:layout_constraintEnd_toEndOf="@id/tv_header_info"
        app:layout_constraintTop_toTopOf="@id/iv_informasi_first" />

    <ImageView
        android:id="@+id/iv_informasi_second"
        android:layout_width="@dimen/_50dp"
        android:layout_height="@dimen/_50dp"
        app:srcCompat="@drawable/category_info_2"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@id/tv_informasi_first"
        app:layout_constraintStart_toStartOf="@id/iv_informasi_first" />

    <TextView
        android:id="@+id/tv_informasi_second"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Body2"
        android:layout_marginStart="@dimen/_10dp"
        android:text="@string/category_info_2"
        app:layout_constraintStart_toEndOf="@id/iv_informasi_second"
        app:layout_constraintEnd_toEndOf="@id/tv_header_info"
        app:layout_constraintTop_toTopOf="@id/iv_informasi_second" />


    <TextView
        android:id="@+id/tv_informasi_third"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        style="@style/Body2"
        android:text="Dengan begitu, Juragan bisa lebih mudah pantau keuangan usaha 😉"
        app:layout_constraintStart_toStartOf="@id/iv_informasi_second"
        app:layout_constraintEnd_toEndOf="@id/tv_informasi_second"
        app:layout_constraintTop_toBottomOf="@id/tv_informasi_second" />

    <TextView
        android:id="@+id/tv_title_2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="Cara Ganti Kategori Secara Manual"
        style="@style/Heading3"
        app:layout_constraintStart_toStartOf="@id/tv_informasi_third"
        app:layout_constraintEnd_toEndOf="@id/tv_informasi_third"
        app:layout_constraintTop_toBottomOf="@id/tv_informasi_third" />

    <TextView
        android:id="@+id/tv_body_2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="Pengen ganti kategori pada transaksi? Gampang Juragan. Berikut caranya:"
        style="@style/Body2"
        app:layout_constraintStart_toStartOf="@id/tv_title_2"
        app:layout_constraintEnd_toEndOf="@id/tv_title_2"
        app:layout_constraintTop_toBottomOf="@id/tv_title_2" />

    <TextView
        android:id="@+id/tv_body_3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/informasi_body_2"
        style="@style/Body2"
        app:layout_constraintStart_toStartOf="@id/tv_body_2"
        app:layout_constraintEnd_toEndOf="@id/tv_body_2"
        app:layout_constraintTop_toBottomOf="@id/tv_body_2" />



</androidx.constraintlayout.widget.ConstraintLayout>