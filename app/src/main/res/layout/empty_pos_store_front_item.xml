<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/come_on_take_notes_using_cashier_mode"
        android:textColor="@color/blue_60"
        android:textSize="@dimen/text_18sp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:gravity="center"
        android:text="@string/pos_store_front_empty_info"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_12sp" />

    <ImageView
        android:id="@+id/iv_store_front_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/_20dp"
        app:srcCompat="@drawable/ic_empty_pos_store_front_info" />

    <TextView
        android:id="@+id/tv_add_product_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:gravity="center"
        android:text="@string/pos_store_front_empty_message"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/_35dp"
        android:weightSum="1">
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_add_first_product"
            style="@style/ButtonFill.Blue"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.5"
            android:text="@string/add_product"
            app:layout_constraintEnd_toEndOf="parent" />
    </LinearLayout>
</LinearLayout>