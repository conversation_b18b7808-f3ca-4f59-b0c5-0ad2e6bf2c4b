<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/white_background_radius_8"
    android:paddingStart="@dimen/_20dp"
    android:paddingTop="@dimen/_24dp"
    android:paddingEnd="@dimen/_20dp"
    android:paddingBottom="@dimen/_24dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Ubah nama usaha dulu, ya!" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="Nama usahamu terdeteksi masuk ke daftar nama yang dilarang. Coba ubah nama usaha untuk melanjutkan transaksi" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_left"
        style="@style/ButtonOutline.Blue"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/cancel"
        android:textAllCaps="false"
        android:textAppearance="@style/SubHeading1"
        app:layout_constraintEnd_toStartOf="@+id/vw_separator"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

    <View
        android:id="@+id/vw_separator"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_2dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toStartOf="@id/btn_right"
        app:layout_constraintStart_toEndOf="@id/btn_left"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_right"
        style="@style/ButtonFill.Blue"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/change"
        android:textAllCaps="false"
        android:textAppearance="@style/SubHeading1"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/vw_separator"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

</androidx.constraintlayout.widget.ConstraintLayout>