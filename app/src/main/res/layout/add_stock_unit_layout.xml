<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/add_unit_parent"
    android:background="@color/white"
    android:paddingTop="@dimen/_16dp"
    android:paddingLeft="20dp"
    android:paddingRight="20dp"
    >

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/plus_image"
        android:src="@mipmap/ic_plus_white_24dp"
        app:tint="@color/blue_60"


        />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/plus_image"
        app:layout_constraintTop_toTopOf="@id/plus_image"
        app:layout_constraintBottom_toBottomOf="@id/plus_image"
        android:text="Satuan Baru"
        android:textColor="@color/blue_60"
        android:layout_marginStart="@dimen/_12dp"
        android:textStyle="bold"
        />

</androidx.constraintlayout.widget.ConstraintLayout>