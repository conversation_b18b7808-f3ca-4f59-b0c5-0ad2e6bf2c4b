<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_104dp"
    android:layout_marginTop="@dimen/_8dp"
    android:layout_marginBottom="@dimen/_8dp"
    android:background="@drawable/background_blue_corner">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_printer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_icon_printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_18dp"
        android:fontFamily="@font/roboto"
        android:gravity="top"
        android:textColor="@color/black_34"
        android:textSize="@dimen/dimen_14sp"
        android:textStyle="normal"
        app:layout_constraintStart_toEndOf="@id/iv_printer"
        app:layout_constraintTop_toTopOf="@id/iv_printer"
        tools:text="Eppos Printer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_printer_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:textColor="@color/grey_91"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tv_printer_name"
        app:layout_constraintTop_toBottomOf="@id/tv_printer_name"
        tools:text="86:67:23:00:4A:C2" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_icon_settings" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:background="@color/grey_217"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_test_printer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_bold"
        android:padding="@dimen/_10dp"
        android:text="TES CETAK"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />

</androidx.constraintlayout.widget.ConstraintLayout>