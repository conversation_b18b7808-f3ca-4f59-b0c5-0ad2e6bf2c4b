<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingTop="@dimen/_24dp">

    <TextView
        android:id="@+id/tv_header"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="👋🏻 Hai, kenalan dulu yuk!"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_bizz_name"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:hint="Nama Toko/Usaha Kamu"
        app:layout_constraintTop_toBottomOf="@id/tv_header">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/tv_bizz_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPersonName"
            android:textColor="@color/black"
            tools:text="asdf" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/tv_business_name_error"
        android:layout_width="0dp"
        style="@style/Label2"
        android:visibility="gone"
        tools:text="Nama usaha terdeteksi mencurigakan. Coba ubah dengan nama lain."
        android:textColor="@color/red_80"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/til_bizz_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_referral"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:hint="Kode Referral"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_business_name_error"
        tools:visibility="visible">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/tv_referral"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textCapCharacters"
            tools:text="asdf" />
    </com.google.android.material.textfield.TextInputLayout>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_gravity="top"
            android:background="@color/new_divider" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_action"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:enabled="false"
            android:text="Lanjut" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
