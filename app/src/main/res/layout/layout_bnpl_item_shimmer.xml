<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_body"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_8dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/shimmer_image" />

    <TextView
        android:id="@+id/tv_item_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_10dp"
        android:layout_marginStart="@dimen/_4dp"
        android:background="@color/shimmer_placeholder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:background="@color/shimmer_placeholder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_icon">

        <TextView
            android:id="@+id/tv_black"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>