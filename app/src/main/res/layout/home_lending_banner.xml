<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_home"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/colorGreyLight"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
  <com.google.android.material.card.MaterialCardView
    android:theme="@style/Theme.MaterialComponents.Light"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    android:layout_marginHorizontal="@dimen/_8dp"
    android:layout_marginVertical="@dimen/_20dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="@dimen/_4dp"
    app:cardCornerRadius="@dimen/_16dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_listing_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/_150dp"
        android:paddingBottom="@dimen/_12dp"
        android:background="@drawable/white_backgroud_radius_16">

        <ImageView
            android:id="@+id/iv_lending_banner"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_0dp"
            android:scaleType="fitXY"

            app:layout_constraintHeight_default="percent"
            app:layout_constraintHeight_percent="0.506"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_get_loan"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_lending_banner"
            style="@style/SubHeading1"
            android:layout_marginHorizontal="@dimen/_10dp"
            android:layout_marginTop="@dimen/_16dp"
            tools:text="Dapatkan pinjaman modal dengan bunga ringan 🤑"/>

        <TextView
            android:id="@+id/tv_cooperate"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_get_loan"
            style="@style/Body3"
            android:textColor="@color/black_80"
            android:layout_marginHorizontal="@dimen/_10dp"
            android:layout_marginTop="@dimen/_6dp"
            tools:text="Bekerja sama dengan partner yang terdaftar di OJK"/>

     </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>
 </androidx.constraintlayout.widget.ConstraintLayout>