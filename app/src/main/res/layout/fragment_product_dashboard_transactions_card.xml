<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_16dp"
    android:layout_marginHorizontal="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="@dimen/_16dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_heading"
            android:layout_marginVertical="@dimen/_16dp"
            android:text="Transaksi Pembukuan"
            style="@style/Heading3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/iv_dropdown"
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:src="@drawable/ic_chevron_down"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_marginEnd="@dimen/_8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/cl_heading"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:id="@+id/viewPagerLayout"
            >

            <LinearLayout
                android:id="@+id/ll_view_pager"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:orientation="vertical">

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/inventory_home_tab_layout"
                    app:tabTextAppearance="@style/SubHeading2"
                    app:tabIndicatorColor="@color/colorPrimary"
                    android:layout_width="match_parent"
                    app:tabTextColor="@color/black_40"
                    android:background="@color/white"
                    app:tabSelectedTextColor="@color/colorPrimary"
                    android:layout_height="wrap_content" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/inventory_home_viewpager"
                    android:layout_width="match_parent"
                    android:layout_weight="1"
                    android:layout_height="0dp"/>
            </LinearLayout>

            <View
                android:id="@+id/divider1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                app:layout_constraintTop_toBottomOf="@id/ll_view_pager"
                android:background="@color/black_5"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider1"
                android:id="@+id/btn_detail_layout"
                >

                <TextView
                    style="@style/Heading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:layout_margin="@dimen/_16dp"
                    android:text="@string/lihat_detail"
                    android:textAlignment="center"
                    android:textSize="@dimen/_14dp"
                    android:id="@+id/btnDetail"
                    android:textColor="@color/blue_60"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>


<!--        <com.bukuwarung.baseui.DefaultViewPagerFixedHeight-->
<!--        android:id="@+id/inventory_home_viewpager"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/cl_heading"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        >-->

<!--        <com.google.android.material.tabs.TabLayout-->
<!--            android:id="@+id/inventory_home_tab_layout"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            app:tabSelectedTextColor="@color/blue_80"-->
<!--            app:tabTextAppearance="@style/SubHeading1"-->
<!--            app:tabTextColor="#8D8D8D"-->
<!--            app:tabPaddingStart="0dp"-->
<!--            app:tabPaddingEnd="0dp"-->
<!--            app:tabGravity="fill"-->
<!--            app:tabBackground="@color/white"-->
<!--            app:tabIndicatorHeight="2dp"-->
<!--            android:elevation="4dp"-->
<!--            android:background="@color/white"-->
<!--            app:tabIndicatorColor="@color/blue_80"-->
<!--            android:layout_gravity="top" />-->

<!--    </com.bukuwarung.baseui.DefaultViewPagerFixedHeight>-->
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>

