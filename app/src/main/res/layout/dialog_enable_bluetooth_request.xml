<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/_16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:text="@string/bluetooth_enable_request_header"
        android:textColor="@color/black_80"
        android:textSize="18sp"
        android:textStyle="bold" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/roboto"
        android:text="@string/bluetooth_enable_request_msg"
        android:textColor="@color/black_60"
        android:textSize="14sp" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_agree"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/go_on"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:textStyle="bold"
        app:backgroundTint="@color/colorPrimary" />
</LinearLayout>