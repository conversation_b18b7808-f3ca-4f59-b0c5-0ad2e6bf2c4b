<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/title"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingEnd="24dp"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/notification_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Usaha Saya 29u4289u8 jenfjksnf md mf 2u3y4u23y mdsnfsnfsnfjks 348u5389u53 " />

            <ImageView
                android:id="@+id/notification_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="?android:attr/selectableItemBackground"
                android:src="@drawable/ic_alert"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white"
                />

            <TextView
                android:id="@+id/notify_highlighter_exp"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:background="@drawable/oval_pink"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/notification_icon"
                app:layout_constraintTop_toTopOf="@id/notification_icon"
                tools:text="2"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/collecting_calendar_icon"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:background="@drawable/rectangle_white_radius16"
                android:foreground="?android:attr/selectableItemBackground"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="4dp"
                android:paddingTop="4dp"
                android:paddingEnd="4dp"
                android:paddingBottom="4dp"
                android:layout_marginEnd="20dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:text="Atur Tempo"
                    android:textAlignment="center"
                    android:textColor="@color/colorPrimary" />

                <TextView
                    android:id="@+id/collecting_calendar_count"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:background="@drawable/bg_collection_red"
                    android:maxLength="2"
                    android:text="@string/default_placeholder"
                    android:gravity="center"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textStyle="bold" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>
    <com.bukuwarung.baseui.DefaultViewPager
        android:id="@+id/inventory_home_viewpager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/inventory_home_tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabSelectedTextColor="@color/blue_80"
            app:tabTextAppearance="@style/SubHeading1"
            app:tabTextColor="#8D8D8D"
            app:tabPaddingStart="0dp"
            app:tabPaddingEnd="0dp"
            app:tabGravity="fill"
            app:tabBackground="@color/white"
            app:tabIndicatorHeight="2dp"
            android:elevation="4dp"
            android:background="@color/white"
            app:tabIndicatorColor="@color/blue_80"
            android:layout_gravity="top" />

    </com.bukuwarung.baseui.DefaultViewPager>


</androidx.constraintlayout.widget.ConstraintLayout>