<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_16dp">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animation"
        android:layout_width="160dp"
        android:layout_height="160dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_autoPlay="false"
        app:lottie_loop="false"
        app:lottie_rawRes="@raw/confetti" />

    <TextView
        android:id="@+id/title_txt"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/animation"
        tools:text="@string/payment_in_finished_title" />

    <TextView
        android:id="@+id/subtitle_txt"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:gravity="center"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_txt"
        tools:text="@string/payment_in_finished_subtitle" />
    <TextView
        android:id="@+id/amount_txt"
        style="@style/Heading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/subtitle_txt"
        tools:text="Rp. 25.000" />
</androidx.constraintlayout.widget.ConstraintLayout>
