<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/cl_tnc"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <WebView
        android:id="@+id/webview_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constrainedHeight="true"
        android:layout_marginVertical="@dimen/_24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/bt_okay">

    </WebView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_okay"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/ButtonFill"
        android:text="@string/ok_button"
        android:layout_margin="@dimen/_24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>