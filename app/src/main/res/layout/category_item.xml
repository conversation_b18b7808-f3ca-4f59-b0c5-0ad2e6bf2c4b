<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        style="@style/BaseTextView"
        android:id="@+id/categoryName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="@string/debit"
        android:padding="@dimen/_16dp"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/categoryName"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginRight="@dimen/_12dp"
        android:layout_marginStart="@dimen/_12dp"
        />


</androidx.constraintlayout.widget.ConstraintLayout>