<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mtrl_picker_header"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingBottom="32dp">

    <!--Header-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="12dp"
        android:paddingBottom="12dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingStart="@dimen/_16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_icon_calendar_start"
                android:drawablePadding="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:text="Tanggal Awal"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/mtrl_picker_header_selection_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:text="11-11-2020"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

        <View
            android:layout_width="10dp"
            android:layout_height="2dp"
            android:background="#FFF" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingStart="@dimen/_16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_icon_calendar_end"
                android:drawablePadding="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:text="Tanggal Akhir"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:text="11-11-2020"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

    <!--Month Navigator-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/_16dp">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            app:tint="#000"
            android:rotation="180"
            android:src="@drawable/ic_chevron_right" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:text="July 2020"
            android:textAlignment="center"
            android:textColor="@color/black_80"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="32dp"
            android:src="@drawable/ic_chevron_right"
            android:tint="#000" />
    </LinearLayout>

    <GridView
        android:id="@+id/day_name_grid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:numColumns="7"
        android:stretchMode="columnWidth"
        tools:layout_height="42dp"
        tools:showIn="@layout/calendar_custom_layout" />

    <GridView
        android:id="@+id/day_grid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:numColumns="7"
        android:stretchMode="columnWidth"
        tools:layout_height="200dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:gravity="end">

        <TextView
            android:id="@+id/btn_negative"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_8dp"
            android:fontFamily="@font/roboto"
            android:padding="12dp"
            android:text="@string/cancel"
            android:textAllCaps="true"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/btn_positive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:padding="12dp"
            android:text="@string/oke"
            android:textAllCaps="true"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>