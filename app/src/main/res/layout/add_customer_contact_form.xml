<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/close"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginTop="17dp"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/toolBarTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:layout_centerVertical="true"
                android:fontFamily="@font/roboto"
                android:layout_toRightOf="@+id/close"
                android:text="@string/add_customer"
                android:textColor="#ffffff"
                android:textStyle="bold"
                android:textSize="18sp" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp">
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="vertical">

        <RelativeLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical">

            <ImageView
                android:id="@+id/nameIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_centerInParent="true"
                android:tint="@color/colorPrimary"
                android:layout_alignParentLeft="true"
                android:src="@mipmap/ic_customer" />

            <EditText
                android:id="@+id/customerName"
                android:layout_width="match_parent"
                android:layout_centerInParent="true"
                android:layout_height="wrap_content"
                android:layout_below="@+id/customerTitle"
                android:layout_marginLeft="16dp"
                android:background="@null"
                android:layout_toRightOf="@+id/nameIcon"
                android:fontFamily="@font/roboto"
                android:ellipsize="end"
                android:hint="@string/customer_name_hint"
                android:inputType="textCapWords"
                android:lineSpacingExtra="4sp"
                android:maxLines="1"
                android:text=""
                android:paddingRight="44dp"
                android:textColor="#666666"
                android:textSize="16.3sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="18dp"
                android:layout_below="@+id/nameIcon"
                android:layout_alignParentLeft="true"
                android:background="#eeeeee" />

            <ImageView
                android:id="@+id/contactIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="0dp"
                android:src="@mipmap/select_contact_blue"
                android:visibility="gone" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="@dimen/form_text_margin_top">
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            <ImageView
                android:id="@+id/mobileIcon"
                android:layout_width="24dp"
                android:layout_centerInParent="true"
                android:layout_height="24dp"
                android:layout_alignParentLeft="true"
                android:tint="@color/colorPrimary"
                android:src="@mipmap/customer_phone_grey" />

            <com.hbb20.CountryCodePicker
                android:id="@+id/countryPicker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_toRightOf="@+id/mobileIcon"
                app:ccp_autoDetectLanguage="true"
                android:layout_centerInParent="true"
                android:layout_centerVertical="true"
                app:ccp_countryPreference="@string/countries_prefferred_in_spinner"
                app:ccp_excludedCountries="@string/countries_in_eu"
                app:ccp_showFlag="false"
                app:ccpDialog_backgroundColor="@color/white"
                app:ccpDialog_textColor="@color/heading_text"
                app:ccp_showFullName="false"
                app:ccp_showNameCode="false"
                app:ccp_showPhoneCode="true"
                app:ccp_textSize="16.3sp" />
            <TextView
                android:id="@+id/topDivider"
                android:layout_width="105dp"
                android:layout_height="1dp"
                android:layout_marginTop="22dp"
                android:layout_below="@+id/countryPicker"
                android:layout_alignParentLeft="true"
                android:background="#eeeeee" />

        </RelativeLayout>
            <Spinner
                android:id="@+id/countrySpinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/mobile"
                android:layout_marginLeft="8dp"
                android:background="@color/white"
                android:layout_toRightOf="@+id/mobileIcon"
                android:visibility="gone" />
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:layout_height="wrap_content">
            <EditText
                android:id="@+id/mobile"
                android:layout_marginTop="22dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_alignParentLeft="true"
                android:hint="@string/customer_mobile_hint"
                android:inputType="number"
                android:fontFamily="@font/roboto"
                android:singleLine="true"
                android:maxLength="18"
                android:text=""
                android:background="@null"
                android:lineSpacingExtra="4sp"
                android:textColor="#666666"
                android:textSize="16sp" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="18dp"
                android:layout_below="@+id/mobile"
                android:layout_alignParentLeft="true"
                android:background="#eeeeee" />
        </RelativeLayout>
        </LinearLayout>
        <com.google.android.material.button.MaterialButton
            android:id="@+id/next"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:paddingTop="14dp"
            android:paddingBottom="14dp"
            android:text="@string/next"
            android:textSize="@dimen/_16dp"
            android:textStyle="bold"
            android:backgroundTint="@color/buku_CTA"
            android:fontFamily="@font/roboto"
            android:elevation="6dp"
            android:textColor="@color/white" />
    </LinearLayout>
</LinearLayout>
</LinearLayout>
