<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_bank_accounts"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/button_add_bank_account"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        tools:listitem="@layout/item_list_bank_accounts" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_add_bank_account"
        style="@style/Button.OutlinePrimary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:textAllCaps="false"
        android:textColor="@color/blue_80"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:strokeColor="@color/blue_80" />
</androidx.constraintlayout.widget.ConstraintLayout>
