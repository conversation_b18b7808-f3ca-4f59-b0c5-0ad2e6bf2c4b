<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/_20dp"
    android:background="@drawable/bg_black_outline_8dp">

    <TextView
        android:id="@+id/tv_unpaid_payment"
        style="@style/SubHeading1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/red_5"
        android:padding="@dimen/_16dp"
        android:text="@string/unpaid_transaction_type"
        android:textColor="@color/red_80"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_selling_price"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/selling_price"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_unpaid_payment" />

    <TextView
        android:id="@+id/tv_selling_price_value"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_40"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_unpaid_payment"
        tools:text="Rp96.000" />

    <View
        android:id="@+id/vw_divider1"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginTop="@dimen/_18dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintTop_toBottomOf="@+id/tv_selling_price" />

    <include
        android:id="@+id/include_create_payment_link"
        layout="@layout/layout_ppob_create_payment_link"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider1" />

    <include
        android:id="@+id/include_share_payment_link"
        layout="@layout/layout_ppob_share_payment_link"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/include_create_payment_link" />

    <include
        android:id="@+id/include_payment_complete"
        layout="@layout/layout_ppob_payment_complete_digitally"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/include_share_payment_link" />

    <View
        android:id="@+id/vw_divider2"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginTop="@dimen/_18dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintTop_toBottomOf="@+id/include_payment_complete" />

    <TextView
        android:id="@+id/tv_paid_cash"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/has_paid_manually"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider2" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_mark_paid"
        style="@style/Button.OutlinePrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto_bold"
        android:paddingStart="@dimen/dimen_28dp"
        android:paddingEnd="@dimen/dimen_28dp"
        android:text="@string/button_success_text"
        android:textAllCaps="false"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_divider2" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_unpaid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="vw_divider2,tv_paid_cash,btn_mark_paid"/>

</androidx.constraintlayout.widget.ConstraintLayout>