<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="fill_parent"
        android:layout_alignParentTop="true"
        android:layout_height="wrap_content"
        android:theme="@style/ActionBarAppTheme">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="fill_parent"
            android:layout_height="?actionBarSize"
            app:contentInsetLeft="0.0dip"
            app:contentInsetStart="0.0dip"
            app:contentInsetStartWithNavigation="0.0dip"
            app:navigationIcon="@drawable/ic_arrow_navigate">

            <TextView
                android:id="@+id/screen_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/share_bukuwarung"
                android:textColor="@color/white"
                android:textSize="18.0dip" />
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <ImageView
        android:id="@+id/preview_image"
        android:layout_width="wrap_content"
        android:layout_above="@+id/share_text_title"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_centerHorizontal="true"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:src="@drawable/illustration_message" />

    <TextView
        android:id="@+id/share_text_title"
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:fontFamily="@font/roboto"
        android:padding="10.0dip"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/share_title"
        android:textAlignment="center"
        android:textStyle="bold"
        android:textColor="@color/heading_text"
        android:textSize="18.0sp" />

    <TextView
        android:id="@+id/share_caption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_below="@id/share_text_title"
        android:paddingLeft="16.0dip"
        android:paddingRight="16.0dip"
        android:text="@string/share_promotion"
        android:textAlignment="center"
        android:textColor="@color/body_text"
        android:textSize="14.0sp" />

    <androidx.cardview.widget.CardView
        android:id="@+id/share_app_button"
        style="@style/PadButtonStyle"
        app:cardElevation="6dp"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="52.0dip"
        android:background="@color/com_accountkit_whatsapp_green"
        android:layout_marginLeft="36dp"
        android:clickable="true"
        android:layout_marginRight="36dp"
        android:layout_marginTop="46dp"
        android:layout_marginBottom="36dp"
        android:paddingLeft="16.0dip"
        android:paddingRight="16.0dip">

        <LinearLayout
            android:gravity="center"
            android:background="@color/colorPrimary"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/whatsapp_icon"
                android:layout_width="24.0dip"
                android:layout_height="24.0dip"
                android:tint="@color/white"
                app:srcCompat="@mipmap/ic_whatsapp_white_24dp" />

            <TextView
                android:id="@+id/share_button_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8.0dip"
                android:fontFamily="@font/roboto"
                android:textStyle="bold"
                android:text="@string/share_app"
                android:textColor="@color/white"
                android:textSize="18.0sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>