<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white">


    <TextView
        android:id="@+id/no_trans_header"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/no_transactions_found"
        android:textColor="@color/heading_text"
        android:textSize="@dimen/text_18sp"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>
