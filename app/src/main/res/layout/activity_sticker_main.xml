<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="fill_parent"
        android:background="#0091ff"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="fill_parent"
            android:layout_height="?actionBarSize"
            app:contentInsetLeft="0.0dip"
            app:contentInsetStart="0.0dip"
            app:contentInsetStartWithNavigation="0.0dip">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_alignParentLeft="true"
                android:layout_width="25dp"
                android:layout_marginLeft="16dp"
                android:layout_height="25dp"
                android:gravity="center"
                android:layout_centerVertical="true"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/screen_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="BukuWarung Stickers"
                android:textStyle="bold"
                android:layout_marginLeft="24dp"
                android:textColor="#ffffff"
                android:textSize="18.0dip" />
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <TextView
        android:id="@+id/error_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:textColor="@android:color/holo_red_dark" />

    <ProgressBar
        android:id="@+id/entry_activity_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />
</FrameLayout>