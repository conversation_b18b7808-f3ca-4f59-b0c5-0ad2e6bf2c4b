<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_5">

    <!-- Appbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:theme="@style/ToolbarTheme">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/btn_close"
                    android:layout_width="28dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center"
                    android:layout_marginLeft="35dp"
                    android:layout_marginRight="35dp"
                    android:layout_toLeftOf="@+id/saveBtn"
                    android:alpha="1"
                    android:fontFamily="@font/roboto"
                    android:text="@string/detail_transaction"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/editBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_toStartOf="@id/deleteBtn"
                    android:drawableTop="@drawable/ic_edit"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/edit"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:tint="@color/white"
                    android:visibility="visible"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/deleteBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:drawableTop="@drawable/delete_red"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="visible"
                    tools:ignore="SmallSp" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    
    <LinearLayout
        android:id="@+id/ll_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/_20dp"
        android:background="@color/white"
        android:weightSum="1"
        android:layout_below="@+id/app_bar">
        
        <TextView
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:text="@string/unpaid_transaction_type"
            android:textColor="@color/black_80"
            android:layout_weight="0.6"
            android:textSize="@dimen/text_14sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_success"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:text="@string/button_success_text"
            android:textSize="@dimen/text_14sp"
            android:layout_weight="0.4"
            style="@style/ButtonOutline.Blue" />
        
    </LinearLayout>

    <ScrollView
        android:id="@+id/mainContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_4dp"
        android:layout_above="@id/container_btn"
        android:layout_below="@+id/ll_info"
        android:animateLayoutChanges="true"
        android:background="@color/white"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_20dp">

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text_12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:layout_marginTop="@dimen/_20dp"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_14sp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/tv_date"
                app:layout_constraintStart_toStartOf="@+id/tv_date"
                tools:text="Warung Chimay" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_check_circle_green"
                android:layout_marginStart="@dimen/_8dp"
                app:layout_constraintTop_toTopOf="@+id/tv_name"
                app:layout_constraintStart_toEndOf="@+id/tv_name"/>

            <TextView
                android:id="@+id/tv_account_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text_12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_name"
                app:layout_constraintBottom_toBottomOf="@+id/tv_name" />

            <TextView
                android:id="@+id/tv_phone_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:layout_marginTop="@dimen/_10dp"
                android:textColor="@color/black_40"
                android:textSize="@dimen/text_12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_account_name"
                tools:text="***********" />

            <TextView
                android:id="@+id/tv_phone_number_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:textColor="@color/black_40"
                android:textSize="@dimen/text_12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_phone_number"
                app:layout_constraintBottom_toBottomOf="@+id/tv_phone_number"
                tools:text="***********" />

            <View
                android:id="@+id/v_below_phone"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="@dimen/_20dp"
                android:background="@color/black_10"
                app:layout_constraintTop_toBottomOf="@+id/tv_phone_number"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tv_head_transaction_items"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:textColor="@color/black_40"
                android:textSize="@dimen/text_14sp"
                android:text="@string/barang"
                app:layout_constraintTop_toBottomOf="@+id/v_below_phone"
                app:layout_constraintStart_toStartOf="parent"/>

            <TextView
                android:id="@+id/tv_head_transaction_items_qty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/quantity"
                android:textColor="@color/black_40"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintTop_toTopOf="@+id/tv_head_transaction_items"
                app:layout_constraintBottom_toBottomOf="@id/tv_head_transaction_items"
                app:layout_constraintEnd_toEndOf="parent"/>

            <LinearLayout
                android:id="@+id/ll_transaction_items"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@id/tv_head_transaction_items" />

            <View
                android:id="@+id/v_below_items"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="@dimen/_20dp"
                android:background="@color/black_10"
                app:layout_constraintTop_toBottomOf="@+id/ll_transaction_items"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tv_transaksi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text_14sp"
                android:layout_marginTop="@dimen/_10dp"
                android:text="@string/total_transaksi"
                app:layout_constraintTop_toBottomOf="@id/v_below_items"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tv_total_transaction"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_14sp"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="@+id/tv_transaksi"
                app:layout_constraintBottom_toBottomOf="@+id/tv_transaksi"
                app:layout_constraintEnd_toEndOf="parent"
                />

            <TextView
                android:id="@+id/tv_from"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                android:textSize="@dimen/text_14sp"
                android:text="@string/status"
                app:layout_constraintTop_toBottomOf="@id/tv_total_transaction"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/iv_lunas"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/red_80"
                android:textSize="@dimen/text_12sp"
                android:textStyle="bold"
                android:padding="@dimen/_10dp"
                android:background="@drawable/bg_lunas"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_from"
                app:layout_constraintBottom_toBottomOf="@id/tv_from" />

            <TextView
                android:id="@+id/tv_lunas"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_from"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tv_from" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <!-- Buttons -->
    <LinearLayout
        android:id="@+id/container_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_vertical"
        android:background="@color/white"
        android:gravity="bottom"
        android:orientation="horizontal"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_8dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_print"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_8dp"
            android:layout_weight="1"
            android:backgroundTint="@color/white"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/transaction_print"
            android:textAllCaps="false"
            android:textColor="@color/buku_CTA"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="visible"
            app:cornerRadius="4dp"
            app:icon="@drawable/ic_printer"
            app:iconGravity="textStart"
            app:iconTint="@color/buku_CTA"
            app:iconTintMode="src_in"
            app:rippleColor="@color/black_40"
            app:strokeColor="@color/buku_CTA"
            app:strokeWidth="1dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_share"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/share"
            android:textAllCaps="false"
            android:textColor="@color/black_80"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@color/new_yellow"
            app:cornerRadius="4dp"
            app:icon="@drawable/ic_share"
            app:iconGravity="textStart"
            app:iconTint="@color/black_80" />

    </LinearLayout>

</RelativeLayout>
