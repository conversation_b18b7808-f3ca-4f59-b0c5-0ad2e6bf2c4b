<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="136dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_top"
        android:layout_width="match_parent"
        android:layout_height="86dp"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_general_share"
            style="@style/ButtonOutline.Blue"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_54dp"
            android:layout_marginVertical="@dimen/_16dp"
            android:layout_marginStart="@dimen/_16dp"
            app:icon="@drawable/ic_share_blue"
            app:iconSize="@dimen/_16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_share_whatsapp"
            style="@style/ButtonFill.Blue"
            android:layout_width="156dp"
            android:layout_height="@dimen/_54dp"
            android:layout_marginVertical="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:lines="2"
            android:text="@string/kirim"
            android:textColor="@color/white"
            android:textSize="@dimen/text_16sp"
            app:icon="@drawable/ic_whatsapp_kirim_svg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_print_invoice"
            style="@style/ButtonOutline.Blue"
            android:layout_width="112dp"
            android:layout_height="@dimen/_54dp"
            android:layout_marginVertical="@dimen/_16dp"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/transaction_print"
            android:textColor="@color/blue_60"
            android:textSize="@dimen/text_16sp"
            app:layout_constraintEnd_toStartOf="@id/bt_share_whatsapp"
            app:layout_constraintStart_toEndOf="@id/bt_general_share"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_open_form"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_50dp"
        android:background="@color/yellow_60"
        android:gravity="center"
        android:text="Catat Utang Baru"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>