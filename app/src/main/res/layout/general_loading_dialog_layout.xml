<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/img_icon"
        android:layout_width="128dp"
        android:layout_height="128dp"
        android:contentDescription="@null"
        android:src="@drawable/otp_verify_icon"
        app:layout_constraintBottom_toTopOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/please_wait_ya"
        app:layout_constraintBottom_toTopOf="@id/tv_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/img_icon" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/we_are_preparing_next_page_for_you"
        app:layout_constraintBottom_toTopOf="@id/barrier_progressbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_progressbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="progressbar_horizontal,progressbar_circle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />


    <ProgressBar
        android:id="@+id/progressbar_circle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:indeterminate="true"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier_progressbar"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/progressbar_horizontal"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier_progressbar"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_goneMarginTop="@dimen/_16dp" />

    <TextView
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="56dp"
        android:background="@color/yellow_10"
        android:drawablePadding="@dimen/_8dp"
        android:gravity="center_vertical|start"
        android:padding="@dimen/_8dp"
        android:id="@+id/tv_warning"
        android:text="@string/dont_kill_app_new"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_12sp"
        app:drawableStartCompat="@drawable/ic_bulk_info_medium"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>