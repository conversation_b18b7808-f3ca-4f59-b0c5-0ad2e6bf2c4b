<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <TextView
        android:id="@+id/verifyTxt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/verifying_number"
        android:textColor="@color/colorPrimary"
        android:textSize="25sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:indeterminate="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/verifyTxt" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/loadingPanel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="verifyTxt,progress_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottom_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:src="@drawable/ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black"
            tools:ignore="ContentDescription" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:backgroundTint="@color/white"
            android:enabled="true"
            android:gravity="center"
            android:padding="14dp"
            android:paddingStart="32dp"
            android:paddingEnd="32dp"
            android:text="@string/help"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:visibility="gone"
            app:cornerRadius="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:strokeColor="@color/colorPrimary"
            app:strokeWidth="1dp"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:text="@string/verify_your_mobile_number"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="20.0sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/phone_number"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />


        <TextView
            android:id="@+id/phone_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="28dp"
            android:text="@string/otp_message_placeholder"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@id/input_otp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_title" />

        <EditText
            android:id="@+id/input_otp"
            style="@style/EditText.VerificationCode"
            android:letterSpacing="0.5"
            app:layout_constraintBottom_toTopOf="@id/otp_error_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/phone_number">

            <requestFocus />
        </EditText>

        <TextView
            android:id="@+id/otp_error_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:animateLayoutChanges="true"
            android:background="@drawable/bg_warning_login"
            android:drawableStart="@drawable/ic_warning"
            android:drawablePadding="@dimen/_8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/_8dp"
            android:paddingTop="@dimen/_8dp"
            android:paddingEnd="@dimen/_8dp"
            android:paddingBottom="@dimen/_8dp"
            android:text="@string/otp_error_wrong"
            android:textColor="@color/black_60"
            android:visibility="gone"
            app:drawableTint="#FF8383"
            app:layout_constraintBottom_toTopOf="@id/text_counter"
            app:layout_constraintTop_toBottomOf="@id/input_otp"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/text_counter"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/otp_counter"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@id/txt_didnt_receive"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/otp_error_text" />

        <TextView
            android:id="@+id/txt_didnt_receive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginEnd="@dimen/_8dp"
            android:text="@string/no_code_sent"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@id/btn_manual_verification"
            app:layout_constraintEnd_toStartOf="@id/txt_try_again"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_counter" />

        <TextView
            android:id="@+id/txt_try_again"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/retry"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@id/txt_didnt_receive"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/txt_didnt_receive"
            app:layout_constraintTop_toTopOf="@id/txt_didnt_receive" />


        <androidx.constraintlayout.widget.Group
            android:id="@+id/try_again_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="txt_try_again,txt_didnt_receive"
            tools:visibility="visible" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_manual_verification"
            style="@style/ButtonOutline.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:text="@string/manual_verification"
            android:textAllCaps="true"
            android:visibility="gone"
            app:iconGravity="textStart"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/skip_login"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_didnt_receive"
            tools:visibility="visible" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/skip_login"
            style="@style/ButtonFill.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/skip"
            android:textAllCaps="true"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_manual_verification"
            app:layout_constraintTop_toBottomOf="@id/txt_didnt_receive"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_change_otp_channel"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="atau Kirim kode melalui WhatsApp"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
