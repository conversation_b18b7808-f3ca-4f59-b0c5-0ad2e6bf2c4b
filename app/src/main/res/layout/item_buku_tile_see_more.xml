<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/_80dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="@dimen/_6dp"
    android:paddingTop="@dimen/_5dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/others_img"
        android:layout_width="@dimen/_50dp"
        android:layout_height="@dimen/_50dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:scaleType="fitXY"
        android:layout_marginTop="@dimen/_2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_other_ppob" />

    <TextView
        android:layout_width="@dimen/_50dp"
        android:layout_height="wrap_content"
        android:text="@string/see_all"
        android:maxLines="2"
        android:paddingBottom="@dimen/_10dp"
        style="@style/Body5"
        android:gravity="center"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintTop_toBottomOf="@id/others_img"
        android:layout_marginTop="@dimen/_14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>