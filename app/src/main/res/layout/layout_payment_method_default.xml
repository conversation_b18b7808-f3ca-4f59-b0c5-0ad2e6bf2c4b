<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_white_blue_5_8dp">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_14dp"
            android:layout_marginBottom="@dimen/_14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_default_payment_method_icon" />

        <TextView
            android:id="@+id/tv_payment_method_txt"
            style="@style/Body2"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_18dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_18dp"
            android:text="@string/choose_payment_method"
            app:layout_constraintEnd_toStartOf="@id/tv_select"
            app:layout_constraintStart_toEndOf="@id/iv_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_select"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/_18dp"
            android:text="@string/select"
            android:textColor="@color/blue_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>