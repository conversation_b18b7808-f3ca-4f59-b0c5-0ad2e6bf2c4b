<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/continue_with_bnpl"
        app:drawableEndCompat="@drawable/ic_close_black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_sub_title"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="Dengan melanjutkan pembayaran ini, pinjaman Talangin Dulu akan aktif dengan ketentuan berikut:" />

    <TextView
        android:id="@+id/tv_box_heading"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/bg_solid_blue60_corner_top_8dp"
        android:padding="@dimen/_8dp"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_sub_title"
        tools:text="Pinjaman aktif periode 29 Nov 2022-13 Dec 2022" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_box_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_solid_blue5_corners_bottom_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_box_heading">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_box1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/cl_box2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_box_info_heading1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Limit Talangin Dulu" />

            <TextView
                android:id="@+id/tv_box_info_value1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_box_info_heading1"
                tools:text="Rp2.000.000" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_box2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/cl_box3"
            app:layout_constraintStart_toEndOf="@+id/cl_box1"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_box_info_heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Jatuh Tempo" />

            <TextView
                android:id="@+id/tv_box_info_value2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_box_info_heading2"
                tools:text="14 Sep 2022" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_box3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cl_box2"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_box_info_heading3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Biaya Layanan" />

            <TextView
                android:id="@+id/tv_box_info_value3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_box_info_heading3"
                tools:text="Rp10.000" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_info"
        style="@style/Label1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_box_info"
        tools:text="*Bebas pakai limit berkali-kali sampai habis, bayar biaya layanan cuma sekali saat melunasi tagihan." />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_dismiss"
        style="@style/ButtonOutline.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_38dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:text="@string/cancel"
        app:layout_constraintEnd_toStartOf="@id/btn_next"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_info" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_next"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_38dp"
        android:text="@string/next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_dismiss"
        app:layout_constraintTop_toBottomOf="@id/tv_info" />
</androidx.constraintlayout.widget.ConstraintLayout>