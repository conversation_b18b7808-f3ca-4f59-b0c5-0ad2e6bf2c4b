<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/white_drawable_round_8dp">

    <ImageView
        android:id="@+id/iv_bank_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_card_tick"
        android:layout_marginStart="@dimen/_10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tv_refund_account"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/refund_account"
        android:layout_marginStart="@dimen/_10dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_bank_card"/>

    <TextView
        style="@style/Label1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/refund_account_messgae"
        android:textColor="@color/black_40"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iv_add_bank"
        app:layout_constraintStart_toStartOf="@+id/tv_refund_account"
        app:layout_constraintTop_toBottomOf="@+id/tv_refund_account"/>

    <ImageView
        android:id="@+id/iv_add_bank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_plus_blue_circle_background"
        android:layout_marginEnd="@dimen/_10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>