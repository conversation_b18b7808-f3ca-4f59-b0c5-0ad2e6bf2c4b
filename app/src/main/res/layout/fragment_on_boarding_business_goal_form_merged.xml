<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/business_goal_form"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/_24dp">

    <TextView
        android:id="@+id/header_label"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/sub_header_label"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/caption_label"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_label" />

    <TextView
        android:id="@+id/caption_label"
        style="@style/Label2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@color/black_5"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="@id/sub_header_label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/sub_header_label"
        app:layout_constraintTop_toTopOf="@id/sub_header_label" />



    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/business_goal_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/divider"
        app:layout_constraintTop_toBottomOf="@id/sub_header_label"
        android:layout_marginTop="@dimen/_12dp" />



    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:background="@color/new_divider"
        app:layout_constraintBottom_toTopOf="@id/business_goal_proceed_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/business_goal_proceed_button"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:visibility="gone"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
