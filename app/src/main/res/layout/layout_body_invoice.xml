<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <TextView
            android:id="@+id/tv_transaction_type_utang"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/Heading3"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/debt_given"
            android:textColor="@color/black_80"
            android:layout_marginEnd="@dimen/_4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guideline"/>

        <TextView
            android:id="@+id/tv_amount_given"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            style="@style/Heading3"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="end"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="50.000"
            android:textColor="@color/black_80"
            android:layout_marginStart="@dimen/_4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_transaction_type_utang"
            app:layout_constraintStart_toEndOf="@id/guideline"/>

    <View
        android:id="@+id/view_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:background="@drawable/horizontal_dashed_line"
        android:layout_marginTop="@dimen/_8dp"
        android:foregroundGravity="center"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_transaction_type_utang"/>

    <include
        android:id="@+id/tv_insufficient_payment"
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        layout="@layout/item_invoice"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_8dp"
        android:layout_marginHorizontal="@dimen/_16dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
