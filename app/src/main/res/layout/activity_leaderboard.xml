<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.referral.leaderboard.LeaderboardActivity"
    android:elevation="0dp"
    android:fitsSystemWindows="true">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="0dp"
        android:theme="@style/ActionBarAppTheme"
        app:elevation="0dp"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentScrim="?attr/colorPrimary"
            app:layout_scrollFlags="scroll"
            app:titleEnabled="false"
            android:elevation="0dp"
            app:elevation="0dp"
            app:toolbarId="@+id/toolbar">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:layout_alignParentTop="true"
                android:background="?attr/colorPrimary"
                app:layout_collapseMode="pin"
                android:elevation="0dp"
                app:elevation="0dp"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <ImageView
                    android:id="@+id/backBtn"
                    android:layout_alignParentStart="true"
                    android:layout_width="25dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_height="25dp"
                    android:gravity="center"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/back_white"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/screen_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/leaderboard_title"
                    android:fontFamily="@font/roboto"
                    android:textStyle="bold"
                    android:layout_marginStart="24dp"
                    android:textColor="@color/white"
                    android:textSize="18sp" />
            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/appBarLayout"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@id/app_bar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:expanded="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                app:contentScrim="@color/white"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="65dp"
                        android:background="@color/colorPrimary"/>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_8dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:layout_marginBottom="@dimen/_16dp"
                        android:elevation="8dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="8dp">

                        <ProgressBar
                            android:id="@+id/mainDataLoading"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:padding="@dimen/_16dp"
                            android:progressTint="@color/colorPrimary"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/emptyReferralText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:paddingTop="40dp"
                            android:paddingBottom="40dp"
                            android:text="Anda belum pernah mereferralkan siapapun"
                            android:textColor="@color/black"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/mainDataContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="@dimen/_16dp"
                            android:visibility="gone">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:baselineAligned="false"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/main_referral_your_rank"
                                        android:textColor="#8D8D8D"
                                        android:textSize="16sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/rankItemTV"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/_16dp"
                                        android:text="@string/default_placeholder"
                                        android:textAlignment="center"
                                        android:textColor="#5C5C5C"
                                        android:textSize="24sp"
                                        android:textStyle="bold" />

                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/pointContainer"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/main_referral_point"
                                        android:textColor="#8D8D8D"
                                        android:textSize="16sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/pointItemTV"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/_16dp"
                                        android:text="@string/default_placeholder"
                                        android:textAlignment="center"
                                        android:textColor="@color/colorPrimary"
                                        android:textSize="24sp"
                                        android:textStyle="bold" />

                                </LinearLayout>

                            </LinearLayout>

                            <View
                                android:layout_width="wrap_content"
                                android:layout_height="1dp"
                                android:layout_marginTop="12dp"
                                android:background="#DBDBDB" />

                            <LinearLayout
                                android:id="@+id/shareContainer"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:layout_marginTop="13dp"
                                android:layout_marginEnd="4dp"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_marginEnd="@dimen/_16dp"
                                    android:src="@drawable/ic_share_invert"
                                    android:tint="@color/colorPrimary" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Bagikan Pencapaianmu"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </FrameLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_below="@id/appBarLayout"
            android:background="@color/white"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="#FFEEC1"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_8dp"
                android:paddingTop="@dimen/_8dp">

                <TextView
                    android:id="@+id/rankTV"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/leaderboard_rank_title"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/pointTV"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/main_referral_point"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:ignore="RelativeOverlap" />

            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/leaderboardRV"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

            <include
                android:id="@+id/empty_view"
                layout="@layout/default_empty_item"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/rvLoading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:progressTint="@color/colorPrimary"
                android:padding="@dimen/_16dp"
                android:layout_gravity="center"
                android:visibility="visible"/>

        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>