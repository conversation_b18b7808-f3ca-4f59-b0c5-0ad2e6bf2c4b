<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black_10"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/widget_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_4dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_back"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@mipmap/back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Heading2"
                android:layout_marginStart="@dimen/_16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/billing_details"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/btn_back"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvHelp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/help"
                android:textColor="@color/white"
                app:drawableTint="@color/white"
                app:drawableTopCompat="@drawable/ic_baseline_help_outline"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsvDetails"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/clBottom"
        app:layout_constraintTop_toBottomOf="@id/widget_toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            android:paddingBottom="@dimen/_16dp">

            <View
                android:id="@+id/viewGradient"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_0dp"
                android:layout_marginBottom="-16dp"
                android:background="@drawable/payment_tab_gradient_bg"
                app:layout_constraintBottom_toBottomOf="@id/clTopDetails"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clTopDetails"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_rounded_rectangle_8dp"
                android:paddingBottom="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvDateTitle"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/waktu"
                    android:textColor="@color/black_40"
                    app:layout_constraintEnd_toStartOf="@id/guide"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.4" />

                <TextView
                    android:id="@+id/tvDateValue"
                    style="@style/SubHeading1"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    app:layout_constraintBottom_toBottomOf="@id/tvDateTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/guide"
                    app:layout_constraintTop_toTopOf="@id/tvDateTitle"
                    tools:text="22 Jan 2024 16:59" />

                <TextView
                    android:id="@+id/tvInvoiceTitle"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/invoice_number"
                    android:textColor="@color/black_40"
                    app:layout_constraintEnd_toStartOf="@id/guide"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvDateTitle" />

                <ImageView
                    android:id="@+id/ivInvoiceValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:src="@drawable/ic_copy"
                    app:layout_constraintBottom_toBottomOf="@+id/tvInvoiceValue"
                    app:layout_constraintEnd_toStartOf="@+id/tvInvoiceValue"
                    app:layout_constraintTop_toTopOf="@+id/tvInvoiceValue" />

                <TextView
                    android:id="@+id/tvInvoiceValue"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    app:layout_constraintBottom_toBottomOf="@id/tvInvoiceTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvInvoiceTitle"
                    tools:text="INVBW/2024EDC0709" />

                <TextView
                    android:id="@+id/tvStatusTitle"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/payment_status"
                    android:textColor="@color/black_40"
                    app:layout_constraintEnd_toStartOf="@id/guide"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvInvoiceTitle" />

                <TextView
                    android:id="@+id/tvStatusValue"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@drawable/bg_solid_yellow5_corner_4dp"
                    android:drawableStart="@drawable/ic_tick_green_bg"
                    android:drawablePadding="@dimen/_4dp"
                    android:padding="@dimen/_8dp"
                    android:textColor="@color/dark_yellow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvStatusTitle"
                    tools:text="Dalam Proses" />

                <TextView
                    android:id="@+id/tvOrderTypeTitle"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/edc_purchase_at"
                    android:textColor="@color/black_40"
                    app:layout_constraintEnd_toStartOf="@id/guide"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvStatusValue" />

                <TextView
                    android:id="@+id/tvOrderTypeValue"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvOrderTypeTitle"
                    tools:text="BukuWarung" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/refundInfo"
                layout="@layout/layout_auto_refund_bank"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewGradient"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clRefund"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_solid_red5_corner_8dp_stroke_red40"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/refundInfo">

                <TextView
                    android:id="@+id/tvRefundTitle"
                    style="@style/SubHeading1.red80"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/verification_period_completed"
                    app:layout_constraintEnd_toStartOf="@+id/btnSelect"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvRefundDesc"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:text="@string/select_refund_method_for_edc_purchase"
                    android:textColor="@color/black_80"
                    app:layout_constraintEnd_toStartOf="@id/btnSelect"
                    app:layout_constraintStart_toStartOf="@id/tvRefundTitle"
                    app:layout_constraintTop_toBottomOf="@id/tvRefundTitle" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSelect"
                    style="@style/ButtonFill.Red"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/choose"
                    android:textAllCaps="false"
                    android:textSize="@dimen/text_14sp"
                    app:cornerRadius="@dimen/_4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:rippleColor="@color/black_40" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnHelp"
                    style="@style/ButtonFill.Red"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/label_help_wa_btn"
                    android:textAllCaps="false"
                    android:textSize="@dimen/text_14sp"
                    app:cornerRadius="@dimen/_4dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvRefundDesc"
                    app:rippleColor="@color/black_40" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clOrderSteps"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_rounded_rectangle_8dp"
                android:paddingBottom="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/clRefund">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clOrderStepDesc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_2dp"
                    android:background="@drawable/payment_tab_gradient_bg_light_blue"
                    android:padding="@dimen/_16dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivSteps"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_sparkles"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvEdcStepTitle"
                        style="@style/Heading3"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:text="@string/complete_the_process"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/ivSteps"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvEdcStepDesc"
                        style="@style/Body3"
                        android:layout_width="@dimen/_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginTop="@dimen/_2dp"
                        android:text="@string/complete_steps_for_edc"
                        android:textColor="@color/black_80"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/ivSteps"
                        app:layout_constraintTop_toBottomOf="@id/tvEdcStepTitle" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvWarning"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/edc_purchase_expired"
                    android:textColor="@color/red"
                    app:layout_constraintTop_toBottomOf="@id/clOrderStepDesc" />

                <com.bukuwarung.activities.edc.orderdetail.ui.EdcOrderStepView
                    android:id="@+id/stepOne"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/bg_rounded_rectangle_8dp"
                    app:isDescription="false"
                    app:layout_constraintTop_toBottomOf="@id/tvWarning" />

                <com.bukuwarung.activities.edc.orderdetail.ui.EdcOrderStepView
                    android:id="@+id/stepTwo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@drawable/bg_rounded_rectangle_8dp"
                    app:isDescription="false"
                    app:layout_constraintTop_toBottomOf="@id/stepOne" />

                <com.bukuwarung.activities.edc.orderdetail.ui.EdcOrderStepView
                    android:id="@+id/stepThree"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@drawable/bg_rounded_rectangle_8dp"
                    app:isDescription="false"
                    app:layout_constraintTop_toBottomOf="@id/stepTwo" />

                <com.bukuwarung.activities.edc.orderdetail.ui.EdcOrderStepView
                    android:id="@+id/stepFour"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:background="@drawable/bg_rounded_rectangle_8dp"
                    app:isDescription="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/stepThree" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clTrackOrders"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_solid_white_corner_8dp"
                android:paddingHorizontal="@dimen/_16dp"
                android:paddingVertical="@dimen/_12dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/clOrderSteps"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvTrackOrderTitle"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:text="@string/shipping_info"
                    app:layout_constraintEnd_toStartOf="@id/tvTrackOrderValue"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvTrackOrderValue"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:drawablePadding="@dimen/_4dp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:text="@string/track_orders"
                    android:textColor="@color/colorPrimary"
                    app:drawableEndCompat="@drawable/ic_external_link_16dp"
                    app:layout_constraintBottom_toBottomOf="@id/tvTrackOrderTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvTrackOrderTitle" />

                <TextView
                    android:id="@+id/tvReceiptNumber"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_24dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:text="@string/receipt_number"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/tvReceiptNumberValue"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTrackOrderTitle" />

                <TextView
                    android:id="@+id/tvReceiptNumberValue"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:drawablePadding="@dimen/_4dp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black80"
                    app:layout_constraintBottom_toBottomOf="@id/tvReceiptNumber"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvReceiptNumber"
                    tools:text="Rp3.999.000" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clMyOrders"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_rounded_rectangle_8dp"
                android:paddingVertical="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/clTrackOrders">

                <TextView
                    android:id="@+id/tvOrderTitle"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:text="@string/my_order"
                    android:layout_marginEnd="@dimen/_4dp"
                    app:layout_constraintEnd_toStartOf="@id/tvOrderValue"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvOrderValue"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:text="@string/view_order_details"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintBottom_toBottomOf="@id/tvOrderTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvOrderTitle" />

                <TextView
                    android:id="@+id/tvDeviceTitle"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_24dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:textColor="@color/black_60"
                    app:layout_constraintEnd_toStartOf="@id/tvDeviceValue"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvOrderTitle"
                    tools:text="Beli EDC Verifone" />

                <TextView
                    android:id="@+id/tvDeviceValue"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black80"
                    app:layout_constraintBottom_toBottomOf="@id/tvDeviceTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvDeviceTitle"
                    tools:text="Rp3.999.000" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clBillingDetails"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_rounded_rectangle_8dp"
                android:paddingBottom="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/clMyOrders">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.4" />

                <TextView
                    android:id="@+id/tvBillTitle"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:text="@string/billing_details"
                    app:drawableEndCompat="@drawable/ic_chevron_up"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/grpBillDetails"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="tvTotalPurTitle, tvTotalPurValue, tvPaymentModeTitle, tvPaymentModeValue, ll_discounts, tvTotalPaymentTitle, tvTotalPaymentValue, vw_separator" />

                <TextView
                    android:id="@+id/tvTotalPurTitle"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/total_purchase"
                    android:textColor="@color/black40"
                    app:layout_constraintEnd_toStartOf="@id/guide2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvBillTitle" />

                <TextView
                    android:id="@+id/tvTotalPurValue"
                    style="@style/Body2"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black80"
                    app:layout_constraintBottom_toBottomOf="@id/tvTotalPurTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/guide2"
                    app:layout_constraintTop_toTopOf="@id/tvTotalPurTitle"
                    tools:text="Rp3.999.000" />

                <LinearLayout
                    android:id="@+id/ll_discounts"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@+id/tvTotalPurTitle" />

                <View
                    android:id="@+id/vw_separator"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:background="@color/black_10"
                    app:layout_constraintTop_toBottomOf="@+id/ll_discounts" />

                <TextView
                    android:id="@+id/tvTotalPaymentTitle"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/label_total_payment"
                    app:layout_constraintEnd_toStartOf="@id/guide2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_separator" />

                <TextView
                    android:id="@+id/tvTotalPaymentValue"
                    style="@style/SubHeading1"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    app:layout_constraintBottom_toBottomOf="@id/tvTotalPaymentTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/guide2"
                    app:layout_constraintTop_toTopOf="@id/tvTotalPaymentTitle"
                    tools:text="Rp3.899.000" />

                <TextView
                    android:id="@+id/tvPaymentModeTitle"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/payment_method"
                    android:textColor="@color/black40"
                    app:layout_constraintEnd_toStartOf="@id/guide2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTotalPaymentTitle" />

                <TextView
                    android:id="@+id/tvPaymentModeValue"
                    style="@style/Body2"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black80"
                    app:layout_constraintBottom_toBottomOf="@id/tvPaymentModeTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/guide2"
                    app:layout_constraintTop_toTopOf="@id/tvPaymentModeTitle"
                    tools:text="Rp20.000" />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="@dimen/_16dp"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@+id/clActivateEdc">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancelOrder"
            style="@style/Button.Outline"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:letterSpacing="0"
            android:paddingHorizontal="@dimen/_10dp"
            android:paddingVertical="@dimen/_11dp"
            android:text="@string/cancel_order"
            android:textAllCaps="false"
            android:textColor="@color/black_80"
            android:textSize="@dimen/text_16sp"
            app:cornerRadius="@dimen/_4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnPayBill"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rippleColor="@color/black_40" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnPayBill"
            style="@style/ButtonFill"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            android:letterSpacing="0"
            android:layout_marginBottom="4dp"
            android:paddingHorizontal="@dimen/_10dp"
            android:paddingVertical="@dimen/_13dp"
            android:text="@string/pay_bill"
            android:textAllCaps="false"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:cornerRadius="@dimen/_4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnCancelOrder"
            app:layout_constraintTop_toTopOf="parent"
            app:rippleColor="@color/black_40" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clActivateEdc"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:padding="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnActivateEdc"
            style="@style/ButtonFill"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:letterSpacing="0"
            android:paddingVertical="@dimen/_11dp"
            android:text="@string/pay_bill"
            android:textAllCaps="false"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:cornerRadius="@dimen/_4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rippleColor="@color/black_40" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/pbLoader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/widget_toolbar" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/bukuErrorView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/widget_toolbar"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>