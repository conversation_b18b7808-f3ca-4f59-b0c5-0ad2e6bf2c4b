<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_16dp"
    android:layout_marginTop="@dimen/_16dp"
    android:layout_marginEnd="@dimen/_16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_transactionLogo"
            android:layout_width="@dimen/_180dp"
            android:layout_height="@dimen/_135dp"
            android:src="@drawable/business_category"
            android:layout_marginTop="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/balanceStatus"
            style="@style/Heading2"
            android:paddingStart="@dimen/_15dp"
            android:paddingEnd="@dimen/_15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:gravity="center"
            android:text="@string/kategori_transaksi_buat_laporan_usaha_kamu_lebih_detil"
            app:autoSizeTextType="uniform"
            android:padding="@dimen/_3dp"
            android:textColor="@color/black_80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_transactionLogo"/>

        <TextView
            android:id="@+id/heading2"
            style="@style/SubHeading2"
            android:layout_marginTop="@dimen/_10dp"
            android:paddingStart="@dimen/_15dp"
            android:paddingEnd="@dimen/_15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:text="@string/laporan_usaha_disajikan_berdasarkan_kategori_yang_kamu_pilih_selama"
            android:textAlignment="center"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/balanceStatus" />



        <View
            android:id="@+id/divider1"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            app:layout_constraintTop_toBottomOf="@id/heading2"
            android:background="@color/black_5"/>
        <TextView
            android:id="@+id/btn_detail"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_bold"
            android:padding="@dimen/_16dp"
            android:text="@string/cara_pakai_kategori"
            android:textAlignment="center"
            android:textColor="@color/blue_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider1" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>

