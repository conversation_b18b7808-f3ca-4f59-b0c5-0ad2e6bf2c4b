<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/info_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardPreventCornerOverlap="false"
        app:contentPadding="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_info_main"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_16dp">

            <ImageView
                android:id="@+id/imageview_icon"
                android:layout_width="60dp"
                android:layout_height="60dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/info_header"
                style="@style/SubHeading1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:textColor="@color/colorPrimary"
                app:layout_constraintStart_toEndOf="@id/imageview_icon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Header" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_steps_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toEndOf="@id/imageview_icon"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/circle1"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_marginStart="@dimen/_8dp"
                    app:srcCompat="@drawable/circle_onboarding_progress"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line1"
                    android:layout_width="@dimen/_16dp"
                    android:layout_height="2dp"
                    android:background="@color/black_20"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/circle1"
                    app:layout_constraintStart_toEndOf="@id/circle1"
                    app:layout_constraintTop_toTopOf="@id/circle1" />

                <ImageView
                    android:id="@+id/circle2"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    app:srcCompat="@drawable/circle_onboarding_progress_default"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/line1"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line2"
                    android:layout_width="@dimen/_16dp"
                    android:layout_height="2dp"
                    android:background="@color/black_20"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/circle2"
                    app:layout_constraintStart_toEndOf="@id/circle2"
                    app:layout_constraintTop_toTopOf="@id/circle2" />

                <ImageView
                    android:id="@+id/circle3"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    app:srcCompat="@drawable/circle_onboarding_progress_default"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/line2"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line3"
                    android:layout_width="@dimen/_16dp"
                    android:layout_height="2dp"
                    android:background="@color/black_20"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/circle3"
                    app:layout_constraintStart_toEndOf="@id/circle3"
                    app:layout_constraintTop_toTopOf="@id/circle3" />

                <ImageView
                    android:id="@+id/circle4"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    app:srcCompat="@drawable/circle_onboarding_progress_default"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/line3"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="11dp"
                android:layout_height="11dp"
                app:srcCompat="@drawable/close"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="info_header" />

            <TextView
                android:id="@+id/textview_info"
                style="@style/Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/imageview_icon"
                app:layout_constraintTop_toBottomOf="@id/barrier_header"
                tools:text="Jika Anda belum memliki catatan transaksi, Anda bisa menekan tombol + untuk membuat catatan transaksi baru." />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/next"
                style="@style/ButtonFill"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="@string/understand"
                android:textAppearance="@style/SubHeading2"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textview_info" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>
