<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/contactLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="16dp"
    android:background="@color/white"
    android:paddingTop="22dp"
    android:gravity="center">

    <LinearLayout
        android:id="@+id/pic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/contact_photo"
            android:layout_width="40dp"
            android:layout_height="40dp" />

        <TextView
            android:id="@+id/nameInitials"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/oval_0"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="A"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:visibility="gone" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_favourite"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignEnd="@+id/pic"
        android:layout_alignBottom="@+id/pic"
        android:visibility="gone"
        app:srcCompat="@drawable/ic_favourite_fill" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:layout_toRightOf="@+id/pic"
        android:ellipsize="end"
        android:lineSpacingExtra="4sp"
        android:maxLines="1"
        android:textStyle="bold"
        android:textColor="@color/body_text"
        android:fontFamily="@font/roboto"
        android:lineHeight="20dp"
        style="@style/ContactTextStyle"
        android:text="Customer"
        android:textSize="15dp" />

    <TextView
        android:id="@+id/mobile"
        style="@style/ContactTextStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignWithParentIfMissing="true"
        android:layout_below="@+id/name"
        android:layout_alignLeft="@+id/name"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="0sp"
        android:text="+628087888899"
        android:textColor="@color/body_text"
        android:textSize="13dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/add"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="56dp"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/name"
        android:layout_alignParentRight="true"
        android:layout_marginTop="-4dp"
        android:layout_marginRight="12dp"
        android:text="@string/add"
        android:textAllCaps="true"
        android:textColor="@color/colorPrimary"
        android:visibility="gone"
        app:cornerRadius="4dp"
        app:rippleColor="@color/colorPrimaryDark"
        app:strokeColor="@color/colorPrimary"
        app:strokeWidth="1dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/mobile"
        android:layout_alignStart="@+id/pic"
        android:layout_marginTop="@dimen/_4dp"
        android:background="#e8eef1"
        android:visibility="visible" />
</RelativeLayout>
