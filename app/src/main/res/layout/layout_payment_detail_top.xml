<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_rectangle_white_8dp"
    android:paddingBottom="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_saldo_text"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/layout_rectangle_blue5_top_radius_8dp"
        android:gravity="center"
        android:paddingVertical="@dimen/_8dp"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Yey! Transaksi jadi 3x lebih cepat pakai Saldo⚡"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_bill_amount"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/bill_amount"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_saldo_text" />

    <TextView
        android:id="@+id/tv_bill_amount_value"
        style="@style/Heading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_bill_amount"
        tools:text="Rp250.000" />

    <TextView
        android:id="@+id/tv_collect_money"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/collect_money_from"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_bill_amount" />

    <TextView
        android:id="@+id/tv_collect_money_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_4dp"
        android:ellipsize="end"
        android:gravity="end|center_vertical"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_collect_money"
        tools:text="Rizky Budianto" />

    <View
        android:id="@+id/divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/horizontal_dotted_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_collect_money" />

    <TextView
        android:id="@+id/tv_transaction_code"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/payment_code"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <TextView
        android:id="@+id/tv_transaction_code_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_4dp"
        android:gravity="end"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:drawableEndCompat="@drawable/ic_copy"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_transaction_code"
        tools:text="RIYYMMDDXXXXXXXX" />

    <TextView
        android:id="@+id/tv_voucher_game_product"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/produk"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_transaction_code" />

    <TextView
        android:id="@+id/tv_voucher_game_product_value"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_voucher_game_product"
        tools:text="Telkomsel Data SMULE 1GB" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/voucher_game_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        app:constraint_referenced_ids="tv_voucher_game_product,tv_voucher_game_product_value" />

    <TextView
        android:id="@+id/tv_receiver"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/penerima"
        android:layout_marginTop="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_voucher_game_product" />

    <TextView
        android:id="@+id/tv_receiver_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"

        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_receiver"
        tools:text="Dea Clarissa Safitri" />

    <TextView
        android:id="@+id/tv_customer_account"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/label_customer_account"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_receiver" />

    <TextView
        android:id="@+id/tv_customer_account_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_customer_account"
        tools:text="Mandiri - 3883134xxxx" />

    <TextView
        android:id="@+id/tv_phone_number"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/label_customer_account"
        android:textColor="@color/black_40"
        android:layout_marginTop="@dimen/_12dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_customer_account" />

    <TextView
        android:id="@+id/tv_phone_number_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColor="@color/black_80"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_phone_number"
        tools:text="Mandiri - 3883134xxxx" />

    <TextView
        android:id="@+id/tv_qris_rrn"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/rrn_qris"
        android:textColor="@color/black_40"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_phone_number" />

    <TextView
        android:id="@+id/tv_qris_rrn_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/black_80"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_qris_rrn"
        tools:text="************" />

    <include android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:id="@+id/include_digital_receipt"
        layout="@layout/layout_digital_receipt"
        android:visibility="gone"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_customer_account" />

    <TextView
        android:id="@+id/tv_favourite"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/favourite_contact"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_qris_rrn"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_favourite_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/ic_favourite_fill"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_favourite"
        tools:text="Dea clarissa"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_expiration_date"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/expiration_date"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_favourite_value"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_expiration_date_value"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_6dp"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline"
        app:layout_constraintTop_toTopOf="@id/tv_expiration_date"
        tools:text="22 Jan 2021"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_expiration_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_expiration_date, tv_expiration_date_value"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.45" />

</androidx.constraintlayout.widget.ConstraintLayout>