<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/_16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_camera_input"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_80dp"
        app:cardCornerRadius="@dimen/_10dp"
        android:layout_margin="@dimen/_16dp"
        style="@style/EditTextBordered"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_camera_input"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black_5">

            <TextView
                android:id="@+id/tv_upload_photo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/upload_foto"
                android:textColor="@color/black_40"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_camera"
                app:layout_constraintBottom_toBottomOf="@id/iv_camera"/>

            <ImageView
                android:id="@+id/iv_camera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_camera_new"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginEnd="@dimen/_10dp"
                app:layout_constraintEnd_toStartOf="@+id/tv_upload_photo"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/black_40"
                android:layout_marginBottom="@dimen/_10dp"
                style="@style/Label1"
                android:text="@string/upload_photo_info"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_image_preview"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_150dp"
        android:layout_margin="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/cv_camera_input">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_image_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_1dp"
                android:gravity="center"
                android:elevation="@dimen/_1dp"
                android:text="@string/hapus_foto"
                style="@style/ButtonOutline.White.Text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_image_preview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="center"
                android:src="@drawable/stiker_banner"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>