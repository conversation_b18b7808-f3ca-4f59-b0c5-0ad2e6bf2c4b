<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="@dimen/_16dp"
    android:paddingRight="@dimen/_16dp"
    android:background="#F9F8FF">
    <RelativeLayout android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/main_view"
        android:elevation="4dp"
        android:layout_alignParentLeft="true"
        android:layout_toLeftOf="@+id/preview"
        android:background="@drawable/round_corner_rectangle_summary"
        android:padding="16dp"
        xmlns:android="http://schemas.android.com/apk/res/android">

        <TextView
            android:id="@+id/balance_credit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="12sp"
            android:text="@string/summary_debit"
            android:textColor="@color/out_red"
            android:textSize="14sp"
            android:padding="4dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/totalCashOutValueTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/balance_credit"
            android:layout_alignParentEnd="true"
            android:layout_marginBottom="8dp"
            android:layout_toEndOf="@+id/balance_credit"
            android:fontFamily="@font/roboto"
            android:gravity="end"
            android:text="@string/number_zero"
            android:textColor="@color/out_red"
            android:textSize="14sp"
            android:padding="4dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/balance_debit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/balance_credit"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="12sp"
            android:text="@string/summary_credit"
            android:textSize="14sp"
            android:padding="4dp"
            android:textColor="@color/in_green"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/totalCashInValueTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/totalDebitsValue"
            android:layout_alignTop="@+id/balance_debit"
            android:layout_alignParentEnd="true"
            android:layout_marginBottom="8dp"
            android:layout_toEndOf="@+id/balance_credit"
            android:fontFamily="@font/roboto"
            android:gravity="end"
            android:text="@string/number_zero"
            android:padding="4dp"
            android:textColor="@color/in_green"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:id="@+id/divider"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:layout_below="@+id/totalCashInValueTv"
            android:background="#eeeeee" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/divider">



            <TextView
                android:id="@+id/balance_net"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="10sp"
                android:text="@string/summary_total"
                android:textColor="@color/heading_text"
                android:textSize="16sp"
                android:padding="3dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/netCashBalanceTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/balance_net"
                android:layout_alignParentEnd="true"
                android:layout_gravity="end"
                android:layout_toEndOf="@+id/balance_net"
                android:fontFamily="@font/roboto"
                android:gravity="end"
                android:text="@string/number_zero"
                android:padding="3dp"
                android:textStyle="bold"
                android:textColor="@color/heading_text"
                android:textSize="14sp" />
        </RelativeLayout>
    </RelativeLayout>

</RelativeLayout>