<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:showIn="@layout/activity_sticker_pack_list">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:background="#0091ff"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="fill_parent"
        android:layout_height="?actionBarSize"
        app:contentInsetLeft="0.0dip"
        app:contentInsetStart="0.0dip"
        app:contentInsetStartWithNavigation="0.0dip">

        <ImageView
            android:id="@+id/backBtn"
            android:layout_alignParentLeft="true"
            android:layout_width="25dp"
            android:layout_marginLeft="16dp"
            android:layout_height="25dp"
            android:gravity="center"
            android:layout_centerVertical="true"
            android:src="@mipmap/back_white" />

        <TextView
            android:id="@+id/screen_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sticker_list_title"
            android:textStyle="bold"
            android:fontFamily="@font/roboto"
            android:layout_marginLeft="24dp"
            android:textColor="#ffffff"
            android:textSize="18.0dip" />
    </androidx.appcompat.widget.Toolbar>
</com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/appbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/error_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:textColor="@android:color/holo_red_dark"
                tools:text=" " />

            <ImageView
                android:id="@+id/iv_stiker_banner"
                android:layout_width="wrap_content"
                android:layout_height="140dp"
                android:src="@drawable/stiker_banner"
                android:layout_margin="@dimen/_10dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <RelativeLayout
                android:layout_width="match_parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:background="@color/white"
                android:padding="@dimen/_16dp"
                android:visibility="gone"
                android:id="@+id/explain"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:id="@+id/shop_icon"
                    android:layout_alignParentLeft="true"
                    android:scaleType="fitCenter"
                    android:padding="@dimen/_8dp"
                    android:background="@drawable/circle_with_border"
                    android:src="@drawable/onboarding_smile"/>
                <TextView
                    android:id="@+id/name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:layout_marginTop="1dp"
                    android:layout_toRightOf="@+id/shop_icon"
                    android:maxLines="1"
                    android:layout_marginLeft="12dp"
                    android:text="#JuraganWarung"
                    android:lineHeight="24dp"
                    android:textStyle="bold"
                    android:textColor="@color/heading_text"
                    android:textSize="16.0sp" />
                <TextView
                    android:id="@+id/phone"
                    android:layout_toRightOf="@+id/shop_icon"
                    android:layout_below="@id/name"
                    android:layout_width="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:fontFamily="@font/roboto"
                    android:layout_height="wrap_content"
                    android:lineHeight="20dp"
                    android:text="@string/sticker_list_desc"
                    android:textColor="#666666"
                    android:textSize="14.0sp" />
            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/sticker_pack_list"
                app:layout_constraintTop_toBottomOf="@id/iv_stiker_banner"
                android:background="@color/rv_bg"
                android:paddingBottom="@dimen/_16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>



</androidx.constraintlayout.widget.ConstraintLayout>
