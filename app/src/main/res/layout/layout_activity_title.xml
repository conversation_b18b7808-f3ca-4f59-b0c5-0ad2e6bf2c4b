<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tb_ppob"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/colorPrimary"
    app:theme="@style/ToolbarTheme">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/toolBarLabel"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/Label1"
            android:id="@+id/tv_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_12dp"
            android:text="@string/help"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_help" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="@id/tv_help"
            app:layout_constraintStart_toStartOf="@id/tv_help"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_baseline_help_outline"
            app:tint="@color/white" />

        <TextView
            style="@style/Label1"
            android:id="@+id/tv_reminder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_12dp"
            android:text="@string/reminder"
            android:textColor="@color/white"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_help"
            app:layout_constraintTop_toBottomOf="@id/iv_reminder" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_reminder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="@id/tv_reminder"
            app:layout_constraintEnd_toEndOf="@id/tv_reminder"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_reminder_white"
            android:visibility="gone"
            tools:visibility="visible"
            app:tint="@color/white" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.appcompat.widget.Toolbar>
