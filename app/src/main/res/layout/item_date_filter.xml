<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_filter_section"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_radio_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:paddingVertical="@dimen/_12dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:textColor="@color/black80"
            app:layout_constraintBottom_toTopOf="@id/tv_subtitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/rb_date"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="7 hari terakhir" />

        <TextView
            android:id="@+id/tv_subtitle"
            style="@style/Body3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2dp"
            android:textColor="@color/black40"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:text="Sel, 05 Mei 2022 - Rab, 11 Mei 2022" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_date"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/_16dp"
            android:clickable="false"
            app:layout_constraintBottom_toBottomOf="@id/tv_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_date_range"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@drawable/bg_corner_4dp_stroke_black5"
        android:drawablePadding="@dimen/_8dp"
        android:gravity="center_vertical"
        android:padding="@dimen/_12dp"
        android:textColor="@color/textview_tint_selector"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_calendar"
        app:drawableTint="@color/textview_tint_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_radio_button"
        tools:text="Kam, 5 Nov 2021 - Jum, 24 Nov 2021 "
        tools:visibility="visible" />

    <View
        android:id="@+id/vw_padding"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintTop_toBottomOf="@id/tv_date_range" />

</androidx.constraintlayout.widget.ConstraintLayout>
