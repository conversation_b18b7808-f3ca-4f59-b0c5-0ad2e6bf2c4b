<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_home"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/colorGreyLight"
    android:minHeight="@dimen/_120dp"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_heading_first"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/SubHeading1"
        android:textSize="@dimen/text_18sp"
        tools:text="Butuh bantuan?"
        android:layout_margin="@dimen/_20dp"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    
    <ImageView
        android:id="@+id/iv_help"
        android:layout_width="@dimen/_36dp"
        android:layout_height="@dimen/_32dp"
        app:srcCompat="@drawable/ic_help"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/_16dp"/>

    <TextView
        android:id="@+id/tv_heading_help"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/iv_help"
        app:layout_constraintTop_toTopOf="@id/tv_heading_first"
        app:layout_constraintBottom_toBottomOf="@id/tv_heading_first"
        style="@style/SubHeading1"
        android:textColor="@color/blue_60"
        tools:text="Hubungi CS"
        android:layout_marginEnd="@dimen/_6dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>