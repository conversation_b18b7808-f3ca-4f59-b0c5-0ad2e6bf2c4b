<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <include
        android:id="@+id/include_bnpl_layout"
        layout="@layout/layout_cashback"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <include
        android:id="@+id/include_layout"
        layout="@layout/layout_cashback"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include_bnpl_layout"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_saldo_reward"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/green_5"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_8dp"
        android:text="@string/pay_using_saldo_bonus"
        android:textAlignment="center"
        android:textColor="@color/green_80"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include_layout"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_warning"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/_6dp"
        android:paddingHorizontal="@dimen/_14dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:paddingVertical="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_saldo_reward"
        tools:text="Pembayaran tidak dapat dilakukan pada jam 23.00 - 06.59 WIB setiap hari. Coba lagi nanti." />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_warning, include_layout" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_pay_method_icon"
        android:layout_width="42dp"
        android:layout_height="33dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier"
        tools:srcCompat="@drawable/ic_bank" />

    <TextView
        android:id="@+id/tv_payment_method"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/label_payment_method"
        android:textColor="@color/black40"
        app:layout_constraintEnd_toStartOf="@id/tv_change"
        app:layout_constraintStart_toEndOf="@id/iv_pay_method_icon"
        app:layout_constraintTop_toBottomOf="@id/barrier" />

    <TextView
        android:id="@+id/tv_payment_method_name"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_2dp"
        app:layout_constraintStart_toEndOf="@id/iv_pay_method_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_payment_method"
        tools:text="Saldo BukuWarung" />

    <TextView
        android:id="@+id/tv_payment_amount"
        style="@style/SubHeading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        app:layout_constraintEnd_toStartOf="@id/tv_change"
        app:layout_constraintStart_toEndOf="@id/tv_payment_method_name"
        app:layout_constraintTop_toBottomOf="@id/tv_payment_method"
        tools:text="- Rp0" />

    <TextView
        android:id="@+id/tv_change"
        style="@style/SubHeading1"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:drawablePadding="@dimen/_4dp"
        android:text="@string/edit_txt"
        android:textColor="@color/blue60"
        app:drawableEndCompat="@drawable/ic_chevron_right_blue_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gp_change_payment_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tv_change, tv_payment_amount, tv_payment_method_name, tv_payment_method, iv_pay_method_icon" />

    <include
        android:id="@+id/include_default_payment_method"
        layout="@layout/layout_payment_method_default"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_payment_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="include_default_payment_method, tv_payment_method_name" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_complete_payment"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textAllCaps="false"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/br_payment_method"
        app:rippleColor="@color/black_40"
        tools:text="@string/payment_button_text" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/btn_complete_payment"
        app:layout_constraintEnd_toEndOf="@id/btn_complete_payment"
        app:layout_constraintStart_toStartOf="@id/btn_complete_payment" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_cash_back"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/cashback_gradient_bg"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_complete_payment"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_cashback_amount"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/_6dp"
            android:paddingVertical="@dimen/_4dp"
            app:drawableStartCompat="@drawable/ic_gift"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Horeee! Kamu akan dapet cashback saldo Rp2.000" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>