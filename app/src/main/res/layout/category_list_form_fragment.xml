<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingTop="@dimen/_24dp">

    <TextView
        android:id="@+id/tv_header"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="👋🏻 Hai, kenalan dulu yuk!" />

    <TextView
        android:id="@+id/tv_sub_header"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textAlignment="textStart"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_header"
        tools:text="Sebelumnya catat pembukuan di mana?" />

    <TextView
        android:id="@+id/tv_sub_header_label"
        style="@style/Label2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:background="@color/black_5"
        android:padding="@dimen/_1dp"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toBottomOf="@id/tv_sub_header"
        app:layout_constraintStart_toEndOf="@id/tv_sub_header"
        app:layout_constraintTop_toTopOf="@id/tv_sub_header"
        tools:ignore="TextContrastCheck"
        tools:text="Pilih salah satu" />

    <TextView
        android:id="@+id/tv_body"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textAlignment="textStart"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_header"
        tools:text="Punya lebih dari satu usaha? Pilih salah satu dulu ya, nanti bisa tambah lagi 😉" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_body,tv_sub_header_label,tv_sub_header"
        tools:layout_editor_absoluteY="731dp" />

    <androidx.core.widget.NestedScrollView
        android:layout_marginTop="@dimen/_4dp"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toTopOf="@id/button_container"
        app:layout_constraintTop_toBottomOf="@id/barrier">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_category"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_referral"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:hint="Kode Referral"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/rv_category"
                tools:visibility="visible">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/tv_referral"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textCapCharacters"
                    tools:text="asdf" />
            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_gravity="top"
            android:background="@color/new_divider" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_action"
            style="@style/ButtonFill"
            android:enabled="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:text="Lanjut" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>