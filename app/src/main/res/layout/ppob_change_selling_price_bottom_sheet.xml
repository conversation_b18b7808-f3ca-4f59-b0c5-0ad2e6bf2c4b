<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_20dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingBottom="@dimen/_18dp">

    <TextView
        android:id="@+id/tv_change_selling_price"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/change_selling_price"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_4dp"
        app:srcCompat="@drawable/close"
        app:layout_constraintBottom_toBottomOf="@+id/tv_change_selling_price"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_change_selling_price" />

    <TextView
        android:id="@+id/tv_selling_price"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/selling_price"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_change_selling_price" />

    <TextView
        android:id="@+id/tv_amount_to_be_paid"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/amount_to_be_paid"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_selling_price" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_input_amount"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:boxStrokeColor="@color/colorPrimary"
        app:boxStrokeWidth="0.5dp"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_selling_price"
        app:passwordToggleDrawable="@null">

        <com.bukuwarung.baseui.CurrencyEditText
            android:id="@+id/input_nominal"
            style="@style/Heading3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/indonesian_rupee"
            android:textColor="@color/blue_60"
            android:imeOptions="actionDone"
            android:inputType="number"
            android:singleLine="true"
            android:textColorHint="@color/black_20" />
    </com.google.android.material.textfield.TextInputLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_change_selling_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/rounded_green_rect_4dp"
        app:layout_constraintTop_toBottomOf="@+id/til_input_amount">

        <ImageView
            android:id="@+id/iv_info"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/_12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_money_save_info"
            app:tint="@color/green_80" />

        <TextView
            style="@style/Body3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_12dp"
            android:text="@string/update_selling_price_info"
            android:textColor="@color/black_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_info"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/DefaultMaterialButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_100dp"
        android:fontFamily="@font/roboto_bold"
        android:letterSpacing="0.05"
        android:padding="@dimen/_12dp"
        android:text="@string/save"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/layout_change_selling_price" />

</androidx.constraintlayout.widget.ConstraintLayout>