<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_main"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_saldo"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_12dp"
    android:layout_marginVertical="@dimen/_8dp"
    android:paddingHorizontal="@dimen/_2dp"
    android:background="@drawable/bg_solid_grey_corner_16_stroke_1"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <include
        android:id="@+id/layout_saldo_tile"
        layout="@layout/item_bnpl_fragment"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_12dp"
        android:layout_marginStart="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/vp_entry_point"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:layout_marginStart="@dimen/_6dp"
        android:id="@+id/vp_entry_point"
        android:orientation="vertical"
        android:layout_marginEnd="@dimen/_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/layout_saldo_tile"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tb_entry_point"
        android:layout_width="28dp"
        android:layout_height="@dimen/_2dp"
        android:rotation="90"
        android:layout_marginStart="@dimen/_2dp"
        app:layout_constraintStart_toEndOf="@id/vp_entry_point"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/colorGreyLight"
        app:tabBackground="@drawable/selector_banner_entry_point"
        app:tabIndicatorHeight="0dp"
        app:tabSelectedTextColor="@android:color/transparent" />

    <include
        android:id="@+id/baru_tag"
        layout="@layout/layout_baru_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6dp"
        android:layout_marginEnd="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="@id/vp_entry_point"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/vw_separator"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:background="@color/vrDivider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_main"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_supported_banks"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginHorizontal="12dp"
        style="@style/Label2"
        android:textColor="@color/black_40"
        android:text="@string/bank_info_home"
        app:layout_constraintTop_toBottomOf="@+id/cl_saldo"
        app:layout_constraintStart_toStartOf="@id/cl_saldo"
        app:layout_constraintEnd_toEndOf="@id/cl_saldo" />

</androidx.constraintlayout.widget.ConstraintLayout>