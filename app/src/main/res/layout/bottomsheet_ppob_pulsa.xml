<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/_16dp">

        <TextView
            android:id="@+id/title"
            style="@style/Heading2"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/ppob_pulsa_bottomsheet_title"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/txt_1"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            android:src="@drawable/pulsa_menit"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="@id/title" />

        <TextView
            android:id="@+id/txt_desc_1"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2dp"
            android:layout_marginStart="@dimen/_12dp"
            android:text="@string/ppob_pulsa_bottomsheet_layani_h"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/txt_1"
            app:layout_constraintTop_toTopOf="@id/txt_1" />

        <TextView
            android:id="@+id/tv_layani_b"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            style="@style/Body2"
            android:text="@string/ppob_pulsa_bottomsheet_layani_b"
            app:layout_constraintStart_toStartOf="@id/txt_desc_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_desc_1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/txt_2"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            android:layout_marginTop="@dimen/_10dp"
            android:src="@drawable/pulsa_rp_dapatkan"
            app:layout_constraintTop_toBottomOf="@id/tv_layani_b"
            app:layout_constraintStart_toStartOf="@id/title" />

        <TextView
            android:id="@+id/txt_desc_2"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginStart="@dimen/_12dp"
            android:text="@string/ppob_pulsa_bottomsheet_dapatkan_h"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/txt_1"
            app:layout_constraintTop_toBottomOf="@id/tv_layani_b" />

        <TextView
            android:id="@+id/tv_dapatkan_b"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            style="@style/Body2"
            android:text="@string/ppob_pulsa_bottomsheet_dapatkan_b"
            app:layout_constraintStart_toStartOf="@id/txt_desc_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_desc_2" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/txt_3"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            android:layout_marginTop="@dimen/_10dp"
            android:src="@drawable/pulsa_maksimalin"
            app:layout_constraintTop_toBottomOf="@id/tv_dapatkan_b"
            app:layout_constraintStart_toStartOf="@id/title" />

        <TextView
            android:id="@+id/txt_desc_3"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginStart="@dimen/_12dp"
            android:text="@string/ppob_pulsa_maksimalin_h"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/txt_1"
            app:layout_constraintTop_toBottomOf="@id/tv_dapatkan_b" />

        <TextView
            android:id="@+id/tv_maksimalin_b"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            style="@style/Body2"
            android:text="@string/ppob_pulsa_maksimalin_b"
            app:layout_constraintStart_toStartOf="@id/txt_desc_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_desc_3" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/txt_4"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            android:layout_marginTop="@dimen/_10dp"
            android:src="@drawable/icon_web_pulsa"
            app:layout_constraintTop_toBottomOf="@id/tv_maksimalin_b"
            app:layout_constraintStart_toStartOf="@id/title" />

        <TextView
            android:id="@+id/txt_desc_4"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginStart="@dimen/_12dp"
            android:text="@string/ppob_pulsa_maksimalin_h"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/txt_1"
            app:layout_constraintTop_toBottomOf="@id/tv_maksimalin_b" />

        <TextView
            android:id="@+id/tv_otomatis_b"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            style="@style/Body2"
            android:text="@string/ppob_pulsa_maksimalin_b"
            app:layout_constraintStart_toStartOf="@id/txt_desc_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_desc_4" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_reject"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:text="@string/try_now_small"
            style="@style/ButtonOutline.Black"
            app:layout_constraintTop_toBottomOf="@+id/tv_otomatis_b"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_allow"
            app:layout_constraintStart_toStartOf="parent" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_allow"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            style="@style/ButtonFill.Blue.Bold"
            android:text="@string/pelajari_fitur"
            app:layout_constraintBottom_toBottomOf="@id/btn_reject"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_reject"
            app:layout_constraintTop_toTopOf="@id/btn_reject" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
