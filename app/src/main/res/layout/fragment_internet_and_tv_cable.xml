<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/payment_tab_gradient_bg"
            app:layout_constraintBottom_toTopOf="@+id/vw_divider"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_input_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_rounded_rectangle_white_8dp"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent">

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_biller"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:hint="@string/internet_dan_tv_cable_hint"
                app:layout_constraintTop_toTopOf="parent"
                app:title="@string/internet_dan_tv_cable_provider" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_customer_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:hint="@string/internet_dan_tv_cable_example"
                app:layout_constraintTop_toBottomOf="@+id/biv_biller"
                app:title="@string/customer_number" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:visibility="gone"
                app:bottomText="@string/phone_number_for_sending_message"
                app:hint="@string/enter_phone_number_hint"
                app:layout_constraintEnd_toStartOf="@+id/iv_contact"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/biv_customer_number"
                app:title="@string/customer_phone"
                app:titleHint="@string/optional"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_contact"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/biv_number"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/biv_number"
                app:srcCompat="@drawable/ic_contact_book"
                tools:visibility="visible" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cek"
                style="@style/ButtonFill"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:enabled="false"
                android:padding="@dimen/_12dp"
                android:text="@string/bt_cek"
                android:visibility="gone"
                tools:visibility="visible"
                app:cornerRadius="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@+id/biv_number" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/vw_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_5dp"
            android:layout_marginTop="@dimen/_12dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@+id/cl_input_area" />

        <FrameLayout
            android:id="@+id/fl_recent_and_fav"
            android:layout_width="@dimen/_0dp"
            android:layout_height="600dp"
            android:background="@color/black_5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
