<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:theme="@style/ToolbarTheme"
        app:contentInsetEnd="0dp"
        app:contentInsetLeft="0dp"
        app:contentInsetRight="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp">
            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="28dp"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:layout_marginLeft="@dimen/_16dp"
                android:src="@mipmap/back_white" />


            <EditText
                android:id="@+id/search_input"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginLeft="@dimen/_8dp"
                android:layout_toRightOf="@id/closeBtn"
                android:background="@drawable/rounded_light_rect_4dp"
                android:hint="@string/search_contacts"
                android:maxLines="1"
                android:paddingLeft="14dp"
                android:textColor="@color/black"
                android:textColorHint="@color/greyTextColor"
                android:textSize="16dp"
                android:drawableRight="@drawable/ic_search"
                android:drawableTint="@color/colorPrimary"
                android:paddingRight="8dp"
                android:layout_marginRight="16dp"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/clear"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="16dp"
                android:background="@color/white"
                android:padding="12dp"
                android:src="@mipmap/close_grey"
                android:visibility="gone"/>
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/phonebook_new_contact_view_item" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/contactPickerRV"
            android:layout_width="match_parent"
            tools:listitem="@layout/phonebook_contact_item"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:id="@+id/explainContactPermission"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp"
            android:gravity="center"
            android:layout_marginTop="24dp"
            android:visibility="visible">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/empty_icon"
                android:layout_centerInParent="true"
                android:layout_alignParentTop="true"
                android:src="@drawable/illustration_concept_2"/>

            <TextView
                android:layout_below="@+id/empty_icon"
                android:id="@+id/permissionMessageHdr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:paddingLeft="24dp"
                android:paddingRight="24dp"
                android:layout_marginTop="32dp"
                android:layout_marginBottom="16dp"
                android:gravity="center"
                android:textSize="18dp"
                android:textStyle="bold"
                android:fontFamily="@font/roboto"
                android:textColor="#222222"
                android:text="@string/permission_header_message" />

            <TextView
                android:id="@+id/permissionMessage"
                android:layout_below="@+id/permissionMessageHdr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:paddingLeft="24dp"
                android:paddingRight="24dp"
                android:layout_marginBottom="40dp"
                android:gravity="center"
                android:fontFamily="@font/roboto"
                android:textColor="#666666"
                android:text="@string/permission_explain_message" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/permissionButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/permissionMessage"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:textColor="@color/white"
                app:backgroundTint="@color/buku_CTA"
                android:padding="12dp"
                android:fontFamily="@font/roboto"
                android:text="@string/add_customer_from_contacts" />
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>
