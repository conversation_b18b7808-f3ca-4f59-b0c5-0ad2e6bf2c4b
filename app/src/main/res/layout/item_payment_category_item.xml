<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?android:attr/selectableItemBackground">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_payment_category"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_category_name"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_payment_category"
        app:layout_constraintEnd_toStartOf="@id/rb_payment_category"
        app:layout_constraintStart_toEndOf="@id/iv_payment_category"
        app:layout_constraintTop_toTopOf="@id/iv_payment_category"
        tools:text="" />

    <RadioButton
        android:id="@+id/rb_payment_category"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:clickable="false"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@id/iv_payment_category"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_payment_category" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_payment_category" />

</androidx.constraintlayout.widget.ConstraintLayout>