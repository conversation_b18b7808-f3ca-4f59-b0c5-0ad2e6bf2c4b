<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    tools:context=".activities.pos.PosPaymentFragment">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="@dimen/dimen_28dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:paddingTop="@dimen/_14dp"
                android:paddingBottom="@dimen/_14dp"
                android:src="@drawable/ic_arrow_back" />

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/_35dp"
                android:layout_marginRight="@dimen/_35dp"
                android:layout_toEndOf="@id/closeBtn"
                android:alpha="1"
                android:fontFamily="@font/roboto"
                android:text="@string/pembayaran"
                android:textColor="@color/white"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/payment_linear_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#EEF8FF"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/total_amount_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:gravity="center"
            android:text="@string/total_amount_to_be_paid"
            android:textColor="@color/black_80"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

        <TextView
            android:id="@+id/tv_total_amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:gravity="center"
            android:textColor="@color/blue_60"
            android:textSize="@dimen/text_28sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/total_amount_text"
            tools:text="Rp125.000" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/payment_linear_layout"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tl_payment"
            app:tabTextAppearance="@android:style/TextAppearance.Widget.TabWidget"
            app:tabIndicatorColor="@color/blue_80"
            android:layout_width="match_parent"
            app:tabTextColor="@color/black_40"
            android:background="#EEF8FF"
            app:tabSelectedTextColor="@color/blue_80"
            android:layout_height="wrap_content" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_payment"
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="@dimen/_0dp" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/success_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_success"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/trx_success" />

        <TextView
            android:id="@+id/tv_trx_success"
            style="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/transaksi_berhasil_dicatat"
            android:textColor="@color/green_80"
            android:textSize="@dimen/text_24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>