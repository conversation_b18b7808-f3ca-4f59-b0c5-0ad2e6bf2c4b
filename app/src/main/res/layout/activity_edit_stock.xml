<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground"
    tools:context=".activities.inventory.detail.EditStockActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:theme="@style/ToolbarTheme">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/closeBtn"
                    android:layout_width="@dimen/dimen_28dp"
                    android:layout_height="match_parent"
                    android:paddingTop="@dimen/_14dp"
                    android:paddingBottom="@dimen/_14dp"
                    app:srcCompat="@mipmap/back_white"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/dimen_28dp"
                    android:layout_marginEnd="@dimen/_35dp"
                    android:layout_toEndOf="@id/closeBtn"
                    android:alpha="1"
                    android:fontFamily="@font/roboto"
                    android:text="@string/add_product_title"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_18sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/closeBtn"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/deleteStockBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:ignore="SmallSp"
                    tools:visibility="visible"
                    app:drawableTopCompat="@drawable/delete_red" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/layout_input_product_name"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_24dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColorHint="@color/black_20"
        app:boxStrokeColor="@color/colorPrimary"
        app:boxStrokeWidth="0.5dp"
        app:hintEnabled="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar"
        app:passwordToggleDrawable="@null">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:hint="@string/add_product_hint"
            android:imeOptions="actionDone"
            android:padding="@dimen/_12dp"
            android:paddingTop="@dimen/_6dp"
            android:paddingBottom="@dimen/_6dp"
            android:singleLine="true"
            android:textColor="@color/black_80"
            android:textSize="@dimen/text_16sp"
            tools:text="Product name" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/layout_input_product_price"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColorHint="@color/black_20"
        app:boxStrokeColor="@color/colorPrimary"
        app:boxStrokeWidth="0.5dp"
        app:hintEnabled="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_input_product_name"
        app:passwordToggleDrawable="@null">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_product_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:digits="0123456789."
            android:fontFamily="@font/roboto"
            android:hint="@string/selling_price"
            android:imeOptions="actionDone"
            android:inputType="numberDecimal"
            android:padding="@dimen/_12dp"
            android:paddingTop="@dimen/_6dp"
            android:paddingBottom="@dimen/_6dp"
            android:singleLine="true"
            android:textColor="@color/green_100"
            android:textSize="@dimen/text_16sp"
            tools:text="100.000" />
    </com.google.android.material.textfield.TextInputLayout>

    <View
        android:id="@+id/cursor"
        android:layout_width="2.0dip"
        android:layout_height="28.0dip"
        android:layout_gravity="center_vertical"
        android:background="@color/black_60"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/layout_input_product_price"
        app:layout_constraintStart_toStartOf="@id/layout_input_product_price"
        app:layout_constraintTop_toTopOf="@id/layout_input_product_price" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/layout_input_product_buying_price"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:textColorHint="@color/black_20"
        android:visibility="gone"
        app:boxStrokeColor="@color/colorPrimary"
        app:boxStrokeWidth="0.5dp"
        app:hintEnabled="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_input_product_price"
        app:passwordToggleDrawable="@null"
        tools:visibility="visible">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_product_buying_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:digits="0123456789."
            android:fontFamily="@font/roboto"
            android:hint="@string/buying_price_not_mandatory"
            android:imeOptions="actionDone"
            android:inputType="numberDecimal"
            android:padding="@dimen/_12dp"
            android:paddingTop="@dimen/_6dp"
            android:paddingBottom="@dimen/_6dp"
            android:singleLine="true"
            android:textColor="@color/red_80"
            android:textSize="@dimen/text_16sp"
            tools:text="100.000" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/tv_profit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:textColor="@color/green_100"
        android:textSize="@dimen/text_14sp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/layout_input_product_buying_price"
        tools:text="Keuntungan Rp5.000"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/rl_buying_price_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@drawable/round_corner_info_message"
        android:orientation="horizontal"
        android:padding="@dimen/_12dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_profit"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_info_icon" />

        <TextView
            android:id="@+id/txt_info_message"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_toEndOf="@+id/iv_info"
            android:text="@string/add_product_buying_price_info"
            android:textColor="@color/black_60" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_product_buying_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="layout_input_product_buying_price, rl_buying_price_info"
        />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/layout_input_product_category"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginEnd="@dimen/_4dp"
        app:boxStrokeColor="@color/colorPrimary"
        app:boxStrokeWidth="0.5dp"
        app:endIconCheckable="true"
        app:endIconDrawable="@drawable/ic_chevron_right"
        app:endIconMode="custom"
        app:endIconTint="@null"
        app:hintEnabled="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier_product_buying_price"
        app:layout_constraintEnd_toEndOf="parent"
        app:passwordToggleDrawable="@null">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_product_category"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:focusable="false"
            android:fontFamily="@font/roboto"
            android:imeOptions="actionDone"
            android:inputType="textCapSentences|textNoSuggestions"
            android:maxLines="1"
            android:padding="@dimen/_12dp"
            android:paddingTop="@dimen/_6dp"
            android:paddingBottom="@dimen/_6dp"
            android:singleLine="true"
            android:text="@string/category_label"
            android:textColor="@color/black"
            android:textSize="@dimen/text_16sp" />
    </com.google.android.material.textfield.TextInputLayout>


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/layout_input_product_measurement"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_18dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:boxStrokeColor="@color/colorPrimary"
        app:boxStrokeWidth="0.5dp"
        app:endIconCheckable="true"
        app:endIconDrawable="@drawable/ic_chevron_right"
        app:endIconMode="custom"
        app:endIconTint="@null"
        app:hintEnabled="true"
        app:layout_constraintEnd_toStartOf="@id/fl_favourite"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_input_product_category"
        app:passwordToggleDrawable="@null">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_product_measurement_unit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:focusable="false"
            android:fontFamily="@font/roboto"
            android:imeOptions="actionDone"
            android:inputType="textCapSentences|textNoSuggestions"
            android:maxLines="1"
            android:padding="@dimen/_12dp"
            android:paddingTop="@dimen/_6dp"
            android:paddingBottom="@dimen/_6dp"
            android:singleLine="true"
            android:text="Pcs"
            android:textColor="@color/black"
            android:textSize="@dimen/text_16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <FrameLayout
        android:id="@+id/fl_favourite"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:layout_marginTop="@dimen/_22dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginStart="@dimen/_4dp"
        android:background="@drawable/button_round_cornerd_edit_stroke_pop_up"
        app:layout_constraintBottom_toBottomOf="@id/layout_input_product_measurement"
        app:layout_constraintStart_toEndOf="@id/layout_input_product_measurement"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_input_product_category">


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/label_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        style="@style/Body3"
        android:gravity="center"
        android:drawablePadding="@dimen/_8dp"
        android:paddingVertical="@dimen/_4dp"
        android:textColor="@color/black_40"
        app:layout_constraintWidth_percent="0.45"
        android:drawableStart="@drawable/ic_fav_grey"
        android:text="Favourit" />

    </FrameLayout>

    <View
        android:id="@+id/gray_layer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_8dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@id/layout_input_product_measurement" />

    <TextView
        android:id="@+id/tv_status_label"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/manage_stock"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="@+id/rg_status"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/rg_status" />

    <ImageView
        android:id="@+id/iv_stock_toggle_tooltip"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_20dp"
        android:layout_marginStart="@dimen/_6dp"
        app:srcCompat="@drawable/ic_info_inventory"
        app:layout_constraintBottom_toBottomOf="@+id/rg_status"
        app:layout_constraintStart_toEndOf="@+id/tv_status_label"
        app:layout_constraintTop_toTopOf="@+id/rg_status"
        app:tint="@color/black_40" />

    <RadioGroup
        android:id="@+id/rg_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@drawable/custom_switch_background"
        android:checkedButton="@id/rb_active"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gray_layer">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_inactive"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/custom_switch_color"
            android:button="@null"
            android:paddingStart="@dimen/_12dp"
            android:paddingTop="@dimen/_8dp"
            android:paddingEnd="@dimen/_12dp"
            android:paddingBottom="@dimen/_8dp"
            android:text="@string/not_active"
            android:textColor="@color/white" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/rb_active"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/custom_switch_color"
            android:button="@null"
            android:paddingStart="@dimen/_12dp"
            android:paddingTop="@dimen/_8dp"
            android:paddingEnd="@dimen/_12dp"
            android:paddingBottom="@dimen/_8dp"
            android:text="@string/aktif"
            android:textColor="@color/white" />
    </RadioGroup>

    <LinearLayout
        android:id="@+id/ll_stock_values"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintTop_toBottomOf="@id/rg_status">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_input_product_stock"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_weight="1"
            android:textColorHint="@color/black_20"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="0.5dp"
            app:hintEnabled="true"
            app:passwordToggleDrawable="@null"
            tools:visibility="visible">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_product_stock"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:digits="0123456789,"
                android:fontFamily="@font/roboto"
                android:hint="@string/current_stock_capitalized"
                android:imeOptions="actionDone"
                android:inputType="numberDecimal"
                android:padding="@dimen/_12dp"
                android:singleLine="true"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_16sp"
                tools:text="50" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_input_product_stock_minimum"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_weight="1"
            android:textColorHint="@color/black_20"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="0.5dp"
            app:hintEnabled="true"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_product_stock_minimum"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto"
                android:hint="@string/stock_minimum_capitalized"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:padding="@dimen/_12dp"
                android:singleLine="true"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_16sp"
                tools:text="20" />
        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

    <View
        android:id="@+id/button_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        android:background="#EEEEEE"
        app:layout_constraintTop_toTopOf="@id/btn_save" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:enabled="false"
        android:text="@string/save"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_expr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_currency"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/ll_result"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/keyboardView"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>