<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <include
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:id="@+id/layout_subtotal_pos"
        layout="@layout/item_pos_payment_detail"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <include
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:id="@+id/layout_discount_pos"
        layout="@layout/item_pos_payment_detail"
        android:visibility="gone"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_subtotal_pos"/>

    <include
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:id="@+id/layout_tax_pos"
        layout="@layout/item_pos_payment_detail"
        android:visibility="gone"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_discount_pos"/>

    <View
        android:id="@+id/view_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:background="@drawable/horizontal_dashed_line"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:foregroundGravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_tax_pos"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <TextView
        android:id="@+id/tv_transaction_type_utang"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/Heading3"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/total"
        android:textColor="@color/black_80"
        android:layout_marginEnd="@dimen/_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        app:layout_constraintEnd_toEndOf="@id/guideline"/>

    <TextView
        android:id="@+id/tv_amount_given"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        style="@style/Heading3"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="end"
        android:layout_marginEnd="@dimen/_16dp"
        tools:text="50.000"
        android:textColor="@color/black_80"
        android:layout_marginStart="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_transaction_type_utang"
        app:layout_constraintStart_toEndOf="@id/guideline"/>

    <View
        android:id="@+id/view_divider2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_2dp"
        android:background="@drawable/horizontal_dashed_line"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:foregroundGravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_transaction_type_utang"/>

    <include
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:id="@+id/layout_cash_paid"
        layout="@layout/item_pos_payment_detail"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider2"/>

    <include
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:id="@+id/layout_return_paid"
        layout="@layout/item_pos_payment_detail"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_cash_paid"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>