<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="isDebit"
            type="boolean" />

        <variable
            name="transactionType"
            type="int" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <!-- Toolbar  for title and back button-->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/colorPrimary"
                app:theme="@style/ToolbarTheme">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/close"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="0dp"
                        android:src="@mipmap/back_white" />

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_toRightOf="@+id/close"
                        android:drawableLeft="@drawable/dot_highlighter"
                        android:ellipsize="end"
                        android:fontFamily="sans-serif-medium"
                        android:maxLines="1"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textColor="#ffffff"
                        android:textStyle="normal"
                        tools:text="@string/new_utang_piutang" />

                </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>

            <!--Customer name-->
            <LinearLayout
                android:id="@+id/fragment_container"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"></LinearLayout>

            <ScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fillViewport="true"
                android:visibility="visible"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/formView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/_16dp">

                    <TextView
                        android:id="@+id/tvOffline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/yellow5"
                        android:elevation="@dimen/_12dp"
                        android:drawablePadding="@dimen/_8dp"
                        android:fontFamily="@font/roboto"
                        android:paddingStart="@dimen/_16dp"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:paddingTop="12dp"
                        android:paddingEnd="@dimen/_16dp"
                        android:paddingBottom="12dp"
                        android:text="@string/recordings_are_saved_in_offline_mode"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        app:drawableStartCompat="@drawable/ic_offline_msg" />

                    <RadioGroup
                        android:id="@+id/rg_trx_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:checkedButton="@id/rb_debit"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/_16dp"
                        android:paddingTop="@dimen/_8dp"
                        android:paddingEnd="@dimen/_8dp">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/rb_debit"
                            style="@style/SubHeading1"
                            android:layout_width="@dimen/_0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="4dp"
                            android:layout_weight="1"
                            android:background="@drawable/trx_type_red_bg_selector_light"
                            android:buttonTint="@color/white"
                            android:paddingStart="@dimen/_4dp"
                            android:paddingTop="@dimen/_12dp"
                            android:paddingEnd="@dimen/_8dp"
                            android:paddingBottom="@dimen/_12dp"
                            android:text="@string/bal_debit"
                            android:textColor="@drawable/trx_type_text_color_selector" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/rb_credit"
                            style="@style/SubHeading1"
                            android:layout_width="@dimen/_0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_weight="1"
                            android:background="@drawable/trx_type_green_bg_selector_light"
                            android:buttonTint="@color/white"
                            android:paddingStart="@dimen/_8dp"
                            android:paddingTop="@dimen/_12dp"
                            android:paddingEnd="@dimen/_8dp"
                            android:paddingBottom="@dimen/_12dp"
                            android:text="@string/bal_credit"
                            android:textColor="@drawable/trx_type_text_color_selector" />
                    </RadioGroup>

                    <!--Customer balance-->
                    <RelativeLayout
                        android:id="@+id/customer_balance_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10dp">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/iv_utang_balance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_8dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginBottom="@dimen/_16dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:srcCompat="@drawable/ic_harga_model" />

                        <TextView
                            android:id="@+id/tv_modal_main_title"
                            style="@style/Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toEndOf="@id/iv_utang_balance"
                            android:text="Jumlah uang"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/currency_symbol"
                            style="@style/Heading3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_toStartOf="@id/transaction_input_amount_result"
                            android:maxLength="18"
                            android:text="Rp"
                            android:textColor="@color/red_80"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/transaction_input_amount_result"
                            style="@style/Heading3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toStartOf="@id/cursor"
                            android:autoSizeMaxTextSize="34dp"
                            android:autoSizeMinTextSize="22dp"
                            android:autoSizeStepGranularity="1dp"
                            android:fontFamily="@font/roboto"
                            android:maxLength="13"
                            android:text=""
                            android:textColor="@color/red_80"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                        <View
                            android:id="@+id/cursor"
                            android:layout_width="2.0dip"
                            android:layout_height="24.0dip"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="@dimen/_24dp"
                            android:background="@color/red_80" />
                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/ll_expr_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <View
                            android:id="@+id/nominal_line"
                            style="@style/Divider.Horizontal"
                            android:layout_width="165dp"
                            android:layout_height="1dp"
                            android:layout_gravity="end"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:background="@color/black_10" />

                        <LinearLayout
                            android:id="@+id/exprLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="end"
                            android:visibility="visible"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/text_amount_calc"
                                style="@style/Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="end"
                                android:layout_marginTop="@dimen/_4dp"
                                android:layout_marginEnd="@dimen/_16dp"
                                android:fontFamily="@font/roboto"
                                android:gravity="center_horizontal"
                                android:lineSpacingExtra="6sp"
                                android:textAlignment="center"
                                android:textColor="@color/black_40"
                                android:textSize="15sp"
                                android:textStyle="normal"
                                tools:text="20x10" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:id="@+id/vw_divider"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_2dp"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_12dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:layout_marginBottom="@dimen/_4dp"
                        android:background="@color/black_10"
                        app:layout_constraintTop_toBottomOf="@id/exprLayout" />

                    <RelativeLayout
                        android:id="@+id/contact_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/utang_contact_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_8dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginBottom="@dimen/_16dp"
                            android:visibility="visible"
                            app:layout_constraintStart_toStartOf="parent"
                            app:srcCompat="@drawable/ic_utang_contact_icon_red" />

                        <TextView
                            android:id="@+id/tv_modal_main_title1"
                            style="@style/Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toEndOf="@id/utang_contact_icon"
                            android:text="@string/contact_header_new"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center"
                            android:layout_margin="@dimen/_16dp"
                            android:gravity="end"
                            android:hint="@string/nama_pelanggan"
                            android:maxLength="18"
                            android:textColor="@color/black_20"
                            android:textSize="14sp" />
                    </RelativeLayout>

                    <com.bukuwarung.favoritecustomer.FavoriteCustomerWidget
                        android:id="@+id/favorite_customer_widget"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        tools:layout_height="96dp"
                        tools:visibility="visible" />

                    <RelativeLayout
                        android:id="@+id/rl_modal_indicator"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/red_5"
                        android:paddingTop="@dimen/_5dp"
                        android:paddingBottom="@dimen/_5dp"
                        android:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent">

                        <TextView
                            android:id="@+id/txt_modal_indicator"
                            style="@style/Body2"
                            android:layout_width="match_parent"
                            android:layout_height="30dp"
                            android:layout_alignParentStart="true"
                            android:layout_centerHorizontal="true"
                            android:gravity="center_horizontal|center_vertical"
                            android:textColor="@color/red_80"
                            android:textSize="@dimen/text_14sp"
                            android:textStyle="bold"
                            tools:text="Utang Banu ke saya jadi Rp400.000" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/black_5">

                        <!--Additional data like date, notes-->
                        <LinearLayout
                            android:id="@+id/additional_data_layout"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/black_5"
                            android:orientation="vertical"
                            android:paddingLeft="@dimen/_16dp"
                            android:paddingTop="@dimen/_10dp"
                            android:paddingRight="@dimen/_16dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    style="@style/Body1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/detail_customer"
                                    android:textStyle="bold" />

                                <TextView
                                    style="@style/Body1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:text="@string/tidak_wajib"
                                    android:textColor="@color/black_40"
                                    android:textSize="12sp" />
                            </RelativeLayout>
                            <!--Note Layout-->
                            <LinearLayout
                                android:id="@+id/customerDetailsLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/_16dp"
                                android:background="@drawable/rounded_rectangle_customer_name">

                                <ImageView
                                    android:id="@+id/phone_icon"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:padding="@dimen/_14dp"
                                    android:paddingStart="@dimen/_16dp"
                                    android:paddingTop="@dimen/_12dp"
                                    android:paddingBottom="@dimen/_12dp"
                                    android:src="@drawable/ic_utang_phone" />

                                <EditText
                                    android:id="@+id/customerDetails"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:background="@null"
                                    android:focusedByDefault="false"
                                    android:fontFamily="@font/roboto"
                                    android:gravity="center_vertical"
                                    android:hint="@string/phone_hint"
                                    android:imeOptions="actionDone"
                                    android:inputType="phone"
                                    android:lineSpacingExtra="5sp"
                                    android:scrollbars="vertical"
                                    android:textColor="#666666"
                                    android:textColorHint="@color/black_20"
                                    android:textSize="16sp" />

                            </LinearLayout>

                            <!--Date Layout-->
                            <RelativeLayout
                                android:id="@+id/linear_layout_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:paddingTop="@dimen/_12dp"
                                android:paddingBottom="@dimen/_12dp">

                                <ImageView
                                    android:id="@+id/date_icon"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:paddingTop="@dimen/_10dp"
                                    android:paddingRight="@dimen/_10dp"
                                    android:paddingBottom="@dimen/_10dp"
                                    android:src="@drawable/ic_utang_calendar_icon" />

                                <TextView
                                    android:id="@+id/date_text_view"
                                    style="@style/Body2"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_gravity="center_vertical"
                                    android:layout_toEndOf="@id/date_icon"
                                    android:fontFamily="@font/roboto"
                                    android:textColor="@color/black_80"
                                    android:textStyle="bold"
                                    tools:text="24 Mar 2021" />

                                <CheckBox
                                    android:id="@+id/sendCustomerSmsCheckbox"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_marginStart="@dimen/_16dp"
                                    android:layout_marginTop="@dimen/_1dp"
                                    android:layout_marginEnd="1dp"
                                    android:checked="false"
                                    android:fontFamily="@font/roboto"
                                    android:paddingStart="@dimen/_4dp"
                                    android:paddingEnd="@dimen/_4dp"
                                    android:text="@string/send_sms_title"
                                    android:textColor="@color/black_60"
                                    android:textSize="14sp"
                                    android:theme="@style/CheckBoxPayment"
                                    android:visibility="visible" />

                            </RelativeLayout>
                            <!--Note Layout-->
                            <LinearLayout
                                android:id="@+id/noteLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/rounded_rectangle_customer_name">

                                <ImageView
                                    android:id="@+id/note_icon"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingStart="@dimen/_14dp"
                                    android:paddingTop="@dimen/_14dp"
                                    android:paddingRight="@dimen/_10dp"
                                    android:paddingBottom="@dimen/_12dp"
                                    android:src="@drawable/ic_edit_with_border"
                                    app:tint="#8D8D8D" />

                                <EditText
                                    android:id="@+id/note"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:background="@null"
                                    android:focusedByDefault="false"
                                    android:fontFamily="@font/roboto"
                                    android:gravity="center_vertical"
                                    android:hint="@string/category_hint_new"
                                    android:imeOptions="actionDone"
                                    android:inputType="text"
                                    android:lineSpacingExtra="5sp"
                                    android:scrollbars="vertical"
                                    android:textColor="#666666"
                                    android:textColorHint="@color/black_20"
                                    android:textSize="16sp" />

                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>

            </ScrollView>

            <FrameLayout
                android:id="@+id/bottom_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                tools:visibility="visible">

                <!--Bottom Layout for save btn-->
                <LinearLayout
                    android:id="@+id/saveOnboarding"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:gravity="bottom"
                    android:orientation="vertical">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE" />

                    <!--Button-->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/save_button"
                        style="@style/DisableMaterialButtonStyleAdjacent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:layout_marginBottom="@dimen/_16dp"
                        android:fontFamily="@font/roboto_bold"
                        android:gravity="center"
                        android:padding="12dp"
                        android:paddingTop="@dimen/_16dp"
                        android:paddingBottom="@dimen/_16dp"
                        android:text="@string/save"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        app:cornerRadius="4dp" />

                </LinearLayout>

                <!--                custom keyboard view-->
                <com.bukuwarung.keyboard.CustomKeyboardView
                    android:id="@+id/keyboard_view"
                    android:layout_width="match_parent"
                    android:layout_height="245dp"
                    android:layout_alignParentBottom="true"
                    android:layout_gravity="bottom"
                    android:animateLayoutChanges="true"
                    android:visibility="gone" />

            </FrameLayout>

        </LinearLayout>

        <!--animation Layout for success message-->
        <include
            android:id="@+id/animation_layout"
            layout="@layout/transaction_success_animation_layout" />
        <!--Fragment container for the User Contact Fragment-->
        <FrameLayout
            android:id="@+id/contact_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

</layout>