<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:context="com.bukuwarung.activities.selfreminder.SetSelfReminderActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:contentInsetLeft="@dimen/_0dp"
            app:contentInsetStart="@dimen/_0dp"
            app:contentInsetStartWithNavigation="@dimen/_0dp"
            app:theme="@style/ToolbarTheme">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/backBtn"
                    android:layout_alignParentStart="true"
                    android:layout_width="@dimen/_25dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_height="@dimen/_25dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="center"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/back_white"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/title"
                    android:layout_marginStart="@dimen/_24dp"
                    android:layout_width="wrap_content"
                    android:drawableStart="@drawable/dot_highlighter"
                    android:ellipsize="end"
                    android:layout_height="@dimen/_24dp"
                    android:layout_toEndOf="@+id/backBtn"
                    android:layout_alignParentTop="true"
                    android:fontFamily="@font/roboto"
                    android:textStyle="bold"
                    android:layout_centerVertical="true"
                    android:textSize="@dimen/text_18sp"
                    android:textColor="@color/white"
                    android:lineSpacingExtra="@dimen/text_8sp"
                    android:lineHeight="@dimen/_26dp"
                    android:gravity="top"
                    android:maxLines="1"
                    android:text="@string/create_new_remainder"
                    tools:ignore="UnusedAttribute" />

            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:orientation="vertical">-->
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/btn_layout"
                android:layout_below="@+id/toolbar"
                android:fillViewport="true">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/_24dp"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        app:layout_behavior="@string/appbar_scrolling_view_behavior" >

                        <LinearLayout
                            android:background="@drawable/rectangle_self_reminder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/_16dp"
                            android:layout_marginBottom="@dimen/_22dp"
                            android:orientation="horizontal"
                            android:layout_gravity="center"
                            android:gravity="center"
                            >

                            <ImageView
                                android:id="@+id/image"
                                android:layout_width="@dimen/_16dp"
                                android:layout_height="@dimen/_16dp"
                                android:src="@drawable/daily_reminder_info"
                                android:contentDescription="@null"
                                android:layout_marginHorizontal="@dimen/_5dp"
                                />


                            <TextView
                                android:id="@+id/selfRemainderDailyMessage"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto"
                                android:text="@string/daily_reminder_message"
                                android:textColor="@color/black_60"
                                android:textSize="@dimen/text_14sp"
                                android:layout_marginVertical="@dimen/_6dp"
                                />

                        </LinearLayout>

                        <TimePicker
                            android:id="@+id/timePicker"
                            android:theme="@style/TimePickerTheme"
                            android:layout_gravity="center_horizontal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:timePickerMode="spinner"/>


                        <TextView
                            android:id="@+id/selfRemainderMessage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:text="@string/time_to_remainder"
                            android:textAlignment="center"
                            android:textColor="@color/black_60"
                            android:textSize="@dimen/text_14sp"
                            android:layout_marginTop="@dimen/_12dp"
                            android:layout_gravity="center_horizontal"
                            />

                        <TextView
                            android:id="@+id/selfRemainderCategory"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:fontFamily="@font/roboto"
                            android:layout_marginTop="@dimen/_24dp"
                            android:text="@string/select_remainder_category"
                            android:textColor="@color/black_60"
                            android:textSize="@dimen/text_18sp"
                            />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginTop="@dimen/_8dp"
                            android:layout_marginBottom="@dimen/_16dp">
                            <LinearLayout
                                android:id="@+id/layoutUtangPiutang"
                                android:layout_width="@dimen/_0dp"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginStart="@dimen/_16dp"
                                android:layout_marginEnd="@dimen/_8dp"
                                android:layout_weight="1"
                                android:background="@drawable/product_edittext_bg">

                                <ImageView
                                    android:id="@+id/imageUtangPiutang"
                                    android:layout_width="wrap_content"
                                    android:layout_height="@dimen/_20dp"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="@dimen/_5dp"
                                    android:layout_marginBottom="@dimen/_10dp"
                                    android:src="@drawable/ic_hutang_inactive"
                                    android:contentDescription="@null"
                                    app:tint="@color/black_60"
                                    />

                                <TextView
                                    android:id="@+id/textUtangPiutang"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/roboto"
                                    android:layout_marginBottom="@dimen/_5dp"
                                    android:text="@string/category_utang_piutang"
                                    android:textColor="@color/black_60"
                                    android:textAlignment="center"
                                    android:textSize="@dimen/text_12sp"
                                    android:layout_gravity="center_horizontal"
                                    />

                            </LinearLayout>
                            <LinearLayout
                                android:id="@+id/layoutTransaksi"
                                android:layout_width="@dimen/_0dp"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_weight="1"
                                android:background="@drawable/product_edittext_bg">

                                <ImageView
                                    android:id="@+id/imageTransaksi"
                                    android:layout_width="wrap_content"
                                    android:layout_height="@dimen/_20dp"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="@dimen/_5dp"
                                    android:layout_marginBottom="@dimen/_10dp"
                                    android:src="@drawable/ic_transaction_inactive"
                                    android:contentDescription="@null"
                                    app:tint="@color/black_60"
                                    />

                                <TextView
                                    android:id="@+id/textTransaksi"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="@dimen/_5dp"
                                    android:fontFamily="@font/roboto"
                                    android:text="@string/category_transaksi"
                                    android:textColor="@color/black_60"
                                    android:textSize="@dimen/text_12sp"
                                    android:layout_gravity="center_horizontal"
                                    />

                            </LinearLayout>
                            <LinearLayout
                                android:id="@+id/layoutPembayaran"
                                android:layout_width="@dimen/_0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_8dp"
                                android:layout_marginEnd="@dimen/_16dp"
                                android:orientation="vertical"
                                android:layout_weight="1"
                                android:background="@drawable/product_edittext_bg">

                                <ImageView
                                    android:id="@+id/imagePembayaran"
                                    android:layout_width="wrap_content"
                                    android:layout_height="@dimen/_20dp"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="@dimen/_5dp"
                                    android:layout_marginBottom="@dimen/_10dp"
                                    android:src="@drawable/pembayaran"
                                    android:contentDescription="@null"
                                    />

                                <TextView
                                    android:id="@+id/textPembayaran"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/roboto"
                                    android:layout_marginBottom="@dimen/_5dp"
                                    android:text="@string/category_pembayaran"
                                    android:textColor="@color/black_60"
                                    android:textSize="@dimen/text_12sp"
                                    android:layout_gravity="center_horizontal"
                                    />

                            </LinearLayout>
                        </LinearLayout>

                        <EditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:drawableLeft="@drawable/ic_reminder_notes"
                            android:hint="@string/remainder_title"
                            android:textColorHint="@color/hint_color"
                            android:drawablePadding="@dimen/_16dp"
                            android:padding="@dimen/_16dp"
                            app:boxStrokeColor="@color/colorPrimary"
                            app:boxStrokeWidth="0.5dp"
                            android:textColor="@color/black"
                            android:background="@drawable/layout_color_change"
                            android:id="@+id/selfReminderNotes"
                            />

                        <LinearLayout
                            android:id="@+id/remainderSound"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:visibility="invisible"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_16dp" >

                            <TextView
                                android:id="@+id/remainderSoundText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto"
                                android:text="@string/remainder_sound"
                                android:textColor="@color/black_60"
                                android:textSize="@dimen/text_16sp"
                                android:layout_weight="1"
                                />

                            <Switch
                                android:id="@+id/remainderSoundSwitch"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginRight="@dimen/_16dp"
                                android:checked="true" />

                        </LinearLayout>

                    </LinearLayout>
            </ScrollView>
            <LinearLayout
                android:id="@+id/btn_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_gravity="center_vertical"
                android:gravity="bottom"
                >
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/saveSelfRemainder"
                    style="@style/DefaultMaterialButtonStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:fontFamily="@font/roboto"
                    android:text="@string/save"
                    android:textAllCaps="false"
                    android:textColor="@color/cta_text_button"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    app:backgroundTint="@color/buku_CTA"
                    />
            </LinearLayout>
    </RelativeLayout>
</LinearLayout>