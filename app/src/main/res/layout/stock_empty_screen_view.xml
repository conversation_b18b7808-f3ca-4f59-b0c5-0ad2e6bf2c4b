<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:padding="@dimen/_24dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        android:id="@+id/empty_screen_title_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/stock_tab_empty_screen_title"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/Heading2"
        android:textColor="@color/colorPrimary"
        />

    <TextView
        android:id="@+id/empty_screen_body_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/stock_empty_string_desc"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/Body3"
        android:textColor="@color/black_60"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/empty_screen_title_txt"
        android:gravity="center"
        />
    <ImageView
        android:id="@+id/empty_screen_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/empty_screen_body_txt"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/stock_empty_image"
        android:layout_marginTop="@dimen/_8dp"
        />
    <TextView
        android:id="@+id/empty_screen_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/stock_empty_screen_msg"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/black_80"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@+id/empty_screen_img"
        android:gravity="center"
        android:textSize="@dimen/text_14sp"
        />

</androidx.constraintlayout.widget.ConstraintLayout>