<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_top_rounded_corner">
    <View
        android:id="@+id/divider"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/black_5"
        />
    <TextView
        android:id="@+id/tv_secure_info"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/secure_info"
        android:textAlignment="center"
        android:layout_marginStart="@dimen/_18dp"
        android:layout_marginEnd="@dimen/_18dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_save_account"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_secure_info"
        >
        <ImageView
            android:id="@+id/iv_save_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_styled_shield"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <TextView
            style="@style/SubHeading1"
            android:id="@+id/tv_save_account"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/save_account_message"
            android:layout_marginStart="@dimen/_14dp"
            android:layout_marginEnd="@dimen/_20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_save_account"/>
        <TextView
            android:id="@+id/tv_save_account_message"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/customers_pay_info"
            app:layout_constraintEnd_toEndOf="@+id/tv_save_account"
            app:layout_constraintStart_toStartOf="@+id/tv_save_account"
            app:layout_constraintTop_toBottomOf="@+id/tv_save_account"/>

        <include
            android:id="@+id/bank_detail_payment_in"
            layout="@layout/select_bank_account_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_save_account_message" />

    </androidx.constraintlayout.widget.ConstraintLayout>



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_receiver_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/layout_save_account"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >
        <ImageView
            android:id="@+id/iv_scissors"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_scissors"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            style="@style/SubHeading1"
            android:id="@+id/tv_receiver_info"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:text="@string/customers_receive_message"
            android:layout_marginStart="@dimen/_20dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_scissors"/>

        <TextView
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/customers_receive_info"
            app:layout_constraintTop_toBottomOf="@+id/tv_receiver_info"
            app:layout_constraintStart_toStartOf="@+id/tv_receiver_info"
            app:layout_constraintEnd_toEndOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_trx_record_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@+id/layout_receiver_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >
        <ImageView
            android:id="@+id/iv_trx_record_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_span_pencil"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <TextView
            style="@style/SubHeading1"
            android:id="@+id/tv_trx_record_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/trx_recorded_message"
            android:layout_marginStart="@dimen/_20dp"
            app:layout_constraintStart_toEndOf="@+id/iv_trx_record_info"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
        <TextView
            style="@style/Body2"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:text="@string/trx_recorded_info"
            app:layout_constraintTop_toBottomOf="@+id/tv_trx_record_info"
            app:layout_constraintStart_toStartOf="@+id/tv_trx_record_info"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_trx_record_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <com.google.android.material.button.MaterialButton
            android:id="@+id/check_video"
            style="@style/Button.OutlinePrimary"
            android:layout_width="@dimen/_0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="4dp"
            android:text="@string/check_video"
            android:textAllCaps="false"
            android:textColor="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="@+id/btn_help"
            app:layout_constraintEnd_toStartOf="@id/btn_help"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/btn_help" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_help"
            style="@style/ButtonFill.Blue"
            android:textAppearance="@style/SubHeading1"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="@dimen/_16dp"
            android:text="@string/need_help"
            app:icon="@mipmap/ic_whatsapp_white_24dp"
            android:textAllCaps="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/check_video"
            app:layout_constraintTop_toTopOf="parent"
            app:iconGravity="textStart"
            app:iconTint="@color/white"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_ok"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
       android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginVertical="@dimen/_20dp"
        android:text="@string/understand"
        android:textAllCaps="false"
        app:layout_constraintTop_toBottomOf="@+id/layout_trx_record_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>