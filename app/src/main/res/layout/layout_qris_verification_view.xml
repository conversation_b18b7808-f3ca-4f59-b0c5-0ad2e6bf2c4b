<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_qris_verification"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/rounded_black_10_10dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_qris_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_qris_rejected" />

        <TextView
            android:id="@+id/tv_qris_status_heading"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_15dp"
            android:layout_marginEnd="@dimen/_12dp"
            app:layout_constraintBottom_toTopOf="@id/tv_qris_status_body"
            app:layout_constraintStart_toEndOf="@+id/iv_qris_status"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Pengajuan QRIS belum berhasil" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/checklist_1"
            android:layout_width="@dimen/_16dp"
            android:layout_height="@dimen/_16dp"
            android:layout_marginStart="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_qris_status_heading"
            app:layout_constraintStart_toEndOf="@id/tv_qris_status_heading"
            app:layout_constraintTop_toTopOf="@id/tv_qris_status_heading"
            app:srcCompat="@drawable/ic_check_circle_green" />

        <View
            android:id="@+id/separator_1"
            android:layout_width="@dimen/_16dp"
            android:layout_height="2dp"
            android:layout_margin="@dimen/_4dp"
            android:background="@color/green_80"
            app:layout_constraintBottom_toBottomOf="@id/checklist_1"
            app:layout_constraintEnd_toStartOf="@id/checklist_2"
            app:layout_constraintStart_toEndOf="@id/checklist_1"
            app:layout_constraintTop_toTopOf="@id/checklist_1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/checklist_2"
            android:layout_width="@dimen/_16dp"
            android:layout_height="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_qris_status_heading"
            app:layout_constraintEnd_toStartOf="@id/separator_2"
            app:layout_constraintStart_toEndOf="@id/separator_1"
            app:layout_constraintTop_toTopOf="@id/tv_qris_status_heading"
            app:srcCompat="@drawable/ic_check_circle_green" />

        <View
            android:id="@+id/separator_2"
            android:layout_width="@dimen/_16dp"
            android:layout_height="2dp"
            android:layout_margin="@dimen/_4dp"
            android:background="@color/black_10"
            app:layout_constraintBottom_toBottomOf="@id/checklist_2"
            app:layout_constraintEnd_toStartOf="@id/checklist_3"
            app:layout_constraintStart_toEndOf="@id/checklist_2"
            app:layout_constraintTop_toTopOf="@id/checklist_2" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/checklist_3"
            android:layout_width="@dimen/_16dp"
            android:layout_height="@dimen/_16dp"
            android:layout_marginStart="@dimen/_4dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_qris_status_heading"
            app:layout_constraintStart_toEndOf="@id/separator_2"
            app:layout_constraintTop_toTopOf="@id/tv_qris_status_heading"
            app:srcCompat="@drawable/ic_check_circle_green"
            app:tint="@color/black_10" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/in_progress_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="checklist_1,checklist_2,checklist_3,separator_1,separator_2" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/header_barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_qris_status_heading" />

        <TextView
            android:id="@+id/tv_qris_status_body"
            style="@style/Label2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginEnd="@dimen/_4dp"
            android:layout_marginBottom="@dimen/_20dp"
            android:lineSpacingExtra="3dp"
            android:textColor="@color/black_40"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_retry"
            app:layout_constraintStart_toEndOf="@+id/iv_qris_status"
            app:layout_constraintTop_toBottomOf="@+id/header_barrier"
            tools:text="KTP kamu gagal untuk diverifikasi. Coba verifikasi ulang KTP kamu. Jangan tampilkan lagi." />

        <TextView
            android:id="@+id/tv_retry"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_10dp"
            android:background="@drawable/bg_rounded_rectangle_red60_4dp"
            android:padding="@dimen/_4dp"
            android:text="@string/reload"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_banner"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:cardCornerRadius="@dimen/_10dp">

        <ImageView
            android:id="@+id/iv_banner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>