<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/cvReceipt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:cardBackgroundColor="@color/white"
        app:contentPaddingBottom="32dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTransactionDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/card_header_gradient_bg"
                android:paddingStart="20dp"
                android:paddingTop="12dp"
                android:paddingEnd="20dp"
                android:paddingBottom="12dp"
                android:textAlignment="textStart"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Senin, 11 Nov 2011" />

            <View
                android:id="@+id/header_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:background="@color/black_10"
                android:visibility="gone" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp">


                <TextView
                    android:id="@+id/tvWarungName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:textColor="@color/black_80"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Warung Chimay" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_check_circle_green"
                    android:layout_marginStart="@dimen/_8dp"
                    app:layout_constraintTop_toTopOf="@+id/tvWarungName"
                    app:layout_constraintBottom_toBottomOf="@+id/tvWarungName"
                    app:layout_constraintStart_toEndOf="@+id/tvWarungName"/>

                <TextView
                    android:id="@+id/tvWarungPhone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:textColor="@color/black_40"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvWarungName"
                    tools:text="08129213122" />

                <View
                    android:id="@+id/bgNominalBackground"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="12dp"
                    android:background="@color/black_33"
                    app:layout_constraintBottom_toBottomOf="@id/tvTransactionNominal"
                    app:layout_constraintTop_toBottomOf="@id/tvWarungPhone" />

                <TextView
                    android:id="@+id/tvTrxType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/black_80"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/bgNominalBackground"
                    tools:text="@string/income_label" />

                <TextView
                    android:id="@+id/tvTransactionNominal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:paddingBottom="@dimen/_8dp"
                    android:textColor="@color/black_80"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="@id/tvTrxType"
                    app:layout_constraintTop_toBottomOf="@id/tvTrxType"
                    tools:text="Rp.50.000" />

                <ImageView
                    android:id="@+id/img_secure"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_secure_transaction_new"
                    app:layout_constraintBottom_toBottomOf="@id/bgNominalBackground"
                    app:layout_constraintDimensionRatio="W, 1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/bgNominalBackground" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@id/bgNominalBackground">
                    <LinearLayout
                        android:id="@+id/catatanLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:fontFamily="@font/roboto"
                            android:text="@string/label_note"
                            android:textColor="@color/black_40"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/tvTransactionNotes"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:textColor="@color/black_80"
                            android:textSize="14sp"
                            tools:text="Jual jualan" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2dp"
                            android:layout_marginTop="12dp"
                            android:background="@color/black_10" />
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/categoryLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:fontFamily="@font/roboto"
                            android:text="@string/category_label"
                            android:textColor="@color/black_40"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/tvTransactionCategory"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:textColor="@color/black_80"
                            android:textSize="14sp"
                            tools:text="Penjualan" />

                        <View
                            android:id="@+id/categoryBottomBorder"
                            android:layout_width="match_parent"
                            android:layout_height="2dp"
                            android:layout_marginTop="12dp"
                            android:background="@color/black_10" />
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/productDetailLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:fontFamily="@font/roboto"
                            android:text="@string/product_detail"
                            android:textColor="@color/black_40"
                            android:textSize="12sp" />

                        <LinearLayout
                            android:id="@+id/llProductList"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>


</LinearLayout>