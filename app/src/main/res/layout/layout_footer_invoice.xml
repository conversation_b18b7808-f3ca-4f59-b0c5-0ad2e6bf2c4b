<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_notes"
        android:layout_width="@dimen/_0dp"
        style="@style/Body2"
        android:textColor="@color/black_40"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/notes"/>

    <TextView
        android:id="@+id/tv_notes_txt"
        android:layout_width="@dimen/_0dp"
        style="@style/Body2"
        android:textColor="@color/black_60"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_notes"
        tools:text="Catatan segera dilnuskan"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_payment_catatan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@color/black_5"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/tv_notes_txt">

        <TextView
            android:id="@+id/tv_pdt_name"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Pulsa Telkomsel 100.000" />

        <TextView
            android:id="@+id/tv_number"
            style="@style/Heading2"
            android:textSize="@dimen/item_22sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_8dp"
            android:textColor="@color/blue_80"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_pdt_name"
            tools:text="081275598545" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/note_hint"
        style="@style/Label1"
        android:layout_width="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="wrap_content"
        android:text="@string/electricity_token_hint"
        android:textColor="@color/black_40"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_payment_catatan" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/note_hint"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/_26dp"
        android:layout_marginTop="@dimen/_18dp"
        android:id="@+id/bukuwarung_ad_layout">

        <ImageView
            android:id="@+id/img_app_logo"
            android:layout_width="@dimen/_26dp"
            android:layout_height="@dimen/_32dp"
            android:src="@drawable/app_logo"
            app:layout_constraintDimensionRatio="H, 1:1"
            app:layout_constraintEnd_toStartOf="@id/tv_footer_one"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_footer_one"
            app:layout_constraintBottom_toBottomOf="@+id/tv_bukuwarung_url" />

        <TextView
            android:id="@+id/tv_footer_one"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginTop="38dp"
            android:text="@string/made_with_bukuwarung_app"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/img_app_logo"
            app:layout_constraintBottom_toTopOf="@+id/tv_bukuwarung_url"
            />

        <TextView
            android:id="@+id/tv_bukuwarung_url"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:text="@string/bukuwarung_url"
            android:textColor="@color/blue_60"
            app:layout_constraintStart_toEndOf="@id/img_app_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_footer_one"
            tools:ignore="SmallSp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>