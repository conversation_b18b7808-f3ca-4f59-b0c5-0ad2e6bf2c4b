<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_home"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/colorGreyLight"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <androidx.viewpager2.widget.ViewPager2
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_80dp"
        android:id="@+id/vp_payment_banner"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tb_payment_banner"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_6dp"
        android:foregroundGravity="bottom"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@color/colorGreyLight"
        app:layout_constraintEnd_toEndOf="@id/vp_payment_banner"
        app:layout_constraintTop_toBottomOf="@id/vp_payment_banner"
        app:layout_constraintBottom_toBottomOf="parent"
        app:tabBackground="@drawable/selector_banner"
        app:tabGravity="center"
        app:tabPaddingEnd="@dimen/_8dp"
        app:tabPaddingStart="@dimen/_8dp"
        app:tabIndicatorHeight="0dp"
        android:layout_marginBottom="@dimen/_12dp"
        app:tabSelectedTextColor="@android:color/transparent"
        android:layout_gravity="bottom"/>
</androidx.constraintlayout.widget.ConstraintLayout>