<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- Appbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:theme="@style/ToolbarTheme">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/closeBtn"
                    android:layout_width="28dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center"
                    android:layout_marginLeft="35dp"
                    android:layout_marginRight="35dp"
                    android:layout_toLeftOf="@+id/saveBtn"
                    android:layout_toRightOf="@id/closeBtn"
                    android:alpha="1"
                    android:fontFamily="@font/roboto"
                    android:text="Detail Transaksi"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/editBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_toStartOf="@id/deleteBtn"
                    android:drawableTop="@drawable/ic_edit"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/edit"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:tint="@color/white"
                    android:visibility="visible"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/deleteBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:drawableTop="@drawable/delete_red"
                    android:drawablePadding="@dimen/_0dp"
                    android:gravity="center"
                    android:paddingStart="@dimen/_8dp"
                    android:paddingTop="@dimen/_8dp"
                    android:paddingEnd="@dimen/_8dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="visible"
                    tools:ignore="SmallSp" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:id="@+id/notFoundContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/app_bar"
        android:background="@color/white"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/transaction_not_found"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/loadingContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/app_bar"
        android:background="@color/white"
        android:paddingStart="@dimen/_16dp"
        android:paddingEnd="@dimen/_16dp"
        android:visibility="visible">

        <ProgressBar
            android:id="@+id/loadingBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:progressTint="@color/colorPrimary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/loadingBar"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/_16dp"
            android:text="Mohon Tunggu..."
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold" />

    </RelativeLayout>


    <TextView
        android:id="@+id/tv_show_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_20dp"
        android:textSize="@dimen/text_14sp"
        android:textColor="@color/black_80"
        android:background="@color/white"
        android:text="@string/show_details"
        android:layout_below="@id/app_bar"
        app:drawableRightCompat="@drawable/ic_right_arrow" />


    <ScrollView
        android:id="@+id/mainContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/container_btn"
        android:layout_below="@+id/tv_show_details"
        android:animateLayoutChanges="true"
        android:fillViewport="true"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="@color/black_5"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/subMainContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_margin="@dimen/_16dp"
                android:animateLayoutChanges="true" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/switch_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:paddingVertical="@dimen/_8dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:background="@drawable/white_background_radius_8">

                <TextView
                    android:id="@+id/tv_tooltip"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:text="@string/show_customer_trx_detail"
                    android:textColor="@color/black_60" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:thumbTint="@color/transaction_detail_switch_thumb"
                    app:trackTint="@color/transaction_detail_switch_track" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/anchor_tooltip"
                android:layout_width="@dimen/_2dp"
                android:layout_height="@dimen/_2dp"
                android:layout_marginStart="@dimen/_30dp" />

            <ProgressBar
                android:id="@+id/payment_info_loader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="56dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/payment_info_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:paddingTop="56dp"
                android:text="@string/label_payment_customer_error"
                android:textAlignment="center"
                android:textColor="@color/black_80"
                android:textSize="18sp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

    </ScrollView>

    <!-- Buttons -->
    <LinearLayout
        android:id="@+id/container_btn"
        android:layout_width="match_parent"
        android:layout_height="130dp"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_vertical"
        android:background="@color/white"
        android:gravity="bottom"
        android:orientation="horizontal">

        <include
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/bottom_container"
            layout="@layout/bottom_share_invoice_layout"/>

    </LinearLayout>

</RelativeLayout>
