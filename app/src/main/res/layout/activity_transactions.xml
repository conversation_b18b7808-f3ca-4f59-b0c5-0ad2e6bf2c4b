<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="@color/colorPrimary"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <RelativeLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:id="@+id/customerProfileLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?android:attr/selectableItemBackground">

                <ImageView
                    android:id="@+id/back_btn"
                    android:layout_width="24dp"
                    android:layout_height="40dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerInParent="true"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginRight="12dp"
                    android:src="@drawable/ic_back"
                    app:tint="@color/white" />

                <LinearLayout
                    android:id="@+id/customerIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="12dp"
                    android:layout_toRightOf="@id/back_btn"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/customerPic"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/firstLetter"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/oval_0"
                        android:fontFamily="@font/roboto"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:textSize="24sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/customer_name_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="4dp"
                    android:layout_toRightOf="@+id/customerIcon"
                    android:background="?attr/selectableItemBackgroundBorderless">

                    <TextView
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/roboto"
                        android:maxLines="1"
                        android:textColor="#ffffff"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/name"
                        android:layout_alignLeft="@+id/name"
                        android:ellipsize="end"
                        android:fontFamily="@font/roboto"
                        android:maxLines="1"
                        android:textColor="#ffffff"
                        android:textSize="12sp"
                        android:textStyle="normal"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/btnShowTutorial"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/ic_baseline_help_outline_white"
                        android:visibility="invisible" />
                </RelativeLayout>

            </RelativeLayout>
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/transactionButtonLayout"
        android:layout_below="@id/toolbar">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@id/app_bar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/toolbar"
            android:background="@color/colorPrimary"
            app:expanded="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                app:contentScrim="@color/colorPrimary"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <LinearLayout
                    android:id="@+id/customer_reminder_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#F9F8FF"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="6dp">

                        <LinearLayout
                            android:id="@+id/card_view_top"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:elevation="2dp"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:id="@+id/lunaskan_layout_tooltip"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/balanceStatus"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentLeft="true"
                                        android:layout_marginTop="@dimen/_16dp"
                                        android:layout_marginStart="@dimen/_16dp"
                                        android:fontFamily="@font/roboto"
                                        android:text="@string/money_owed_to"
                                        android:textColor="@color/heading_text"
                                        android:textSize="15sp"
                                        app:autoSizeMaxTextSize="16sp"
                                        app:autoSizeMinTextSize="10sp"
                                        app:autoSizeStepGranularity="2sp"
                                        app:autoSizeTextType="uniform" />

                                    <TextView
                                        android:id="@+id/customerBalance"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentStart="true"
                                        android:layout_below="@+id/balanceStatus"
                                        android:layout_marginTop="4dp"
                                        android:layout_marginStart="@dimen/_16dp"
                                        android:fontFamily="@font/roboto"
                                        android:maxLines="1"
                                        android:textColor="@color/out_red"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        app:autoSizeTextType="uniform"
                                        tools:text="0" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/lunaskanBtn"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentRight="true"
                                    android:layout_marginTop="26dp"
                                    android:layout_marginEnd="@dimen/_16dp"
                                    android:layout_marginBottom="12dp"
                                    android:fontFamily="@font/roboto"
                                    android:letterSpacing="0.05"
                                    android:text="@string/lunaskan"
                                    android:textAllCaps="false"
                                    android:textColor="@color/cta_text_button"
                                    android:textSize="12sp"
                                    android:textStyle="bold"
                                    app:backgroundTint="@color/buku_CTA" />

                                    <TextView
                                        android:id="@+id/lunas_label"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_below="@+id/customerBalance"
                                        android:layout_alignParentRight="true"
                                        android:layout_gravity="right"
                                        android:layout_marginLeft="16dp"
                                        android:layout_marginTop="-16dp"
                                        android:layout_marginRight="20dp"
                                        android:layout_marginBottom="@dimen/_8dp"
                                        android:fontFamily="@font/roboto"
                                        android:maxLines="1"
                                        android:text="@string/filter_nil"
                                        android:textColor="@color/in_green"
                                        android:textSize="15sp"
                                        android:textStyle="bold"
                                        android:visibility="gone"
                                        app:autoSizeMaxTextSize="24sp"
                                        app:autoSizeMinTextSize="12sp"
                                        app:autoSizeStepGranularity="2sp"
                                        app:autoSizeTextType="uniform" />
                                </RelativeLayout>


                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#eeeeee" />

                            <RelativeLayout
                                android:id="@+id/setDueDateArea"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="?attr/selectableItemBackground"
                                android:paddingLeft="@dimen/_16dp"
                                android:paddingRight="@dimen/_16dp">

                                <ImageView
                                    android:id="@+id/dueDateIcon"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_centerVertical="true"
                                    android:layout_marginTop="@dimen/_16dp"
                                    android:layout_marginBottom="@dimen/_16dp"
                                    android:src="@drawable/ic_calendar_raw" />

                                <TextView
                                    android:id="@+id/paymentReminderDate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_marginStart="@dimen/_16dp"
                                    android:layout_toEndOf="@+id/dueDateIcon"
                                    android:fontFamily="@font/roboto"
                                    android:text="@string/set_due_date"
                                    android:textColor="@color/heading_text"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:src="@drawable/ic_chevron_right"
                                    app:tint="@color/colorPrimary" />
                            </RelativeLayout>

                        </LinearLayout>


                    </androidx.cardview.widget.CardView>

                    <!--New menu-->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_8dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:layout_marginBottom="@dimen/_16dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="6dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvMenuLaporan"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:background="?attr/selectableItemBackground"
                                android:drawablePadding="8dp"
                                android:paddingTop="@dimen/_8dp"
                                android:paddingBottom="@dimen/_8dp"
                                android:text="@string/tab_reports_label"
                                android:textAlignment="center"
                                android:textColor="@color/black_80"
                                android:textSize="12sp"
                                app:drawableTint="@color/black_60"
                                app:drawableTopCompat="@drawable/ic_feather_download" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/layoutPayment"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:background="?android:attr/selectableItemBackground">

                                <ImageView
                                    android:id="@+id/imgWallet"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    app:srcCompat="@drawable/ic_wallet"
                                    app:layout_constraintBottom_toTopOf="@id/tvMenuPayment"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:id="@+id/free_txt"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|top"
                                    android:background="@drawable/bg_text_badge"
                                    android:fontFamily="@font/roboto"
                                    android:paddingStart="4dp"
                                    android:paddingTop="1dp"
                                    android:paddingEnd="4dp"
                                    android:paddingBottom="1dp"
                                    android:text="@string/free"
                                    android:textColor="@color/black_80"
                                    android:textSize="12sp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="@id/imgWallet"
                                    app:layout_constraintStart_toEndOf="@id/imgWallet"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintVertical_bias="0.08" />

                                <TextView
                                    android:id="@+id/tvMenuPayment"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="bottom"
                                    android:layout_marginBottom="@dimen/_8dp"
                                    android:gravity="center"
                                    android:text="@string/label_payment_only"
                                    android:textColor="@color/black_80"
                                    android:textSize="12sp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent" />

                            </androidx.constraintlayout.widget.ConstraintLayout>


                            <LinearLayout
                                android:id="@+id/reminderArea"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2">

                                <TextView
                                    android:id="@+id/tvMenuWhatsApp"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:background="?android:attr/selectableItemBackground"
                                    android:drawablePadding="8dp"
                                    android:paddingTop="@dimen/_8dp"
                                    android:paddingBottom="@dimen/_8dp"
                                    android:text="@string/remind"
                                    android:textAlignment="center"
                                    android:textColor="@color/black_80"
                                    android:textSize="12sp"
                                    app:drawableTint="@color/black_60"
                                    app:drawableTopCompat="@drawable/ic_outline_whatsapp" />


                                <TextView
                                    android:id="@+id/tvMenuShare"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:background="?android:attr/selectableItemBackground"
                                    android:drawablePadding="8dp"
                                    android:paddingTop="@dimen/_8dp"
                                    android:paddingBottom="@dimen/_8dp"
                                    android:text="@string/remind_via_other"
                                    android:textAlignment="center"
                                    android:textColor="@color/black_80"
                                    android:textSize="12sp"
                                    app:drawableTint="@color/black_60"
                                    app:drawableTopCompat="@drawable/ic_mdi_share" />
                            </LinearLayout>
                        </LinearLayout>

                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/summaryView"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:layout_marginBottom="12dp"
                    android:background="@color/white"
                    android:elevation="1dip"
                    android:visibility="gone"
                    app:cardCornerRadius="4.0dp">

                    <RelativeLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:id="@+id/summaryViewTxt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_alignParentTop="true"
                            android:layout_marginStart="20.0dip"
                            android:layout_marginEnd="16.0dip"
                            android:orientation="horizontal"
                            android:weightSum="14">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16.0dip"
                                android:layout_marginBottom="12.0dip"
                                android:layout_weight="7"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/creditTotal"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="7"
                                    android:fontFamily="@font/roboto"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="0"
                                    android:textColor="@color/in_green"
                                    android:textSize="16.0sp"
                                    android:textStyle="bold"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeMinTextSize="12sp"
                                    app:autoSizeStepGranularity="1sp"
                                    app:autoSizeTextType="uniform" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="7"
                                    android:fontFamily="@font/roboto"
                                    android:gravity="center"
                                    android:lineHeight="18dp"
                                    android:maxLines="1"
                                    android:text="@string/credit_summary_text"
                                    android:textColor="@color/in_green"
                                    android:textSize="14.0sp" />
                            </LinearLayout>

                            <TextView
                                android:layout_width="1dp"
                                android:layout_height="match_parent"
                                android:background="@color/vrDivider" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16.0dip"
                                android:layout_marginBottom="12.0dip"
                                android:layout_weight="7"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/debitTotal"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="7"
                                    android:fontFamily="@font/roboto"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="0"
                                    android:textColor="@color/out_red"
                                    android:textSize="16.0sp"
                                    android:textStyle="bold"
                                    app:autoSizeMaxTextSize="16sp"
                                    app:autoSizeMinTextSize="12sp"
                                    app:autoSizeStepGranularity="1sp"
                                    app:autoSizeTextType="uniform" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="7"
                                    android:fontFamily="@font/roboto"
                                    android:gravity="center"
                                    android:lineHeight="18dp"
                                    android:maxLines="1"
                                    android:text="@string/debit_summary_text"
                                    android:textColor="@color/out_red"
                                    android:textSize="14.0sp" />
                            </LinearLayout>
                        </LinearLayout>

                        <RelativeLayout
                            android:id="@+id/summary_btn"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/summaryViewTxt"
                            android:paddingTop="8dp"
                            android:paddingBottom="12dp">

                            <ImageView
                                android:id="@+id/report_icon"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_alignParentLeft="true"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="@dimen/_16dp"
                                android:gravity="center"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:src="@drawable/ic_laporan_blue"
                                android:tint="@color/colorPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="@dimen/_16dp"
                                android:layout_toRightOf="@id/report_icon"
                                android:text="@string/credit_debit_report"
                                android:textColor="@color/heading_text"
                                android:textSize="14dp"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/rightArrow"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_alignParentEnd="true"
                                android:layout_alignParentRight="true"
                                android:layout_centerVertical="true"
                                android:layout_marginEnd="@dimen/_16dp"
                                android:gravity="center"
                                android:src="@drawable/ic_chevron_right"
                                android:tint="@color/colorPrimary" />
                        </RelativeLayout>
                    </RelativeLayout>
                </com.google.android.material.card.MaterialCardView>
            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <TextView
                android:id="@+id/topSection"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/app_bar"
                android:background="@color/section_end" />

            <TextView
                android:id="@+id/topSectionSplit"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/topSection"
                android:background="@color/section_end" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/transactionRV"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/transactionButtonLayout"
                android:layout_below="@+id/topSectionSplit"
                android:elevation="3dp" />

        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/transactionButtonLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:elevation="@dimen/stdElevation"
        android:orientation="vertical">

        <TextView
            android:id="@+id/section"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_above="@+id/bottom"
            android:layout_marginBottom="16dp"
            android:background="@color/section_end" />

        <LinearLayout
            android:id="@+id/bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp"
            android:layout_alignParentBottom="true"
            >


            <TextView
                android:id="@+id/debitBtn"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:fontFamily="@font/roboto"
                android:text="@string/debitBtn"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:layout_weight="1"
                android:background="@drawable/type_exp_selected_bg"
                android:gravity="center"
                />

            <TextView
                android:id="@+id/creditBtn"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginStart="8dp"
                android:text="@string/creditBtn"
                android:textStyle="bold"
                android:fontFamily="@font/roboto"
                android:textColor="@color/white"
                android:layout_weight="1"
                android:gravity="center"
                android:background="@drawable/type_inc_selected_bg"
                />
        </LinearLayout>
    </LinearLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/layoutWhatsAppPreview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="invisible"
        app:cardCornerRadius="8dp"
        app:cardElevation="@dimen/_8dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/shop_detail_header"
                android:layout_marginStart="12dp"
                android:layout_marginTop="@dimen/_16dp">

                <ImageView
                    android:id="@+id/shop_icon"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_alignParentStart="true"
                    android:background="@drawable/circle_with_border"
                    android:padding="8dp"
                    android:src="@drawable/logo_white_ticker" />

                <TextView
                    android:id="@+id/shopName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="3dp"
                    android:layout_toEndOf="@+id/shop_icon"
                    android:fontFamily="@font/roboto"
                    android:maxLines="1"
                    android:text="StoresName"
                    android:textColor="@color/heading_text"
                    android:textSize="16.0sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/shopPhone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/shopName"
                    android:layout_marginStart="12dp"
                    android:layout_toEndOf="@+id/shop_icon"
                    android:fontFamily="@font/roboto"
                    android:text="0812-3456-789"
                    android:textColor="#666666"
                    android:textSize="14.0sp" />
            </RelativeLayout>


            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16.0dip"
                android:layout_marginTop="12dp"
                android:layout_marginRight="16.0dip"
                android:layout_marginBottom="12.0dip"
                android:background="@drawable/round_stroke_light_background"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingLeft="8.0dip"
                android:paddingTop="19.0dip"
                android:paddingRight="8.0dip"
                android:paddingBottom="16.0dip">

                <TextView
                    android:id="@+id/reminderCardHeader"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="9.799988sp"
                    android:text="@string/payment_reminder"
                    android:textAllCaps="true"
                    android:textColor="#222222"
                    android:textSize="15.0sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/dueAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16.0dip"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:maxLines="1"
                    android:paddingLeft="8.0dip"
                    android:paddingRight="8.0dip"
                    tools:text="Rp 92300"
                    android:textColor="@color/out_red"
                    android:textSize="34.0sp"
                    android:textStyle="bold"
                    app:autoSizeMaxTextSize="32.0sp"
                    app:autoSizeMinTextSize="14.0sp"
                    app:autoSizeStepGranularity="2.0sp"
                    app:autoSizeTextType="uniform" />

                <TextView
                    android:id="@+id/reminderExplain"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/round_corner_blue_rectangle"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="5sp"
                    android:paddingLeft="19.0dip"
                    android:paddingTop="7.0dip"
                    android:paddingRight="19.0dip"
                    android:paddingBottom="7.0dip"
                    android:text="You owe SGD 2300 as on 5:00PM, 19th Nov, 2018"
                    android:textColor="@color/colorPrimary"
                    android:textSize="11sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_verified" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="@font/roboto"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="4sp"
                    android:text="@string/verifiedbyapp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</RelativeLayout>