<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="top"
    android:paddingTop="20dp"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_marginRight="@dimen/_16dp"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:gravity="center"
        android:id="@+id/customerWalkThruBtn"
        android:background="@drawable/rounded_dark_rect"
        android:orientation="horizontal"
        android:elevation="6dp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:layout_gravity="center"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/learn_utang_feature"
            android:fontFamily="@font/roboto"
            android:textColor="#ffffff"
            android:layout_marginLeft="12dp"
            android:id="@+id/customerTutorText"
            android:textSize="14sp"
            android:layout_toStartOf="@id/see"
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true"/>

        <TextView
            android:id="@+id/see"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_width="wrap_content"
            android:layout_marginRight="12dp"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_height="wrap_content"
            android:textColor="@color/main_btn"
            android:text="@string/learn"></TextView>

    </RelativeLayout>


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:id="@+id/no_trans"
        android:layout_centerInParent="true"
        android:backgroundTint="@color/white"
        app:cardElevation="@dimen/_8dp"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        app:cardCornerRadius="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/utang_social_message"
            android:fontFamily="@font/roboto"
            android:textColor="@color/black_80"
            android:layout_below="@id/no_trans"
            android:textSize="14sp"
            android:textAlignment="textStart"
            android:id="@+id/social_message"
            android:layout_marginTop="16dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_centerInParent="true"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/userImage"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="4dp"
                    app:srcCompat="@drawable/image_profile_1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_toRightOf="@id/userImage"
                    android:layout_alignParentTop="true"
                    android:text="Bu Ance, pemilik warung"
                    android:textSize="12sp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="12dp"
                    android:textColor="@color/black_66"
                    android:lineSpacingExtra="6sp"
                    android:id="@+id/userName"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_toRightOf="@id/userImage"
                    android:layout_below="@+id/userName"
                    android:layout_marginLeft="10dp"
                    android:lineSpacingExtra="6sp"
                    android:layout_marginTop="2dp"
                    android:text="Pengguna aktif sejak Januari 2020"
                    android:textSize="12sp"
                    android:id="@+id/messageDate"
                    android:textColor="@color/colorPrimary"
                    android:layout_height="wrap_content"/>
            </RelativeLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_customer_yet"
        android:fontFamily="@font/roboto"
        android:textColor="@color/heading_text"
        android:layout_below="@id/no_trans"
        android:textSize="16sp"
        android:id="@+id/no_trans_header"
        android:layout_marginTop="20dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:gravity="center"
        android:layout_centerInParent="true"/>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:gravity="center"
        android:layout_centerHorizontal="true"
        android:id="@+id/customerHelpBtn"
        android:background="@drawable/rounded_light_rect"
        android:orientation="horizontal"
        android:elevation="6dp"
        android:paddingTop="8dp"
        android:visibility="gone"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_below="@+id/no_trans_header"
        android:paddingBottom="@dimen/_8dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:layout_gravity="center"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_centerVertical="true"
            android:tint="@color/in_green"
            android:id="@+id/wa_icon"
            android:src="@drawable/ic_whatsapp_new"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/need_help"
            android:fontFamily="@font/roboto"
            android:textColor="@color/in_green"
            android:layout_marginLeft="7dp"
            android:id="@+id/customerHelpText"
            android:textSize="12dp"
            android:layout_toRightOf="@+id/wa_icon"
            android:layout_centerVertical="true"/>

    </RelativeLayout>

</RelativeLayout>
