<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="top"
    android:paddingTop="@dimen/_20dp">

    <RelativeLayout
        android:id="@+id/cashWalkThruBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginRight="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/rounded_dark_rect"
        android:elevation="@dimen/_6dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/_8dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingRight="@dimen/_8dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <TextView
            android:id="@+id/customerTutorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_toStartOf="@id/seeBtn"
            android:fontFamily="@font/roboto"
            android:text="@string/learn_cash_title"
            android:textColor="@color/white"
            android:textSize="@dimen/text_14sp" />

        <TextView
            android:id="@+id/seeBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/_12dp"
            android:text="@string/learn"
            android:textColor="@color/main_btn"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_video_tutorial_snack_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginRight="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/rounded_dark_blue_rect"
        android:elevation="@dimen/_6dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/_8dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingRight="@dimen/_8dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/img_youtube_play_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:src="@drawable/ic_youtube_play_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_video_tutorial"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:fontFamily="@font/roboto"
            android:text="@string/video_tutorial_record_transaction"
            android:textColor="@color/white"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintStart_toEndOf="@id/img_youtube_play_btn"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_open_video_tutorial"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/_12dp"
            android:text="@string/watch_video"
            android:textColor="@color/white"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_coachmark_video_snack_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/_16dp"
        android:layout_marginRight="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/rounded_light_blue_rect"
        android:elevation="@dimen/_6dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="@dimen/_12dp"
        android:paddingBottom="@dimen/_12dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_coachmark_video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:fontFamily="@font/roboto"
            android:text="@string/find_how_to_record_transactions"
            android:textColor="@color/black_80"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            app:layout_constraintTop_toBottomOf="@id/tv_coachmark_video"
            android:orientation="horizontal"
            android:weightSum="2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_coachmark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="@dimen/_12dp"
                android:backgroundTint="#E9F5FF"
                android:text="@string/written_guide"
                android:textAllCaps="false"
                android:textColor="@color/black_60"
                android:textSize="@dimen/text_12sp"
                android:letterSpacing="0"
                android:textStyle="bold"
                app:strokeColor="#75B3DE"
                app:strokeWidth="@dimen/_1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_video_tutorial"
                android:layout_width="wrap_content"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="@dimen/_12dp"
                android:backgroundTint="#E9F5FF"
                android:letterSpacing="0"
                android:drawableLeft="@drawable/ic_youtube_play_btn_dark"
                android:text="@string/watch_tutorial"
                android:textAllCaps="false"
                android:textColor="@color/black_60"
                android:textSize="@dimen/text_12sp"
                android:layout_marginEnd="@dimen/_12dp"
                android:textStyle="bold"
                app:strokeColor="#75B3DE"
                app:strokeWidth="@dimen/_1dp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_snack_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="cashWalkThruBtn, cl_video_tutorial_snack_bar, cl_coachmark_video_snack_bar"
        app:layout_constraintTop_toBottomOf="@id/cashWalkThruBtn" />

    <androidx.cardview.widget.CardView
        android:id="@+id/no_trans"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_40dp"
        android:layout_marginTop="@dimen/_36dp"
        android:layout_marginEnd="@dimen/_40dp"
        android:backgroundTint="@color/white"
        app:cardCornerRadius="@dimen/_8dp"
        app:cardElevation="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/barrier_snack_bar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/social_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_12dp"
                android:fontFamily="@font/roboto"
                android:text="@string/transaksi_social_message"
                android:textAlignment="textStart"
                android:textColor="@color/black_80"
                android:textSize="@dimen/text_14sp" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/userImage"
                    android:layout_width="@dimen/_35dp"
                    android:layout_height="@dimen/_35dp"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_marginBottom="@dimen/_4dp"
                    app:srcCompat="@drawable/img_profile_2" />

                <TextView
                    android:id="@+id/userName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_toEndOf="@id/userImage"
                    android:lineSpacingExtra="6sp"
                    android:text="Pak Yudhistira, pemilik toko grosir"
                    android:textColor="@color/black_66"
                    android:textSize="@dimen/text_12sp" />

                <TextView
                    android:id="@+id/messageDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/userName"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginTop="@dimen/_2dp"
                    android:layout_toEndOf="@id/userImage"
                    android:lineSpacingExtra="6sp"
                    android:text="Pengguna aktif sejak Februari 2020"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/text_12sp" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/no_trans_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/no_transaction_yet_basic"
        android:textColor="@color/heading_text"
        android:textSize="@dimen/text_16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/no_trans" />

    <RelativeLayout
        android:id="@+id/cashHelpBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginTop="34dp"
        android:background="@drawable/rounded_light_rect"
        android:elevation="@dimen/_6dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/_12dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingRight="@dimen/_12dp"
        android:paddingBottom="@dimen/_8dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/no_trans_header"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/wa_icon"
            android:layout_width="@dimen/_14dp"
            android:layout_height="@dimen/_14dp"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_whatsapp_new"
            app:tint="@color/in_green" />

        <TextView
            android:id="@+id/customerHelpText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="7dp"
            android:layout_toEndOf="@+id/wa_icon"
            android:fontFamily="@font/roboto"
            android:text="@string/need_help"
            android:textColor="@color/in_green"
            android:textSize="@dimen/text_12sp" />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
