<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="top|center_horizontal"
    android:paddingStart="56dp"
    android:paddingEnd="56dp"
    android:paddingBottom="@dimen/_16dp"
    android:paddingTop="72dp">

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_calendar"
        android:tint="#69BDFD" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Belum ada jadwal penagihan"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:textColor="@color/black_66"
        android:layout_marginTop="@dimen/_16dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Buat catatan utang piutang dulu kemudian atur tanggal penagihan"
        android:textSize="16sp"
        android:textAlignment="center"
        android:textColor="@color/black_66"
        android:layout_marginTop="@dimen/_8dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/createBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:cornerRadius="24dp"
        android:layout_marginBottom="24dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:background="@color/buku_CTA"
        android:backgroundTint="@color/buku_CTA"
        android:textStyle="bold"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="6sp"
        android:textColor="@color/white"
        app:icon="@mipmap/ic_plus_white_24dp"
        app:iconTint="@color/white"
        style="@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon"
        android:text="Utang Piutang" />

</LinearLayout>