<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/setupScreen"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="24dp"
    android:orientation="vertical"
    android:paddingLeft="20dp"
    android:background="@color/white"
    android:paddingRight="20dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:layout_marginTop="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="Info kartu nama belum lengkap"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:layout_marginTop="16dp"
        android:alpha="0.7"
        android:lineSpacingExtra="1dp"
        android:text="Lengkapi informasi pada kartu nama sebelum bisa kamu bagikan"
        android:textColor="@color/black"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:weightSum="2"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/ok"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:gravity="center"
            android:lineSpacingExtra="1.9sp"
            android:minHeight="52dp"
            android:layout_weight="1"
            android:text="OK"
            app:cornerRadius="4dp"
            android:textAllCaps="true"
            android:backgroundTint="@color/colorPrimary"
            android:textColor="@color/white"
            android:textSize="14.1sp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>
