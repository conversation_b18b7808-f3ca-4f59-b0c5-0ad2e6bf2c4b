<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    android:fitsSystemWindows="true">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="fill_parent"
        android:layout_alignParentTop="true"
        android:layout_height="wrap_content"
        android:theme="@style/ActionBarAppTheme">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="fill_parent"
            android:layout_height="56dp"
            app:contentInsetLeft="0.0dip"
            app:contentInsetStart="0.0dip"
            app:contentInsetStartWithNavigation="0.0dip">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:id="@+id/back_btn"
                android:layout_marginLeft="@dimen/_16dp"
                android:src="@drawable/ic_back"
                />

            <TextView
                android:id="@+id/screen_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_reports_label"
                android:fontFamily="@font/roboto"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:layout_marginLeft="24dp"
                android:textSize="18.0dip" />
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/appbar"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@id/app_bar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="#F8F9FE"
            app:expanded="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="#F8F9FE"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:layout_height="wrap_content">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/summaryView"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/_16dp"
                        android:background="@color/white"
                        android:elevation="1dip"
                        app:cardCornerRadius="4.0dp">

                        <RelativeLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:id="@+id/summaryViewTxt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_alignParentTop="true"
                                android:orientation="horizontal"
                                android:weightSum="6">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="14.0dip"
                                    android:layout_marginBottom="12.0dip"
                                    android:layout_weight="3"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/creditTotal"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_weight="7"
                                        android:fontFamily="@font/roboto"
                                        android:gravity="center"
                                        android:maxLines="1"
                                        android:text="0"
                                        tools:text="Rp 2.000.000"
                                        app:autoSizeMaxTextSize="16sp"
                                        app:autoSizeMinTextSize="12sp"
                                        app:autoSizeStepGranularity="1sp"
                                        app:autoSizeTextType="uniform"
                                        android:textColor="@color/in_green"
                                        android:textSize="16.0sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_weight="7"
                                        android:fontFamily="@font/roboto"
                                        android:gravity="center"
                                        android:lineHeight="18dp"
                                        android:maxLines="1"
                                        android:id="@+id/credit_caption"
                                        android:text="@string/credit_summary_text"
                                        android:textColor="@color/in_green"
                                        android:textSize="14.0sp" />
                                </LinearLayout>

                                <TextView
                                    android:layout_width="1dp"
                                    android:layout_height="match_parent"
                                    android:background="@color/vrDivider" />

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="14.0dip"
                                    android:layout_marginBottom="12.0dip"
                                    android:layout_weight="3"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/debitTotal"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_weight="7"
                                        android:fontFamily="@font/roboto"
                                        android:gravity="center"
                                        android:maxLines="1"
                                        app:autoSizeMaxTextSize="16sp"
                                        app:autoSizeMinTextSize="12sp"
                                        app:autoSizeStepGranularity="1sp"
                                        app:autoSizeTextType="uniform"
                                        android:text="0"
                                        tools:text="Rp 400.000"
                                        android:textColor="@color/out_red"
                                        android:textSize="16.0sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_weight="7"
                                        android:fontFamily="@font/roboto"
                                        android:gravity="center"
                                        android:lineHeight="18dp"
                                        android:id="@+id/debit_caption"
                                        android:maxLines="1"
                                        android:text="@string/debit_summary_text"
                                        android:textColor="@color/out_red"
                                        android:textSize="14.0sp" />
                                </LinearLayout>
                            </LinearLayout>

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/summaryViewTxt"
                                android:layout_marginTop="16dp"
                                android:id="@+id/report_summary"
                                android:layout_marginBottom="16dp">
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:layout_centerInParent="true"
                                    android:gravity="center">
                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_alignParentLeft="true"
                                        android:id="@+id/net_total_label"
                                        android:fontFamily="@font/roboto"
                                        android:layout_marginRight="@dimen/_16dp"
                                        app:autoSizeMaxTextSize="14sp"
                                        app:autoSizeMinTextSize="12sp"
                                        app:autoSizeStepGranularity="1sp"
                                        app:autoSizeTextType="uniform"
                                        android:maxLines="1"
                                        android:text="Total"
                                        tools:text="Untung"
                                        android:textStyle="bold"
                                        android:textColor="@color/out_red"
                                        android:textSize="16.0sp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:gravity="left"
                                        android:textStyle="bold"
                                        android:id="@+id/net_total"
                                        android:fontFamily="@font/roboto"
                                        android:maxLines="1"
                                        android:text="Rp. 0"
                                        app:autoSizeMaxTextSize="14sp"
                                        app:autoSizeMinTextSize="12sp"
                                        app:autoSizeStepGranularity="1sp"
                                        app:autoSizeTextType="uniform"
                                        android:textColor="@color/out_red"
                                        android:textSize="16.0sp" />
                                </LinearLayout>
                            </RelativeLayout>
                        </RelativeLayout>
                    </com.google.android.material.card.MaterialCardView>
                    <include layout="@layout/report_date_filter" />
                </LinearLayout>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>
        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@color/white"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <include layout="@layout/report_header_view_item"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/transactionReportRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-4dp"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />
        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/downloadPdf"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/cta_button_text"
        app:iconTint="@color/cta_button_text"
        android:text="@string/download_report"
        android:textStyle="bold"
        app:backgroundTint="@color/buku_CTA"
        app:icon="@drawable/ic_download" />
</RelativeLayout>
