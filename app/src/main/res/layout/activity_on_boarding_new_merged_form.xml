<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">


    <androidx.appcompat.widget.Toolbar
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize">

<!--        <LinearLayout-->
<!--            android:id="@+id/step_container"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:gravity="center"-->
<!--            android:orientation="horizontal" />-->

    </androidx.appcompat.widget.Toolbar>


    <com.bukuwarung.baseui.DefaultViewPager
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        />


    <androidx.core.widget.NestedScrollView
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintBottom_toTopOf="@+id/proceed_button_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:background="@drawable/bg_top_rounded_corner"
        android:paddingBottom="@dimen/_14dp"
        android:id="@+id/scroll_view"
        android:fillViewport="true"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/business_name_form_fragment"
                />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/business_category_form_fragment"
                />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/business_goal_form_fragment"
                />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/business_past_usage_form_fragment"
                />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/white"
        android:id="@+id/proceed_button_container"
        >

        <com.google.android.material.button.MaterialButton

            android:id="@+id/proceed_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            style="@style/ButtonFill"
            android:text="@string/next"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginVertical="@dimen/_8dp"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
