<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/white_background_radius_8"
    android:layout_gravity="top"
    android:animateLayoutChanges="true"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:id="@+id/layout_store_detail"
        layout="@layout/layout_store_detail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <include
        android:id="@+id/layout_header_invoice"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        layout="@layout/layout_header_invoice"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_store_detail"/>

    <include
        android:id="@+id/layout_payment_detail"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:visibility="gone"
        layout="@layout/layout_transaction_status"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_header_invoice"/>

    <include
        android:id="@+id/layout_body_invoice"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        layout="@layout/layout_body_invoice"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_payment_detail"/>

    <include
        android:id="@+id/layout_footer_invoice"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        layout="@layout/layout_footer_invoice"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_body_invoice"/>

</androidx.constraintlayout.widget.ConstraintLayout>