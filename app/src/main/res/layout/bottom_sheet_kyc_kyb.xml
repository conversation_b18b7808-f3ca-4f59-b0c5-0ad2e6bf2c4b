<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_10dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingBottom="@dimen/_14dp">

    <View
        android:id="@+id/vw_close_bar"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:background="@drawable/background_filled_black10_border_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_top_banner"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_close_bar"
        tools:srcCompat="@drawable/ic_shop_warning" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_top_banner"
        tools:text="Yuk, verifikasi ke Akun Premium dan nikmati keuntungannya!" />

    <ScrollView
        android:id="@+id/sv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_20dp"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/tv_learn_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_first_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_first_message_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:srcCompat="@drawable/ic_styled_shield" />

                <TextView
                    android:id="@+id/tv_first_title"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_first_message_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Pembayaran jadi lebih aman dan nyaman" />

                <TextView
                    android:id="@+id/tv_first_message"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tv_first_title"
                    app:layout_constraintTop_toBottomOf="@+id/tv_first_title"
                    tools:text="Perlindungan akun yang lebih aman dengan inovasi teknologi biometrik terkini." />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_second_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_first_message">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_second_message_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:srcCompat="@drawable/ic_coins" />

                <TextView
                    android:id="@+id/tv_second_title"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_second_message_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Anti kehabisan modal usaha" />

                <TextView
                    android:id="@+id/tv_second_message"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tv_second_title"
                    app:layout_constraintTop_toBottomOf="@+id/tv_second_title"
                    tools:text="Akses mendapatkan pinjaman Solusi Modal Usaha hingga Rp50 juta." />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_third_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_second_message">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_third_message_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_purse" />

                <TextView
                    android:id="@+id/tv_third_title"
                    style="@style/SubHeading1"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_third_message_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Akses Isi Saldo" />

                <TextView
                    android:id="@+id/tv_third_message"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tv_third_title"
                    app:layout_constraintTop_toBottomOf="@+id/tv_third_title"
                    tools:text="Bisa dapet untung dari jualan Produk Digital seperti pulsa, token listrik, hingga tagihan dengan limit saldo bulanan s.d. Rp50 juta." />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_fourth_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_third_message">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_fourth_message_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_qr" />

                <TextView
                    android:id="@+id/tv_fourth_title"
                    style="@style/SubHeading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_fourth_message_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Limit saldo lebih besar" />

                <TextView
                    android:id="@+id/tv_fourth_message"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tv_fourth_title"
                    app:layout_constraintTop_toBottomOf="@+id/tv_fourth_title"
                    tools:text="Pakai saldo untuk jualan Produk Digital dengan limit bulanan s.d. Rp100 juta." />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <TextView
        android:id="@+id/tv_learn_more"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_22dp"
        android:layout_marginBottom="@dimen/_38dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center_vertical"
        android:text="@string/learn_more"
        android:textColor="@color/colorPrimary"
        app:drawableEndCompat="@drawable/ic_arrow_right_blue_circle_bg"
        app:layout_constraintBottom_toTopOf="@id/btn_later"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_later"
        style="@style/ButtonOutline.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:paddingStart="@dimen/_2dp"
        android:paddingEnd="@dimen/_2dp"
        android:text="@string/cancel_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_verify_now"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_verify_now"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:paddingStart="@dimen/_2dp"
        android:paddingEnd="@dimen/_2dp"
        android:text="@string/verify_now"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_later" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="Verfikasi Akun diproses maksimal 7 hari kerja. Setelah berhasil, kamu bisa gunakan fitur ini. Silakan cek status verifikasi secara berkala, ya." />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_understand"
        style="@style/ButtonFill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_38dp"
        android:text="@string/understand"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="@dimen/_6dp"
        android:padding="@dimen/_4dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/close" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gr_cta"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_learn_more, btn_later, btn_verify_now" />

</androidx.constraintlayout.widget.ConstraintLayout>
