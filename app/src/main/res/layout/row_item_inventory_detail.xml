<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="item"
            type="com.bukuwarung.activities.inventory.model.InventoryHistoryData" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:attr/selectableItemBackground"
        android:foreground="?android:attr/selectableItemBackground"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/dataLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingLeft="@dimen/_16dp"
            android:paddingTop="@dimen/_12dp"
            android:paddingRight="@dimen/_16dp"
            android:paddingBottom="@dimen/_12dp">

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="6sp"
                android:maxLines="1"
                android:paddingEnd="8dp"
                android:text="@{item.name}"
                android:textColor="#222222"
                android:textSize="15sp"
                android:textStyle="normal"
                tools:text="Siomay Bu Ani" />

            <TextView
                android:id="@+id/tv_transaction_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/name"
                android:layout_marginTop="@dimen/_4dp"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="6sp"
                android:maxLines="1"
                android:textColor="#8D8D8D"
                android:textSize="@dimen/text_12sp"
                android:textStyle="normal"
                tools:text="03d2sd4jr4" />

            <TextView
                android:id="@+id/dateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/name"
                android:layout_marginTop="4dp"
                android:layout_toRightOf="@id/tv_transaction_id"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="6sp"
                android:maxLines="1"
                android:paddingEnd="8dp"
                android:text="@{item.readableTimeStr}"
                android:textColor="#8D8D8D"
                android:textSize="@dimen/text_12sp"
                android:textStyle="normal"
                tools:text="Penjualan" />

            <TextView
                android:id="@+id/stok"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="6sp"
                android:maxLines="1"
                android:paddingEnd="8dp"
                android:text="@{item.stok}"
                android:textColor="#8D8D8D"
                android:textSize="13sp"
                android:textStyle="normal"
                tools:text="Stok" />

            <TextView
                android:id="@+id/stockValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/stok"
                android:layout_alignParentEnd="true"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/roboto"
                android:lineSpacingExtra="6sp"
                android:maxLines="1"
                android:paddingEnd="8dp"
                android:textColor="#222222"
                android:textSize="14sp"
                android:textStyle="normal"
                tools:text="150" />


            <TextView
                android:id="@+id/amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/name"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="end|center_vertical"
                android:lineSpacingExtra="6sp"
                android:maxLines="2"
                android:textSize="15sp"
                app:autoSizeMaxTextSize="16sp"
                app:autoSizeMinTextSize="14sp"
                app:autoSizeStepGranularity="1sp"
                app:autoSizeTextType="uniform"
                tools:text="+50" />

        </RelativeLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/new_divider" />

    </LinearLayout>

</layout>