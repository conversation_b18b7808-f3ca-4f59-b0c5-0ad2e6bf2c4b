<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorGreyLight"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/sl_loyalty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:weightSum="1">

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_new_shimmer"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:elevation="@dimen/_4dp"
                android:layout_margin="@dimen/_10dp"
                app:cardCornerRadius="@dimen/_10dp"
                android:layout_weight="0.9" >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" >

                    <include
                        android:id="@+id/layout_points_shimmer"
                        layout="@layout/layout_user_tier_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintWidth_percent="0.4"/>

                    <include
                        android:id="@+id/layout_membership_shimmer"
                        layout="@layout/layout_user_tier_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toEndOf="@id/layout_points_shimmer"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintWidth_percent="0.4"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </com.facebook.shimmer.ShimmerFrameLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_loyalty_points"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:elevation="@dimen/_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/_10dp"
        app:cardCornerRadius="@dimen/_10dp"
        app:layout_constraintWidth_percent="0.95">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/layout_membership_status"
                layout="@layout/layout_user_tier_item_new"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.5"
                android:stateListAnimator="@null" />

            <View
                android:id="@+id/divider"
                android:layout_width="@dimen/_1dp"
                android:layout_height="@dimen/_0dp"
                android:layout_marginVertical="@dimen/_8dp"
                android:background="@color/black_10"
                app:layout_constraintStart_toStartOf="@+id/layout_points"
                app:layout_constraintTop_toTopOf="@+id/layout_points"
                app:layout_constraintBottom_toBottomOf="@+id/layout_points"
                />

            <include
                android:id="@+id/layout_points"
                layout="@layout/layout_user_tier_item_new"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="@id/layout_membership_status"
                app:layout_constraintEnd_toStartOf="@id/img_enter"
                app:layout_constraintWidth_percent="0.5"
                android:stateListAnimator="@null" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_enter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_10dp"
                app:srcCompat="@mipmap/next_grey"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/layout_points"
                app:layout_constraintBottom_toBottomOf="@id/layout_points"
                android:stateListAnimator="@null"
                android:clickable="false"/>

            <View
                android:id="@+id/vw_home"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/layout_membership_status" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>