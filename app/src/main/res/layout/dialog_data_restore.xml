<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_height="match_parent"
    android:elevation="@dimen/_8dp"
    android:paddingTop="60dp"
    android:paddingBottom="60dp"
    android:background="@drawable/round_corner_white_picture_picker">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:visibility="gone"
        android:id="@+id/recovered_layout"
        android:orientation="vertical">
        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:alpha="1"
            android:tint="@color/in_green"
            android:layout_gravity="center"
            android:src="@drawable/ic_restored_big"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:layout_marginTop="@dimen/_16dp"
            android:textAlignment="center"
            android:fontFamily="@font/roboto"
            android:textColor="@color/heading_text"
            android:text="@string/restore_complete"/>

        <com.google.android.material.button.MaterialButton
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:id="@+id/completeBtn"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:textColor="@color/white"
            android:fontFamily="@font/roboto"
            android:textSize="16sp"
            android:layout_marginTop="80dp"
            android:text="@string/restore_complete_btn"
            app:backgroundTint="@color/buku_CTA"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:id="@+id/preparing_layout"
        android:orientation="vertical">
        <ProgressBar
            android:layout_margin="20dp"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:indeterminate="true"
            android:max="100"
            android:progress="0" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textAlignment="center"
            android:textColor="@color/heading_text"
            android:text="@string/preparing_setup"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:id="@+id/restoring_layout"
        android:visibility="gone"
        android:orientation="vertical">

        <ImageView
            android:layout_width="180dp"
            android:layout_height="120dp"
            android:alpha="1"
            android:layout_gravity="center"
            android:src="@drawable/ic_restoring"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:fontFamily="@font/roboto"
            android:textSize="16sp"
            android:layout_marginTop="@dimen/_16dp"
            android:textColor="@color/body_text"
            android:textStyle="bold"
            android:text="Cadangan data ditemukan"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:id="@+id/statusTv"
            android:fontFamily="@font/roboto"
            android:textColor="@color/body_text"
            android:textSize="14sp"
            android:layout_marginTop="@dimen/_4dp"
            android:text="Kami sedang pulihkan data kamu"/>

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_marginLeft="@dimen/_20dp"
            android:layout_marginRight="@dimen/_20dp"
            android:layout_marginTop="12dp"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:indeterminate="false"
            android:max="100"
            android:progress="0" />

        <LinearLayout
            android:layout_width="match_parent"
            android:background="@drawable/round_corner_info_message_yellow"
            android:layout_margin="@dimen/_24dp"
            android:padding="12dp"
            android:layout_height="wrap_content">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_mdi_info"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_8dp"
                android:textSize="14sp"
                android:fontFamily="@font/roboto"
                android:id="@+id/warningTv"
                android:textColor="@color/heading_text"
                android:text="@string/restoring_dont_kill_app"/>
        </LinearLayout>
    </LinearLayout>


</RelativeLayout>