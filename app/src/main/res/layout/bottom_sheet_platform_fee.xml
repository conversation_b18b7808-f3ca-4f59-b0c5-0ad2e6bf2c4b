<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_10dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingBottom="@dimen/_20dp">

    <View
        android:id="@+id/vw_close_bar"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:background="@drawable/background_filled_black10_border_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30dp"
        android:text="@string/what_is_platform_fee"
        app:layout_constraintTop_toBottomOf="@id/vw_close_bar" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/platform_fee_info"
        android:textColor="@color/black_60"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />


    <TextView
        android:id="@+id/tv_learn_more"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_22dp"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/learn_more"
        android:textColor="@color/colorPrimary"
        app:drawableEndCompat="@drawable/ic_arrow_right_blue_circle_bg"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_understand"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/understand"
        app:layout_constraintTop_toBottomOf="@id/tv_learn_more" />

</androidx.constraintlayout.widget.ConstraintLayout>
