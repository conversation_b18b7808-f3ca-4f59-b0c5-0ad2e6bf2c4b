<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.bukuwarung.ui_component.component.error_view.BukuErrorView
            android:id="@+id/bev_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@id/pb_progress"
            android:layout_width="@dimen/_45dp"
            android:layout_height="@dimen/_45dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_payment_method"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:id="@+id/vw_line"
                android:layout_width="48dp"
                android:layout_height="5dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@color/black_10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Heading3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_4dp"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vw_line"
                tools:text="Pilih Metode Pembayaran" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_highlighted_methods"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title"
                tools:itemCount="2"
                tools:listitem="@layout/layout_ppob_payment_method" />

            <View
                android:id="@+id/vw_divider"
                android:layout_width="0dp"
                android:layout_height="4dp"
                android:background="@color/black_5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rv_highlighted_methods" />

            <TextView
                android:id="@+id/tv_other_payments_title"
                style="@style/Heading3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                app:drawableEndCompat="@drawable/ic_chevron_down"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vw_divider"
                tools:text="Metode Pembayaran Lainnya" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_other_methods"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_other_payments_title"
                tools:itemCount="3"
                tools:listitem="@layout/layout_other_payment_methods"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.bukuwarung.widget.PoweredByFooterView
            android:id="@+id/powered_by_footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/cl_payment_method" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
