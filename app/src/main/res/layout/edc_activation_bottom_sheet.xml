<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingBottom="@dimen/_16dp">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="match_parent"
        android:layout_height="360dp"
        android:scaleType="fitXY"
        android:src="@drawable/bukuagen_activation"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_margin="@dimen/_16dp"
        android:src="@drawable/ic_cross_circle_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20dp"
        android:text="@string/activate_edc_title"
        app:layout_constraintTop_toBottomOf="@+id/iv_image" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20dp"
        android:text="@string/activate_edc_subtitle"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnLater"
        style="@style/Button.Outline"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_38dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:letterSpacing="0"
        android:paddingHorizontal="@dimen/_10dp"
        android:paddingVertical="@dimen/_11dp"
        android:text="@string/cancel_btn"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toStartOf="@id/btnActivate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle"
        app:rippleColor="@color/black_40" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnActivate"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:letterSpacing="0"
        android:paddingHorizontal="@dimen/_10dp"
        android:paddingVertical="@dimen/_13dp"
        android:text="@string/open_bukuagen"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnLater"
        app:layout_constraintTop_toTopOf="@+id/btnLater"
        app:layout_goneMarginStart="@dimen/_4dp"
        app:rippleColor="@color/black_40" />

</androidx.constraintlayout.widget.ConstraintLayout>