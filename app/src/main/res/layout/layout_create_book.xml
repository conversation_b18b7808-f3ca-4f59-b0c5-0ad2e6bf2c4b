<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="@dimen/_25dp"
            android:layout_height="@dimen/_25dp"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_marginLeft="@dimen/_20dp"
            android:fontFamily="@font/roboto"
            android:src="@drawable/ic_back"
            android:layout_gravity="top" />

        <TextView
            android:id="@+id/title_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_24dp"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_toEndOf="@+id/back_btn"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:layout_gravity="top"
            android:lineHeight="@dimen/_26dp"
            android:lineSpacingExtra="@dimen/text_8sp"
            android:maxLines="1"
            android:text="@string/new_business_title"
            android:textColor="@color/white"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/back_btn"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UnusedAttribute" />

    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:id="@+id/form"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:fillViewport="true"
        android:scrollbars="none"
        android:background="@color/colorPrimary"
        app:layout_constraintBottom_toTopOf="@id/button_divider"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/white_background_top_radius_16"
        >

        <TextView
            android:id="@+id/tv_basic_detail"
            android:layout_width="@dimen/_0dp"
            android:text="@string/basic_detail"
            style="@style/Heading2"
            android:layout_margin="@dimen/_16dp"
            android:textColor="@color/black_80"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_enter_basic_info"
            android:layout_width="@dimen/_0dp"
            android:text="@string/enter_basic_info"
            style="@style/Body3"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:textColor="@color/black_40"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_basic_detail"/>

        <include
            layout="@layout/additional_info_section_heading"
            android:id="@+id/tv_heading_name"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            style="@style/SubHeading1"
            android:text="@string/business_address_hint"
            android:textColor="@color/black_80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_24dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_enter_basic_info" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_name"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:paddingStart="@dimen/_16dp"
            android:paddingEnd="@dimen/_16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_heading_name"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_14sp" />

        </com.google.android.material.textfield.TextInputLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_marginTop="@dimen/_10dp"
            android:layout_width="@dimen/_0dp"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/til_name"
            android:background="@drawable/bg_rounded_rectangle_blue_5"
            android:paddingHorizontal="@dimen/_5dp"
            android:paddingVertical="@dimen/_12dp"
            android:visibility="gone"
            android:id="@+id/profile_name_edit_info_layout"
            >

            <TextView
                android:id="@+id/tv_info"
                style="@style/Body2"
                android:drawableStart="@drawable/ic_info_primary"

                android:drawablePadding="@dimen/_5dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/qris_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_business_name_error"
            android:layout_width="0dp"
            style="@style/Label2"
            android:visibility="gone"
            tools:text="Nama usaha terdeteksi mencurigakan. Coba ubah dengan nama lain."
            android:textColor="@color/red_80"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_6dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/profile_name_edit_info_layout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <include
            layout="@layout/additional_info_section_heading"
            android:id="@+id/tv_heading_category"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            style="@style/SubHeading1"
            android:text="@string/business_address_hint"
            android:textColor="@color/black_80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_business_name_error"
            android:layout_margin="@dimen/_16dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_category"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:paddingStart="@dimen/_16dp"
            android:paddingEnd="@dimen/_16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_heading_category"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_category"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_14sp"
                android:focusableInTouchMode="false"
                android:editable="false"
                android:focusable="false"
                android:drawableTint="@color/black_80"
                android:drawableEnd="@drawable/ic_chevron_down" />

        </com.google.android.material.textfield.TextInputLayout>

        <include
            layout="@layout/additional_info_section_heading"
            android:id="@+id/tv_heading_phone"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            style="@style/SubHeading1"
            android:text="@string/business_address_hint"
            android:textColor="@color/black_80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/til_category"
            android:layout_margin="@dimen/_16dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_phone"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:paddingStart="@dimen/_16dp"
            android:paddingEnd="@dimen/_16dp"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="@dimen/_1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_heading_phone"
            app:passwordToggleDrawable="@null">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:inputType="number"
                android:textColorHint="@color/hint_color"
                android:textSize="@dimen/text_14sp" />

        </com.google.android.material.textfield.TextInputLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>
    <View
        android:id="@+id/saveOnboarding"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@+id/button_divider" />

    <View
        android:id="@+id/button_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#EEEEEE"
        app:layout_constraintBottom_toTopOf="@id/btn_save" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save"
        style="@style/ButtonFill"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:text="@string/save"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>