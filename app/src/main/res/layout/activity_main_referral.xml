<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.referral.main_referral.MainReferralActivity">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_alignParentTop="true"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:elevation="8dp"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ToolbarTheme">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="25dp"
                android:layout_marginLeft="@dimen/_8dp"
                android:layout_height="25dp"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:src="@mipmap/back_white" />

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_16dp"
                android:drawableLeft="@drawable/dot_highlighter"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:text="@string/main_referral_title"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textColor="#ffffff"
                android:textStyle="normal" />
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:layout_below="@id/toolbar"
        android:background="#EEF8FF"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/mainBanner"
                    android:layout_width="match_parent"
                    android:layout_height="175dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/referral_banner"
                    tools:ignore="ContentDescription" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/referralShareContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="160dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_16dp"
                    android:elevation="8dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingStart="@dimen/_16dp"
                        android:paddingTop="@dimen/_16dp"
                        android:paddingEnd="@dimen/_16dp"
                        android:paddingBottom="24dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginBottom="@dimen/_16dp"
                            android:text="@string/main_referral_h1"
                            android:textColor="@color/black"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:id="@+id/referralStepContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_8dp"
                            android:text="@string/main_referral_hint"
                            android:textColor="#8D8D8D"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/referralCodeTV"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/_8dp"
                                android:layout_weight="2"
                                android:background="@drawable/edittext_bg_flat"
                                android:hint="@string/main_referral_hint"
                                android:maxLines="1"
                                android:text="@string/default_placeholder"
                                android:textColor="#08B890"
                                android:textSize="14sp"
                                tools:text="666a7xcode" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/shareReferralButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:elevation="6dp"
                                android:gravity="center"
                                android:paddingTop="10dp"
                                android:paddingBottom="10dp"
                                android:text="@string/main_referral_share_cta"
                                android:textColor="@color/white"
                                android:textStyle="bold"
                                app:backgroundTint="@color/buku_CTA"
                                app:cornerRadius="2dp" />

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </FrameLayout>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:elevation="4dp"
                android:visibility="visible"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/myRankEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_16dp"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/emptyReferralText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/referral_empty"
                        android:textColor="@color/black"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/dividerEmpty"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:background="#F1F1F1"
                        app:layout_constraintTop_toBottomOf="@id/emptyReferralText" />

                    <RelativeLayout
                        android:id="@+id/emptyLeaderboardContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/dividerEmpty">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_alignParentTop="true"
                            android:layout_alignParentBottom="true"
                            android:layout_marginTop="@dimen/_16dp"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:text="@string/main_referral_check_leaderboard"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/check_rank_empty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentBottom="true"
                            android:layout_marginTop="@dimen/_16dp"
                            android:src="@drawable/ic_chevron_right"
                            android:tint="@color/colorPrimary" />

                    </RelativeLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/myRankLoading"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_16dp"
                    android:visibility="visible">

                    <ProgressBar
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:progressTint="@color/colorPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/myRankContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/_16dp"
                    android:paddingTop="@dimen/_16dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="24dp"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/title_rank"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/main_referral_your_rank"
                        android:textColor="@color/colorPrimary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/title_point"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/main_referral_point"
                        android:textColor="@color/colorPrimary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/rankTV"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:maxWidth="100dp"
                        android:maxLines="3"
                        android:text="@string/default_placeholder"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@id/firstLetter"
                        app:layout_constraintTop_toBottomOf="@id/title_rank" />

                    <TextView
                        android:id="@+id/firstLetter"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/oval_0"
                        android:fontFamily="@font/roboto"
                        android:gravity="center"
                        android:maxLength="1"
                        android:text="@string/default_placeholder"
                        android:textAllCaps="true"
                        android:textColor="@color/white"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toRightOf="@id/rankTV"
                        app:layout_constraintTop_toBottomOf="@id/title_rank"
                        tools:text="A" />

                    <TextView
                        android:id="@+id/nameTV"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_8dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:text="@string/default_placeholder"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        app:layout_constraintLeft_toRightOf="@id/firstLetter"
                        app:layout_constraintRight_toLeftOf="@id/pointTV"
                        app:layout_constraintTop_toBottomOf="@id/title_rank" />

                    <TextView
                        android:id="@+id/pointTV"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="56dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:text="@string/default_placeholder"
                        android:textColor="@color/colorPrimary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title_point" />

                    <View
                        android:id="@+id/divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:background="#F1F1F1"
                        app:layout_constraintTop_toBottomOf="@id/nameTV" />

                    <RelativeLayout
                        android:id="@+id/leaderboardContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/divider">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_alignParentTop="true"
                            android:layout_alignParentBottom="true"
                            android:layout_marginTop="@dimen/_16dp"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:text="@string/main_referral_check_leaderboard"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/check_rank"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentBottom="true"
                            android:layout_marginTop="@dimen/_16dp"
                            android:src="@drawable/ic_chevron_right"
                            android:tint="@color/colorPrimary" />

                    </RelativeLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:elevation="4dp"
                android:visibility="visible"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/_16dp"
                    android:paddingTop="@dimen/_16dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="24dp">

                    <TextView
                        android:id="@+id/title_prize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/main_referral_prize_title"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.cardview.widget.CardView
                        android:id="@+id/image_prize_container"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:elevation="0dp"
                        app:cardBackgroundColor="@android:color/transparent"
                        app:cardCornerRadius="4dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title_prize">

                        <ImageView
                            android:id="@+id/image_prize"
                            android:layout_width="match_parent"
                            android:layout_height="150dp"
                            android:scaleType="centerCrop"
                            android:src="@drawable/referral_prize_banner" />

                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:id="@+id/prizeContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="@dimen/_16dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/image_prize_container">

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="4dp"
                android:layout_margin="@dimen/_16dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/_16dp"
                    android:paddingEnd="@dimen/_16dp"
                    android:paddingBottom="24dp"
                    android:paddingTop="@dimen/_16dp">

                    <TextView
                        android:id="@+id/title_tnc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/main_referral_tnc_title"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <LinearLayout
                        android:id="@+id/tncContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title_tnc">

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</RelativeLayout>