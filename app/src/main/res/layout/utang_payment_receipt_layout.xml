<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_utang_payment_receipt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/white"
            app:contentPaddingBottom="32dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/header_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/card_header_gradient_bg"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvTransactionDate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="20dp"
                        android:paddingTop="12dp"
                        android:paddingEnd="20dp"
                        android:paddingBottom="12dp"
                        android:textAlignment="viewStart"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Senin, 11 Nov 2011" />

                    <TextView
                        android:id="@+id/tv_invoice_number"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="20dp"
                        android:paddingTop="12dp"
                        android:paddingEnd="20dp"
                        android:paddingBottom="12dp"
                        android:textAlignment="viewEnd"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="IYYMMDDXXXXXXXX" />

                </LinearLayout>

                <View
                    android:id="@+id/header_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/black_10"
                    android:visibility="gone" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp">


                    <TextView
                        android:id="@+id/tvWarungName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/ic_check_circle_green"
                        android:drawablePadding="@dimen/_8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Warung Chimay" />

                    <TextView
                        android:id="@+id/tvWarungPhone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_40"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvWarungName"
                        tools:text="08129213122" />

                    <View
                        android:id="@+id/line1"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:background="@color/black_10"
                        app:layout_constraintTop_toBottomOf="@id/tvWarungPhone" />

                    <TextView
                        android:id="@+id/tv_cst_name_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/name_label"
                        android:textColor="@color/black_40"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/line1" />

                    <TextView
                        android:id="@+id/tv_cst_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_8dp"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toStartOf="@+id/tv_cst_phone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_cst_name_label"
                        tools:text="Zulfakar Muhammad" />

                    <TextView
                        android:id="@+id/tv_cst_phone_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/mobile_phone_label"
                        android:textColor="@color/black_40"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/line1" />

                    <TextView
                        android:id="@+id/tv_cst_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_cst_phone_label"
                        tools:text="089671744371" />

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/gp_customer_details"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="tv_cst_name_label, tv_cst_name, tv_cst_phone_label, tv_cst_phone"/>

                    <androidx.constraintlayout.widget.Barrier
                        android:id="@+id/barrier_customer_details"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:barrierDirection="bottom"
                        app:constraint_referenced_ids="tv_cst_name, tv_cst_phone"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_payment_kode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_8dp"
                        android:background="@color/alice_blue"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/barrier_customer_details"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_code"
                            style="@style/Body3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8dp"
                            android:text="@string/code_booking"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_token"
                            style="@style/Heading2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_8dp"
                            android:textColor="@color/blue_80"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_code"
                            tools:text="081275598545" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/bgNominalBackground"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="12dp"
                        android:background="@color/black_33"
                        app:layout_constraintBottom_toBottomOf="@id/tv_free_charge"
                        app:layout_constraintTop_toBottomOf="@id/layout_payment_kode" />

                    <TextView
                        android:id="@+id/tv_transaction_type"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginTop="8dp"
                        android:text="@string/payment_successful"
                        android:textColor="@color/black_80"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/bgNominalBackground" />

                    <TextView
                        android:id="@+id/tvTransactionNominal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="@id/tv_transaction_type"
                        app:layout_constraintTop_toBottomOf="@id/tv_transaction_type"
                        tools:text="Rp.50.000" />

                    <TextView
                        android:id="@+id/tv_free_charge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:paddingBottom="@dimen/_8dp"
                        android:text="@string/free_charge_label"
                        android:textColor="@color/black_80"
                        app:layout_constraintStart_toStartOf="@id/tv_transaction_type"
                        app:layout_constraintTop_toBottomOf="@id/tvTransactionNominal"
                        tools:visibility="visible" />


                    <ImageView
                        android:id="@+id/img_secure"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/_8dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:layout_marginBottom="@dimen/_8dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_secure_transaction_new"
                        app:layout_constraintBottom_toBottomOf="@id/bgNominalBackground"
                        app:layout_constraintDimensionRatio="1:1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/bgNominalBackground" />

                    <TextView
                        android:id="@+id/tv_nominal_amount_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/nominal_transaksi"
                        android:textColor="@color/black_40"
                        android:fontFamily="@font/roboto"
                        android:layout_marginTop="@dimen/_16dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/bgNominalBackground"/>

                    <TextView
                        style="@style/Body2"
                        android:id="@+id/tv_nominal_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Rp100.000"
                        app:layout_constraintTop_toBottomOf="@+id/tv_nominal_amount_message"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/tv_service_fee_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/service_fee"
                        android:textColor="@color/black_40"
                        android:fontFamily="@font/roboto_bold"
                        android:layout_marginTop="@dimen/_16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/bgNominalBackground"/>

                    <TextView
                        android:id="@+id/tv_service_fee"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Rp5.000"
                        app:layout_constraintTop_toBottomOf="@+id/tv_service_fee_message"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <View
                        android:id="@+id/divider"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_1dp"
                        android:background="@color/black_10"
                        android:layout_marginTop="8dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_nominal_amount"
                        />

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/layout_service_fee"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:constraint_referenced_ids="divider, tv_service_fee, tv_service_fee_message, tv_nominal_amount, tv_nominal_amount_message"/>

                    <!--Sender-->
                    <TextView
                        android:id="@+id/sender_txt"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        android:text="@string/sender"
                        android:textColor="@color/black_40"
                        android:textSize="12sp"
                        android:layout_marginTop="@dimen/_16dp"
                        app:layout_constraintTop_toBottomOf="@+id/divider"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_sender_name"
                        tools:visibility="visible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        tools:text="Zulfakar"
                        app:layout_constraintTop_toBottomOf="@+id/sender_txt"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <!--Recipient-->
                    <TextView
                        android:id="@+id/recipient_txt"
                        android:visibility="gone"
                        android:layout_width="wrap_content"
                        tools:visibility="visible"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        android:text="@string/penerima"
                        android:textColor="@color/black_40"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_sender_name"/>

                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_recipient_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        tools:visibility="visible"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        tools:text="Muamalat - Zulfakar"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/recipient_txt"/>

                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_recipient_aaccount_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:visibility="visible"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        tools:text="**********"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_recipient_name"/>


                    <View
                        android:id="@+id/recipient_divider"
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        tools:visibility="visible"
                        android:layout_marginTop="12dp"
                        android:background="@color/black_10"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_recipient_aaccount_number"
                        />

                    <TextView
                        android:id="@+id/tv_transaction_note_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        tools:visibility="visible"
                        android:text="@string/label_note"
                        android:textColor="@color/black_40"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/recipient_divider"/>

                    <TextView
                        android:id="@+id/tv_transaction_note"
                        android:layout_width="wrap_content"
                        tools:visibility="visible"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto"
                        android:textColor="@color/black_80"
                        android:textSize="14sp"
                        tools:text="Ongkos pekerja minggu pertama"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_transaction_note_message" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_payment_catatan"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:background="@color/black_5"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:layout_constraintTop_toBottomOf="@id/tv_transaction_note">

                        <TextView
                            android:id="@+id/tv_pdt_name"
                            style="@style/Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="Pulsa Telkomsel 100.000" />

                        <TextView
                            android:id="@+id/tv_number"
                            style="@style/Heading2"
                            android:textSize="@dimen/item_22sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_8dp"
                            android:textColor="@color/blue_80"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_pdt_name"
                            tools:text="081275598545" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <TextView
                        android:id="@+id/note_hint"
                        style="@style/Label1"
                        android:layout_width="wrap_content"
                        android:visibility="gone"
                        android:layout_height="wrap_content"
                        android:text="@string/electricity_token_hint"
                        android:textColor="@color/black_40"
                        android:layout_marginTop="@dimen/_12dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layout_payment_catatan" />

                    <View
                        android:id="@+id/note_line_bottom"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:background="@color/black_10"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/note_hint" />

                    <TextView
                        android:id="@+id/serial_number_txt"
                        style="@style/Body3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10dp"
                        android:layout_marginEnd="@dimen/_12dp"
                        android:textColor="@color/black_60"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/note_line_bottom"
                        tools:text="Serial Number: " />

                    <View
                        android:id="@+id/serial_number_divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:background="@color/black_10"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/serial_number_txt"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_transaction_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/serial_number_divider">

                        <TextView
                            android:id="@+id/transaction_total_label_payment"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_8dp"
                            android:text="@string/total_utang"
                            android:textColor="@color/black_60"
                            app:layout_constraintEnd_toStartOf="@id/tv_transaction_total_payment"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_transaction_total_payment"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:textColor="@color/black_60"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="Rp123.0000" />

                        <TextView
                            android:id="@+id/paid_total_label_payment"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_8dp"
                            android:text="@string/total_sudah_dibayar"
                            android:textColor="@color/black_60"
                            app:layout_constraintEnd_toStartOf="@id/tv_paid_total_payment"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/transaction_total_label_payment" />

                        <TextView
                            android:id="@+id/tv_paid_total_payment"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:textColor="@color/black_60"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/transaction_total_label_payment"
                            tools:text="Rp123.0000" />

                        <TextView
                            android:id="@+id/remaining_total_label_payment"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_8dp"
                            android:text="@string/kurang_bayar"
                            android:textColor="@color/black_60"
                            app:layout_constraintEnd_toStartOf="@id/tv_remaining_total_payment"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/paid_total_label_payment" />

                        <TextView
                            android:id="@+id/tv_remaining_total_payment"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_16dp"
                            android:textColor="@color/black_60"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/paid_total_label_payment"
                            tools:text="Rp123.0000" />

                        <View
                            android:id="@+id/detail_line_bottom_payment"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:background="@color/black_10"
                            app:layout_constraintTop_toBottomOf="@id/tv_remaining_total_payment" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <TextView
                        android:id="@+id/tv_footer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:fontFamily="@font/roboto"
                        android:textAlignment="center"
                        android:textColor="@color/black_40"
                        android:textSize="12sp"
                        app:layout_constraintTop_toBottomOf="@+id/layout_transaction_detail"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</layout>
