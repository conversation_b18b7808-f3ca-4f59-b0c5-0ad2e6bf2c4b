<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/_16dp"
    android:background="@color/white">
    <View
        android:id="@+id/vw_div"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_10dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tv_preview_catalog"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/preview_katalog"
        app:layout_constraintEnd_toStartOf="@id/btn_change"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_div" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body4"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/selling_price_filled"
        app:layout_constraintEnd_toStartOf="@id/btn_change"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_preview_catalog" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_change"
        style="@style/Button.OutlinePrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingTop="@dimen/_4dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_4dp"
        android:text="@string/ubah_harga"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@color/black_40" />

    <View
        android:id="@+id/vw_divider"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle" />

    <TextView
        android:id="@+id/tv_arrangement"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:text="@string/arrangement"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_selector"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_arrangement" />
</androidx.constraintlayout.widget.ConstraintLayout>