<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rv_cashback_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_rp"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_rp_icon_green" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title_item"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/cashback"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toEndOf="@id/iv_rp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_date"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_2dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toEndOf="@id/iv_rp"
        app:layout_constraintTop_toBottomOf="@id/tv_title_item"
        tools:text="18 Mar 2022, 09:30" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_right"
        android:layout_width="@dimen/_6dp"
        android:layout_height="@dimen/_10dp"
        android:layout_marginTop="@dimen/dimen_28dp"
        android:layout_marginEnd="@dimen/_18dp"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_right_black_40" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_amount_cashback"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:textColor="@color/green_80"
        app:layout_constraintEnd_toStartOf="@id/iv_right"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Rp1.500" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_status"
        style="@style/Body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:text="@string/cashback"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toTopOf="@id/tv_inprocess_info"
        app:layout_constraintEnd_toStartOf="@id/iv_right"
        app:layout_constraintTop_toBottomOf="@id/tv_amount_cashback"
        app:layout_goneMarginBottom="@dimen/_12dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_inprocess_info"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="34dp"
        android:layout_marginTop="@dimen/_12dp"
        android:background="@color/blue_5"
        android:gravity="center"
        android:text="@string/cashback_saldo"
        android:textColor="@color/blue_90"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_status" />

    <View
        android:layout_width="@dimen/_0dp"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/_16dp"
        android:background="@color/new_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>