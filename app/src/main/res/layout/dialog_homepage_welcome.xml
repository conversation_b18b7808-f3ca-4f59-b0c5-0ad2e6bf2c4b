<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_welcome_dialog"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/bg_top_rounded_corner"
    android:backgroundTint="@color/fui_transparent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_home_welcome"
        android:layout_width="match_parent"
        android:scaleType="fitXY"
        android:layout_height="280dp"
        app:srcCompat="@drawable/welcome_image"
        app:layout_constraintWidth_percent="1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/selected_tab"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/iv_home_welcome"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

    <TextView
        android:id="@+id/tv_welcome_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/Heading2"
        android:textColor="@color/heading_text"
        android:maxLines="4"
        android:gravity="center"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginHorizontal="20dp"
        android:textSize="@dimen/text_18sp"
        android:text="Tampilan baru, kelola usaha jadi makin mudah dan seru!"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_subheading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:text="Ada banyak fitur andalan yang bisa bantu maksimalin pengelolaan usahamu."
        android:fontFamily="@font/roboto"
        android:textColor="#5c5c5c"
        android:gravity="center"
        android:maxLines="2"
        android:textSize="@dimen/_14dp"
        android:layout_marginHorizontal="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_welcome_text"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_ok"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/_14dp"
        android:text="Cek Sekarang"
        android:textColor="@color/heading_text"
        style="@style/Heading3"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:textSize="@dimen/text_16sp"
        android:layout_marginTop="15dp"
        android:backgroundTint="@color/new_yellow"
        android:layout_marginBottom="@dimen/_20dp"
        app:layout_constraintStart_toStartOf="@+id/tv_subheading"
        app:layout_constraintEnd_toEndOf="@+id/tv_subheading"
        app:layout_constraintTop_toBottomOf="@+id/tv_subheading" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>