<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black5">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/save_button_container"
        android:layout_alignParentTop="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include
                android:id="@+id/category_group_container"
                layout="@layout/item_transaction_category_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_4dp"
                android:layout_marginEnd="@dimen/_8dp" />

            <include
                android:id="@+id/add_product_container"
                layout="@layout/item_transaction_add_product"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_4dp" />

            <include
                android:id="@+id/selected_product_container"
                layout="@layout/item_transaction_product_summary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_4dp" />

            <include
                android:id="@+id/balance_main_container"
                layout="@layout/item_transaction_balance_main"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp" />

            <include
                android:id="@+id/balance_detail_container"
                layout="@layout/item_transaction_balance_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_20dp"
                android:orientation="horizontal">

                <include
                    android:id="@+id/calendar_container"
                    layout="@layout/item_transaction_calendar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_weight="1" />

                <include
                    android:id="@+id/payment_status_container"
                    layout="@layout/item_transaction_payment_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

            </LinearLayout>

            <TextView
                android:id="@+id/payment_status_note_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:drawablePadding="@dimen/_8dp"
                android:text="@string/transaction_payment_not_paid_note"
                android:textColor="@color/black40"
                android:textSize="@dimen/text_14sp"
                app:drawableStartCompat="@drawable/ic_info_filled" />

            <include
                android:id="@+id/contact_primary_container"
                layout="@layout/item_transaction_contact"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp" />

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_20dp"
                android:background="@color/black10" />

            <RelativeLayout
                android:id="@+id/extra_info_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingHorizontal="@dimen/_16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/optional_information"
                    android:textColor="@color/black80"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/expansion_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    app:tint="@color/black40" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:orientation="vertical">

                <include
                    android:id="@+id/upload_image_container"
                    layout="@layout/item_transaction_upload_image"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_16dp" />

                <include
                    android:id="@+id/note_container"
                    layout="@layout/item_transaction_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <include
                    android:id="@+id/contact_secondary_container"
                    layout="@layout/item_transaction_contact"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/save_button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_12dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/save_button"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/save" />

    </LinearLayout>

    <com.bukuwarung.keyboard.CustomKeyboardView
        android:id="@+id/calculator_keyboard"
        android:layout_width="match_parent"
        android:layout_height="245dp"
        android:layout_alignParentBottom="true"
        android:visibility="gone" />

</RelativeLayout>
