<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingTop="27dp"
    android:paddingStart="@dimen/_16dp"
    android:paddingEnd="@dimen/_16dp">
    <ImageView
        android:id="@+id/closeDialog"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_16dp"
        android:src="@drawable/close"
        android:layout_marginStart="7dp"
        />

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/_8dp"
        android:layout_marginTop="23dp">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:src="@drawable/tutorial_completion_message_image" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/tutorial_completion_message"
        android:textSize="18sp"
        android:textColor="@color/black_80"
        android:fontFamily="@font/roboto"
        android:layout_marginTop="@dimen/_16dp"
        />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/select_menu"
        android:textSize="14sp"
        android:textColor="@color/black_60"
        android:layout_marginTop="@dimen/_16dp"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        >
        <com.google.android.material.button.MaterialButton
            android:id="@+id/recordUtang"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:backgroundTint="@color/white"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="@string/record_utang"
            android:textAllCaps="false"
            android:textColor="@color/buku_CTA"
            android:textSize="16sp"
            android:textStyle="bold"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:letterSpacing="0.05"
            android:visibility="visible"
            app:cornerRadius="4dp"
            app:strokeColor="@color/buku_CTA"
            app:strokeWidth="1dp"
            />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/recordTransaksi"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="@string/record_transaksi"
            android:textAllCaps="false"
            android:textColor="@color/black_80"
            android:textSize="16sp"
            android:letterSpacing="0.05"
            android:textStyle="bold"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:backgroundTint="@color/new_yellow"
            app:cornerRadius="4dp"
            android:layout_marginStart="@dimen/_16dp"
            />
    </LinearLayout>



</LinearLayout>