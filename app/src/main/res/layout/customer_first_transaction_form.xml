<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:theme="@style/ToolbarTheme">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="0dp"
                    android:src="@mipmap/back_white" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_toRightOf="@+id/close"
                    android:drawableLeft="@drawable/dot_highlighter"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:maxLines="1"
                    android:text="@string/new_utang_piutang"
                    android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                    android:textColor="#ffffff"
                    android:textStyle="normal" />

            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true">

            <LinearLayout
                android:id="@+id/formView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="@dimen/_16dp">

                <!--Switch-->
                <LinearLayout
                    android:id="@+id/type_rg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:baselineAligned="false"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/expense"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginRight="4dp"
                        android:layout_weight="1"
                        android:background="@drawable/type_exp_selected_bg"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/debit_button"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="2dp"
                            android:buttonTint="@color/white" />

                        <ImageView
                            android:id="@+id/icon_expense"
                            android:layout_width="@dimen/_16dp"
                            android:layout_height="@dimen/_16dp"
                            android:layout_marginStart="10dp"
                            android:src="@drawable/ic_switch_utang"
                            android:visibility="gone"
                            tools:ignore="ContentDescription" />

                        <TextView
                            android:id="@+id/text_expense"
                            android:layout_width="wrap_content"
                            android:layout_height="21dp"
                            android:layout_marginLeft="8dp"
                            android:layout_marginRight="12dp"
                            android:fontFamily="@font/roboto"
                            android:gravity="top"
                            android:text="@string/radio_debit_label"
                            android:textColor="@color/white"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/income"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginLeft="4dp"
                        android:layout_weight="1"
                        android:background="@drawable/type_unselected_bg"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/credit_button"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="4dp"
                            android:buttonTint="@color/white" />

                        <ImageView
                            android:id="@+id/icon_income"
                            android:layout_width="@dimen/_16dp"
                            android:layout_height="@dimen/_16dp"
                            android:layout_marginStart="10dp"
                            android:src="@drawable/ic_switch_piutang"
                            android:visibility="gone"
                            tools:ignore="ContentDescription" />

                        <TextView
                            android:id="@+id/text_income"
                            android:layout_width="wrap_content"
                            android:layout_height="21dp"
                            android:layout_marginLeft="8dp"
                            android:layout_marginRight="12dp"
                            android:fontFamily="sans-serif"
                            android:text="@string/radio_credit_label"
                            android:textAlignment="center"
                            android:textColor="@color/greyDisabled"
                            android:textStyle="bold" />
                    </LinearLayout>
                </LinearLayout>

                <!--Phone Layout-->
                <LinearLayout
                    android:id="@+id/layoutNewCustomer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:background="@drawable/bg_blue_outline"
                    android:gravity="center"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp">

                    <LinearLayout
                        android:id="@+id/phoneLayout"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="@dimen/_8dp"
                        android:background="@drawable/oval_0"
                        android:backgroundTint="#66BDFF"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/firstLetter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:gravity="center"
                            android:textAllCaps="true"
                            android:textColor="@color/white"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            tools:text="R" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/phone_icon"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="@dimen/_8dp"
                        android:background="@drawable/oval_0"
                        android:backgroundTint="@color/colorPrimary"
                        android:padding="8dp"
                        android:src="@drawable/ic_person"
                        android:visibility="visible"
                        app:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:paddingStart="@dimen/_8dp"
                        android:paddingEnd="@dimen/_8dp">

                        <TextView
                            android:id="@+id/name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:fontFamily="@font/roboto"
                            android:lineSpacingExtra="5sp"
                            android:scrollbars="vertical"
                            android:text="@string/add_customer"
                            android:textColor="@color/colorPrimary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:lineSpacingExtra="5sp"
                            android:scrollbars="vertical"
                            android:text="@string/add_customer_note"
                            android:textAlignment="textStart"
                            android:textColor="@color/black_60"
                            android:textSize="12sp"
                            android:textStyle="normal" />

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/chevron_contact"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:layout_weight="4"
                        android:src="@drawable/ic_chevron_right" />

                </LinearLayout>

                <!--Amount Input-->
                <include
                    android:id="@+id/transaction_input_amount_layout"
                    layout="@layout/credit_amount_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <requestFocus />
                </include>

                <!--Currently Not used-->
                <LinearLayout
                    android:id="@+id/debit_button_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:ignore="Orientation"></LinearLayout>

                <LinearLayout
                    android:id="@+id/additional_data_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <!--Note Layout-->
                    <LinearLayout
                        android:id="@+id/noteLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:gravity="center"
                        android:paddingStart="@dimen/_8dp"
                        android:paddingEnd="@dimen/_16dp">

                        <ImageView
                            android:id="@+id/note_icon"
                            android:layout_width="@dimen/form_icon_size"
                            android:layout_height="@dimen/form_icon_size"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:src="@drawable/ic_notes_primary"
                            app:tint="@color/colorPrimary" />

                        <EditText
                            android:id="@+id/note"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusedByDefault="false"
                            android:fontFamily="@font/roboto"
                            android:hint="@string/transaction_note_hint"
                            android:inputType="none|text|textCapSentences|textMultiLine"
                            android:lineSpacingExtra="5sp"
                            android:scrollbars="vertical"
                            android:textColor="#666666"
                            android:textColorHint="#666666"
                            android:textSize="16sp" />

                    </LinearLayout>

                    <!--Date Layout-->
                    <LinearLayout
                        android:id="@+id/rl_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:gravity="center"
                        android:paddingStart="@dimen/_8dp"
                        android:paddingEnd="@dimen/_16dp">

                        <ImageView
                            android:id="@+id/date_icon"
                            android:layout_width="@dimen/form_icon_size"
                            android:layout_height="@dimen/form_icon_size"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:src="@drawable/ic_date_range_green"
                            app:tint="@color/colorPrimary" />

                        <EditText
                            android:id="@+id/calender_picker"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:fontFamily="@font/roboto"
                            android:hint="@string/transaction_date_hint"
                            android:inputType="none|text|textCapSentences|textMultiLine"
                            android:lineSpacingExtra="5sp"
                            android:scrollbars="vertical"
                            android:textColor="#666666"
                            android:textSize="16.3sp" />

                    </LinearLayout>

                    <!--Checkbox-->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="visible">

                        <CheckBox
                            android:id="@+id/sendCustomerSms"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_16dp"
                            android:layout_marginTop="@dimen/_16dp"
                            android:layout_marginEnd="@dimen/_16dp"
                            android:buttonTint="@color/colorPrimary"
                            android:checked="false"
                            android:fontFamily="@font/roboto"
                            android:paddingLeft="@dimen/_8dp"
                            android:text="@string/send_customer_sms"
                            android:textColor="#666666"
                            android:textSize="14sp"
                            android:textStyle="normal"
                            android:visibility="visible" />

                    </RelativeLayout>

                    <!--Line-->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="2dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="#EEE" />

                    <!--Preview message-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/clSMSPreview"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/sendCustomerSms"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvPreviewLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto"
                            android:text="Pratinjau Pesan ke Pelanggan"
                            android:textColor="@color/black_60"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            app:layout_constraintGuide_percent="0.25" />

                        <TextView
                            android:id="@+id/tvSmsPreview"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8dp"
                            android:background="@drawable/sms_bubble"
                            android:fontFamily="@font/roboto"
                            android:paddingStart="32dp"
                            android:paddingTop="@dimen/_16dp"
                            android:paddingEnd="32dp"
                            android:paddingBottom="@dimen/_16dp"
                            android:textColor="@color/black_80"
                            android:textSize="14sp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="@id/guideline"
                            app:layout_constraintTop_toBottomOf="@id/tvPreviewLabel"
                            tools:text="Bapak/Ibu Rano Setiawan, Anda masih memiliki transaksi belum lunas sebesar Rp200.000. Silakan lakukan pembayaran.\n\nPesan ini dikirim melalui Aplikasi BukuWarung https://bukuwarung.com/y?ae=k068GM31J9" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!--Bottom Layout for save btn-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:gravity="bottom"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#EEEEEE" />

                <!--Button-->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/save"
                    style="@style/DefaultMaterialButtonStyleAdjacent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:layout_marginRight="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:enabled="true"
                    android:fontFamily="@font/roboto"
                    android:gravity="center"
                    android:padding="12dp"
                    android:text="@string/save_utang_piutang"
                    android:textColor="@color/cta_button_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:cornerRadius="4dp" />

            </LinearLayout>

            <com.bukuwarung.keyboard.CustomKeyboardView
                android:id="@+id/keyboardView"
                android:layout_width="match_parent"
                android:layout_height="245dp"
                android:layout_alignParentBottom="true"
                android:layout_gravity="bottom"
                android:animateLayoutChanges="true"
                android:visibility="gone" />

        </FrameLayout>

    </LinearLayout>

    <include layout="@layout/transaction_creation_success_animation_layout" />

</FrameLayout>