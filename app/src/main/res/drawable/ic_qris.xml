<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="49dp"
    android:height="48dp"
    android:viewportWidth="49"
    android:viewportHeight="48">
  <path
      android:pathData="M24.5,0L24.5,0A24,24 0,0 1,48.5 24L48.5,24A24,24 0,0 1,24.5 48L24.5,48A24,24 0,0 1,0.5 24L0.5,24A24,24 0,0 1,24.5 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="24.5"
          android:startY="24"
          android:endX="24.5"
          android:endY="83"
          android:type="linear">
        <item android:offset="0" android:color="#FFF2FAFF"/>
        <item android:offset="0.457" android:color="#FFB3DEFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M10.5,10h28v28h-28z"/>
    <path
        android:pathData="M37.333,20.145C36.688,20.145 36.166,19.623 36.166,18.979V13.449C36.166,12.834 35.666,12.333 35.052,12.333H29.522C28.877,12.333 28.355,11.811 28.355,11.167C28.355,10.523 28.877,10 29.522,10H35.052C36.954,10 38.5,11.547 38.5,13.449V18.979C38.5,19.623 37.978,20.145 37.333,20.145Z"
        android:fillColor="#66BDFF"/>
    <path
        android:pathData="M11.667,20.145C11.021,20.145 10.5,19.623 10.5,18.979V13.449C10.5,11.547 12.046,10 13.948,10H19.478C20.123,10 20.644,10.523 20.644,11.167C20.644,11.811 20.123,12.333 19.478,12.333H13.948C13.334,12.333 12.833,12.834 12.833,13.449V18.979C12.833,19.623 12.312,20.145 11.667,20.145Z"
        android:fillColor="#66BDFF"/>
    <path
        android:pathData="M35.052,38H29.522C28.877,38 28.355,37.477 28.355,36.833C28.355,36.189 28.877,35.667 29.522,35.667H35.052C35.666,35.667 36.166,35.166 36.166,34.551V29.021C36.166,28.377 36.688,27.854 37.333,27.854C37.978,27.854 38.5,28.377 38.5,29.021V34.551C38.5,36.453 36.954,38 35.052,38Z"
        android:fillColor="#66BDFF"/>
    <path
        android:pathData="M19.478,38H13.948C12.046,38 10.5,36.453 10.5,34.551V29.021C10.5,28.377 11.021,27.854 11.667,27.854C12.312,27.854 12.833,28.377 12.833,29.021V34.551C12.833,35.166 13.334,35.667 13.948,35.667H19.478C20.123,35.667 20.644,36.189 20.644,36.833C20.644,37.477 20.123,38 19.478,38Z"
        android:fillColor="#66BDFF"/>
    <path
        android:pathData="M21.557,23.018H15.75C15.104,23.018 14.583,22.496 14.583,21.852V15.25C14.583,14.606 15.104,14.083 15.75,14.083H21.557C22.202,14.083 22.724,14.606 22.724,15.25V21.852C22.724,22.496 22.201,23.018 21.557,23.018ZM16.916,20.685H20.391V16.416H16.916V20.685Z"
        android:fillColor="#0091FF"/>
    <path
        android:pathData="M33.25,33.917H27.709C27.063,33.917 26.542,33.394 26.542,32.75V29.038C26.542,28.394 27.063,27.871 27.709,27.871C28.354,27.871 28.875,28.394 28.875,29.038V31.583H32.084V28.083H30.705C30.059,28.083 29.538,27.561 29.538,26.917C29.538,26.273 30.059,25.75 30.705,25.75H33.25C33.896,25.75 34.417,26.273 34.417,26.917V32.75C34.417,33.394 33.896,33.917 33.25,33.917Z"
        android:strokeAlpha="0.5"
        android:fillColor="#0091FF"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M33.25,21.189H28.477C27.832,21.189 27.31,20.667 27.31,20.022V15.25C27.31,14.606 27.832,14.083 28.477,14.083H33.25C33.895,14.083 34.417,14.606 34.417,15.25V20.022C34.417,20.667 33.895,21.189 33.25,21.189ZM29.644,18.856H32.083V16.416H29.644V18.856Z"
        android:fillColor="#0091FF"/>
    <path
        android:pathData="M25.057,26.492H15.75C15.104,26.492 14.583,25.969 14.583,25.325C14.583,24.681 15.104,24.158 15.75,24.158H23.891V16.31C23.891,15.666 24.412,15.144 25.057,15.144C25.702,15.144 26.224,15.666 26.224,16.31V25.325C26.224,25.969 25.701,26.492 25.057,26.492Z"
        android:fillColor="#0091FF"/>
    <path
        android:pathData="M28.292,26.2C27.646,26.2 27.125,25.677 27.125,25.033V23.443C27.125,22.799 27.646,22.276 28.292,22.276H33.542C34.187,22.276 34.708,22.799 34.708,23.443C34.708,24.087 34.187,24.61 33.542,24.61H29.458V25.033C29.458,25.677 28.937,26.2 28.292,26.2Z"
        android:fillColor="#0091FF"/>
    <path
        android:pathData="M24.526,32.401C23.881,32.401 23.359,31.878 23.359,31.234V28.584C23.359,27.94 23.881,27.417 24.526,27.417C25.171,27.417 25.693,27.94 25.693,28.584V31.234C25.693,31.878 25.171,32.401 24.526,32.401Z"
        android:strokeAlpha="0.5"
        android:fillColor="#0091FF"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M21.291,33.917H15.75C15.104,33.917 14.583,33.395 14.583,32.75V28.508C14.583,27.865 15.104,27.342 15.75,27.342H21.291C21.937,27.342 22.458,27.865 22.458,28.508V32.75C22.458,33.395 21.937,33.917 21.291,33.917ZM16.916,31.584H20.125V29.675H16.916V31.584Z"
        android:strokeAlpha="0.5"
        android:fillColor="#0091FF"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M11.5,23L37.5,23A1,1 0,0 1,38.5 24L38.5,24A1,1 0,0 1,37.5 25L11.5,25A1,1 0,0 1,10.5 24L10.5,24A1,1 0,0 1,11.5 23z"
        android:fillColor="#FFC700"/>
    <path
        android:pathData="M12.9,25h23.2v9.6h-23.2z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="24.5"
            android:startY="25"
            android:endX="24.5"
            android:endY="34.6"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9FDFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M12.5,25h24v10h-24z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="24.5"
            android:startY="25"
            android:endX="24.5"
            android:endY="35"
            android:type="linear">
          <item android:offset="0" android:color="#FF77CAFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
