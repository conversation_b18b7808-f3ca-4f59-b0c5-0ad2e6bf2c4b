<?xml version="1.0" encoding="UTF-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true" >
        <shape>
            <solid
                android:color="#EFEFEF" />
            <stroke
                android:width="1dp"
                android:color="#EFEFEF" />
        </shape>
    </item>
    <item>
        <shape>
            <gradient
                android:startColor="@color/white"
                android:endColor="@color/white"
                android:angle="270" />
            <stroke
                android:width="1dp"
                android:color="@color/white" />
        </shape>
    </item>
</selector>