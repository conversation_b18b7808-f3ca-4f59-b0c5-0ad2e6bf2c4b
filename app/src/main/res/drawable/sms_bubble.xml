<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:height="20dp">
        <rotate
            android:fromDegrees="50"
            android:pivotX="100%"
            android:pivotY="0%"
            android:toDegrees="50">

            <shape android:shape="rectangle">
                <solid android:color="@color/sms_background" />
            </shape>
        </rotate>
    </item>

    <item android:end="@dimen/_16dp" android:left="@dimen/_16dp">
        <shape android:shape="rectangle">

            <solid android:color="@color/sms_background" />
            <corners
                android:bottomLeftRadius="12dp"
                android:bottomRightRadius="12dp"
                android:topLeftRadius="12dp"
                android:topRightRadius="0dp" />

        </shape>
    </item>
</layer-list>