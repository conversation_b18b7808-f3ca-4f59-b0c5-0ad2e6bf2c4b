package com.bukuwarung

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.test.espresso.*
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.BoundedMatcher
import androidx.test.espresso.matcher.ViewMatchers
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.platform.app.InstrumentationRegistry
import org.hamcrest.Description
import org.hamcrest.Matcher
import org.hamcrest.Matchers
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.allOf
import org.hamcrest.TypeSafeMatcher
import java.io.File


fun waitForExecution(millisToWait: Long = Constants.WAIT_TIME_FOR_API) {
    try {
        Thread.sleep(millisToWait)
    } catch (ex: Exception) {
        ex.printStackTrace()
    }
}

fun clearData() {
    try {
        val root: File = InstrumentationRegistry.getInstrumentation().targetContext.filesDir.parentFile
        val sharedPreferencesFileNames: Array<String> = File(root, "shared_prefs").list()
        for (fileName in sharedPreferencesFileNames) {
            InstrumentationRegistry.getInstrumentation().targetContext.getSharedPreferences(fileName.replace(".xml", ""),
                    Context.MODE_PRIVATE).edit().clear().commit()
        }
        InstrumentationRegistry.getInstrumentation().targetContext.deleteDatabase("khatabook-main");
    }catch (ex: Exception) {
        ex.printStackTrace()
        throw ex
    }
}

fun String.getInitial2Char(): String {
    val initial: String = this.split(" ").let {
        if (it.size >= 2) {
            "${it[0].first()}${it[1].first()}"
        } else {
            "${it[0].first()}"
        }
    }
    return initial
}

fun String.getInitial1Char(): String {
    return this.first().toString()
}

fun childAtPosition(
        parentMatcher: Matcher<View>, position: Int): Matcher<View> {

    return object : TypeSafeMatcher<View>() {
        override fun describeTo(description: Description) {
            description.appendText("Child at position $position in parent ")
            parentMatcher.describeTo(description)
        }

        public override fun matchesSafely(view: View): Boolean {
            val parent = view.parent
            return parent is ViewGroup && parentMatcher.matches(parent)
                    && view == parent.getChildAt(position)
        }
    }
}

fun ViewInteraction.doesNotExist() {
    try {
        this.check(ViewAssertions.matches(Matchers.not(ViewMatchers.isDisplayed())))
        throw Exception()
    } catch (ex: NoMatchingViewException) {
        // go through
    }
}

fun getText(matcher: Matcher<View?>?): String? {
    val stringHolder = arrayOf<String?>(null)
    onView(matcher).perform(object : ViewAction {
        override fun getConstraints(): Matcher<View> {
            return isAssignableFrom(TextView::class.java)
        }

        override fun getDescription(): String {
            return "getting text from a TextView"
        }

        override fun perform(uiController: UiController?, view: View) {
            val tv = view as TextView //Save, because of check in getConstraints()
            stringHolder[0] = tv.text.toString()
        }
    })
    return stringHolder[0]
}

class ScrollToEndAction : ViewAction {
    override fun getDescription(): String {
        return "scroll RecyclerView to end"
    }

    override fun getConstraints(): Matcher<View> {
        return allOf<View>(isAssignableFrom(RecyclerView::class.java), isDisplayed())
    }

    override fun perform(uiController: UiController?, view: View?) {
        val recyclerView = view as RecyclerView
        val itemCount = recyclerView.adapter?.itemCount
        val position = itemCount?.minus(1) ?: 0
        recyclerView.scrollToPosition(position)
        uiController?.loopMainThreadUntilIdle()
    }
}

fun allowMultipleViews(call: () -> Unit) {
    try {
        call()
    } catch (ex: AmbiguousViewMatcherException) {
        // continue
    }
}