package com.bukuwarung.feature.transaction.record.fragment.income.screen

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.contrib.RecyclerViewActions.scrollToPosition
import androidx.test.espresso.matcher.ViewMatchers.isChecked
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.isEnabled
import androidx.test.espresso.matcher.ViewMatchers.isNotChecked
import androidx.test.espresso.matcher.ViewMatchers.isNotEnabled
import androidx.test.espresso.matcher.ViewMatchers.isSelected
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withTagValue
import androidx.test.espresso.matcher.ViewMatchers.withText
import com.bukuwarung.R
import com.bukuwarung.base.database.api.DatabaseManager
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.matrix.api.WritableMatrix
import com.bukuwarung.base.messenger.api.MessageRecorder
import com.bukuwarung.base.remote.api.WritableNetworkMonitor
import com.bukuwarung.base.screen.BaseScreenTest
import com.bukuwarung.feature.transaction.record.fragment.adapter.TransactionCategoryViewHolder
import com.bukuwarung.feature.transaction.record.screen.TransactionRecordActivity
import com.bukuwarung.utils.Utility
import dagger.hilt.android.testing.HiltAndroidTest
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.allOf
import org.hamcrest.Matchers.not
import org.junit.Assert.assertTrue
import org.junit.Test
import javax.inject.Inject

@HiltAndroidTest
class TransactionRecordIncomeFragmentTest : BaseScreenTest() {
    @Inject
    lateinit var lector: Lector

    @Inject
    lateinit var matrix: WritableMatrix

    @Inject
    lateinit var databaseManager: DatabaseManager

    @Inject
    lateinit var networkMonitor: WritableNetworkMonitor

    @Inject
    lateinit var messageRecorder: MessageRecorder

    @Test
    fun initialize_view() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Penjualan",
                    "categoryId": "penjualan"
                }
            ]
            """,
        )

        matrix.writeConfig(key = "show_transaksi_image", value = true)

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-info-expansion")))
            .perform(click())

        // then
        onView(withText(lector.literate(resId = R.string.record_transaksi)))
            .check(matches(isDisplayed()))

        onView(withText(lector.literate(resId = R.string.income_label)))
            .check(matches(isSelected()))

        onView(withText(lector.literate(resId = R.string.record_transaksi)))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-category")))
            .check(matches(isDisplayed()))

        onView(withText("Penjualan"))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-balance-main-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-date-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-payment-status-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-contact-primary-form")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-image-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-note-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-contact-secondary-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-save-button")))
            .check(matches(isDisplayed()))
    }

    @Test
    fun contact_primary_displayed_when_payment_status_not_paid() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Penjualan",
                    "categoryId": "penjualan"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-payment-status-switch")))
            .perform(click())

        onView(withTagValue(`is`("income-info-expansion")))
            .perform(click())

        // then
        onView(withTagValue(`is`("income-contact-primary-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-contact-secondary-form")))
            .check(matches(not(isDisplayed())))
    }

    @Test
    fun penjualan_selected_category() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Penjualan",
                    "categoryId": "penjualan"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        // then
        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isEnabled()))
    }

    @Test
    fun pendapatan_selected_category() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Pendapatan Lain-Lain",
                    "categoryId": "pendapatan"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-category-list")))
            .perform(scrollToPosition<TransactionCategoryViewHolder>(1))

        onView(withText("Pendapatan Lain-Lain"))
            .perform(click())

        // then
        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isEnabled()))
    }

    @Test
    fun jasa_selected_category() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Pendapatan Jasa/Komisi",
                    "categoryId": "jasa"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-category-list")))
            .perform(scrollToPosition<TransactionCategoryViewHolder>(1))

        onView(withText("Pendapatan Jasa/Komisi"))
            .perform(click())

        // then
        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isEnabled()))
    }

    @Test
    fun penambahan_selected_category() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Penambahan Modal",
                    "categoryId": "penambahan"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-category-list")))
            .perform(scrollToPosition<TransactionCategoryViewHolder>(1))

        onView(withText("Penambahan Modal"))
            .perform(click())

        // then
        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isEnabled()))
    }

    @Test
    fun lending_selected_category() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Terima Pinjaman",
                    "categoryId": "lending"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-category-list")))
            .perform(scrollToPosition<TransactionCategoryViewHolder>(1))

        onView(withText("Terima Pinjaman"))
            .perform(click())

        // then
        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isNotEnabled()))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isNotChecked()))
    }

    @Test
    fun piutang_selected_category() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Penagihan Utang/Cicilan",
                    "categoryId": "piutang"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-category-list")))
            .perform(scrollToPosition<TransactionCategoryViewHolder>(1))

        onView(withText("Penagihan Utang/Cicilan"))
            .perform(click())

        // then
        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isNotEnabled()))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isChecked()))
    }

    @Test
    fun pendapatanDiLuarUsaha_selected_category() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Pendapatan Di Luar Usaha",
                    "categoryId": "pendapatanDiLuarUsaha"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-category-list")))
            .perform(scrollToPosition<TransactionCategoryViewHolder>(1))

        onView(withText("Pendapatan Di Luar Usaha"))
            .perform(click())

        // then
        onView(withTagValue(`is`("income-add-product-container")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-balance-secondary-form")))
            .check(matches(not(isDisplayed())))

        onView(withTagValue(`is`("income-payment-status-switch")))
            .check(matches(isEnabled()))
    }

    @Test
    fun product_inventory_navigation() = test {
        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-add-product")))
            .perform(click())

        // then
        onView(withText(lector.literate(resId = R.string.item_list)))
            .check(matches(isDisplayed()))
    }

    @Test
    fun product_inventory_result() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Penjualan",
                    "categoryId": "penjualan"
                }
            ]
            """,
        )

        databaseManager.insert(
            table = "products",
            values = mapOf(
                "product_id" to "1",
                "book_id" to "0000011111",
                "name" to "Product 1",
                "unit_price" to 1000.0,
                "buying_price" to 500.0,
                "has_updated_price" to false,
                "favourite" to false,
                "is_imported_from_catalog" to false,
                "track_inventory" to 0,
                "deleted" to 0,
            )
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-add-product")))
            .perform(click())

        onView(withId(R.id.add_button))
            .perform(click())

        onView(withId(R.id.confirmation_button))
            .perform(click())

        // then
        onView(withText("Product 1"))
            .check(matches(isDisplayed()))

        onView(withTagValue(`is`("income-balance-main-form")))
            .check(matches(not(isDisplayed())))

        onView(
            allOf(
                withId(R.id.balance_primary_input),
                withText(Utility.priceToString(1000.0, false)),
            )
        ).check(matches(isDisplayed()))
    }

    @Test
    fun contact_mandatory_error() = test {
        // given
        matrix.writeConfig(
            key = "category_transaction_credit_new",
            value = """
            [
                {
                    "categoryImage": "",
                    "categoryName": "Penjualan",
                    "categoryId": "penjualan"
                }
            ]
            """,
        )

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-payment-status-switch")))
            .perform(click())

        onView(withTagValue(`is`("income-save-button")))
            .perform(click())

        // then
        onView(
            allOf(
                withTagValue(`is`("income-contact-primary-error")),
                withText(lector.literate(resId = R.string.add_contact_message)),
            )
        ).check(matches(isDisplayed()))
    }

    @Test
    fun error_shown_when_no_network() = test {
        // given
        networkMonitor.disableNetwork()

        // when
        launchActivity<TransactionRecordActivity>()

        onView(withTagValue(`is`("income-save-button")))
            .perform(click())

        // then
        val hasMessage = messageRecorder.hasToastMessage(
            message = lector.literate(resId = R.string.no_internet_error)
        )
        assertTrue(hasMessage)
    }
}
