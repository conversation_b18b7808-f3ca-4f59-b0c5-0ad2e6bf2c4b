package com.bukuwarung.feature.onboarding.form.fragment.category.screen

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.hasChildCount
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.isEnabled
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withText
import com.bukuwarung.R
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.matrix.api.WritableMatrix
import com.bukuwarung.base.screen.BaseScreenTest
import com.bukuwarung.base.telemetry.api.TraceableTelemetry
import dagger.hilt.android.testing.HiltAndroidTest
import org.hamcrest.Matchers.allOf
import org.hamcrest.Matchers.not
import org.junit.Assert.assertTrue
import org.junit.Test
import javax.inject.Inject

@HiltAndroidTest
class OnBoardingBusinessCategoryFormFragmentTest : BaseScreenTest() {
    @Inject
    lateinit var lector: Lector

    @Inject
    lateinit var matrix: WritableMatrix

    @Inject
    lateinit var telemetry: TraceableTelemetry

    @Test
    fun initialize_view() = test {
        // given
        matrix.writeConfig(
            key = "on_boarding_categories",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "3",
                    "name": "Makanan atau minuman",
                    "image_url": ""
                },
                {
                    "id": 2,
                    "resource_id": "0",
                    "name": "Pulsa, token listrik, dan tagihan",
                    "image_url": ""
                },
                {
                    "id": 3,
                    "resource_id": "1",
                    "name": "Pakaian, tas atau aksesoris",
                    "image_url": ""
                }
            ]
            """,
        )

        // when
        launchFragment<OnBoardingBusinessCategoryFormFragment>()

        // then
        onView(
            allOf(
                withId(R.id.header_label),
                withText(lector.literate(resId = R.string.business_category)),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.description_label),
                withText(lector.literate(resId = R.string.business_category_selection)),
            )
        ).check(matches(isDisplayed()))

        onView(withId(R.id.business_category_list))
            .check(matches(hasChildCount(3)))

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Makanan atau minuman"),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Pulsa, token listrik, dan tagihan"),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Pakaian, tas atau aksesoris"),
            )
        ).check(matches(isDisplayed()))

        onView(withId(R.id.divider))
            .check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.business_category_proceed_button),
                withText(lector.literate(resId = R.string.next)),
            )
        ).check(
            matches(
                allOf(
                    isDisplayed(),
                    not(isEnabled()),
                )
            )
        )
    }

    @Test
    fun proceed_button_enabled_when_business_category_is_selected() = test {
        // given
        matrix.writeConfig(
            key = "on_boarding_categories",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "3",
                    "name": "Makanan atau minuman",
                    "image_url": ""
                }
            ]
            """,
        )

        // when
        launchFragment<OnBoardingBusinessCategoryFormFragment>()

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Makanan atau minuman"),
            )
        ).perform(click())

        // then
        onView(withId(R.id.business_category_proceed_button))
            .check(matches(isEnabled()))
    }

    @Test
    fun analytics_recorded_when_form_completes() = test {
        // given
        matrix.writeConfig(
            key = "on_boarding_categories",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "3",
                    "name": "Makanan atau minuman",
                    "image_url": ""
                }
            ]
            """,
        )

        // when
        launchFragment<OnBoardingBusinessCategoryFormFragment>()

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Makanan atau minuman"),
            )
        ).perform(click())

        onView(withId(R.id.business_category_proceed_button))
            .perform(click())

        // then
        val parameter = telemetry.events["continue_business_detail_dialogue"] ?: mapOf()
        assertTrue(parameter["page_name"] == "business_category")
        assertTrue(parameter["type"] == "3")
    }
}
