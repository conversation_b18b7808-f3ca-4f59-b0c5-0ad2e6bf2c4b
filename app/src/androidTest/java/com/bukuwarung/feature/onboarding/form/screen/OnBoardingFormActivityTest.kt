package com.bukuwarung.feature.onboarding.form.screen

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.action.ViewActions.closeSoftKeyboard
import androidx.test.espresso.action.ViewActions.typeText
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.hasChildCount
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withText
import com.bukuwarung.R
import com.bukuwarung.base.matrix.api.WritableMatrix
import com.bukuwarung.base.remote.api.RemoteServer
import com.bukuwarung.base.remote.api.model.FakeResponse
import com.bukuwarung.base.screen.BaseScreenTest
import dagger.hilt.android.testing.HiltAndroidTest
import org.hamcrest.Matchers.allOf
import org.junit.Ignore
import org.junit.Test
import javax.inject.Inject

@HiltAndroidTest
class OnBoardingFormActivityTest : BaseScreenTest() {
    @Inject
    lateinit var matrix: WritableMatrix

    @Inject
    lateinit var remoteServer: RemoteServer

    @Test
    fun initialize_view() = test {
        // given
        matrix.writeConfig(
            key = "user_onboarding_forms",
            value = "business_name,business_category,usage_goal",
        )

        // when
        launchActivity<OnBoardingFormActivity>()

        // then
        onView(withId(R.id.view_pager))
            .check(matches(isDisplayed()))

        onView(withId(R.id.step_container))
            .check(matches(hasChildCount(3)))
    }

    @Test
    @Ignore("MainActivity hasn't supported testing environment. Hence, this test will fail after the navigation.")
    fun complete_forms() = test {
        // given
        matrix.writeConfig(
            key = "user_onboarding_forms",
            value = "business_name,business_category,usage_goal,usage_past",
        )

        setupBusinessNameFormFixture()
        setupBusinessCategoryFormFixture()
        setupBusinessGoalFormFixture()
        setupBusinessPastUsageFormFixture()

        // when
        launchActivity<OnBoardingFormActivity>()

        assertBusinessNameFormDisplayed()
        completeBusinessNameForm()

        assertBusinessCategoryFormDisplayed()
        completeBusinessCategoryForm()

        assertBusinessGoalFormDisplayed()
        completeBusinessGoalForm()

        assertBusinessPastUsageFormDisplayed()
        completeBusinessPastUsageForm()
    }

    private fun setupBusinessNameFormFixture() {
        remoteServer.submit(
            responses = listOf(
                FakeResponse(
                    method = "POST",
                    path = "/risk/api/blacklist/name/validate",
                    body = """
                    {}
                    """,
                    code = 200,
                ),
            ),
        )
    }

    private fun setupBusinessCategoryFormFixture() {
        matrix.writeConfig(
            key = "on_boarding_categories",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "3",
                    "name": "Makanan atau minuman",
                    "image_url": ""
                }
            ]
            """,
        )
    }

    private fun setupBusinessGoalFormFixture() {
        matrix.writeConfig(
            key = "on_boarding_usage_goal_options",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "2",
                    "name": "Catat Transaksi",
                    "image_url": ""
                }
            ]
            """,
        )
    }

    private fun setupBusinessPastUsageFormFixture() {
        matrix.writeConfig(
            key = "on_boarding_usage_past_options",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "1",
                    "name": "Aplikasi HP",
                    "image_url": ""
                }
            ]
            """,
        )
    }

    private fun assertBusinessNameFormDisplayed() {
        onView(withId(R.id.business_name_form))
            .check(matches(isDisplayed()))
    }

    private fun assertBusinessCategoryFormDisplayed() {
        onView(withId(R.id.business_category_form))
            .check(matches(isDisplayed()))
    }

    private fun assertBusinessGoalFormDisplayed() {
        onView(withId(R.id.business_goal_form))
            .check(matches(isDisplayed()))
    }

    private fun assertBusinessPastUsageFormDisplayed() {
        onView(withId(R.id.business_past_usage_form))
            .check(matches(isDisplayed()))
    }

    private fun completeBusinessNameForm() {
        onView(withId(R.id.business_name_input))
            .perform(
                typeText("Hello World"),
                closeSoftKeyboard(),
            )

        onView(withId(R.id.business_name_proceed_button))
            .perform(click())
    }

    private fun completeBusinessCategoryForm() {
        onView(
            allOf(
                withId(R.id.category_label),
                withText("Makanan atau minuman"),
            )
        ).perform(click())

        onView(withId(R.id.business_category_proceed_button))
            .perform(click())
    }

    private fun completeBusinessGoalForm() {
        onView(
            allOf(
                withId(R.id.category_label),
                withText("Catat Transaksi"),
            )
        ).perform(click())

        onView(withId(R.id.business_goal_proceed_button))
            .perform(click())
    }

    private fun completeBusinessPastUsageForm() {
        onView(
            allOf(
                withId(R.id.category_label),
                withText("Aplikasi HP"),
            )
        ).perform(click())

        onView(withId(R.id.business_past_usage_proceed_button))
            .perform(click())
    }
}
