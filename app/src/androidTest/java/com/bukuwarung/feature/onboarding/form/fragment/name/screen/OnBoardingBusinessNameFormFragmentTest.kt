package com.bukuwarung.feature.onboarding.form.fragment.name.screen

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.clearText
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.action.ViewActions.closeSoftKeyboard
import androidx.test.espresso.action.ViewActions.typeText
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.hasFocus
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.isEnabled
import androidx.test.espresso.matcher.ViewMatchers.withHint
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withText
import com.bukuwarung.R
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.messenger.api.MessageRecorder
import com.bukuwarung.base.remote.api.RemoteServer
import com.bukuwarung.base.remote.api.WritableNetworkMonitor
import com.bukuwarung.base.remote.api.model.FakeResponse
import com.bukuwarung.base.screen.BaseScreenTest
import com.bukuwarung.base.telemetry.api.TraceableTelemetry
import dagger.hilt.android.testing.HiltAndroidTest
import org.hamcrest.Matchers.allOf
import org.hamcrest.Matchers.not
import org.junit.Assert.assertTrue
import org.junit.Test
import javax.inject.Inject

@HiltAndroidTest
class OnBoardingBusinessNameFormFragmentTest : BaseScreenTest() {
    @Inject
    lateinit var lector: Lector

    @Inject
    lateinit var networkMonitor: WritableNetworkMonitor

    @Inject
    lateinit var remoteServer: RemoteServer

    @Inject
    lateinit var telemetry: TraceableTelemetry

    @Inject
    lateinit var messageRecorder: MessageRecorder

    @Test
    fun initialize_view() = test {
        // when
        launchFragment<OnBoardingBusinessNameFormFragment>()

        // then
        onView(
            allOf(
                withId(R.id.header_label),
                withText(lector.literate(resId = R.string.greetings)),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.business_name_input),
                withHint(lector.literate(resId = R.string.business_name_input)),
            )
        ).check(
            matches(
                allOf(
                    isDisplayed(),
                    hasFocus(),
                    withText(""),
                )
            )
        )

        onView(withId(R.id.business_name_error_label))
            .check(matches(not(isDisplayed())))

        onView(withId(R.id.divider))
            .check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.business_name_proceed_button),
                withText(lector.literate(resId = R.string.next)),
            )
        ).check(
            matches(
                allOf(
                    isDisplayed(),
                    not(isEnabled()),
                )
            )
        )
    }

    @Test
    fun proceed_button_enabled_when_business_name_not_blank() = test {
        // when (1)
        launchFragment<OnBoardingBusinessNameFormFragment>()

        onView(withId(R.id.business_name_input))
            .perform(typeText("Hello World"))

        // then (1)
        onView(withId(R.id.business_name_proceed_button))
            .check(matches(isEnabled()))

        // when (2)
        onView(withId(R.id.business_name_input))
            .perform(clearText())

        // then (2)
        onView(withId(R.id.business_name_proceed_button))
            .check(matches(not(isEnabled())))
    }

    @Test
    fun error_shown_when_business_name_contains_invalid_char() = test {
        // when
        launchFragment<OnBoardingBusinessNameFormFragment>()

        onView(withId(R.id.business_name_input))
            .perform(
                typeText("123!@#"),
                closeSoftKeyboard(),
            )

        onView(withId(R.id.business_name_proceed_button))
            .perform(click())

        // then
        onView(
            allOf(
                withId(R.id.business_name_error_label),
                withText(
                    lector.literate(
                        resId = R.string.business_name_invalid_char_message,
                        "(^\$*.[]{}()?-\"!#%&/\\,><':;|_~`) \uD83D\uDE03",
                    )
                ),
            )
        ).check(matches(isDisplayed()))
    }

    @Test
    fun error_hidden_when_business_name_is_updated() = test {
        // when
        launchFragment<OnBoardingBusinessNameFormFragment>()

        onView(withId(R.id.business_name_input))
            .perform(
                typeText("123!@#"),
                closeSoftKeyboard(),
            )

        onView(withId(R.id.business_name_proceed_button))
            .perform(click())

        onView(withId(R.id.business_name_input))
            .perform(typeText("x"))

        // then
        onView(withId(R.id.business_name_error_label))
            .check(matches(not(isDisplayed())))
    }

    @Test
    fun error_shown_when_no_network() = test {
        // given
        networkMonitor.disableNetwork()

        // when
        launchFragment<OnBoardingBusinessNameFormFragment>()

        onView(withId(R.id.business_name_input))
            .perform(
                typeText("Hello World"),
                closeSoftKeyboard(),
            )

        onView(withId(R.id.business_name_proceed_button))
            .perform(click())

        // then
        val hasMessage = messageRecorder.hasToastMessage(
            message = lector.literate(resId = R.string.no_internet_error)
        )
        assertTrue(hasMessage)
    }

    @Test
    fun error_shown_when_business_name_blacklisted() = test {
        // given
        remoteServer.submit(
            responses = listOf(
                FakeResponse(
                    method = "POST",
                    path = "/risk/api/blacklist/name/validate",
                    body = """
                    {
                        "message": "Business name is blacklisted"
                    }
                    """,
                    code = 422,
                ),
            ),
        )

        // when
        launchFragment<OnBoardingBusinessNameFormFragment>()

        onView(withId(R.id.business_name_input))
            .perform(
                typeText("Hello World"),
                closeSoftKeyboard(),
            )

        onView(withId(R.id.business_name_proceed_button))
            .perform(click())

        // then
        onView(
            allOf(
                withId(R.id.business_name_error_label),
                withText(
                    lector.literate(
                        resId = R.string.change_name_of_business_msg_2,
                        "HELLO WORLD",
                    )
                ),
            )

        ).check(matches(isDisplayed()))
    }

    @Test
    fun analytics_recorded_when_form_completes() = test {
        // given
        remoteServer.submit(
            responses = listOf(
                FakeResponse(
                    method = "POST",
                    path = "/risk/api/blacklist/name/validate",
                    body = """
                    {}
                    """,
                    code = 200,
                ),
            ),
        )

        // when
        launchFragment<OnBoardingBusinessNameFormFragment>()

        onView(withId(R.id.business_name_input))
            .perform(
                typeText("Hello World"),
                closeSoftKeyboard(),
            )

        onView(withId(R.id.business_name_proceed_button))
            .perform(click())

        // then
        val parameter = telemetry.events["continue_business_detail_dialogue"] ?: mapOf()
        assertTrue(parameter["page_name"] == "business_name")
        assertTrue(parameter["business_name"] == "Hello World")
    }
}
