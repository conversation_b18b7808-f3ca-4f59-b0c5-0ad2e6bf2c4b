package com.bukuwarung.feature.onboarding.form.fragment.past.screen

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.hasChildCount
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.isEnabled
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withText
import com.bukuwarung.R
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.matrix.api.WritableMatrix
import com.bukuwarung.base.screen.BaseScreenTest
import com.bukuwarung.base.telemetry.api.TraceableTelemetry
import dagger.hilt.android.testing.HiltAndroidTest
import org.hamcrest.Matchers.allOf
import org.hamcrest.Matchers.not
import org.junit.Assert.assertTrue
import org.junit.Test
import javax.inject.Inject

@HiltAndroidTest
class OnBoardingBusinessPastUsageFormFragmentTest : BaseScreenTest() {
    @Inject
    lateinit var lector: Lector

    @Inject
    lateinit var matrix: WritableMatrix

    @Inject
    lateinit var telemetry: TraceableTelemetry

    @Test
    fun initialize_view() = test {
        // given
        matrix.writeConfig(
            key = "on_boarding_usage_past_options",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "1",
                    "name": "Aplikasi HP",
                    "image_url": ""
                },
                {
                    "id": 2,
                    "resource_id": "2",
                    "name": "Microsoft Excel",
                    "image_url": ""
                },
                {
                    "id": 3,
                    "resource_id": "3",
                    "order": 3,
                    "name": "Catat di buku",
                    "image_url": ""
                }
            ]
            """,
        )

        // when
        launchFragment<OnBoardingBusinessPastUsageFormFragment>()

        // then
        onView(
            allOf(
                withId(R.id.header_label),
                withText(lector.literate(resId = R.string.business_past_usage)),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.sub_header_label),
                withText(lector.literate(resId = R.string.business_past_usage_description)),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.caption_label),
                withText(lector.literate(resId = R.string.select_one)),
            )
        ).check(matches(isDisplayed()))

        onView(withId(R.id.business_past_usage_list))
            .check(matches(hasChildCount(3)))

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Aplikasi HP"),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Microsoft Excel"),
            )
        ).check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Catat di buku"),
            )
        ).check(matches(isDisplayed()))

        onView(withId(R.id.divider))
            .check(matches(isDisplayed()))

        onView(
            allOf(
                withId(R.id.business_past_usage_proceed_button),
                withText(lector.literate(resId = R.string.next)),
            )
        ).check(
            matches(
                allOf(
                    isDisplayed(),
                    not(isEnabled()),
                )
            )
        )
    }

    @Test
    fun proceed_button_enabled_when_business_past_usage_is_selected() = test {
        // given
        matrix.writeConfig(
            key = "on_boarding_usage_past_options",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "1",
                    "name": "Aplikasi HP",
                    "image_url": ""
                }
            ]
            """,
        )

        // when
        launchFragment<OnBoardingBusinessPastUsageFormFragment>()

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Aplikasi HP"),
            )
        ).perform(click())

        // then
        onView(withId(R.id.business_past_usage_proceed_button))
            .check(matches(isEnabled()))
    }

    @Test
    fun analytics_recorded_when_form_completes() = test {
        // given
        matrix.writeConfig(
            key = "on_boarding_usage_past_options",
            value = """
            [
                {
                    "id": 1,
                    "resource_id": "1",
                    "name": "Aplikasi HP",
                    "image_url": ""
                }
            ]
            """,
        )

        // when
        launchFragment<OnBoardingBusinessPastUsageFormFragment>()

        onView(
            allOf(
                withId(R.id.category_label),
                withText("Aplikasi HP"),
            )
        ).perform(click())

        onView(withId(R.id.business_past_usage_proceed_button))
            .perform(click())

        // then
        val parameter = telemetry.events["continue_business_detail_dialogue"] ?: mapOf()
        assertTrue(parameter["page_name"] == "usage_past")
        assertTrue(parameter["usage_past"] == "Aplikasi HP")
    }
}
