package com.bukuwarung.activities

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.*
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.filters.LargeTest
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.rule.ActivityTestRule
import androidx.test.rule.GrantPermissionRule
import androidx.test.runner.AndroidJUnit4
import androidx.test.uiautomator.UiDevice
import com.bukuwarung.R
import com.bukuwarung.*
import com.bukuwarung.actions.auth.LoginAction
import com.bukuwarung.actions.home.CustomerTabAction
import com.bukuwarung.actions.home.HomeAction
import com.bukuwarung.actions.transaction.TransactionType
import com.bukuwarung.actions.utang.*
import org.hamcrest.Matchers.allOf
import org.junit.Before
import org.junit.FixMethodOrder
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.MethodSorters
import java.util.*

@LargeTest
@RunWith(AndroidJUnit4::class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
class UtangFlowTest {

    private val USER_ID = Constants.USER_ID

    @Rule
    @JvmField
    var mActivityTestRule = ActivityTestRule(SplashActivity::class.java, false, false)

    @Rule
    @JvmField
    var mGrantPermissionRule =
            GrantPermissionRule.grant(
                    "android.permission.READ_CONTACTS",
                    "android.permission.WRITE_CONTACTS",
                    "android.permission.INTERNET")

    private var mUiDevice: UiDevice? = null

    @Before
    @Throws(Exception::class)
    fun before() {
        mUiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
    }

    @Test
    fun A_createsNewCustomerTest() {
        // data exists from prev test (transaction)
//        clearData()

        mActivityTestRule.launchActivity(null)

        // data exists from prev test (transaction)
//        LoginAction.doNormalLogin(mUiDevice!!, USER_ID)

        waitForExecution(millisToWait = 6000)

        HomeAction.goToUtangTab()

        CustomerTabAction.findsTutorialView()

        CustomerTabAction.checkTabVisible()

        CustomerTabAction.findsHomeIcons()

        CustomerTabAction.checkBusinessName()

        UtangAction.createNewCustomer(utangType = UtangType.EXPENSE, customerName = "Test Contact")

        CustomerTabAction.checkCustomerExist(customerName = "Test Contact", formattedBalance = "1")

        CustomerTabAction.checkCustomerCount(1)

        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "1", formattedUtangSaya = "0")

        CustomerTabAction.findsTopIcons()

        CustomerTabAction.findsTabFilter()
    }

    @Test
    fun B_crudUtangForOldCustomersTest() {
        mActivityTestRule.launchActivity(null)

        waitForExecution(millisToWait = 5000)

        CustomerTabAction.checkCustomerExist("Test Contact",
                formattedBalance = "1", balanceState = "Utang Pelanggan")

        val testContactRow = onView(
                allOf(withText("Test Contact"), isDisplayed())
        )

        testContactRow.perform(click())

        UtangAction.utangDetail_findsTopComponentForExpense(
                customerName = "Test Contact",
                amountFormatted = "1"
        )
        UtangAction.utangDetail_findsExpenseIcon()

        UtangAction.utangDetail_createNewExpense(note="Keper", amount = 1)

        UtangAction.utangDetail_checkCustomerBalance("2")

        UtangAction.utangDetail_findsUtangWithNote("keperluan")

        UtangAction.utangDetail_setNewCollectingDate(mUiDevice = mUiDevice)

        UtangAction.utangDetail_createNewIncome("pemasukan", amount = 3)

        UtangAction.utangDetail_checkCustomerBalance("1")

        UtangAction.utangDetail_findsUtangWithNote("pemasukan")

        UtangAction.utangDetail_utangItem_findsCreditWithAmt("3")

        UtangAction.utangDetail_createNewExpense(amount = 1)

        UtangAction.utangDetail_checkCustomerBalance("0")

        UtangAction.utangDetail_checkLunas()

        // add another utang and see if collecting date is not set
        UtangAction.utangDetail_createNewExpense(note="Another", amount = 8)

        UtangAction.utangDetail_findsCollectingDateNotSet()

        UtangAction.utangDetail_findsUtangWithNote("Another")

        UtangAction.utangDetail_goToUtangWithAmount("8")

        UtangDetailActions.checkBasicViews(
                transactionType = TransactionType.EXPENSE,
                formattedAmt = "8",
                note = "Another",
                contactName = "Test Contact"
        )

        UtangDetailActions.clickDeleteBtnAndCancel()

        UtangDetailActions.clickDeleteBtnAndOk()

        UtangAction.utangDetail_checkUserInfoAppbar("Test Contact")

        UtangAction.utangDetail_checkPdfButton()

        UtangAction.utangDetail_goToUtangWithAmount("3")

        UtangDetailActions.checkBasicViews(
                transactionType = TransactionType.INCOME,
                formattedAmt = "3",
                note = "pemasukan",
                contactName = "Test Contact"
        )

        UtangDetailActions.clickDeleteBtnAndCancel()

        UtangDetailActions.goToEditTrx()

        UtangAction.editUtang_checkBasicState(isExpense = false)

        UtangAction.editUtang_ClicksBackThenCancels_shouldSeePrompt()

        UtangAction.editUtang_OpensKeyboard()

        // change to 65
        UtangAction.editUtang_ChangeAmount(65)

        UtangDetailActions.checkBasicViews(
                transactionType = TransactionType.INCOME,
                formattedAmt = "65",
                note = "pemasukan",
                contactName = "Test Contact"
        )

        UtangDetailActions.goToEditTrx()

        // change note to New Note
        UtangAction.editUtang_ChangeNote("New Note")

        UtangDetailActions.checkBasicViews(
                transactionType = TransactionType.INCOME,
                formattedAmt = "65",
                note = "New Note",
                contactName = "Test Contact"
        )

        UtangDetailActions.goToEditTrx()

        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_MONTH, 15)

        // change date to New Date
        UtangAction.editUtang_ChangeDate(
                15,
                mUiDevice)

        UtangDetailActions.checkBasicViews(
                transactionType = TransactionType.INCOME,
                formattedAmt = "65",
                note = "New Note",
                contactName = "Test Contact",
                date = calendar.time
        )

        UtangDetailActions.pressBack()
        
        UtangAction.utangDetail_utangItem_findsCreditWithAmt("65")
        UtangAction.utangDetail_utangItem_findsCreditWithAmt("3", flag = false)
        // old utang should disappear

        UtangAction.utangDetail_findsUtangWithNote("New Note")
        UtangAction.utangDetail_findsUtangWithDate(calendar.time)

        UtangAction.utangDetail_goToUtangWithAmount("1")

        UtangDetailActions.clickDeleteBtnAndOk()

        pressBack()

        UtangAction.utangDetail_pressBack()

        CustomerTabAction.checkCustomerExist("Test Contact",
                formattedBalance = "63", balanceState = "Utang Saya")

        CustomerTabAction.checkCustomerCount(1)

        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "0", formattedUtangSaya = "63")
    }

    @Test
    fun C_createAnotherCustomerAndDeletesTest() {
        mActivityTestRule.launchActivity(null)

        waitForExecution(millisToWait = 5000)

        UtangAction.createNewCustomer(
                utangType = UtangType.EXPENSE, customerName = "Ardian", amount = 5)

        CustomerTabAction.checkCustomerExist(
                "Ardian",
                formattedBalance = "5", balanceState = "Utang Pelanggan")

        CustomerTabAction.checkCustomerCount(2)

        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "5", formattedUtangSaya = "63")

        val testContactRow = onView(
                allOf(withText("Ardian"), isDisplayed())
        )

        testContactRow.perform(click())

        onView(
                allOf(withText("Ardian"), isDisplayed())
        ).perform(click())

        CustomerDetailAction.checkBasicViews(
                name = "Ardian"
        )

        CustomerDetailAction.changeSmsSwitchAndTest()

        CustomerDetailAction.changeNameAndTest("Ardian New")

        CustomerDetailAction.deleteCustomer(customerName = "Ardian New")

        CustomerTabAction.checkCustomerCount(1)

        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "0", formattedUtangSaya = "63")
    }

    @Test
    fun D_createAnotherCustomerWithPhoneAndFilterSearchTest() {
        mActivityTestRule.launchActivity(null)

        waitForExecution(millisToWait = 5000)

        UtangAction.createNewCustomer(
                utangType = UtangType.EXPENSE, customerName = "Agung", phoneNumber = "0812345678",amount = 10)

        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "10", formattedUtangSaya = "63")

        CustomerTabAction.checkCustomerExist(customerName = "Agung", formattedBalance = "10")
        CustomerTabAction.checkCustomerExist(customerName = "Test Contact", formattedBalance = "63",
                balanceState = "Utang Saya")

        CustomerTabAction.checkCustomerCount(2)

        CustomerTabAction.clickCustomerFilter("Utang Pelanggan")

        CustomerTabAction.checkCustomerExist(customerName = "Agung", formattedBalance = "10")
        CustomerTabAction.checkCustomerExist(customerName = "Test Contact", formattedBalance = "63",
                balanceState = "Utang Saya",exist = false)

        CustomerTabAction.checkCustomerCount(1)
        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "10", formattedUtangSaya = "0")

        CustomerTabAction.clickCustomerFilter("Utang Saya")

        CustomerTabAction.checkCustomerExist(customerName = "Agung", formattedBalance = "10", exist = false)
        CustomerTabAction.checkCustomerExist(customerName = "Test Contact", balanceState = "Utang Saya",
                formattedBalance = "63")

        CustomerTabAction.checkCustomerCount(1)
        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "0", formattedUtangSaya = "63")

        val filterTabRv = onView(withId(R.id.filterTabRecyclerView))

        filterTabRv.perform(
                        ScrollToEndAction()
                )

        CustomerTabAction.clickCustomerFilter("Lunas")

        CustomerTabAction.checkCustomerExist(customerName = "Agung", formattedBalance = "10", exist = false)
        CustomerTabAction.checkCustomerExist(customerName = "Test Contact", formattedBalance = "63",
                balanceState = "Utang Saya", exist = false)

        CustomerTabAction.checkCustomerCount(0)
        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "0", formattedUtangSaya = "0")

        CustomerTabAction.checkDoesNotExist()

        CustomerTabAction.clickCustomerFilter("Semua")

        CustomerTabAction.searchCustomer(query = "Conta")

        CustomerTabAction.checkCustomerExist(customerName = "Agung", formattedBalance = "10", exist = false)
        CustomerTabAction.checkCustomerExist(customerName = "Test Contact", formattedBalance = "63",
                balanceState = "Utang Saya")

        CustomerTabAction.closeSearch()

        CustomerTabAction.checkCustomerExist(customerName = "Agung", formattedBalance = "10")
        CustomerTabAction.checkCustomerExist(customerName = "Test Contact", formattedBalance = "63",
                balanceState = "Utang Saya")
        CustomerTabAction.checkCustomerCount(2)
        CustomerTabAction.findsTopTab(formattedUtangPelanggan = "10", formattedUtangSaya = "63")

    }

    @Test
    fun E_utangReportTest() {
        mActivityTestRule.launchActivity(null)

        waitForExecution(millisToWait = 5000)

        CustomerTabAction.openUtangReport()

        UtangReportAction.checkBasicTexts()

        UtangReportAction.checkIncomeTotal("65")

        UtangReportAction.checkExpenseTotal("12")

        UtangReportAction.checkAmountTotal("53")

        UtangReportAction.checkUtangCount(4, 3, 1)

        UtangReportAction.findsExpenseWithAmt("10")

        UtangReportAction.findsIncomeWithAmt("65")

        UtangReportAction.findsItemWithName("Agung")

        UtangReportAction.findsItemWithName("Test Contact")

        val calendar = Calendar.getInstance()
        UtangReportAction.findsItemWithDate(calendar.time)
    }
}
