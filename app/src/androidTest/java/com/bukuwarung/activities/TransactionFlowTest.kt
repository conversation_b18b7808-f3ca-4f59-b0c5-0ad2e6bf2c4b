package com.bukuwarung.activities

import androidx.test.filters.LargeTest
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.rule.ActivityTestRule
import androidx.test.rule.GrantPermissionRule
import androidx.test.runner.AndroidJUnit4
import androidx.test.uiautomator.UiDevice
import com.bukuwarung.activities.SplashActivity
import com.bukuwarung.Constants
import com.bukuwarung.actions.auth.LoginAction
import com.bukuwarung.actions.home.HomeAction
import com.bukuwarung.actions.home.TransactionTabAction
import com.bukuwarung.actions.transaction.EditTransactionActions
import com.bukuwarung.actions.transaction.TransactionDetailActions
import com.bukuwarung.actions.transaction.TransactionType
import com.bukuwarung.clearData
import org.junit.Before
import org.junit.FixMethodOrder
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.MethodSorters
import java.util.*

@LargeTest
@RunWith(AndroidJUnit4::class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
class TransactionFlowTest {

    private val USER_ID = Constants.USER_ID

    @Rule
    @JvmField
    var mActivityTestRule = ActivityTestRule(SplashActivity::class.java, false, false)

    @Rule
    @JvmField
    var mGrantPermissionRule =
            GrantPermissionRule.grant(
                    "android.permission.READ_CONTACTS",
                    "android.permission.WRITE_CONTACTS",
                    "android.permission.INTERNET")

    private var mUiDevice: UiDevice? = null

    @Before
    @Throws(Exception::class)
    fun before() {
        mUiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
    }

    @Test
    fun A_createsNewTrxTest() {
        clearData()

        mActivityTestRule.launchActivity(null)

        LoginAction.doNormalLogin(mUiDevice!!, USER_ID)

        HomeAction.goToTransactionTab()

        TransactionTabAction.closeTutorial(mUiDevice!!)

        TransactionTabAction.checkEmptyViews()

        TransactionTabAction.createsNewTransaction(amount = 15, categoryName = "Bayar Listrik",
                note = "Transaksiku", transactionType = TransactionType.EXPENSE,
                productName = "Produkku")

        TransactionTabAction.findsTopTab(formattedIncome = "0", formattedExpense = "15")

        TransactionTabAction.checkTrxCount(1)

        TransactionTabAction.dateExist(Calendar.getInstance().time)

        TransactionTabAction.trxWithCategoryExist("Bayar Listrik")

        TransactionTabAction.trxWithAmtExist(formattedAmt = "15", transactionType = TransactionType.EXPENSE)
    }

    @Test
    fun B_editExistingTrxTest() {
        mActivityTestRule.launchActivity(null)

        TransactionTabAction.clickTrxWithAmt(formattedAmt = "15", transactionType = TransactionType.EXPENSE)

        TransactionDetailActions.checkBasicViews(
                transactionType = TransactionType.EXPENSE,
                formattedAmt = "15",
                note = "Transaksiku",
                categoryName = "Bayar Listrik",
                productName = "Produkku"
        )

        TransactionDetailActions.clickDeleteBtnAndCancel()

        TransactionDetailActions.goToEditTrx()

        EditTransactionActions.checkBasicViews(
                transactionType = TransactionType.EXPENSE,
                formattedAmt = "15",
                note = "Transaksiku",
                categoryName = "Bayar Listrik",
                productName = "Produkku"
        )

        EditTransactionActions.changeTrxType(TransactionType.INCOME)
        EditTransactionActions.changeTrxAmt(oldAmtFormatted = "15", newAmt = 26)
        EditTransactionActions.clickModal()
        EditTransactionActions.changeModalAmt(oldAmtFormatted = "0", newAmt = 20, newAmtFormatted = "20")
        EditTransactionActions.checkMargin(
                isProfit = true,
                newAmtFormatted = "6"
        )
        EditTransactionActions.changeCategory("Pemasukan")
        EditTransactionActions.changeNote("Transaksimu")
        EditTransactionActions.changeProductName("Produkmu")
        EditTransactionActions.changeTrxDate(15, mUiDevice!!)

        EditTransactionActions.clickBackAndCancels()

        EditTransactionActions.saveTrx()

        val newCalendar = Calendar.getInstance()
        newCalendar.set(Calendar.DAY_OF_MONTH, 15)

        TransactionDetailActions.checkBasicViews(
                transactionType = TransactionType.INCOME,
                formattedAmt = "26",
                note = "Transaksimu",
                categoryName = "Pemasukan",
                date = newCalendar.time,
                productName = "Produkmu"
        )

        TransactionDetailActions.pressBack()

        TransactionTabAction.trxWithCategoryExist("Pemasukan")
        TransactionTabAction.trxWithAmtExist(formattedAmt = "26", transactionType = TransactionType.INCOME)

        TransactionTabAction.findsTopTab(formattedIncome = "26", formattedExpense = "0")

        TransactionTabAction.checkTrxCount(1)

        TransactionTabAction.clickTrxWithAmt(formattedAmt = "26", transactionType = TransactionType.INCOME)

        TransactionDetailActions.clickDeleteBtnAndOk()

        TransactionTabAction.checkEmptyViews()
    }

}