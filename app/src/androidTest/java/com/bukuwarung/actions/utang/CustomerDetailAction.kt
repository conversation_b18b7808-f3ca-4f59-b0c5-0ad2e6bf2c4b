package com.bukuwarung.actions.utang

import androidx.test.espresso.Espresso
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import com.bukuwarung.doesNotExist
import com.bukuwarung.getInitial1Char
import com.bukuwarung.waitForExecution
import org.hamcrest.Matchers

class CustomerDetailAction {

    companion object {

        fun checkBasicViews(name: String, phoneNumber: String? = null, address: String? = null) {
            val initial = name.getInitial1Char()

            onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Profil Pelanggan")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.nameInitial),
                            ViewMatchers.withText(initial)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.customerName),
                            ViewMatchers.withText(name)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            if (!phoneNumber.isNullOrBlank()) {
                onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.mobile),
                                ViewMatchers.withText(phoneNumber)
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            }

            if (!address.isNullOrBlank()) {
                onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.address),
                                ViewMatchers.withText(address)
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            }

            onView(
                    Matchers.allOf(
                            ViewMatchers.withText("PENGATURAN")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withText("SMS transaksi")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Bahasa SMS")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Hapus Pelanggan")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun changeSmsSwitchAndTest() {
            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.switch_sms)
                    )
            ).check(ViewAssertions.matches(
                    ViewMatchers.isChecked()
            ))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.switch_sms)
                    )
            ).perform(click())

            Espresso.pressBack()

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.name)
                    )
            ).perform(click())

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.switch_sms)
                    )
            ).check(
                ViewAssertions.matches(
                   ViewMatchers.isNotChecked()
                )
            )
        }

        fun changeNameAndTest(newCustomerName: String) {
            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.edit)
                    )
            ).perform(click())

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.cameraIcon)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.customerName)
                    )
            ).perform(click())
                    .perform(ViewActions.replaceText(newCustomerName), ViewActions.closeSoftKeyboard())
                    .check(ViewAssertions.matches(ViewMatchers.withText(newCustomerName)))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.save)
                    )
            ).perform(click())

            Espresso.pressBack()

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.name)
                    )
            ).perform(click())

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.customerName)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.withText(newCustomerName)))
        }

        fun deleteCustomer(customerName:String) {
            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.delete_cst)
                    )
            ).perform(click())

            waitForExecution(millisToWait = 1000)

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText("Hapus Pelanggan")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.body),
                            ViewMatchers.withText("Semua transaksi dari pelanggan ini akan terhapus, beserta data pelanggan ini")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.ok),
                            ViewMatchers.withText("HAPUS")
                    )
            ).perform(click())

            waitForExecution(millisToWait = 1000)

            onView(
                    Matchers.allOf(
                            ViewMatchers.withText(customerName)
                    )
            ).doesNotExist()



        }

    }

}