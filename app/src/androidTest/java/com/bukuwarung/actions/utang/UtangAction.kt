package com.bukuwarung.actions.utang

import android.icu.text.SimpleDateFormat
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers
import androidx.test.uiautomator.UiDevice
import com.bukuwarung.R
import com.bukuwarung.*
import com.bukuwarung.actions.misc.DatePickerAction
import com.bukuwarung.actions.misc.KeyboardAction
import org.hamcrest.Matchers
import org.hamcrest.Matchers.not
import org.hamcrest.core.IsInstanceOf
import java.util.*

enum class UtangType {
    EXPENSE,
    INCOME
}

class UtangAction {

    companion object {

        fun createNewCustomer(utangType: UtangType = UtangType.EXPENSE, customerName: String = "Test Contact",
                              note: String = "keperluan", amount: Int = 1, phoneNumber: String? = null) {
            val extendedFloatingActionButton = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.addCustomerBtn),
                            ViewMatchers.isDisplayed()))
            extendedFloatingActionButton.perform(ViewActions.click())

            val switchId = if (utangType == UtangType.EXPENSE) {
                R.id.expense
            } else {
                R.id.income
            }
            val linearLayout2 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(switchId)
                    ))
            linearLayout2.perform(ViewActions.scrollTo(), ViewActions.click())

            KeyboardAction.enterAmountToKeyboard(amount)

            val textView2 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_input_amount_result),
                            ViewMatchers.withText(amount.toString()),
                            childAtPosition(
                                    Matchers.allOf(ViewMatchers.withId(R.id.amount_box),
                                            childAtPosition(
                                                    ViewMatchers.withId(R.id.transaction_input_amount_layout),
                                                    1)),
                                    1),
                            ViewMatchers.isDisplayed()))
            textView2.check(matches(ViewMatchers.withText(amount.toString())))

            val appCompatTextView = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.phone), ViewMatchers.withText("Tambah Pelanggan"),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.formView),
                                            3),
                                    2)))
            appCompatTextView.perform(ViewActions.scrollTo(), ViewActions.click())

            val relativeLayout = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.new_contact_layout),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withClassName(Matchers.`is`("android.widget.LinearLayout")),
                                            0),
                                    0),
                            ViewMatchers.isDisplayed()))
            relativeLayout.perform(ViewActions.click())

            val appCompatEditText6 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.customerNm),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withClassName(Matchers.`is`("android.widget.LinearLayout")),
                                            2),
                                    0),
                            ViewMatchers.isDisplayed()))
            appCompatEditText6.perform(ViewActions.replaceText(customerName), ViewActions.closeSoftKeyboard())

            if (!phoneNumber.isNullOrBlank()) {
                val phone = Espresso.onView(
                        Matchers.allOf(ViewMatchers.withId(R.id.customerPhone),
                                ViewMatchers.isDisplayed()))
                phone.perform(ViewActions.replaceText(phoneNumber), ViewActions.closeSoftKeyboard())
            }

            val appCompatTextView2 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.btn_save), ViewMatchers.withText("Simpan"),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withClassName(Matchers.`is`("android.widget.LinearLayout")),
                                            2),
                                    1),
                            ViewMatchers.isDisplayed()))
            appCompatTextView2.perform(ViewActions.click())

            KeyboardAction.submitKeyboard()

            val initial: String = customerName.getInitial2Char()

            val textView3 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.firstLetter), ViewMatchers.withText(initial),
                            childAtPosition(
                                    Matchers.allOf(ViewMatchers.withId(R.id.phoneLayout),
                                            childAtPosition(
                                                    IsInstanceOf.instanceOf(LinearLayout::class.java),
                                                    0)),
                                    0),
                            ViewMatchers.isDisplayed()))
            textView3.check(matches(ViewMatchers.withText(initial)))

            val textView4 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.phone),
                            ViewMatchers.isDisplayed()))
            textView4.check(matches(ViewMatchers.withText(customerName)))

            val appCompatEditText7 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.note),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.additional_data_layout),
                                            0),
                                    1)))
            appCompatEditText7.perform(ViewActions.scrollTo(), ViewActions.replaceText(note),
                    ViewActions.closeSoftKeyboard())

            val materialButton2 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.save), ViewMatchers.withText("Simpan"),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.formView),
                                            5),
                                    1)))
            materialButton2.perform(ViewActions.scrollTo(), ViewActions.click())
        }

        fun utangDetail_createNewExpense(note: String? = null, amount: Int) {
            val materialButton3 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.debitBtn), ViewMatchers.withText("Berikan"),
                            childAtPosition(
                                    Matchers.allOf(ViewMatchers.withId(R.id.bottom),
                                            childAtPosition(
                                                    ViewMatchers.withId(R.id.transactionButtonLayout),
                                                    1)),
                                    0),
                            ViewMatchers.isDisplayed()))
            materialButton3.perform(ViewActions.click())

            val textView16 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_title), ViewMatchers.withText("Jumlah yang Anda Berikan"),
                            ViewMatchers.isDisplayed()))
            textView16.check(matches(ViewMatchers.withText("Jumlah yang Anda Berikan")))

            val inputAmt = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_input_amount_layout),
                            ViewMatchers.isDisplayed()))
            inputAmt.perform(ViewActions.click())

            KeyboardAction.enterAmountToKeyboard(amount)

            val textView9 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_input_amount_result),
                            ViewMatchers.withText(amount.toString()),
                            ViewMatchers.isDisplayed()))
            textView9.check(matches(ViewMatchers.withText(amount.toString())))

            KeyboardAction.submitKeyboard()

            if (!note.isNullOrBlank()) {
                val appCompatEditText8 = Espresso.onView(
                        Matchers.allOf(ViewMatchers.withId(R.id.transaction_note),
                                childAtPosition(
                                        childAtPosition(
                                                ViewMatchers.withId(R.id.formLayout),
                                                1),
                                        1),
                                ViewMatchers.isDisplayed()))
                appCompatEditText8.perform(ViewActions.replaceText(note), ViewActions.closeSoftKeyboard())
            }

            val materialButton4 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.btn_save_transaction), ViewMatchers.withText("Simpan")
                            ))
            materialButton4.perform(ViewActions.click())
        }

        fun utangDetail_createNewIncome(note: String?, amount: Int = 3) {
            val materialButton5 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.creditBtn), ViewMatchers.withText("Terima"),
                            childAtPosition(
                                    Matchers.allOf(ViewMatchers.withId(R.id.bottom),
                                            childAtPosition(
                                                    ViewMatchers.withId(R.id.transactionButtonLayout),
                                                    1)),
                                    1),
                            ViewMatchers.isDisplayed()))
            materialButton5.perform(ViewActions.click())

            val textView16 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_title), ViewMatchers.withText("Jumlah yang Anda Terima"),
                            ViewMatchers.isDisplayed()))
            textView16.check(matches(ViewMatchers.withText("Jumlah yang Anda Terima")))

            val inputAmt1 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_input_amount_layout),
                            ViewMatchers.isDisplayed()))
            inputAmt1.perform(ViewActions.click())

            KeyboardAction.enterAmountToKeyboard(amount)
            KeyboardAction.submitKeyboard()

            val appCompatEditText9 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_note),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.formLayout),
                                            1),
                                    1),
                            ViewMatchers.isDisplayed()))
            appCompatEditText9.perform(ViewActions.replaceText(note), ViewActions.closeSoftKeyboard())

            val materialButton6 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.btn_save_transaction), ViewMatchers.withText("Simpan"),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.constraint_layout_view_transaction),
                                            0),
                                    2)))
            materialButton6.perform(ViewActions.click())
        }

        fun utangDetail_checkCustomerBalance(amountFormatted: String) {
            val amountAppend = "Rp $amountFormatted"

            val textView10 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.customerBalance), ViewMatchers.withText(amountAppend),
                            childAtPosition(
                                    childAtPosition(
                                            IsInstanceOf.instanceOf(LinearLayout::class.java),
                                            0),
                                    1),
                            ViewMatchers.isDisplayed()))
            textView10.check(matches(ViewMatchers.withText(amountAppend)))
        }

        fun utangDetail_findsUtangWithNote(note: String) {
            val textView12 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.note), ViewMatchers.withText(note),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.main_container),
                                            0),
                                    1),
                            ViewMatchers.isDisplayed()))
            textView12.check(matches(ViewMatchers.isDisplayed()))
        }

        fun utangDetail_findsUtangWithDate(date: Date) {
            val dateFormat = SimpleDateFormat("dd MMM yyyy")
            val dateStr = dateFormat.format(date)

            val textView12 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.date), ViewMatchers.withText(dateStr),
                            ViewMatchers.isDisplayed()))
            textView12.check(matches(ViewMatchers.isDisplayed()))
        }

        fun utangDetail_utangItem_findsCreditWithAmt(amountFormatted: String,
                                                     flag: Boolean = true) {
            val amountAppend = "Rp $amountFormatted"

            val textView15 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.creditAmount), ViewMatchers.withText(amountAppend),
                            childAtPosition(
                                    Matchers.allOf(ViewMatchers.withId(R.id.main_container),
                                            childAtPosition(
                                                    IsInstanceOf.instanceOf(RelativeLayout::class.java),
                                                    1)),
                                    2),
                            ViewMatchers.isDisplayed()))

            if (flag) {
                textView15.check(matches(ViewMatchers.withText(amountAppend)))
            } else {
                textView15.doesNotExist()
            }
        }

        fun utangDetail_checkLunas() {
            val textView18 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.lunas_label), ViewMatchers.withText("Lunas"),
                            childAtPosition(
                                    childAtPosition(
                                            IsInstanceOf.instanceOf(LinearLayout::class.java),
                                            0),
                                    2),
                            ViewMatchers.isDisplayed()))
            textView18.check(matches(ViewMatchers.withText("Lunas")))

            utangDetail_findsTopComponentForLunas()
        }

        fun utangDetail_checkPdfButton() {
            val textView18 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.lunas_label), ViewMatchers.withText("Lunas"),
                            childAtPosition(
                                    childAtPosition(
                                            IsInstanceOf.instanceOf(LinearLayout::class.java),
                                            0),
                                    2),
                            ViewMatchers.isDisplayed()))
            textView18.check(matches(ViewMatchers.withText("Lunas")))
        }

        fun utangDetail_checkUserInfoAppbar(name:String) {
            val initial = name.getInitial1Char()

            val textView19 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.name), ViewMatchers.withText(name),
                            childAtPosition(
                                    Matchers.allOf(ViewMatchers.withId(R.id.customer_name_layout),
                                            childAtPosition(
                                                    ViewMatchers.withId(R.id.customerProfileLayout),
                                                    2)),
                                    0),
                            ViewMatchers.isDisplayed()))
            textView19.check(matches(ViewMatchers.withText(name)))

            val textView20 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.firstLetter), ViewMatchers.withText(initial))
            )
            textView20.check(matches(ViewMatchers.withText(initial)))
        }

        fun utangDetail_pressBack() {
            val backBtn = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.back_btn)
                    )
            )
            backBtn.perform(ViewActions.click())
        }

        fun utangDetail_findsTopComponentForExpense(customerName: String, amountFormatted: String,
                                                    paymentDueDate: String? = null) {
            val customerFirstWord = customerName.split(" ").first()
            val totalWord = "Total Utang $customerFirstWord"

            val amt = "Rp $amountFormatted"

            val paymentReminderDate = paymentDueDate ?: "Atur Tanggal Jatuh Tempo"

            val textView19 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.balanceStatus), ViewMatchers.withText(totalWord),
                            ViewMatchers.isDisplayed()))
            textView19.check(matches(ViewMatchers.withText(totalWord)))

            val amtText = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.customerBalance), ViewMatchers.withText(amt),
                            ViewMatchers.isDisplayed()))
            amtText.check(matches(ViewMatchers.withText(amt)))

            val duedateIcon = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.dueDateIcon),
                            ViewMatchers.isDisplayed()))
            duedateIcon.check(matches(ViewMatchers.withId(R.id.dueDateIcon)))

            val paymentReminderText = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.paymentReminderDate),
                            ViewMatchers.withText(paymentReminderDate),
                            ViewMatchers.isDisplayed()))
            paymentReminderText.check(matches(ViewMatchers.withText(paymentReminderDate)))
        }

        fun utangDetail_findsTopComponentForLunas() {

            val textView19 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.balanceStatus),
                            ViewMatchers.withText("Total Utang Anda"),
                            ViewMatchers.isDisplayed()))
            textView19.check(matches(
                    Matchers.allOf(
                            ViewMatchers.withText("Total Utang Anda")
                    )
            )
            )

            val amtText = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.customerBalance), ViewMatchers.withText("Rp 0"),
                            ViewMatchers.isDisplayed()))
            amtText.check(matches(ViewMatchers.withText("Rp 0")))

            val duedateIcon = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.dueDateIcon)))
            duedateIcon.check(
                    matches(
                            Matchers.allOf(
                                    not(ViewMatchers.isDisplayed())
                            )
                    )
            )

            val paymentReminderText = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.paymentReminderDate)
                    ))
            paymentReminderText.check(
                    matches(
                            Matchers.allOf(
                                    not(ViewMatchers.isDisplayed())
                            )
                    )
            )
        }

        fun utangDetail_findsExpenseIcon() {
            utangDetail_findsWaIcon()
            utangDetail_findsSmsIcon()
        }

        private fun utangDetail_findsWaIcon() {
            val backBtn = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.tvMenuWhatsApp)
                    )
            )
            backBtn.check(matches(ViewMatchers.withId(R.id.tvMenuWhatsApp)))
        }

        private fun utangDetail_findsSmsIcon() {
            val backBtn = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.tvMenuShare)
                    )
            )
            backBtn.check(matches(ViewMatchers.withId(R.id.tvMenuShare)))
        }

        fun utangDetail_goToUtangWithAmount(amountFormatted: String) {
            val amt = "Rp $amountFormatted"

            val testContactRow = Espresso.onView(
                    ViewMatchers.thatMatchesFirst(
                            ViewMatchers.withText(amt)
                    )
            )

            testContactRow.perform(ViewActions.click())
        }

        fun editUtang_checkBasicState(isExpense: Boolean = true) {
            val title = if (isExpense) {
                "Berikan"
            } else {
                "Terima"
            }

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Jumlah yang Anda $title")
                    )
            ).check(matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.transaction_date_icon)
                    )
            ).check(matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.transaction_note_icon)
                    )
            ).check(matches(ViewMatchers.isDisplayed()))

        }

        fun editUtang_ClicksBackThenCancels_shouldSeePrompt() {
            Espresso.pressBack()

            waitForExecution(millisToWait = 1000)

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Apakah Anda yakin ingin keluar?")
                    )
            ).check(matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Catatan transaksi yang dibuat tidak akan tersimpan.")
                    )
            ).check(matches(ViewMatchers.isDisplayed()))

            val cancelBtn = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_no)

                    )
            )
            cancelBtn
                    .perform(ViewActions.click())

            waitForExecution(millisToWait = 2000)

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Apakah Anda yakin ingin keluar?")
                    )
            ).doesNotExist()

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Catatan transaksi yang dibuat tidak akan tersimpan.")
                    )
            ).doesNotExist()
        }

        fun editUtang_OpensKeyboard() {
            val inputAmt = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_input_amount_layout),
                            ViewMatchers.isDisplayed()))
            inputAmt.perform(ViewActions.click())
        }

        fun editUtang_ChangeAmount(newAmount: Int) {

            val oldAmt = getText(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.transaction_input_amount_result)
                    )
            )

            oldAmt!!.forEach { _ ->
                KeyboardAction.delete1Char()
            }

            KeyboardAction.enterAmountToKeyboard(newAmount)

            KeyboardAction.submitKeyboard()

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("SIMPAN")
                    )
            ).perform(ViewActions.click())
        }

        fun editUtang_ChangeNote(newNote: String) {
            val noteET = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_note),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.formLayout),
                                            1),
                                    1),
                            ViewMatchers.isDisplayed()))

            noteET.perform(ViewActions.click())

            noteET.perform(ViewActions.replaceText(newNote), ViewActions.closeSoftKeyboard())

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("SIMPAN")
                    )
            ).perform(ViewActions.click())
        }

        fun editUtang_ChangeDate(newDate: Int,
                                 mUiDevice: UiDevice?) {

            val dateET = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.transaction_date),
                            ViewMatchers.isDisplayed()))

            dateET.perform(ViewActions.click())

            DatePickerAction.pickADate(newDate, mUiDevice!!)

            DatePickerAction.clicksOk(mUiDevice)

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("SIMPAN")
                    )
            ).perform(ViewActions.click())
        }

        fun utangDetail_findsCollectingDateNotSet() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Atur Tanggal Jatuh Tempo")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun utangDetail_setNewCollectingDate(mUiDevice: UiDevice?) {
            val calendar = Calendar.getInstance()
            val currentDate = calendar.get(Calendar.DAY_OF_MONTH)

            // only run calendar test on 28 date
            if (currentDate <= 28) {
                val newDate = currentDate+1
                calendar.set(Calendar.DAY_OF_MONTH, newDate)

                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withText("Atur Tanggal Jatuh Tempo")
                        )
                ).perform(ViewActions.click())

                DatePickerAction.pickADate(newDate, mUiDevice!!)

                DatePickerAction.clicksOk(mUiDevice!!)

                val dateFormat = SimpleDateFormat("dd MMM yyyy")
                val dateStr = dateFormat.format(calendar.time)

                val textView12 = Espresso.onView(
                        Matchers.allOf(ViewMatchers.withId(R.id.paymentReminderDate),
                                ViewMatchers.withText(dateStr),
                                ViewMatchers.isDisplayed()))
                textView12.check(matches(ViewMatchers.isDisplayed()))
            }
        }

    }

}