package com.bukuwarung.actions.utang

import androidx.test.espresso.Espresso
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import org.hamcrest.Matchers
import java.util.*

class UtangReportAction {

    companion object {

        fun checkIncomeTotal(formattedAmt: String) {
            val amt = "Rp $formattedAmt"
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.creditTotal),
                            ViewMatchers.withText(amt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.credit_caption)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun checkExpenseTotal(formattedAmt: String) {
            val amt = "Rp $formattedAmt"
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.debitTotal),
                            ViewMatchers.withText(amt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.debit_caption)
                            )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun checkAmountTotal(formattedAmt: String) {
            val amt = "Rp $formattedAmt"
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.net_total),
                            ViewMatchers.withText(amt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.net_total_label)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun checkUtangCount(totalCount: Int, expenseCount: Int, incomeCount: Int) {
            val total = "$totalCount Transaksi"
            val expense = "$expenseCount Transaksi"
            val income = "$incomeCount Transaksi"
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.transCount),
                            ViewMatchers.withText(total)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.debitCount),
                            ViewMatchers.withText(expense)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.creditCount),
                            ViewMatchers.withText(income)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun findsIncomeWithAmt(formattedAmt: String) {
            val amt = "Rp $formattedAmt"

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.credit),
                            ViewMatchers.withText(amt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun findsExpenseWithAmt(formattedAmt: String) {
            val amt = "Rp $formattedAmt"

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.debit),
                            ViewMatchers.withText(amt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun findsItemWithName(customerName: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.note),
                            ViewMatchers.thatMatchesFirst(
                                    ViewMatchers.withText(customerName)
                            )
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun findsItemWithDate(date: Date) {
            val dateFormat = android.icu.text.SimpleDateFormat("dd MMM yyyy")
            val dateStr = dateFormat.format(date)
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.date),
                            ViewMatchers.thatMatchesFirst(
                                    ViewMatchers.withText(dateStr)
                            )
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun checkBasicTexts() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Laporan Utang Piutang"),
                            ViewMatchers.withId(R.id.screen_title)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Pilih Tanggal Laporan")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Transaksi")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Berikan(-)")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Terima(+)")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("UNDUH LAPORAN")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

    }

}