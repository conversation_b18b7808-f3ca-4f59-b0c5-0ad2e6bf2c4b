package com.bukuwarung.actions.utang

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import com.bukuwarung.actions.transaction.TransactionType
import com.bukuwarung.allowMultipleViews
import org.hamcrest.Matchers
import java.text.SimpleDateFormat
import java.util.*

class UtangDetailActions {

 companion object {

     fun checkBasicViews(transactionType: TransactionType, formattedAmt: String,
                         contactName: String, date: Date = Calendar.getInstance().time,
                         note: String? = null) {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.title),
                         ViewMatchers.withText("Detail Utang Piutang")
                 )
         ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.editBtn)
                 )
         ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.deleteBtn)
                 )
         ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

         val drawableChosen = if (transactionType == TransactionType.EXPENSE) {
             R.drawable.ic_switch_utang
         } else {
             R.drawable.ic_switch_piutang
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.icon_transaction)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.withTagValue(
                                 Matchers.equalTo(drawableChosen)
                         )
                 )
         ))

         val textColor = if (transactionType == TransactionType.EXPENSE) {
             R.color.out_red
         } else {
             R.color.in_green
         }

         val textString = if (transactionType == TransactionType.EXPENSE) {
             R.string.utang_detail_header
         } else {
             R.string.piutang_detail_header
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.transaction_title)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed(),
                         ViewMatchers.hasTextColor(textColor),
                         ViewMatchers.withText(textString)
                 )
         ))

         val amt = "Rp $formattedAmt"
         allowMultipleViews {
             Espresso.onView(
                     Matchers.allOf(
                             ViewMatchers.withId(R.id.transaction_amount),
                             ViewMatchers.withText(amt)
                     )
             ).check(ViewAssertions.matches(
                     Matchers.allOf(
                             ViewMatchers.isDisplayed()
                     )
             ))
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.firstLetter)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed(),
                         ViewMatchers.withText(contactName.first().toString())
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.phone),
                         ViewMatchers.withText(contactName)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         val noteToCheck = if (!note.isNullOrBlank()) {
             note
         } else {
             "-"
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.noteIcon)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.note),
                         ViewMatchers.withText(noteToCheck)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         val formatter = SimpleDateFormat("dd MMM YYYY")
         val formattedDate = formatter.format(date)

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.dateIcon)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.dateTitle),
                         ViewMatchers.withText(formattedDate)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.btn_print),
                         ViewMatchers.withText("Cetak Struk")
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed(),
                         ViewMatchers.isEnabled()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.btn_share),
                         ViewMatchers.withText("Bagikan")
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed(),
                         ViewMatchers.isEnabled()
                 )
         ))

     }

     fun goToEditTrx() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.editBtn)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     private fun clickDeleteBtn() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.deleteBtn)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     private fun checkDeleteDialog() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.title),
                         ViewMatchers.withText("Hapus Transaksi")
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed()
                         )
                 )
         )

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.body),
                         ViewMatchers.withText("Transaksi ini akan dihapus. Lanjut?")
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed()
                         )
                 )
         )

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.cancel)
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed(),
                                 ViewMatchers.isEnabled()
                         )
                 )
         )

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.ok)
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed(),
                                 ViewMatchers.isEnabled()
                         )
                 )
         )
     }

     private fun clickDeleteCancel() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.cancel)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     private fun clickDeleteOk() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.ok)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     fun clickDeleteBtnAndCancel() {
         clickDeleteBtn()

         checkDeleteDialog()

         clickDeleteCancel()
     }

     fun clickDeleteBtnAndOk() {
         clickDeleteBtn()

         checkDeleteDialog()

         clickDeleteOk()
     }

     fun pressBack() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.closeBtn)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

 }

}