package com.bukuwarung.actions.transaction

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import com.bukuwarung.allowMultipleViews
import org.hamcrest.Matchers
import java.text.SimpleDateFormat
import java.util.*

class TransactionDetailActions {

 companion object {

     fun checkBasicViews(transactionType: TransactionType, formattedAmt: String,
                         categoryName: String, date: Date = Calendar.getInstance().time,
                         note: String? = null, productName: String? = null,
                         formattedModal: String? = null, formattedMargin: String? = null) {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.title),
                         ViewMatchers.withText("Detail Transaksi")
                 )
         ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.editBtn)
                 )
         ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.deleteBtn)
                 )
         ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

         val drawableChosen = if (transactionType == TransactionType.EXPENSE) {
             R.drawable.ic_switch_utang
         } else {
             R.drawable.ic_switch_piutang
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.icon_transaction)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.withTagValue(
                                 Matchers.equalTo(drawableChosen)
                         )
                 )
         ))

         val textColor = if (transactionType == TransactionType.EXPENSE) {
             R.color.out_red
         } else {
             R.color.in_green
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.transaction_title)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed(),
                         ViewMatchers.hasTextColor(textColor)
                 )
         ))

         val amt = "Rp $formattedAmt"
         allowMultipleViews {
             Espresso.onView(
                     Matchers.allOf(
                             ViewMatchers.withId(R.id.transaction_amount),
                             ViewMatchers.withText(amt)
                     )
             ).check(ViewAssertions.matches(
                     Matchers.allOf(
                             ViewMatchers.isDisplayed()
                     )
             ))
         }

         if (!formattedModal.isNullOrBlank() && !formattedMargin.isNullOrBlank()) {
             val modalAmt = "Rp $formattedModal"
             allowMultipleViews {
                 Espresso.onView(
                         Matchers.allOf(
                                 ViewMatchers.withId(R.id.transaction_amount_modal),
                                 ViewMatchers.withText(modalAmt)
                         )
                 ).check(ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed()
                         )
                 ))
             }

             val marginAmt = "Rp $formattedMargin"
             allowMultipleViews {
                 Espresso.onView(
                         Matchers.allOf(
                                 ViewMatchers.withId(R.id.profitAmountText),
                                 ViewMatchers.withText(marginAmt)
                         )
                 ).check(ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed()
                         )
                 ))
             }
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.categoryIcon)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.selectedCatTv),
                         ViewMatchers.withText(categoryName)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         val noteToCheck = if (!note.isNullOrBlank()) {
             note
         } else {
             "-"
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.noteIcon)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.note),
                         ViewMatchers.withText(noteToCheck)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         val productToCheck = if (!productName.isNullOrBlank()) {
             "$productName, 1"
         } else {
             "Detail Produk"
         }

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.productIcon)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.productText),
                         ViewMatchers.withText(productToCheck)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         val formatter = SimpleDateFormat("dd MMM YYYY")
         val formattedDate = formatter.format(date)

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.dateIcon)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.dateTitle),
                         ViewMatchers.withText(formattedDate)
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.btn_print),
                         ViewMatchers.withText("Cetak Struk")
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed(),
                         ViewMatchers.isEnabled()
                 )
         ))

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.btn_share),
                         ViewMatchers.withText("Bagikan")
                 )
         ).check(ViewAssertions.matches(
                 Matchers.allOf(
                         ViewMatchers.isDisplayed(),
                         ViewMatchers.isEnabled()
                 )
         ))

     }

     fun goToEditTrx() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.editBtn)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     private fun clickDeleteBtn() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.deleteBtn)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     private fun checkDeleteDialog() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.title),
                         ViewMatchers.withText("Hapus Transaksi")
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed()
                         )
                 )
         )

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.body),
                         ViewMatchers.withText("Transaksi ini akan dihapus. Lanjut?")
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed()
                         )
                 )
         )

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.cancel)
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed(),
                                 ViewMatchers.isEnabled()
                         )
                 )
         )

         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.ok)
                 )
         ).check(
                 ViewAssertions.matches(
                         Matchers.allOf(
                                 ViewMatchers.isDisplayed(),
                                 ViewMatchers.isEnabled()
                         )
                 )
         )
     }

     private fun clickDeleteCancel() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.cancel)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     private fun clickDeleteOk() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.ok)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

     fun clickDeleteBtnAndCancel() {
         clickDeleteBtn()

         checkDeleteDialog()

         clickDeleteCancel()
     }

     fun clickDeleteBtnAndOk() {
         clickDeleteBtn()

         checkDeleteDialog()

         clickDeleteOk()
     }

     fun pressBack() {
         Espresso.onView(
                 Matchers.allOf(
                         ViewMatchers.withId(R.id.closeBtn)
                 )
         ).perform(
                 ViewActions.click()
         )
     }

 }

}