package com.bukuwarung.actions.transaction

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.bukuwarung.R
import com.bukuwarung.actions.misc.DatePickerAction
import com.bukuwarung.actions.misc.KeyboardAction
import com.bukuwarung.allowMultipleViews
import com.bukuwarung.waitForExecution
import org.hamcrest.Matchers
import java.text.SimpleDateFormat
import java.util.*

class EditTransactionActions {

    companion object {

        fun checkBasicViews(transactionType: TransactionType, formattedAmt: String,
                            categoryName: String, date: Date = Calendar.getInstance().time,
                            note: String? = null, productName: String? = null) {
            Espresso.onView(
                Matchers.allOf(
                        ViewMatchers.withId(R.id.title),
                        ViewMatchers.withText("Edit transaksi")
                )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            val idChosen = if (transactionType == TransactionType.EXPENSE) {
                R.id.text_expense
            } else {
                R.id.text_income
            }

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(idChosen)
                    )
            ).check(ViewAssertions.matches(
                    Matchers.allOf(
                            ViewMatchers.isDisplayed(),
                            ViewMatchers.hasTextColor(R.color.white)
                    )
            ))

            allowMultipleViews {
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withText(formattedAmt)
                        )
                ).check(ViewAssertions.matches(
                        Matchers.allOf(
                                ViewMatchers.isDisplayed()
                        )
                ))
            }

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.selectedCatTv),
                            ViewMatchers.withText(categoryName)
                    )
            ).check(ViewAssertions.matches(
                    Matchers.allOf(
                            ViewMatchers.isDisplayed()
                    )
            ))

            if (!note.isNullOrBlank()) {
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.note),
                                ViewMatchers.withText(note)
                        )
                ).check(ViewAssertions.matches(
                        Matchers.allOf(
                                ViewMatchers.isDisplayed()
                        )
                ))
            }

            if (!productName.isNullOrBlank()) {
                val product = "$productName (1)"
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.productText),
                                ViewMatchers.withText(product)
                        )
                ).check(ViewAssertions.matches(
                        Matchers.allOf(
                                ViewMatchers.isDisplayed()
                        )
                ))
            }

            val formatter = SimpleDateFormat("dd MMM YYYY")
            val formattedDate = formatter.format(date)

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.dateTitle),
                            ViewMatchers.withText(formattedDate)
                    )
            ).check(ViewAssertions.matches(
                    Matchers.allOf(
                            ViewMatchers.isDisplayed()
                    )
            ))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_save),
                            ViewMatchers.withText("SIMPAN TRANSAKSI")
                    )
            ).check(ViewAssertions.matches(
                    Matchers.allOf(
                            ViewMatchers.isDisplayed(),
                            ViewMatchers.isEnabled()
                    )
            ))

        }

        fun changeTrxAmt(newAmt: Int, oldAmtFormatted: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.transaction_input_amount_layout)
                    )
            ).perform(ViewActions.click())

            oldAmtFormatted.forEach {
                KeyboardAction.delete1Char()
            }

            KeyboardAction.enterAmountToKeyboard(newAmt)

            KeyboardAction.submitKeyboard()
        }

        fun changeModalAmt(newAmt: Int, newAmtFormatted: String, oldAmtFormatted: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.modal_input_amount_layout)
                    )
            ).perform(ViewActions.click())

            oldAmtFormatted.forEach {
                KeyboardAction.delete1Char()
            }

            KeyboardAction.enterAmountToKeyboard(newAmt)

            KeyboardAction.submitKeyboard()

            Espresso.onView(
                    ViewMatchers.withId(R.id.balance_modal)
            ).check(ViewAssertions.matches(ViewMatchers.withText(newAmtFormatted)))
        }

        fun checkMargin(isProfit: Boolean = true, newAmtFormatted: String) {
            val text = if (isProfit) {
                "Keuntungan"
            } else {
                "Kerugian"
            }

            Espresso.onView(
                   Matchers.allOf(
                           ViewMatchers.withId(R.id.modalIndicatorText),
                           ViewMatchers.withText(text)
                   )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            val amt = "Rp $newAmtFormatted"
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.profitAmountText),
                            ViewMatchers.withText(amt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun changeCategory(newCategoryName: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.selectedCatTv)
                    )
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.openSearchBtn)
                    )
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.clear)
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.searchQueryBox),
                            ViewMatchers.withHint("Cari Disini")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.searchQueryBox)
                    )
            ).perform(
                    ViewActions.replaceText(newCategoryName),
                    ViewActions.closeSoftKeyboard()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText(newCategoryName)
                    )
            ).perform(
                    ViewActions.click()
            )
        }

        fun changeNote(note: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.note)
                    )
            ).perform(ViewActions.replaceText(note), ViewActions.closeSoftKeyboard())
        }

        fun changeProductName(productName: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productText)
                    )
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText("Detail Produk")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Tambah")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Simpan Produk")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            allowMultipleViews {
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.productNameET)
                        )
                ).check(
                        ViewAssertions.matches(ViewMatchers.isDisplayed())
                )
            }

            Espresso.onView(
                    ViewMatchers.thatMatchesFirst(
                            ViewMatchers.withId(R.id.productNameET)
                    )
            ).perform(
                    ViewActions.replaceText(productName), ViewActions.closeSoftKeyboard()
            )

            Espresso.onView(
                    ViewMatchers.withText("Simpan Produk")
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productText)
                    )
            ).check(
                    ViewAssertions.matches(
                            ViewMatchers.withText("$productName (1)")
                    )
            )
        }

        fun changeTrxType(transactionType: TransactionType) {
            val idChosen = if (transactionType == TransactionType.EXPENSE) {
                R.id.text_expense
            } else {
                R.id.text_income
            }

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(idChosen)
                    )
            ).perform(ViewActions.click())
        }

        fun clickModal() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.modalCheckBox)
                    )
            ).perform(ViewActions.click())
        }

        fun changeTrxDate(newDate: Int, mUiDevice: UiDevice) {
            val dateET = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.dateTitle),
                            ViewMatchers.isDisplayed()))

            dateET.perform(ViewActions.click())

            DatePickerAction.pickADate(newDate, mUiDevice)

            DatePickerAction.clicksOk(mUiDevice)
        }

        fun clickBackAndCancels() {
            Espresso.pressBack()

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Apakah Anda yakin ingin keluar?")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Catatan transaksi yang dibuat tidak akan tersimpan.")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_no)
                    )
            ).perform(ViewActions.click())

            waitForExecution(millisToWait = 2000)
        }

        fun saveTrx() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_save)
                    )
            ).perform(ViewActions.click())
        }

        fun deleteTrx() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_delete)
                    )
            ).perform(ViewActions.click())

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText("Hapus Transaksi")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.body),
                            ViewMatchers.withText("Transaksi ini akan dihapus. Lanjut?")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.ok)
                    )
            ).perform(ViewActions.click())
        }

    }

}