package com.bukuwarung.actions.transaction

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import org.hamcrest.Matchers
import java.text.SimpleDateFormat
import java.util.*

enum class TransactionType {
    INCOME,
    EXPENSE
}

class TransactionActions {

    companion object {

        fun checkEmptyViews() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText("Tambah Pengeluaran")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.text_expense),
                            ViewMatchers.withText("Pengeluaran")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Masukan Nominal")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("0")
                    )
            ).check(ViewAssertions.matches(
                    Matchers.allOf(
                            ViewMatchers.withText("0")
                    )
            ))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Informasi Tambahan")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.categoryIcon)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.selectedCatTv),
                            ViewMatchers.withText("Pilih kategori")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.note),
                            ViewMatchers.withHint("Keperluan (Tidak Wajib)")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            val calendar = Calendar.getInstance()
            val formatter = SimpleDateFormat("dd MMM yyyy")
            val formatted = formatter.format(calendar.time)

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.dateTitle),
                            ViewMatchers.withText(formatted)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productIcon)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productText),
                            ViewMatchers.withText("Detail Produk")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_save)
                    )
            ).check(
                    ViewAssertions.matches(
                            Matchers.allOf(
                                    ViewMatchers.isDisplayed(),
                                    ViewMatchers.isNotEnabled()
                            )
                    )
            )
        }

        fun checkModalCheck(isChecked: Boolean = false) {
            val matchers = if (isChecked) {
                ViewMatchers.isChecked()
            } else {
                ViewMatchers.isNotChecked()
            }

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Tambahkan harga pokok / modal")
                    )
            ).check(
                    ViewAssertions.matches(
                            Matchers.allOf(
                                    ViewMatchers.isDisplayed()
                            )
                    )
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.modalCheckBox)
                    )
            ).check(
                    ViewAssertions.matches(
                            Matchers.allOf(
                                    ViewMatchers.isDisplayed(),
                                    matchers
                            )
                    )
            )

            if (isChecked) {
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withText("Harga Pokok / Modal")
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withText("Tambahkan Modal jika Anda belum mencatatkan Transaksi Pengeluaran (Beli Stok) sebelumnya")
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            }
        }

        fun checkMargin(isProfit: Boolean = true, formattedMarginAmt: String) {
            val text = if (isProfit) {
                "Keuntungan"
            } else {
                "Kerugian"
            }

            val color = if (isProfit) {
                R.color.in_green
            } else {
                R.color.out_red
            }

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.modalIndicatorText),
                            ViewMatchers.withText(text)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            val amt = "Rp $formattedMarginAmt"

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.profitAmountText),
                            ViewMatchers.withText(amt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.modalIndicatorContainer)
                    )
            ).check(ViewAssertions.matches(
                    Matchers.allOf(
                        ViewMatchers.isDisplayed(),
                        ViewMatchers.hasBackground(color)
                    )
            ))

        }

        fun clickExpense() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Pengeluaran")
                    )
            ).perform(ViewActions.click())

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText("Tambah Pengeluaran")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun clickIncome() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Pemasukan")
                    )
            ).perform(ViewActions.click())

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText("Tambah Pemasukan")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

        }

        fun clickInputLayout() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.transaction_input_amount_layout)
                    )
            ).perform(ViewActions.click())
        }

        fun ensureSaveBtnEnabled() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_save)
                    )
            ).check(
                    ViewAssertions.matches(
                            Matchers.allOf(
                                    ViewMatchers.isDisplayed(),
                                    ViewMatchers.isEnabled()
                            )
                    )
            )
        }

        fun selectCategory(categoryName: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.selectedCatTv)
                    )
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Pilih kategori")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.openSearchBtn)
                    )
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.clear)
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.searchQueryBox),
                            ViewMatchers.withHint("Cari Disini")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.searchQueryBox)
                    )
            ).perform(
                    ViewActions.replaceText(categoryName)
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText(categoryName)
                    )
            ).perform(
                    ViewActions.click()
            )
        }

        fun ensureCategoryName(categoryName: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.selectedCatTv),
                            ViewMatchers.withText(categoryName)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun enterNote(note: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.note)
                    )
            ).perform(ViewActions.replaceText(note), ViewActions.closeSoftKeyboard())
        }

        fun ensureNote(note: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.note),
                            ViewMatchers.withText(note)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun enterProductName(productName: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productText)
                    )
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.title),
                            ViewMatchers.withText("Detail Produk")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Tambah")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Simpan Produk")
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productNameET)
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.isDisplayed())
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productNameET)
                    )
            ).perform(
                    ViewActions.replaceText(productName), ViewActions.closeSoftKeyboard()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_plus)
                    )
            ).perform(
                    ViewActions.click()
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_count)
                    )
            ).check(
                    ViewAssertions.matches(ViewMatchers.withText("1"))
            )

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Simpan Produk")
                    )
            ).perform(
                    ViewActions.click()
            )
        }

        fun ensureProductName(productName: String) {
            val text = "$productName (1)"
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.productText),
                            ViewMatchers.withText(text)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun saveTrx() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.btn_save)
                    )
            ).perform(ViewActions.click())
        }

    }

}