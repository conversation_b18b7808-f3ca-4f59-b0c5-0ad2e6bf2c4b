package com.bukuwarung.actions.misc

import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObjectNotFoundException
import androidx.test.uiautomator.UiSelector

class DatePickerAction {

    companion object {

        fun pickADate(newDate: Int, mUiDevice: UiDevice) {
            val mDate = mUiDevice.findObject(UiSelector().text(newDate.toString()))
            mDate.click()
        }

        fun clicksOk(mUiDevice: UiDevice) {
            try {
                // if device is in English Locale
                val okBtn = mUiDevice.findObject(UiSelector().text("OK"))
                okBtn.click()
            } catch (ex: UiObjectNotFoundException) {
                // if device is in Indonesian Locale
                val okBtn = mUiDevice.findObject(UiSelector().text("OKE"))
                okBtn.click()
            }
        }

    }

}