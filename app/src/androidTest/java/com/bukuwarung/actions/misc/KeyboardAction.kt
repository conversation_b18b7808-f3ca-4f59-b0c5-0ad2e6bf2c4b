package com.bukuwarung.actions.misc

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import org.hamcrest.Matchers
import java.lang.Exception

class KeyboardAction {

    companion object {

        fun enterAmountToKeyboard(amount: Int) {
            val amountString = amount.toString()

            amountString.forEach {
                when (it) {
                    '0' -> {
                        click0()
                    }
                    '1' -> {
                        click1()
                    }
                    '2' -> {
                        click2()
                    }
                    '3' -> {
                        click3()
                    }
                    '4' -> {
                        click4()
                    }
                    '5' -> {
                        click5()
                    }
                    '6' -> {
                        click6()
                    }
                    '7' -> {
                        click7()
                    }
                    '8' -> {
                        click8()
                    }
                    '9' -> {
                        click9()
                    }
                }
            }
        }

        fun delete1Char() {
            try {
                val appCompatImageView2 = Espresso.onView(
                        Matchers.allOf(ViewMatchers.withId(R.id.delete_button),
                                ViewMatchers.isDisplayed()))
                appCompatImageView2.perform(ViewActions.click())
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }

        fun submitKeyboard() {
            try {
                val appCompatImageView2 = Espresso.onView(
                        Matchers.allOf(ViewMatchers.withId(R.id.submit_button),
                                ViewMatchers.isDisplayed()))
                appCompatImageView2.perform(ViewActions.click())
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }

        private fun clickNumber(numberId: Int, numberString: String) {
            val appCompatButton = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(numberId), ViewMatchers.withText(numberString),
                            ViewMatchers.isDisplayed()))
            appCompatButton.perform(ViewActions.click())
        }

        private fun click1() {
            clickNumber(numberId = R.id.one_button, numberString = "1")
        }

        private fun click2() {
            clickNumber(numberId = R.id.two_button, numberString = "2")
        }

        private fun click3() {
            clickNumber(numberId = R.id.three_button, numberString = "3")
        }

        private fun click4() {
            clickNumber(numberId = R.id.four_button, numberString = "4")
        }

        private fun click5() {
            clickNumber(numberId = R.id.five_button, numberString = "5")
        }

        private fun click6() {
            clickNumber(numberId = R.id.six_button, numberString = "6")
        }

        private fun click7() {
            clickNumber(numberId = R.id.seven_button, numberString = "7")
        }

        private fun click8() {
            clickNumber(numberId = R.id.eight_button, numberString = "8")
        }

        private fun click9() {
            clickNumber(numberId = R.id.nine_button, numberString = "9")
        }

        private fun click0() {
            clickNumber(numberId = R.id.zero_button, numberString = "0")
        }

    }

}