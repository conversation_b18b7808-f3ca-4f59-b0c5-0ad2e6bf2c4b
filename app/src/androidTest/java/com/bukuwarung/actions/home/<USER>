package com.bukuwarung.actions.home

import androidx.test.espresso.AmbiguousViewMatcherException
import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.bukuwarung.R
import com.bukuwarung.actions.misc.KeyboardAction
import com.bukuwarung.actions.transaction.TransactionActions
import com.bukuwarung.actions.transaction.TransactionType
import com.bukuwarung.allowMultipleViews
import com.bukuwarung.waitForExecution
import org.hamcrest.Matchers
import java.text.SimpleDateFormat
import java.util.*

class TransactionTabAction {

    companion object {

        fun closeTutorial(mUiDevice: UiDevice?) {
            try {
                waitForExecution(millisToWait = 2000)

                val skipBtn = mUiDevice!!.findObject(UiSelector().text("<PERSON><PERSON>"))
                skipBtn.click()
            } catch (ex: Exception) {
                // continue
            }
        }

        fun checkEmptyViews() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.no_trans_header),
                            ViewMatchers.withText("Belum Ada Transaksi")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.addCashTransactionBtn),
                            ViewMatchers.withText("TAMBAH TRANSAKSI")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun createsNewTransaction(amount: Int, categoryName: String? = null,
                                  note: String? = null, transactionType: TransactionType = TransactionType.EXPENSE,
                                  productName: String? = null) {
            val extendedFloatingActionButton = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.addCashTransactionBtn),
                            ViewMatchers.isDisplayed()))
            extendedFloatingActionButton.perform(ViewActions.click())

            TransactionActions.checkEmptyViews()

            TransactionActions.clickIncome()

            if (transactionType == TransactionType.EXPENSE)
                TransactionActions.clickExpense()

            TransactionActions.clickInputLayout()

            KeyboardAction.enterAmountToKeyboard(amount)

            KeyboardAction.submitKeyboard()

            TransactionActions.ensureSaveBtnEnabled()

            if (!categoryName.isNullOrBlank()) {
                TransactionActions.selectCategory(categoryName)

                TransactionActions.ensureCategoryName(categoryName)
            }

            if (!note.isNullOrBlank()) {
                TransactionActions.enterNote(note)

                TransactionActions.ensureNote(note)
            }

            if (!productName.isNullOrBlank()) {
                TransactionActions.enterProductName(productName)

                TransactionActions.ensureProductName(productName)
            }

            TransactionActions.saveTrx()

        }

        fun findsTopTab(formattedIncome: String, formattedExpense: String) {
            val calendar = Calendar.getInstance()
            val formatter = SimpleDateFormat("MMM YYYY")
            val date = formatter.format(calendar.time)
            val trxMonth = "Transaksi $date"

            val income = "Rp $formattedIncome"
            val expense = "Rp $formattedExpense"

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.incomeTitle),
                            ViewMatchers.withText("Pemasukan")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            allowMultipleViews {
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.creditTotal),
                                ViewMatchers.withText(income)
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            }

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.expenseTitle),
                            ViewMatchers.withText("Pengeluaran")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            allowMultipleViews {
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.debitTotal),
                                ViewMatchers.withText(expense)
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            }

            allowMultipleViews {
                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withId(R.id.report_icon)
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

                Espresso.onView(
                        Matchers.allOf(
                                ViewMatchers.withText("Lihat Laporan Keuangan")
                        )
                ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
            }
        }

        fun checkTrxCount(count: Int) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.transactionCountTv),
                            ViewMatchers.withText("$count Transaksi")
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun dateExist(date: Date) {
            val formatter = SimpleDateFormat("dd MMM YYYY")
            val date = formatter.format(date)

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.name),
                            ViewMatchers.withText(date)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun trxWithCategoryExist(categoryName: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.name),
                            ViewMatchers.withText(categoryName)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun trxWithAmtExist(formattedAmt: String, transactionType: TransactionType) {
            val id = if (transactionType == TransactionType.EXPENSE) {
                R.id.expenseAmount
            } else {
                R.id.incomeAmount
            }
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(id),
                            ViewMatchers.withText(formattedAmt)
                    )
            ).check(ViewAssertions.matches(ViewMatchers.isDisplayed()))
        }

        fun clickTrxWithAmt(formattedAmt: String, transactionType: TransactionType) {
            val id = if (transactionType == TransactionType.EXPENSE) {
                R.id.expenseAmount
            } else {
                R.id.incomeAmount
            }
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(id),
                            ViewMatchers.withText(formattedAmt)
                    )
            ).perform(ViewActions.click())
        }
    }

}