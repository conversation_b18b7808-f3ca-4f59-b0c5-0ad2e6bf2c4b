package com.bukuwarung.actions.home

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import com.bukuwarung.childAtPosition
import com.bukuwarung.doesNotExist
import com.bukuwarung.getInitial1Char
import org.hamcrest.Matchers

class CustomerTabAction {

    companion object {

        fun checkCustomerExist(customerName: String, formattedBalance: String,
                               balanceState: String = "Utang Pelanggan", exist: Boolean = true) {
            val initial = customerName.getInitial1Char()
            val appendBalance = "Rp $formattedBalance"

            val textView5 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.tvName), ViewMatchers.withText(customerName),
                            ViewMatchers.isDisplayed()))

            if (exist) {
                textView5.check(ViewAssertions.matches(ViewMatchers.withText(customerName)))
            } else {
                textView5.doesNotExist()
            }

            val textView6 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.nameInitials), ViewMatchers.withText(initial),
                            ViewMatchers.isDisplayed()))

            if (exist) {
                textView6.check(ViewAssertions.matches(ViewMatchers.withText(initial)))
            } else {
                textView6.doesNotExist()
            }

            val textView7 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.balanceTv), ViewMatchers.withText(appendBalance),
                            ViewMatchers.isDisplayed()))

            if (exist) {
                textView7.check(ViewAssertions.matches(ViewMatchers.withText(appendBalance)))
            } else {
                textView7.doesNotExist()
            }

            val textView8 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.balanceState), ViewMatchers.withText(balanceState),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.balanceLayout),
                                            1),
                                    0),
                            ViewMatchers.isDisplayed()))

            if (exist) {
                textView8.check(ViewAssertions.matches(ViewMatchers.withText(balanceState)))
            } else {
                textView8.doesNotExist()
            }
        }

        fun checkBusinessName(businessName: String = "Usaha Saya") {
            val textView8 = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withText(businessName),
                            ViewMatchers.isDisplayed()))
            textView8.check(ViewAssertions.matches(ViewMatchers.withText(businessName)))
        }

        fun checkTabVisible() {
            val textView8 = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.largeLabel),
                            ViewMatchers.withText("Utang Piutang"),
                            ViewMatchers.isDisplayed()))
            textView8.check(ViewAssertions.matches(ViewMatchers.withText("Utang Piutang")))
        }

        fun checkCustomerCount(customerCount: Int) {
            val countText = "$customerCount Pelanggan"
            val textView8 = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.customersCountTv),
                            ViewMatchers.withText(countText),
                            ViewMatchers.isDisplayed()))
            textView8.check(ViewAssertions.matches(ViewMatchers.withText(countText)))
        }

        fun findsTutorialView() {
            val textView8 = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.customerTutorText),
                            ViewMatchers.withText("Lihat tutorial membuat catatan utang"),
                            ViewMatchers.isDisplayed()))
            textView8.check(ViewAssertions.matches(ViewMatchers.withText("Lihat tutorial membuat catatan utang")))

            val textView9 = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Lihat"),
                            ViewMatchers.isDisplayed()))
            textView9.check(ViewAssertions.matches(ViewMatchers.withText("Lihat")))

            val image = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Belum Ada Catatan Utang"),
                            ViewMatchers.isDisplayed()))
            image.check(ViewAssertions.matches(ViewMatchers.withText("Belum Ada Catatan Utang")))

            val waIcon = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.wa_icon),
                            ViewMatchers.isDisplayed()))
            waIcon.check(ViewAssertions.matches(ViewMatchers.withId(R.id.wa_icon)))

            val textview = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.customerHelpText),
                            ViewMatchers.withText("Butuh Bantuan?"),
                            ViewMatchers.isDisplayed()))
            textview.check(ViewAssertions.matches(ViewMatchers.withText("Butuh Bantuan?")))
        }

        fun findsHomeIcons() {
            findsCollectingCalendarIcon()
            findsNotificationIcon()
        }

        fun findsCollectingCalendarIcon() {
            val textView9 = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.collecting_calendar_icon),
                            ViewMatchers.isDisplayed()))
            textView9.check(ViewAssertions.matches(ViewMatchers.withId(R.id.collecting_calendar_icon)))
        }

        fun findsNotificationIcon() {
            val textView9 = Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.notification_icon),
                            ViewMatchers.isDisplayed()))
            textView9.check(ViewAssertions.matches(ViewMatchers.withId(R.id.notification_icon)))
        }

        fun findsTopTab(formattedUtangSaya: String, formattedUtangPelanggan: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.report_icon),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withId(R.id.report_icon)))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Lihat Laporan Utang"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Lihat Laporan Utang")))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Total Utang Saya"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Total Utang Saya")))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.creditTotal),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Rp $formattedUtangSaya")))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Total Utang Pelanggan"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Total Utang Pelanggan")))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.debitTotal),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Rp $formattedUtangPelanggan")))

        }

        fun findsTopIcons() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.openSearchBtn),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withId(R.id.openSearchBtn)))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.sortMenu),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withId(R.id.sortMenu)))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.downloadCstPdf),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withId(R.id.downloadCstPdf)))
        }

        fun findsTabFilter() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.filterText),
                            ViewMatchers.withText("Semua"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Semua")))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.filterText),
                            ViewMatchers.withText("Utang Pelanggan"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Utang Pelanggan")))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.filterText),
                            ViewMatchers.withText("Utang Saya"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Utang Saya")))

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.filterText),
                            ViewMatchers.withText("Lunas"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Lunas")))
        }

        fun clickCustomerFilter(name: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.filterText),
                            ViewMatchers.withText(name),
                            ViewMatchers.isDisplayed())
            ).perform(click())
        }

        fun checkDoesNotExist() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withText("Transaksi tidak ditemukan"),
                            ViewMatchers.isDisplayed())
            ).check(ViewAssertions.matches(ViewMatchers.withText("Transaksi tidak ditemukan")))
        }

        fun searchCustomer(query: String) {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.customersCountTv),
                            ViewMatchers.isDisplayed())
            ).perform(click())

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.searchQueryBox),
                            ViewMatchers.isDisplayed())
            ).perform(ViewActions.replaceText(query), ViewActions.closeSoftKeyboard())
        }

        fun closeSearch() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.clear),
                            ViewMatchers.isDisplayed())
            ).perform(click())
            Espresso.closeSoftKeyboard()
        }

        fun openUtangReport() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.summary_btn),
                            ViewMatchers.isDisplayed())
            ).perform(click())
        }

    }

}