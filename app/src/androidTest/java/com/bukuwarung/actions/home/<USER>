package com.bukuwarung.actions.home

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.matcher.ViewMatchers
import com.bukuwarung.R
import org.hamcrest.Matchers

class HomeAction {

    companion object {

        fun goToUtangTab() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.navigation_customers)
                    )
            ).perform(click())
        }

        fun goToTransactionTab() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.navigation_cash)
                    )
            ).perform(click())
        }

        fun goToProfileTab() {
            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.navigation_profile)
                    )
            ).perform(click())
        }

    }

}