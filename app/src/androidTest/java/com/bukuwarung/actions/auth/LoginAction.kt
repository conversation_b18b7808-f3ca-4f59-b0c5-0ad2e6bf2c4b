package com.bukuwarung.actions.auth

import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.matcher.ViewMatchers
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObjectNotFoundException
import androidx.test.uiautomator.UiSelector
import com.bukuwarung.R
import com.bukuwarung.childAtPosition
import com.bukuwarung.waitForExecution
import org.hamcrest.Matchers

class LoginAction {

    companion object {
        fun doNormalLogin(mUiDevice: UiDevice, userId: String) {
            val linearLayout = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.btn_start_phone_verification),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.overlay),
                                            1),
                                    1),
                            ViewMatchers.isDisplayed()))
            linearLayout.perform(ViewActions.click())

            // wont show up on device farm
            try {
                val mText = mUiDevice!!.findObject(UiSelector().text("NONE OF THE ABOVE"))
                mText.click() // to cancel Google auto fill number
            } catch (exception: UiObjectNotFoundException) {
                // continue
            }

            val appCompatEditText = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.phoneET),
                            childAtPosition(
                                    childAtPosition(
                                            ViewMatchers.withId(R.id.mobileNumberInputLayout),
                                            0),
                                    0),
                            ViewMatchers.isDisplayed()))
            appCompatEditText.perform(ViewActions.replaceText(userId), ViewActions.closeSoftKeyboard())

            val materialButton = Espresso.onView(
                    Matchers.allOf(ViewMatchers.withId(R.id.btn_sms), ViewMatchers.withText("SMS"),
                            ViewMatchers.isDisplayed()))
            materialButton.perform(ViewActions.click())

            waitForExecution()

            Espresso.onView(
                    Matchers.allOf(
                            ViewMatchers.withId(R.id.input_otp)
                    )
            ).perform(
                    ViewActions.replaceText("1234"), ViewActions.closeSoftKeyboard()
            )

            waitForExecution(millisToWait = 13000)
        }
    }

}