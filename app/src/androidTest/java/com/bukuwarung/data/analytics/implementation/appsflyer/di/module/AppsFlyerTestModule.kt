package com.bukuwarung.data.analytics.implementation.appsflyer.di.module

import com.bukuwarung.data.analytics.implementation.appsflyer.api.AppsFlyerIdGenerator
import com.bukuwarung.data.analytics.implementation.appsflyer.implementation.FakeAppsFlyerIdGenerator
import dagger.Binds
import dagger.Module
import dagger.hilt.components.SingletonComponent
import dagger.hilt.testing.TestInstallIn

@Module
@TestInstallIn(
    components = [SingletonComponent::class],
    replaces = [AppsFlyerModule::class],
)
interface AppsFlyerTestModule {
    @Binds
    fun FakeAppsFlyerIdGenerator.bindFakeAppsFlyerIdGenerator(): AppsFlyerIdGenerator
}
